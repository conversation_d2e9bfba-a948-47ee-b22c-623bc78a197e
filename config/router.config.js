/*
 * @Description:
 * @Author:
 * @Date: 2023-02-20 11:37:44
 * @LastEditTime: 2023-03-15 08:51:46
 * @LastEditors:
 */
export default [
  // app
  {
    path: '/',
    component: '../layouts/BlankLayout',
    routes: [
      { path: '/', redirect: '/user/login' },
      // 电子账单
      { path: '/fbd', redirect: '/financialManagement/myBill?foreignKey=billDownload' },
      // 运单明细
      { path: '/abnormalWaybill', redirect: '/smallBag/anomalyOrder?lineType=3&foreignKey=crm' },
      // 运单预报导入
      {
        path: '/forecastImport',
        redirect: '/smallBag/orderManagement/wayBillForecastImport?foreignKey=crm',
      },
      // 仓外异常件
      { path: '/abnormalOutsideWare', redirect: '/smallBag/anomalyOrder?type=2&foreignKey=crm' },
      // TEMU面单上传
      {
        path: '/temuLabelUpload',
        redirect: '/smallBag/orderManagement/finalFaceUpload?foreignKey=crm',
      },
      {
        name: 'websiteLogin',
        path: '/user/websitelogin',
        component: './WebSiteLogin/WebSiteLogin',
      },
      //user
      {
        path: '/user',
        component: '../layouts/UserLayout',
        routes: [
          {
            path: '/user',
            redirect: '/user/login',
          },
          {
            name: '登录',
            icon: 'smile',
            path: '/user/login',
            component: './User/Login',
          },
        ],
      },
      // 注册
      {
        path: '/register',
        component: '../layouts/RegisterLayout',
        routes: [
          //注册
          {
            name: '注册',
            path: '/register',
            component: './Register/index',
          },
        ],
      },
      // 忘记密码短信验证
      {
        name: '重置密码',
        path: '/resetPassword',
        component: './Forget/ResetPassword/index',
      },
      {
        path: '/userInitPassword',
        name: '初始化用户密码',
        icon: 'icon-wodeshouye',
        breadcrumbName: '初始化用户密码',
        component: './CustomerManager/CustomerInformation/UserInitPassword/index',
      },

      // 忘记密码
      {
        path: '/forget',
        component: '../layouts/RegisterLayout',
        routes: [
          //忘记密码
          {
            name: '注册',
            path: '/forget/password',
            component: './Forget/Password/index',
          },
          // 不符合密码规则的重置密码
          {
            name: '重置密码',
            path: '/forget/modificationPassword',
            component: './User/ModificationPassword',
          },
        ],
      },
      {
        path: '/',
        component: '../layouts/BasicLayout',
        routes: [
          // 我的首页
          {
            path: '/index',
            redirect: '/homePageList',
          },
          // 我的首页
          {
            path: '/homePageList',
            name: '首页',
            breadcrumbName: '首页',
            // icon: 'icon-homePage',
            icon: 'icon-wodeshouye',
            component: './HomePage/index',
          },
          // 小包专线
          {
            path: '/smallBag',
            name: '小包专线',
            breadcrumbName: '小包专线',
            // icon: 'icon-wodeshouye',
            // component: './SmallPacketLine/index',
            routes: [
              {
                path: '/smallBag',
                redirect: '/smallBag/smallPacketLine',
              },
              {
                path: '/smallBag/smallPacketLine',
                name: '小包专线',
                icon: 'icon-wodeshouye',
                breadcrumbName: '小包专线',
                component: './SmallBag/SmallPacketLine/index',
              },
              {
                path: '/smallBag/orderManagement',
                name: '订单管理',
                icon: 'icon-wodeshouye',
                breadcrumbName: '订单管理',
                routes: [
                  {
                    path: '/smallBag/orderManagement/creatOrder',
                    name: '创建订单',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '创建订单',
                    component: './SmallBag/OrderManagement/CreatOrder/index',
                  },
                  {
                    path: '/smallBag/orderManagement/orderList',
                    name: '订单查询',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '订单查询',
                    component: './SmallBag/OrderManagement/OrderList/index',
                  },
                  {
                    path: '/smallBag/orderManagement/expressProductChangeOrderRecord',
                    name: '转单号查询',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '转单号查询',
                    component: './SmallBag/AnomalyOrder/components/ExpressProductChangeOrderRecord',
                  },
                  {
                    // 订单详情
                    path: '/smallBag/orderManagement/orderDetails',
                    name: '订单详情',
                    component:
                      './SmallBag/OrderManagement/OrderList/components/YanwenOrder/YanwenOrderDetails',
                    hideInMenu: true,
                  },
                  {
                    // 订单详情
                    path: '/smallBag/orderManagement/businessDetails',
                    name: '订单详情',
                    component:
                      './SmallBag/OrderManagement/OrderList/components/BusinessOrder/BusinessOrderDetails',
                    hideInMenu: true,
                  },
                  {
                    // 已制单编辑
                    path: '/smallBag/orderManagement/makeEditing',
                    name: '已制单编辑',
                    component:
                      './SmallBag/OrderManagement/OrderList/components/YanwenOrder/YanwenMakeEditing',
                    hideInMenu: true,
                  },
                  {
                    // 商业快递已制单编辑
                    path: '/smallBag/orderManagement/businessMakeEditing',
                    name: '已制单编辑',
                    component:
                      './SmallBag/OrderManagement/OrderList/components/BusinessOrder/BusinessMakeEditing',
                    hideInMenu: true,
                  },
                  // {
                  //   path: '/smallBag/orderManagement/waybillCollected',
                  //   name: '待领取运单',
                  //   icon: 'icon-wodeshouye',
                  //   breadcrumbName: '待领取运单',
                  //   hideInMenu: true,
                  //   component: './SmallBag/OrderManagement/WaybillCollected/index',
                  // },
                  // {
                  //   path: '/smallBag/orderManagement/waybillCollected/WaybillCollectedDetails',
                  //   name: '待领取运单详情',
                  //   icon: 'icon-wodeshouye',
                  //   breadcrumbName: '待领取运单详情',
                  //   hideInMenu: true,
                  //   component:
                  //     './SmallBag/OrderManagement/WaybillCollected/WaybillCollectedDetails',
                  // },
                  {
                    path: '/smallBag/orderManagement/makeAssistant',
                    name: '制单助手',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '制单助手',
                    component: './SmallBag/OrderManagement/MakeAssistant/index',
                  },
                  {
                    path: '/smallBag/orderManagement/proofApply',
                    name: '证明申请',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '证明申请',
                    component: './SmallBag/OrderManagement/ProofApply/index',
                  },
                  {
                    path: '/smallBag/orderManagement/wayBillForecastImport',
                    name: '运单预报导入',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '运单预报导入',
                    component: './SmallBag/OrderManagement/WayBillForecastImport/index',
                  },
                  {
                    path: '/smallBag/orderManagement/proofOfTransaction',
                    name: '交易证明',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '交易证明',
                    component: './SmallBag/OrderManagement/ProofOfTransaction/index',
                  },
                  {
                    path: '/smallBag/orderManagement/deliveredForecast',
                    name: '自送预报',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '自送预报',
                    component: './SmallBag/OrderManagement/DeliveredForecast/index',
                  },
                  {
                    path: '/smallBag/orderManagement/finalFaceUpload',
                    name: 'Temu尾程面单上传',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: 'Temu尾程面单上传',
                    component: './SmallBag/OrderManagement/FinalFaceUpload/index',
                  },
                  {
                    path: '/smallBag/orderManagement/headTrack',
                    name: 'Temu头程轨迹查询',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: 'Temu头程轨迹查询',
                    component: './SmallBag/OrderManagement/HeadTrack/index',
                  },
                ],
              },
              // 海外派 -- 内有导入预报和订单状态
              {
                path: '/smallBag/hwp',
                name: '海外派',
                icon: 'shopping',
                routes: [
                  {
                    path: '/smallBag/hwp',
                    redirect: '/smallBag/hwp/importForecast',
                  },
                  // 导入预报 start
                  {
                    path: '/smallBag/hwp/importForecast',
                    name: '导入预报',
                    component: './SmallBag/Hwp/Import/ImportForecastIndex',
                  },
                  {
                    path: '/smallBag/hwp/importForecastUpdate',
                    name: '修改导入预报',
                    hideInMenu: true,
                    component: './SmallBag/Hwp/Import/ImportForecastUpdate',
                  },
                  // 导入预报 end
                  // 订单状态 start
                  {
                    path: '/smallBag/hwp/sendWingIndex',
                    name: '订单状态',
                    component: './SmallBag/Hwp/SendWing/sendWingIndex',
                  },
                ],
              },
              {
                path: '/smallBag/anomalyOrder',
                name: '异常订单',
                icon: 'icon-wodeshouye',
                breadcrumbName: '异常订单',
                component: './SmallBag/AnomalyOrder/index',
              },
              {
                path: '/smallBag/collectManagement',
                name: '揽收管理',
                icon: 'icon-wodeshouye',
                breadcrumbName: '揽收管理',
                routes: [
                  {
                    path: '/smallBag/collectManagement/addressMaintenance',
                    name: '地址库维护',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '地址库维护',
                    component: './SmallBag/CollectManagement/AddressMaintenance/index',
                  },
                  {
                    path: '/smallBag/collectManagement/collectApply',
                    name: '揽收申请',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '揽收申请',
                    component: './SmallBag/CollectManagement/CollectApply/index',
                  },
                  {
                    path: '/smallBag/collectManagement/material',
                    name: '物料申请',
                    icon: 'icon-wodeshouye',
                    routes: [
                      {
                        path: '/smallBag/collectManagement/material/materialList',
                        name: 'materialList',
                        icon: 'icon-wodeshouye',
                        component: './WorkOrder/Material/MaterialList',
                        routes: [
                          {
                            path:
                              '/smallBag/collectManagement/material/materialList/MaterStatusList',
                            name: 'MaterStatusList',
                            component: './WorkOrder/Material/MaterStatusList',
                            hideInMenu: true,
                          },
                        ],
                      },
                      {
                        path: '/smallBag/collectManagement/material/materapply',
                        name: '申请物料',
                        component: './WorkOrder/Material/Materapply',
                        hideInMenu: true,
                      },
                      {
                        path: '/smallBag/collectManagement/material/materupdate',
                        name: 'materupdate',
                        component: './WorkOrder/Material/Materupdate',
                      },
                      {
                        path: '/smallBag/collectManagement/material/materSearchlist/:searchId',
                        name: 'materSearchlist',
                        component: './WorkOrder/Material/MaterSearchList',
                      },
                      {
                        path: '/smallBag/collectManagement/material/materdetail/:workId',
                        name: 'materdetail',

                        component: './WorkOrder/Material/Materdetail',
                      },
                    ],
                  },
                ],
              },
              {
                path: '/smallBag/complianceMerchant',
                name: '9610合规化',
                icon: 'icon-wodeshouye',
                breadcrumbName: '9610合规化',
                component: './SmallBag/ComplianceMerchant/index',
              },
              {
                path: '/smallBag/accountManagement',
                name: '账号管理',
                icon: 'icon-wodeshouye',
                breadcrumbName: '账号管理',
                routes: [
                  {
                    path: '/smallBag/accountManagement/TaxNumberManagement',
                    name: '税号管理',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '税号管理',
                    component: './SmallBag/AccountManagement/TaxNumberManagement/index',
                  },
                  {
                    path: '/smallBag/accountManagement/platformAccount',
                    name: '平台账号管理',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '平台账号管理',
                    component: './SmallBag/AccountManagement/PlatformAccount/index',
                  },
                  {
                    path: '/smallBag/accountManagement/billingAccount',
                    name: '制单账号管理',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '制单账号管理',
                    component: './SmallBag/AccountManagement/BillingAccount/index',
                  },
                ],
              },
            ],
          },
          // FBA专线
          {
            path: '/fba',
            name: 'FBA专线',
            breadcrumbName: 'FBA专线',
            icon: 'icon-wodeshouye',
            // component: './FBA/index',
            routes: [
              {
                path: '/fba',
                redirect: '/fba/specialLine',
              },
              {
                path: '/fba/specialLine',
                name: 'FBA专线',
                icon: 'icon-wodeshouye',
                breadcrumbName: 'FBA专线',
                component: './FBA/SpecialLine/index',
              },
              {
                path: '/fba/trackQuery',
                name: '轨迹查询',
                icon: 'icon-wodeshouye',
                breadcrumbName: '轨迹查询',
                component: './FBA/TrackQuery/index',
                hideInMenu: true,
              },
              {
                path: '/fba/fbaOrder',
                name: '创建订单',
                icon: 'icon-wodeshouye',
                breadcrumbName: '创建订单',
                component: './FBA/FBAOrder/index',
              },
              {
                // FBA修改
                path: '/fba/fbaUpdate',
                name: '订单修改',
                breadcrumbName: '订单修改',
                component: './FBA/FBAOrderUpdate/index',
                hideInMenu: true,
              },
              // FBA详情信息
              {
                path: '/fba/fbaInfo',
                name: '订单详情',
                breadcrumbName: '订单详情',
                component: './FBA/FBAInfo/index',
                hideInMenu: true,
              },
              {
                path: '/fba/priceSheet',
                name: '报价单',
                icon: 'icon-wodeshouye',
                breadcrumbName: '报价单',
                component: './FBA/PriceSheet/index',
                hideInMenu: true,
              },
              {
                path: '/fba/fbaList',
                name: '订单查询',
                icon: 'icon-wodeshouye',
                breadcrumbName: '订单查询',
                component: './FBA/FBAList/index',
              },
              {
                path: '/fba/accountManagement',
                name: '账号管理',
                icon: 'icon-wodeshouye',
                breadcrumbName: '账号管理',
                routes: [
                  {
                    path: '/fba/accountManagement/receivingAddress',
                    name: '揽收地址管理',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '揽收地址管理',
                    component: './FBA/AccountManagement/ReceivingAddress/index',
                  },
                  {
                    path: '/fba/accountManagement/recipientsAddress',
                    name: '收件人地址管理',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '收件人地址管理',
                    component: './FBA/AccountManagement/RecipientsAddress/index',
                  },
                  {
                    path: '/fba/accountManagement/importInfo',
                    name: '进口信息管理',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '进口信息管理',
                    component: './FBA/AccountManagement/ImportInfo/index',
                  },
                  {
                    path: '/fba/accountManagement/makingAccount',
                    name: '制单账号管理',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '制单账号管理',
                    component: './FBA/AccountManagement/MakingAccount/index',
                  },
                ],
              },
            ],
          },
          // 海外派
          {
            path: '/overseas',
            name: '海外派',
            breadcrumbName: '海外派',
            icon: 'icon-wodeshouye',
            // component: './FBA/index',
            routes: [
              {
                path: '/overseas',
                redirect: '/overseas/home',
              },
              {
                path: '/overseas/home',
                name: '海外派',
                icon: 'icon-wodeshouye',
                breadcrumbName: '海外派',
                component: './Overseas/Home/index',
              },
              {
                path: '/overseas/orderManagement',
                name: '订单管理',
                icon: 'icon-wodegongdan',
                breadcrumbName: '订单管理',
                component: './Overseas/OrderManagement/index',
              },
              {
                path: '/overseas/finalWarehousing',
                name: '尾程入库',
                icon: 'icon-wodegongdan',
                breadcrumbName: '尾程入库',
                routes: [
                  {
                    path: '/overseas/finalWarehousing',
                    redirect: '/overseas/finalWarehousing/appointment',
                  },
                  {
                    path: '/overseas/finalWarehousing/appointment',
                    name: '尾程入库预约',
                    icon: 'icon-wodegongdan',
                    breadcrumbName: '尾程入库预约',
                    component: './Overseas/FinalWarehousing/Appointment/index',
                  },
                  {
                    path: '/overseas/finalWarehousing/query',
                    name: '入库预约查询',
                    icon: 'icon-wodegongdan',
                    breadcrumbName: '入库预约查询',
                    component: './Overseas/FinalWarehousing/Query/index',
                  },
                ],
              },
              {
                path: '/overseas/refundTicket',
                name: '退款工单',
                icon: 'icon-wodegongdan',
                breadcrumbName: '退款工单',
                component: './Overseas/RefundTicket/index',
              },
              {
                path: '/overseas/claimWorkOrder',
                name: '索赔工单',
                icon: 'icon-wodegongdan',
                breadcrumbName: '索赔工单',
                component: './Overseas/ClaimWorkOrder/index',
              },
              {
                path: '/overseas/abnormalOrder',
                name: '异常订单',
                icon: 'icon-wodegongdan',
                breadcrumbName: '异常订单',
                component: './Overseas/OverseaAbnormal/index',
              },
              {
                path: '/overseas/informationManagement',
                name: '信息管理',
                icon: 'icon-wodegongdan',
                breadcrumbName: '信息管理',
                routes: [
                  {
                    path: '/overseas/informationManagement',
                    redirect: '/overseas/informationManagement/addressManagement',
                  },
                  {
                    path: '/overseas/informationManagement/addressManagement',
                    name: '地址管理',
                    icon: 'icon-wodegongdan',
                    breadcrumbName: '地址管理',
                    component: './Overseas/AddressManagement/index',
                  },
                  {
                    path: '/overseas/informationManagement/myInformation',
                    name: '账号管理',
                    icon: 'icon-wodegongdan',
                    breadcrumbName: '账号管理',
                    component: './Overseas/MyInformation/index',
                  },
                ],
              },
            ],
          },
          // 财务管理
          {
            path: '/financialManagement',
            name: '财务管理',
            breadcrumbName: '财务管理',
            routes: [
              {
                path: '/financialManagement',
                redirect: '/financialManagement/myBill',
                icon: 'icon-wodezhangdan',
              },
              {
                path: '/financialManagement/myBill',
                name: '我的账单',
                icon: 'icon-wodeshouye',
                breadcrumbName: '我的账单',
                component: './FinancialManagement/MyBill/index',
              },
              {
                path: '/financialManagement/rechargeManagement',
                name: '充值管理',
                icon: 'icon-wodeshouye',
                breadcrumbName: '充值管理',
                routes: [
                  {
                    path: '/financialManagement/rechargeManagement/offlineTopUp',
                    name: '线下充值',
                    breadcrumbName: '线下充值',
                    component: './FinancialManagement/RechargeManagement/OfflineTopUp/index',
                  },
                  {
                    path: '/financialManagement/rechargeManagement/onlineCharge',
                    name: '线上充值',
                    breadcrumbName: '线上充值',
                    component: './FinancialManagement/RechargeManagement/OnlineCharge/index',
                  },
                  {
                    path: '/financialManagement/rechargeManagement/merchantConfirmPaymentAccount',
                    name: '确认商户',
                    breadcrumbName: '确认商户',
                    hideInMenu: true,
                    component:
                      './FinancialManagement/RechargeManagement/OfflineTopUp/components/SmallBagOfflineTopUp/MerchantConfirmPaymentAccount',
                  },
                  {
                    path: '/financialManagement/rechargeManagement/merchantUpdatePaymentAccount',
                    name: '补齐资料',
                    breadcrumbName: '补齐资料',
                    hideInMenu: true,
                    component:
                      './FinancialManagement/RechargeManagement/OfflineTopUp/components/SmallBagOfflineTopUp/MerchantUpdatePaymentAccount',
                  },
                ],
              },
              {
                path: '/financialManagement/invoiceList',
                name: 'invoiceList',
                component: './WorkOrder/InvoiceApply/InvoiceList',
                hideInMenu: true,
                routes: [
                  {
                    path: '/financialManagement/invoiceList/invoiceStatusList',
                    name: '发票申请',
                    breadcrumbName: '发票申请',
                    component: './WorkOrder/InvoiceApply/InvoiceStatusList',
                    hideInMenu: true,
                  },
                ],
              },
              {
                path: '/financialManagement/invoice',
                name: 'invoice',
                hideInMenu: true,
                routes: [
                  {
                    path: '/financialManagement/invoice/invoiceApply',
                    name: 'invoiceApply',
                    component: './WorkOrder/InvoiceApply/InvoiceApply',
                  },
                  {
                    path: '/financialManagement/invoice/invoiceReapply/:wordId',
                    name: 'invoiceReapply',
                    component: './WorkOrder/InvoiceApply/InvoiceReapply',
                  },
                  {
                    path: '/financialManagement/invoice/invoiceDetail/:wordId',
                    name: 'invoiceDetail',
                    component: './WorkOrder/InvoiceApply/InvoiceDetail',
                  },
                  {
                    path: '/financialManagement/invoice/invoiceSearchList/:searchValue',
                    name: 'invoiceSearchList',
                    component: './WorkOrder/InvoiceApply/InvoiceSearchList',
                  },
                ],
              },
              {
                path: '/financialManagement/priceInquiry',
                name: '价格查询',
                icon: 'icon-wodeshouye',
                breadcrumbName: '价格查询',
                routes: [
                  {
                    path: '/financialManagement/priceInquiry/quotationDownload',
                    name: '报价单下载',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '报价单下载',
                    component: './FinancialManagement/PriceInquiry/QuotationDownload/index',
                  },
                  {
                    path: '/financialManagement/priceInquiry/freightTrial',
                    name: '运价试算',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '运价试算',
                    component: './FinancialManagement/PriceInquiry/FreightTrial/index',
                  },
                  {
                    path: '/financialManagement/priceInquiry/currencyExchangeRate',
                    name: '货币税率查询',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '货币税率查询',
                    component: './FinancialManagement/PriceInquiry/CurrencyExchangeRate/index',
                  },
                ],
              },
              {
                path: '/financialManagement/payRecord',
                name: '充值记录',
                icon: 'icon-wodeshouye',
                breadcrumbName: '充值记录',
                component: './FinancialManagement/PayRecord/index',
              },
              {
                path: '/financialManagement/freightInterface',
                name: '运费接口',
                icon: 'icon-wodeshouye',
                breadcrumbName: '运费接口',
                component: './FinancialManagement/FreightInterface/index',
              },
            ],
          },
          // 服务管理
          {
            path: '/serviceManagement',
            name: '服务管理',
            breadcrumbName: '服务管理',
            // hideInMenu: true,
            routes: [
              {
                path: '/serviceManagement/',
                redirect: '/serviceManagement/workOrder/thawingRequest',
              },
              {
                path: '/serviceManagement/workOrder',
                name: '我的工单',
                icon: 'icon-wodeshouye',
                breadcrumbName: '我的工单',
                routes: [
                  {
                    path: '/serviceManagement/workOrder/thawingRequest',
                    name: '解冻申请',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '解冻申请',
                    component: './ServiceManagement/WorkOrder/ThawingRequest/index',
                  },
                  {
                    path: '/serviceManagement/workOrder/complaints',
                    name: '投诉',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '投诉',
                    component: './ServiceManagement/WorkOrder/Complaints/index',
                  },
                  {
                    path: '/serviceManagement/workOrder/recommendation',
                    name: '建议',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '建议',
                    component: './ServiceManagement/WorkOrder/Recommendation/index',
                  },
                  {
                    path: '/serviceManagement/workOrder/search',
                    name: '查件',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '查件',
                    component: './ServiceManagement/WorkOrder/Search/index',
                  },
                ],
              },
              {
                path: '/serviceManagement/tracking',
                name: '轨迹查询',
                icon: 'icon-wodeshouye',
                breadcrumbName: '轨迹查询',
                component: './SmallBag/Track/Tracking',
              },
              {
                path: '/serviceManagement/subscriptionManagement',
                name: '轨迹订阅',
                icon: 'icon-wodeshouye',
                breadcrumbName: '轨迹订阅',
                routes: [
                  {
                    path: '/serviceManagement/subscriptionManagement',
                    redirect: '/serviceManagement/subscriptionManagement/trajectorySubscription',
                  },
                  {
                    path: '/serviceManagement/subscriptionManagement/trajectorySubscription',
                    name: '轨迹订阅',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '轨迹订阅',
                    component:
                      './ServiceManagement/SubscriptionManagement/TrajectorySubscription/index',
                  },
                ],
              },
              {
                path: '/serviceManagement/timeBaseStatistics',
                name: '时效统计',
                icon: 'icon-wodeshouye',
                breadcrumbName: '时效统计',
                component:
                  './SmallBag/OrderManagement/TimeBaseStatistics/components/TimeBaseStatistics',
              },
              {
                name: '通知通告',
                icon: 'warning',
                path: '/serviceManagement/content',
                hideInMenu: true,
                routes: [
                  {
                    path: '/serviceManagement/content/noticeList/:categoryId',
                    name: 'notice',
                    component: './NotificationContent/noticeList',
                    hideInMenu: true,
                  },
                  {
                    path: '/serviceManagement/content/notificationMessage',
                    name: 'notice',
                    component: './NotificationContent/notificationMessage',
                    hideInMenu: true,
                  },
                ],
              },
              {
                path: '/serviceManagement/messageCenter',
                name: '消息中心',
                icon: 'icon-wodeshouye',
                breadcrumbName: '消息中心',
                routes: [
                  {
                    path: '/serviceManagement/messageCenter/messageNotification',
                    name: '消息通知',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '消息通知',
                    component: './CustomerManager/MessageCenter/MessageNotification/index',
                  },
                  {
                    path: '/serviceManagement/messageCenter/messageSubscription',
                    name: '消息订阅',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '消息订阅',
                    component: './CustomerManager/MessageCenter/MessageSubscription/index',
                  },
                  {
                    path: '/serviceManagement/messageCenter/downloadCenter',
                    name: '下载中心',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '下载中心',
                    component: './CustomerManager/MessageCenter/DownloadCenter/index',
                  },
                ],
              },
              {
                path: '/serviceManagement/helpCenter',
                name: '帮助中心',
                icon: 'icon-wodeshouye',
                breadcrumbName: '帮助中心',
                component: './ServiceManagement/HelpCenter/index',
              },
            ],
          },
          // 用户管理
          {
            path: '/customerManager',
            name: '用户管理',
            breadcrumbName: '用户管理',
            // hideInMenu: true,
            routes: [
              {
                path: '/customerManager/',
                redirect: '/customerManager/customerInformation/myProfile',
              },
              {
                path: '/customerManager/customerInformation',
                name: '商户信息',
                icon: 'icon-wodeshouye',
                breadcrumbName: '商户信息',
                routes: [
                  {
                    path: '/customerManager/customerInformation/myProfile',
                    name: '我的资料',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '我的资料',
                    component: './CustomerManager/CustomerInformation/MyProfile/index',
                  },
                  {
                    path: '/customerManager/customerInformation/realName',
                    name: '实名认证',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '实名认证',
                    component: './CustomerManager/CustomerInformation/RealName/index',
                  },
                  {
                    path: '/customerManager/customerInformation/changePassword',
                    name: '修改密码',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '修改密码',
                    component: './CustomerManager/CustomerInformation/ChangePassword/index',
                  },
                  {
                    path: '/customerManager/customerInformation/contract',
                    name: '合同信息',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '合同信息',
                    component: './CustomerManager/CustomerInformation/Contract/index',
                  },
                  {
                    path: '/customerManager/customerInformation/CompleteAttach',
                    name: '补齐证件照',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '补齐证件照',
                    component: './CustomerManager/CustomerInformation/CompleteAttach/index',
                  },
                  {
                    path: '/customerManager/customerInformation/userInitPassword',
                    name: '初始化用户密码',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '初始化用户密码',
                    component: './CustomerManager/CustomerInformation/UserInitPassword/index',
                  },
                ],
              },
              {
                path: '/customerManager/PersonnelManagement',
                name: '人员管理',
                icon: 'icon-wodeshouye',
                breadcrumbName: '人员管理',
                routes: [
                  {
                    path: '/customerManager/personnelManagement/contactManagement',
                    name: '联系人管理',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '联系人管理',
                    component: './CustomerManager/PersonnelManagement/ContactManagement/index',
                  },
                  {
                    path: '/customerManager/personnelManagement/subuserManagement',
                    name: '用户管理',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '用户管理',
                    component: './CustomerManager/PersonnelManagement/SubuserManagement/index',
                  },
                  {
                    path: '/customerManager/personnelManagement/customerSafe',
                    name: '安全设置',
                    icon: 'icon-wodeshouye',
                    breadcrumbName: '安全设置',
                    component: './CustomerManager/PersonnelManagement/CustomerSafe/index',
                  },
                ],
              },
            ],
          },
          {
            name: 'exception',
            icon: 'warning',
            path: '/exception',
            hideInMenu: true,
            routes: [
              {
                name: '403',
                path: '/exception/403',
                component: './Exception/403',
              },
              {
                name: '404',
                path: '/exception/404',
                component: './Exception/404',
              },
              {
                name: '500',
                path: '/exception/500',
                component: './Exception/500',
              },
              {
                name: '400',
                path: '/exception/400',
                component: './Exception/400',
              },
              {
                name: '3001',
                path: '/exception/3001',
                component: './Exception/3001',
              },
              {
                name: '3005',
                path: '/exception/3005',
                component: './Exception/3005',
              },
              {
                name: '3007',
                path: '/exception/3007',
                component: './Exception/3007',
              },
              {
                name: '5015',
                path: '/exception/5015',
                component: './Exception/5015',
              },
            ],
          },
        ],
      },
    ],
  },
];
