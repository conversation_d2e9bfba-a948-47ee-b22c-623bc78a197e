import React, { useEffect, useRef, useState } from 'react';
import { router } from 'umi';
import {
  Avatar,
  Badge,
  Button,
  Dropdown,
  Modal,
  Select,
  Space,
  Popover,
  Tabs,
  Divider,
  List,
} from 'antd';
import {
  BellOutlined,
  LockOutlined,
  LogoutOutlined,
  SearchOutlined,
  UserOutlined,
  DownOutlined,
} from '@ant-design/icons';
import useBusinessApplySimpleStatus from '@/hooks/useBusinessApplySimpleStatus';
import useSingleSignOn from '@/hooks/useSingleSignOn';
import { useMount, useUpdateEffect } from 'ahooks';
import { businessIntroduction } from '@/utils/commonConstant';
import BillingServiceModal from './BillingServiceModal';
import DeliveryPromiseModal from './DeliveryPromiseModal';
import AuthNode from '@/components/AuthNode';
import './index.less';
import moment from 'moment';
import { formatMessage, setLocale, getLocale } from 'umi-plugin-react/locale';
import { logSave, LogType } from '@/utils/logSave';
import { handleBusinessStatus } from '@/utils/utils';

const tabData = [
  { label: formatMessage({ id: `下载中心` }), key: '1', children: null }, // 务必填写 key
  { label: formatMessage({ id: `消息通知` }), key: '2', children: null },
];

const Index = props => {
  const {
    dispatch,
    user: {
      userInfo: { userName, isAdmin },
      menuSearchList,
    },
    smallBagStatus,
    fbaStatus,
    location: { pathname },
    sendReadMessage,
    downloadMessage,
  } = props;
  const modalRef = useRef(); // 计费服务确认书
  const deliveryPromiseModalRef = useRef(); // 发货承诺书
  const [searchState, setSearchState] = useState(2);
  const [isShowButton, setIsShowButton] = useState(false); // 是否显示切换旧版按钮
  const [selectMenuValue, setSelectMenuValue] = useState(); // 选择的菜单
  const [isShowBillServiceButton, setIsShowBillServiceButton] = useState(false); // 是否显示计费服务确认书
  const [isShowDeliveryPromiseButton, setIsShowDeliveryPromiseButton] = useState(false); // 是否显示发货承诺书
  const [messageTotal, setMessageTotal] = useState(0); // 消息通知总数
  const [downloadTotal, setDownloadTotal] = useState(0); // 下载中心总数
  const [tabActiveKey, setTabActiveKey] = useState('1');
  const [tabList, setTabList] = useState(tabData);

  useMount(() => {
    initialFunc();
    getMessageCount();
  });

  const initialFunc = async () => {};

  useEffect(() => {
    if (pathname.includes('smallBag')) {
      // setIsShowBillServiceButton(true); && pathname.split('/').length - 1 > 1
      checkBillServiceStatus();
      checkDeliveryPromiseStatus();
    }
  }, [pathname]);

  useUpdateEffect(() => {
    if (sendReadMessage || downloadMessage) {
      getMessageCount();
    }
  }, [sendReadMessage, downloadMessage]);

  useUpdateEffect(() => {
    if (isShowDeliveryPromiseButton) {
      return deliveryPromiseModalRef.current?.show();
    } else if (isShowBillServiceButton) {
      return modalRef.current?.show();
    }
  }, [isShowBillServiceButton, isShowDeliveryPromiseButton]);
  /**
   * 发货承诺书
   */
  const checkDeliveryPromiseStatus = () => {
    dispatch({
      type: 'homePage/checkDeliveryPromiseStatus',
      callback: response => {
        if (response.success) {
          setIsShowDeliveryPromiseButton(response?.data?.status == 0 ? false : true);
        }
      },
    });
  };
  /**
   * 计费服务确认书
   */
  const checkBillServiceStatus = () => {
    dispatch({
      type: 'homePage/checkBillServiceStatus',
      callback: response => {
        if (response.success) {
          setIsShowBillServiceButton(response?.data?.billService == 0 ? false : true);
        }
      },
    });
  };

  const handleLogout = () => {
    logSave(LogType.home18);
    localStorage.removeItem('productData');
    localStorage.removeItem('countriesData');
    localStorage.removeItem('tableData');
    localStorage.removeItem('deliveryAccount');
    localStorage.removeItem('miscellaneous');
    localStorage.removeItem('number');
    sessionStorage.removeItem('USER_ACTION_BUSINESS');
    sessionStorage.removeItem('PATHNAME');
    dispatch({
      type: 'login/logout',
    });
  };

  const items = [
    {
      key: '2',
      label: (
        <a
          onClick={() => {
            router.push(`/customerManager/customerInformation/changePassword`);
            logSave(LogType.home17);
          }}
        >
          <LockOutlined />
          {formatMessage({ id: `修改密码` })}
        </a>
      ),
    },
    {
      key: '3',
      label: (
        <a onClick={handleLogout}>
          <LogoutOutlined />
          {formatMessage({ id: `退出登录` })}
        </a>
      ),
    },
  ];

  const handleSelectChange = value => {
    if (value) {
      logSave(LogType.home15);
      if (value === '/fba/trackQuery') {
        setSelectMenuValue('/fba/specialLine');
      } else if (value === '/serviceManagement/tracking') {
        setSelectMenuValue('/smallBag/smallPacketLine');
      } else {
        setSelectMenuValue(undefined);
      }
      // setSearchState(2);
      // 当未开通小包和FBA专线时，点击小包和FBA专线需要单独处理
      if ((value === '/smallBag' || value.includes('smallBag')) && !smallBagStatus) {
        handleBusinessStatus('0', dispatch);
        return;
      } else if ((value === '/fba' || value.includes('fba')) && !fbaStatus) {
        handleBusinessStatus('1', dispatch);
        return;
      } else if (value === '/overseas' || value.includes('overseas')) {
        handleBusinessStatus('2', dispatch);
        return;
      }
      let PATHNAME = sessionStorage.getItem('PATHNAME');
      if (PATHNAME == value) {
        localStorage.removeItem('productData');
        localStorage.removeItem('countriesData');
        localStorage.removeItem('tableData');
        localStorage.removeItem('deliveryAccount');
        localStorage.removeItem('miscellaneous');
        localStorage.removeItem('number');
        router.go(0);
      }
      if (PATHNAME != value && value == '/smallBag/orderManagement/orderList') {
        localStorage.removeItem('productData');
        localStorage.removeItem('countriesData');
        localStorage.removeItem('tableData');
        localStorage.removeItem('deliveryAccount');
        localStorage.removeItem('miscellaneous');
        localStorage.removeItem('number');
      }
      router.push(value);
    } else {
      // setSearchState(2);
      setSelectMenuValue(undefined);
    }
  };

  const handleChangeName = item => {
    return `${item?.parentMenuName?.join('/')}${item?.parentMenuName?.length > 0 ? '/' : ''}${
      item.name
    }`;
  };

  const renderTabChildren = (data, key) => (
    <List
      size="small"
      dataSource={data}
      renderItem={item => (
        <List.Item style={{ width: '220px' }}>
          <a onClick={() => handleToMessage(key)} style={{ color: '#817b7b' }}>
            {item}
          </a>
        </List.Item>
      )}
      className="mb-3"
    />
  );
  /**
   * 获取消息总数
   */
  const getMessageCount = () => {
    const params = {
      readSign: '0',
      current: 1,
      size: 10,
      downloadFlag: false,
    };
    dispatch({
      type: 'messageCenter/pushList',
      payload: params,
      callback: response => {
        if (response.success) {
          setMessageTotal(response.data.total);
          setDownloadTotal(response.data.downloadTaskData.total);
          setTabList(
            tabList.map(item => {
              if (item.key === '2') {
                return {
                  ...item,
                  label: `${formatMessage({ id: '消息通知' })}(${response.data.total})`,
                  children: renderTabChildren(
                    response?.data?.data?.map(
                      val => `${val?.subscribeType}${formatMessage({ id: '调整通知' })}`
                    ) ?? [],
                    item?.key
                  ),
                };
              } else if (item.key === '1') {
                return {
                  ...item,
                  label: `${formatMessage({ id: '下载中心' })}(${
                    response?.data?.downloadTaskData?.total
                  })`,
                  children: renderTabChildren(
                    response?.data?.downloadTaskData?.list?.map(
                      val => `${val?.taskName}${formatMessage({ id: '已生成' })}${val?.createTime}`
                    ) ?? [],
                    item?.key
                  ),
                };
              }
              return item;
            })
          );
        }
      },
    });
  };

  // 跳转到消息订阅页面
  const handleToMessage = key => {
    let keyValue = key ?? tabActiveKey;
    if (keyValue === '2') {
      logSave(LogType.home16);
      router.push('/serviceManagement/messageCenter/messageNotification');
    } else if (keyValue === '1') {
      router.push('/serviceManagement/messageCenter/downloadCenter');
    }
  };

  const popoverContent = (
    <>
      <Tabs items={tabList} activeKey={tabActiveKey} onChange={setTabActiveKey} />
      <Divider style={{ margin: 0 }} />
      <Button type="link" block onClick={() => handleToMessage()}>
        {formatMessage({ id: '查看更多' })}
      </Button>
    </>
  );

  const handleChooseLanguage = ({ key }) => {
    setLocale(key);
  };

  return (
    <Space className="pr-3">
      {isShowDeliveryPromiseButton && (
        <Button
          type="link"
          onClick={() => {
            deliveryPromiseModalRef.current?.show();
          }}
          style={{
            fontSize: 14,
            color: '#333',
          }}
        >
          {formatMessage({ id: '发货承诺书' })}
        </Button>
      )}
      {isShowBillServiceButton && (
        <Button
          type="link"
          onClick={() => {
            modalRef.current?.show();
          }}
          style={{
            fontSize: 14,
            color: '#333',
          }}
        >
          {formatMessage({ id: '计费服务确认书' })}
        </Button>
      )}

      {searchState === 1 && (
        <Button type="link" icon={<SearchOutlined />} onClick={() => setSearchState(2)} />
      )}
      {searchState === 2 && (
        <Select
          allowClear
          showSearch
          value={selectMenuValue}
          showArrow={false}
          style={{ width: 250 }}
          placeholder={formatMessage({ id: '请输入关键字搜索菜单' })}
          filterOption={(input, option) =>
            option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
          }
          getPopupContainer={triggerNode => triggerNode.parentElement}
          onChange={handleSelectChange}
        >
          {menuSearchList &&
            menuSearchList.map((item, index) => {
              return (
                <Select.Option key={index} value={item.path}>
                  {handleChangeName(item)}
                </Select.Option>
              );
            })}
        </Select>
      )}
      <Badge count={messageTotal + downloadTotal} size="small" offset={[-4, 0]}>
        <Popover
          title={null}
          content={popoverContent}
          overlayStyle={{ paddingTop: '8.25px' }}
          overlayClassName="ant-top-popover"
        >
          <Button type="link" icon={<BellOutlined />} className="mr-2" />
        </Popover>
      </Badge>
      {isShowButton && (
        <AuthNode userInfo={{ isAdmin }}>
          <Button type="text" onClick={event => useSingleSignOn(dispatch)}>
            {formatMessage({ id: '切换旧版' })}
          </Button>
        </AuthNode>
      )}
      <Dropdown
        menu={{
          items: [
            {
              label: '简体中文',
              key: 'zh-CN',
            },
            {
              label: '繁体中文',
              key: 'zh-TW',
            },
          ],
          onClick: handleChooseLanguage,
        }}
        placement="bottomCenter"
        arrow
      >
        <span className="mr-2 cursor-pointer" onClick={e => e.preventDefault()}>
          <Space>
            {getLocale() === 'zh-CN' ? '简体中文' : '繁体中文'}
            <DownOutlined />
          </Space>
        </span>
      </Dropdown>
      <Dropdown menu={{ items, selectable: true }}>
        <Space>
          <Avatar size={24} icon={<UserOutlined />} />
          <span className="cursor-pointer">{userName}</span>
        </Space>
      </Dropdown>
      <BillingServiceModal
        {...props}
        modalRef={modalRef}
        onFinish={() => {
          setIsShowBillServiceButton(false);
        }}
      />
      <DeliveryPromiseModal
        {...props}
        modalRef={deliveryPromiseModalRef}
        onFinish={() => {
          setIsShowDeliveryPromiseButton(false);
        }}
      />
    </Space>
  );
};

export default Index;
