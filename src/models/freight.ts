import {
  busManyPieceBatch,
  chargePrice,
  chargePriceOverseas,
  downloadCount,
  getCalcConfig,
  getCountries,
  getProducts,
  getQuotationInfo,
  getWarehouses,
  manyPiece,
  manyPieceBatch,
  quotationDownloadList,
  applyQuotation,
} from '@/services/freight';

export default {
  namespace: 'freight',

  state: {},

  effects: {
    *applyQuotation({ payload, callback }, { call }) {
      const response = yield call(applyQuotation, payload);
      if (callback) {
        callback(response);
      }
    },
    *getWarehouses({ payload, callback }, { call }) {
      const response = yield call(getWarehouses, payload);
      if (callback) {
        callback(response);
      }
    },
    *manyPieceBatch({ payload, callback }, { call }) {
      const response = yield call(manyPieceBatch, payload);
      if (callback) {
        callback(response);
      }
    },
    *busManyPieceBatch({ payload, callback }, { call }) {
      const response = yield call(busManyPieceBatch, payload);
      if (callback) {
        callback(response);
      }
    },
    *getCalcConfig({ payload, callback }, { call }) {
      const response = yield call(getCalcConfig, payload);
      if (callback) {
        callback(response);
      }
    },
    *manyPiece({ payload, callback }, { call }) {
      const response = yield call(manyPiece, payload);
      if (callback) {
        callback(response);
      }
    },
    *getProducts({ payload, callback }, { call }) {
      const response = yield call(getProducts, payload);
      if (callback) {
        callback(response);
      }
    },
    *getCountries({ payload, callback }, { call }) {
      const response = yield call(getCountries, payload);
      if (callback) {
        callback(response);
      }
    },
    *chargePrice({ payload, callback }, { call }) {
      const response = yield call(chargePrice, payload);
      if (callback) {
        callback(response);
      }
    },
    *chargePriceOverseas({ payload, callback }, { call }) {
      const response = yield call(chargePriceOverseas, payload);
      if (callback) {
        callback(response);
      }
    },
    *quotationInfo({ payload, callback }, { call }) {
      const response = yield call(getQuotationInfo, {});
      if (callback) {
        callback(response);
      }
    },
    *quotationDownloadList({ payload, callback }, { call }) {
      const response = yield call(quotationDownloadList, payload);
      if (callback) {
        callback(response);
      }
    },
    *downloadCount({ payload, callback }, { call }) {
      const response = yield call(downloadCount, payload);
      if (callback) {
        callback(response);
      }
    },
  },

  reducers: {},

  subscriptions: {},
};
