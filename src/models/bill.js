import {
  getBillBalance,
  getNumberList,
  getAccountPeriodList,
  typeList,
  getHistoryListByType,
  detail,
  getBankAccountList,
  unBilledBillList,
  validateHwpMerchant,
  withoutList,
  sendWinglist,
  getChargeRecordList,
  getFbaBankAccountList,
  cancelOrderWithDebitPage,
  exportFile,
  exportCheck,
  getEBillList,
  getOverseaRecord,
  createAlipayScanOrder,
  queryOrderState,
  generateExampleData,
  addReceiverConfig,
  queryReceiverConfig,
  unBilledTaxBill,
} from '@/services/bill';

export default {
  namespace: 'bill',
  state: {},
  effects: {
    *unBilledTaxBill({ payload, callback }, { call }) {
      const response = yield call(unBilledTaxBill, payload);
      if (callback) callback(response);
    },
    *queryReceiverConfig({ payload, callback }, { call }) {
      const response = yield call(queryReceiverConfig, payload);
      if (callback) callback(response);
    },
    *addReceiverConfig({ payload, callback }, { call }) {
      const response = yield call(addReceiverConfig, payload);
      if (callback) callback(response);
    },
    *generateExampleData({ payload, callback }, { call }) {
      const response = yield call(generateExampleData, payload);
      if (callback) callback(response);
    },
    *queryOrderState({ payload, callback }, { call }) {
      const response = yield call(queryOrderState, payload);
      if (callback) callback(response);
    },
    *createAlipayScanOrder({ payload, callback }, { call }) {
      const response = yield call(createAlipayScanOrder, payload);
      if (callback) callback(response);
    },
    *getOverseaRecord({ payload, callback }, { call }) {
      const response = yield call(getOverseaRecord, payload);
      if (callback) callback(response);
    },
    *getEBillList({ payload, callback }, { call }) {
      const response = yield call(getEBillList, payload);
      if (callback) callback(response);
    },
    *exportCheck({ payload, callback }, { call, put }) {
      const response = yield call(exportCheck, payload);
      if (callback) callback(response);
    },
    *exportFile({ payload, callback }, { call, put }) {
      const response = yield call(exportFile, payload);
      if (callback) callback(response);
    },
    *cancelOrderWithDebitPage({ payload, callback }, { call, put }) {
      const response = yield call(cancelOrderWithDebitPage, payload);
      if (callback) callback(response);
    },
    *getBillBalance({ payload, callback }, { call, put }) {
      const response = yield call(getBillBalance, payload);
      if (callback) callback(response);
    },
    *getNumberList({ payload, callback }, { call, put }) {
      const response = yield call(getNumberList, payload);
      if (callback) callback(response);
    },
    *getAccountPeriodList({ payload, callback }, { call, put }) {
      const response = yield call(getAccountPeriodList, payload);
      if (callback) callback(response);
    },
    *typeList({ payload, callback }, { call, put }) {
      const response = yield call(typeList, payload);
      if (callback) callback(response);
    },
    *getHistoryListByType({ payload, callback }, { call, put }) {
      const response = yield call(getHistoryListByType, payload);
      if (callback) callback(response);
    },
    *detail({ payload, callback }, { call, put }) {
      const response = yield call(detail, payload);
      if (callback) callback(response);
    },
    *getBankAccountList({ payload, callback }, { call, put }) {
      const response = yield call(getBankAccountList, payload);
      if (callback) callback(response);
    },
    *unBilledBillList({ payload, callback }, { call, put }) {
      const response = yield call(unBilledBillList, payload);
      if (callback) callback(response);
    },
    *validateHwpMerchant({ payload, callback }, { call, put }) {
      const response = yield call(validateHwpMerchant, {});
      if (callback) callback(response);
    },
    *withoutList({ payload, callback }, { call, put }) {
      const response = yield call(withoutList, payload);
      if (callback) callback(response);
    },
    *sendWinglist({ payload, callback }, { call, put }) {
      const response = yield call(sendWinglist, payload);
      if (callback) callback(response);
    },
    /**
     * 充值记录
     * @param payload
     * @param callback
     * @param call
     * @param put
     * @returns {Generator<*, void, ?>}
     */
    *getChargeRecordList({ payload, callback }, { call, put }) {
      const response = yield call(getChargeRecordList, payload);
      if (callback) callback(response);
    },
    *getFbaBankAccountList({ payload, callback }, { call, put }) {
      const response = yield call(getFbaBankAccountList, payload);
      if (callback) callback(response);
    },
  },
  reducers: {},
};
