import {
  sendVerifyPhone,
  download,
  createTask,
  deleteTask,
  list,
  queryTaskState,
  getCityPostCodeList,
  getCurrencyRateList,
  getAbnormalTypeList,
} from '@/services/common';

export default {
  namespace: 'common',

  state: {},

  effects: {
    *getAbnormalTypeList({ payload, callback }, { call }) {
      const response = yield call(getAbnormalTypeList, payload);
      if (callback) callback(response);
    },
    *getCurrencyRateList({ payload, callback }, { call }) {
      const response = yield call(getCurrencyRateList, payload);
      if (callback) callback(response);
    },
    *getCityPostCodeList({ payload, callback }, { call }) {
      const response = yield call(getCityPostCodeList, payload);
      if (callback) callback(response);
    },
    *createTask({ payload, callback }, { call }) {
      const response = yield call(createTask, payload);
      if (callback) callback(response);
    },
    *deleteTask({ payload, callback }, { call }) {
      const response = yield call(deleteTask, payload);
      if (callback) callback(response);
    },
    *download({ payload, callback }, { call }) {
      const response = yield call(download, payload);
      if (callback) callback(response);
    },
    *queryTaskState({ payload, callback }, { call }) {
      const response = yield call(queryTaskState, payload);
      if (callback) callback(response);
    },
    *list({ payload, callback }, { call }) {
      const response = yield call(list, payload);
      if (callback) callback(response);
    },
  },

  reducers: {},

  subscriptions: {},
};
