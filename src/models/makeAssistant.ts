import {
  uploadFiles,
  getKYCList,
  updateFiles,
  delKYCRecord,
  recordUpload,
  getRecordedList,
  USAddressCheck,
  exportUSAAddressData,
  getShippingAccount,
  getCountriesName,
  getProductName,
  restrictNameQuery,
  germanPostcodeValidAddressQuery,
  frenchPostcodeValidForCityEnquiries,
  getKYCDerive,
  getUSATaxRateList,
  getUSAHTSRateList,
  getUSAProductList,
  getUSACalculateRate,
} from '@/services/makeAssistant';

export default {
  namespace: 'makeAssistant',

  state: {
    status: undefined,
    account: [], // 制单账号
  },

  effects: {
    *getUSACalculateRate({ payload, callback }, { call }) {
      const response = yield call(getUSACalculateRate, payload);
      if (callback) callback(response);
    },
    *getUSAProductList({ payload, callback }, { call }) {
      const response = yield call(getUSAProductList, payload);
      if (callback) callback(response);
    },
    *getUSAHTSRateList({ payload, callback }, { call }) {
      const response = yield call(getUSAHTSRateList, payload);
      if (callback) callback(response);
    },
    *getUSATaxRateList({ payload, callback }, { call }) {
      const response = yield call(getUSATaxRateList, payload);
      if (callback) callback(response);
    },
    *exportUSAAddressData({ payload, callback }, { call }) {
      const response = yield call(exportUSAAddressData, payload);
      if (callback) callback(response);
    },
    *getShippingAccount({ payload, callback }, { call }) {
      const response = yield call(getShippingAccount, payload);
      if (callback) callback(response);
    },
    *getCountriesName({ payload, callback }, { call }) {
      const response = yield call(getCountriesName, payload);
      if (callback) callback(response);
    },
    *getProductName({ payload, callback }, { call }) {
      const response = yield call(getProductName, payload);
      if (callback) callback(response);
    },
    *uploadFiles({ payload, callback }, { call }) {
      const response = yield call(uploadFiles, payload);
      if (callback) callback(response);
    },
    *updateFiles({ payload, callback }, { call }) {
      const response = yield call(updateFiles, payload);
      if (callback) callback(response);
    },
    *getKYCList({ payload, callback }, { call }) {
      const response = yield call(getKYCList, payload);
      if (callback) callback(response);
    },
    *delKYCRecord({ payload, callback }, { call }) {
      const response = yield call(delKYCRecord, payload);
      if (callback) callback(response);
    },
    *recordUpload({ payload, callback }, { call }) {
      const response = yield call(recordUpload, payload);
      if (callback) callback(response);
    },
    *getRecordedList({ payload, callback }, { call }) {
      const response = yield call(getRecordedList, payload);
      if (callback) callback(response);
    },
    *USAddressCheck({ payload, callback }, { call }) {
      const response = yield call(USAddressCheck, payload);
      if (callback) callback(response);
    },
    *restrictNameQuery({ payload, callback }, { call }) {
      const response = yield call(restrictNameQuery, payload);
      if (callback) callback(response);
    },
    *germanPostcodeValidAddressQuery({ payload, callback }, { call }) {
      const response = yield call(germanPostcodeValidAddressQuery, payload);
      if (callback) callback(response);
    },
    *frenchPostcodeValidForCityEnquiries({ payload, callback }, { call }) {
      const response = yield call(frenchPostcodeValidForCityEnquiries, payload);
      if (callback) callback(response);
    },
    *getKYCDerive({ payload, callback }, { call }) {
      const response = yield call(getKYCDerive, payload);
      if (callback) callback(response);
    },
  },

  reducers: {},
};
