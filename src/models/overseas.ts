import {
  getOrResetKey,
  getOverseaAccounts,
  getOverseaMerchantInfo,
  getRefundList,
  getOverseaRefundTemplate,
  createRefundOder,
  getClaimDamageList,
  createOverseaClaimDamageOrder,
  batchHandleRefund,
  getOversea,
  getOverseaDetail,
  exportOverseaDetail,
  exportRefundData,
  updateEncryptInfo,
  getPublicKey,
  addForecast,
  getForecastList,
  getForecastDetail,
  getForecastChannelList,
  saveOverseaApply,
  getOverseaApplyInfo,
  getInjectWarehouseList,
  addAddress,
  getAddressList,
  updateAddress,
  deleteAddress,
  getYWEBankAccountList,
  exportClaimOrder,
  exportClaimTemplate,
  importClaimData,
  cancelOverseaOrder,
  cancelOverseaOrderWithDebitPage,
} from '@/services/overseas';

export default {
  namespace: 'overseas',

  state: {
    status: undefined,
    account: [], // 制单账号
  },

  effects: {
    *cancelOverseaOrderWithDebitPage({ payload, callback }, { call, put }) {
      const response = yield call(cancelOverseaOrderWithDebitPage, payload);
      if (callback) callback(response);
    },
    *cancelOverseaOrder({ payload, callback }, { call, put }) {
      const response = yield call(cancelOverseaOrder, payload);
      if (callback) callback(response);
    },
    *importClaimData({ payload, callback }, { call, put }) {
      const response = yield call(importClaimData, payload);
      if (callback) callback(response);
    },
    *exportClaimTemplate({ payload, callback }, { call, put }) {
      const response = yield call(exportClaimTemplate, payload);
      if (callback) callback(response);
    },
    *exportClaimOrder({ payload, callback }, { call, put }) {
      const response = yield call(exportClaimOrder, payload);
      if (callback) callback(response);
    },
    *getYWEBankAccountList({ payload, callback }, { call, put }) {
      const response = yield call(getYWEBankAccountList, payload);
      if (callback) callback(response);
    },
    *getInjectWarehouseList({ payload, callback }, { call, put }) {
      const response = yield call(getInjectWarehouseList, payload);
      if (callback) callback(response);
    },
    *getOverseaApplyInfo({ payload, callback }, { call, put }) {
      const response = yield call(getOverseaApplyInfo, payload);
      if (callback) callback(response);
    },
    *saveOverseaApply({ payload, callback }, { call, put }) {
      const response = yield call(saveOverseaApply, payload);
      if (callback) callback(response);
    },
    *getForecastChannelList({ payload, callback }, { call, put }) {
      const response = yield call(getForecastChannelList, payload);
      if (callback) callback(response);
    },
    *addForecast({ payload, callback }, { call, put }) {
      const response = yield call(addForecast, payload);
      if (callback) callback(response);
    },
    *getForecastList({ payload, callback }, { call, put }) {
      const response = yield call(getForecastList, payload);
      if (callback) callback(response);
    },
    *getForecastDetail({ payload, callback }, { call, put }) {
      const response = yield call(getForecastDetail, payload);
      if (callback) callback(response);
    },
    *getPublicKey({ payload, callback }, { call, put }) {
      const response = yield call(getPublicKey, payload);
      if (callback) callback(response);
    },
    *updateEncryptInfo({ payload, callback }, { call, put }) {
      const response = yield call(updateEncryptInfo, payload);
      if (callback) callback(response);
    },
    *exportRefundData({ payload, callback }, { call, put }) {
      const response = yield call(exportRefundData, payload);
      if (callback) callback(response);
    },
    *exportOverseaDetail({ payload, callback }, { call, put }) {
      const response = yield call(exportOverseaDetail, payload);
      if (callback) callback(response);
    },
    *getOversea({ payload, callback }, { call, put }) {
      const response = yield call(getOversea, payload);
      if (callback) callback(response);
    },
    *getOverseaDetail({ payload, callback }, { call, put }) {
      const response = yield call(getOverseaDetail, payload);
      if (callback) callback(response);
    },
    *batchHandleRefund({ payload, callback }, { call, put }) {
      const response = yield call(batchHandleRefund, payload);
      if (callback) callback(response);
    },
    *createOverseaClaimDamageOrder({ payload, callback }, { call, put }) {
      const response = yield call(createOverseaClaimDamageOrder, payload);
      if (callback) callback(response);
    },
    *getClaimDamageList({ payload, callback }, { call, put }) {
      const response = yield call(getClaimDamageList, payload);
      if (callback) callback(response);
    },
    *createRefundOder({ payload, callback }, { call, put }) {
      const response = yield call(createRefundOder, payload);
      if (callback) callback(response);
    },
    *getRefundList({ payload, callback }, { call, put }) {
      const response = yield call(getRefundList, payload);
      if (callback) callback(response);
    },
    *getOverseaRefundTemplate({ payload, callback }, { call, put }) {
      const response = yield call(getOverseaRefundTemplate, payload);
      if (callback) callback(response);
    },
    *getOverseaAccounts({ payload, callback }, { call, put }) {
      const response = yield call(getOverseaAccounts, payload);
      if (callback) callback(response);
      return response;
    },
    *getOverseaMerchantInfo({ payload, callback }, { call, put }) {
      const response = yield call(getOverseaMerchantInfo, payload);
      if (callback) callback(response);
    },
    *getOrResetKey({ payload, callback }, { call, put }) {
      const response = yield call(getOrResetKey, payload);
      if (callback) callback(response);
    },
    *addAddress({ payload, callback }, { call, put }) {
      const response = yield call(addAddress, payload);
      if (callback) callback(response);
      return response;
    },
    *getAddressList({ payload, callback }, { call, put }) {
      const response = yield call(getAddressList, payload);
      if (callback) callback(response);
      return response;
    },
    *updateAddress({ payload, callback }, { call, put }) {
      const response = yield call(updateAddress, payload);
      if (callback) callback(response);
      return response;
    },
    *deleteAddress({ payload, callback }, { call, put }) {
      const response = yield call(deleteAddress, payload);
      if (callback) callback(response);
      return response;
    },
  },

  reducers: {},
};
