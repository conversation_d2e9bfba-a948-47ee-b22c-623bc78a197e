import {
  saveApply,
  getPacketApplyInfo,
  getSignUrl,
  getBusinessApplyStatus,
  getHomeContentList,
  getContentList,
  getNoticeSearchList,
  getUnReadNoticeCount,
  getContentInfo,
  checkMainSaleByType,
  validateSale,
  getFbaApplyInfo,
  fbaApply,
  popupMessage,
  checkBillServiceStatus,
  getSignBillServiceBook,
  checkDeliveryPromiseStatus,
  getDeliveryPromiseBook,
  selectFreezeReasons,
  readAnnouncement,
  enterpriseWeChat,
  journalism,
  getEncodeMerchant,
  contractAndRecord,
  getPersonRecordSign,
  getCustomerIndexAuthState,
  queryAuthAndBusinessState,
  checkUnLimitNotice,
  getIMParam,
} from '@/services/homePage';
import {
  getWarehouseByType,
  getCountryCodeList,
  getBusinessStatusByTypes,
  getAllWarehouseByType,
} from '@/services/common';

export default {
  namespace: 'homePage',

  state: {
    smallBagStatus: false, // 开通小包专线状态
    fbaStatus: false, // 开通FBA专线状态
    overseasStatus: false, // 开通海外派业务状态
    downloadMessage: 0, // 下载消息
    problemMap: {}, // 异常件map
  },

  effects: {
    *getAllWarehouseByType({ payload, callback }, { call }) {
      const response = yield call(getAllWarehouseByType, payload);
      if (callback) callback(response);
    },
    *getIMParam({ payload, callback }, { call }) {
      const response = yield call(getIMParam, payload);
      if (callback) callback(response);
    },
    *checkUnLimitNotice({ payload, callback }, { call }) {
      const response = yield call(checkUnLimitNotice, payload);
      if (callback) callback(response);
    },
    *queryAuthAndBusinessState({ payload, callback }, { call }) {
      const response = yield call(queryAuthAndBusinessState, payload);
      if (callback) callback(response);
    },
    *getCustomerIndexAuthState({ payload, callback }, { call }) {
      const response = yield call(getCustomerIndexAuthState, payload);
      if (callback) callback(response);
    },
    *getPersonRecordSign({ payload, callback }, { call }) {
      const response = yield call(getPersonRecordSign, payload);
      if (callback) callback(response);
    },
    *contractAndRecord({ payload, callback }, { call }) {
      const response = yield call(contractAndRecord, payload);
      if (callback) callback(response);
    },
    *getEncodeMerchant({ payload, callback }, { call }) {
      const response = yield call(getEncodeMerchant, payload);
      if (callback) callback(response);
    },
    *journalism({ payload, callback }, { call }) {
      const response = yield call(journalism, payload);
      if (callback) callback(response);
    },
    *enterpriseWeChat({ payload, callback }, { call }) {
      const response = yield call(enterpriseWeChat, payload);
      if (callback) callback(response);
    },
    *readAnnouncement({ payload, callback }, { call }) {
      const response = yield call(readAnnouncement, payload);
      if (callback) callback(response);
    },
    *selectFreezeReasons({ payload, callback }, { call }) {
      const response = yield call(selectFreezeReasons, payload);
      if (callback) callback(response);
    },
    *checkDeliveryPromiseStatus({ payload, callback }, { call }) {
      const response = yield call(checkDeliveryPromiseStatus, payload);
      if (callback) callback(response);
    },
    *getDeliveryPromiseBook({ payload, callback }, { call, put }) {
      const response = yield call(getDeliveryPromiseBook, payload);
      if (callback) callback(response);
    },
    *checkBillServiceStatus({ payload, callback }, { call, put }) {
      const response = yield call(checkBillServiceStatus, payload);
      if (callback) callback(response);
    },
    *getSignBillServiceBook({ payload, callback }, { call, put }) {
      const response = yield call(getSignBillServiceBook, payload);
      if (callback) callback(response);
    },
    *saveApply({ payload, callback }, { call, put }) {
      const response = yield call(saveApply, payload);
      if (callback) callback(response);
    },
    *getWarehouseByType({ payload, callback }, { call, put }) {
      const response = yield call(getWarehouseByType, payload);
      if (callback) callback(response);
    },
    *getCountryCodeList({ payload, callback }, { call, put }) {
      const response = yield call(getCountryCodeList, payload);
      if (callback) callback(response);
    },
    *getPacketApplyInfo({ payload, callback }, { call, put }) {
      const response = yield call(getPacketApplyInfo, payload);
      if (callback) callback(response);
    },
    *getSignUrl({ payload, callback }, { call, put }) {
      const response = yield call(getSignUrl, payload);
      if (callback) callback(response);
    },
    *getBusinessApplyStatus({ payload, callback }, { call, put }) {
      const response = yield call(getBusinessApplyStatus, payload);
      if (callback) callback(response);
    },
    *getHomeContentList({ payload, callback }, { call, put }) {
      const response = yield call(getHomeContentList, payload);
      if (callback) callback(response);
    },
    *getContentList({ payload, callback }, { call, put }) {
      const response = yield call(getContentList, payload);
      if (callback) callback(response);
    },
    *getNoticeSearchList({ payload, callback }, { call, put }) {
      const response = yield call(getNoticeSearchList, payload);
      if (callback) callback(response);
    },
    *getUnReadNoticeCount({ payload, callback }, { call, put }) {
      const response = yield call(getUnReadNoticeCount, payload);
      if (callback) callback(response);
    },
    *getContentInfo({ payload, callback }, { call, put }) {
      const response = yield call(getContentInfo, payload);
      if (callback) callback(response);
    },
    *checkMainSaleByType({ payload, callback }, { call, put }) {
      const response = yield call(checkMainSaleByType, payload);
      if (callback) callback(response);
    },
    *validateSale({ payload, callback }, { call, put }) {
      const response = yield call(validateSale, payload);
      if (callback) callback(response);
    },
    *getFbaApplyInfo({ payload, callback }, { call, put }) {
      const response = yield call(getFbaApplyInfo, payload);
      if (callback) callback(response);
    },
    *fbaApply({ payload, callback }, { call, put }) {
      const response = yield call(fbaApply, payload);
      if (callback) callback(response);
    },
    *popupMessage({ payload, callback }, { call, put }) {
      const response = yield call(popupMessage, payload);
      if (callback) callback(response);
      if (response.success) {
        const problemData = response.data?.filter(
          data =>
            data.hasOwnProperty('problemNumber') ||
            data.hasOwnProperty('warehouseNumber') ||
            data.hasOwnProperty('overseasNumber') ||
            data.hasOwnProperty('returnNumber') ||
            data.hasOwnProperty('productNameNumber')
        );
        yield put({
          type: 'saveProblemMap',
          payload: problemData?.[0],
        });
      }
    },
    *getBusinessStatusByTypes({ payload, callback }, { call, put }) {
      const response = yield call(getBusinessStatusByTypes, payload);
      if (callback) callback(response);
    },
    *handleOpenStatus({ payload, callback }, { call, put }) {
      yield put({
        type: 'saveStatus',
        payload,
      });
    },
    *handleDownloadMessage({ payload, callback }, { call, put }) {
      // 用于通知头部下载消息重新获取数据
      yield put({
        type: 'saveDownloadMessage',
      });
    },
  },

  reducers: {
    saveStatus(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    saveDownloadMessage(state) {
      return {
        ...state,
        downloadMessage: state?.downloadMessage + 1,
      };
    },
    saveProblemMap(state, { payload }) {
      return {
        ...state,
        problemMap: payload,
      };
    },
  },

  subscriptions: {},
};
