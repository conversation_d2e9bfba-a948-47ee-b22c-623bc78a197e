import { message } from 'antd';
import { makeAccountLoad } from '@/services/anomalyOrder'; // 异常件中共用
import {
  getExpressByNumbers,
  getTmsStatusByNumbers,
  cancelInterceptNormal,
  interceptLog,
  createIntercept,
} from '@/services/interceptionApplications'; // 截留申请
import {
  problemParcelUploading,
  problemPiecesAllAbnormalCause,
  problemPiecesDownload,
  problemPiecesGetHandleMode,
  problemPiecesList,
  problemPiecesOldList,
  problemPiecesOrderinformation,
  problemPiecesStatisticsnumber,
  problemPieceschooseHandleType,
  problemPiecesGetByOrderId,
  returnReceiptBatchConfirmation,
  returnReceiptExcelDownload,
  returnReceiptList,
  returnReceiptNumbers,
  outsideTheWarehouseCustomerBatchHandle,
  outsideTheWarehouseCustomerHandle,
  outsideTheWarehouseDownload,
  outsideTheWarehouseGetAbnormalType,
  outsideTheWarehouseSelectPage,
  outsideTheWarehousePortionDownload,
  calculate,
  getFreezeStatus,
  taxationNumber,
  computational,
  getAddress,
  commercialExpressDetail,
  commercialExpressDispose,
  commercialExpressList,
  orderInformation,
  getMatchNameList,
  productNameChangeQueryList,
  productNameChangeBatchHandle,
  productNameChangeDetail,
  outsideOverseaBatchHandle,
  outsideOverseaExportHandle,
  importOverseaBatch,
  getOperaLog,
} from '@/services/problemPieces';
import {
  editNewsArray,
  recognitionExpense,
  invoiceArr,
  Issueagain,
  provideInformation,
  CancelIntercept,
  Modifycustomernum,
} from '@/utils/commonConstant';

export default {
  namespace: 'anomalyOrder',

  state: {
    historyPayload: {},
    identifier: 'other',
    parameter: {},
    saveListPara: undefined,
    successNumber: '0',
    auditNumber: '0',
    currentPage: 1,
    formPage: 'abnormalList',
    disposeCodeArray: [],
    orderInfoArray: [],
    failureList: [],
    changeFiter: false,
    // ==================
    tableList: [],
    totalNum: '0',
    // 保存参数
    para: {},
    pageNum: 1,
    Oldpara: {},
    OldtableList: [],
    OldtotalNum: 0,
    OldpageNum: 1,
  },
  effects: {
    *getOperaLog({ payload, callback }, { call, put }) {
      const response = yield call(getOperaLog, payload);
      if (callback) callback(response);
    },
    *importOverseaBatch({ payload, callback }, { call, put }) {
      const response = yield call(importOverseaBatch, payload);
      if (callback) callback(response);
    },
    *outsideOverseaExportHandle({ payload, callback }, { call, put }) {
      const response = yield call(outsideOverseaExportHandle, payload);
      if (callback) callback(response);
    },
    *outsideOverseaBatchHandle({ payload, callback }, { call, put }) {
      const response = yield call(outsideOverseaBatchHandle, payload);
      if (callback) callback(response);
    },
    *productNameChangeDetail({ payload, callback }, { call, put }) {
      const response = yield call(productNameChangeDetail, payload);
      if (callback) callback(response);
    },
    *productNameChangeBatchHandle({ payload, callback }, { call, put }) {
      const response = yield call(productNameChangeBatchHandle, payload);
      if (callback) callback(response);
    },
    *productNameChangeQueryList({ payload, callback }, { call, put }) {
      const response = yield call(productNameChangeQueryList, payload);
      if (callback) callback(response);
    },
    *getMatchNameList({ payload, callback }, { call, put }) {
      const response = yield call(getMatchNameList, payload);
      if (callback) callback(response);
    },
    *orderInformation({ payload, callback }, { call, put }) {
      const response = yield call(orderInformation, payload);
      if (callback) callback(response);
    },
    *commercialExpressList({ payload, callback }, { call, put }) {
      const response = yield call(commercialExpressList, payload);
      if (callback) callback(response);
    },
    *commercialExpressDispose({ payload, callback }, { call, put }) {
      const response = yield call(commercialExpressDispose, payload);
      if (callback) callback(response);
    },
    *commercialExpressDetail({ payload, callback }, { call, put }) {
      const response = yield call(commercialExpressDetail, payload);
      if (callback) callback(response);
    },
    *getAddress({ payload, callback }, { call }) {
      const response = yield call(getAddress, payload);
      if (callback) callback(response);
    },
    *computational({ payload, callback }, { call, put }) {
      const response = yield call(computational, payload);
      if (callback) callback(response);
    },
    *taxationNumber({ payload, callback }, { call, put }) {
      const response = yield call(taxationNumber, payload);
      if (callback) callback(response);
    },
    *getFreezeStatus({ payload, callback }, { call, put }) {
      const response = yield call(getFreezeStatus, payload);
      if (callback) callback(response);
    },
    *makeAccountLoad({ payload, callback }, { call, put }) {
      const response = yield call(makeAccountLoad, payload);
      if (callback) callback(response);
    },
    *getExpressByNumbers({ payload, callback }, { call, put }) {
      const response = yield call(getExpressByNumbers, payload);
      yield put({
        type: 'saveHistoryPayload',
        payload: payload,
      });
      if (callback) callback(response);
    },
    *getTmsStatusByNumbers({ payload, callback }, { call, put }) {
      const response = yield call(getTmsStatusByNumbers, payload);
      if (callback) callback(response);
    },
    *cancelInterceptNormal({ payload, callback }, { call, put }) {
      const response = yield call(cancelInterceptNormal, payload);
      if (callback) callback(response);
    },
    *interceptLog({ payload, callback }, { call, put }) {
      const response = yield call(interceptLog, payload);
      if (callback) callback(response);
    },
    *createIntercept({ payload, callback }, { call, put }) {
      const response = yield call(createIntercept, payload);
      if (callback) callback(response);
    },
    *problemParcelUploading({ payload, callback }, { call, put }) {
      const response = yield call(problemParcelUploading, payload);
      if (callback) callback(response);
    },
    *problemPiecesAllAbnormalCause({ payload, callback }, { call, put }) {
      const response = yield call(problemPiecesAllAbnormalCause, payload);
      if (callback) callback(response);
    },
    *problemPiecesDownload({ payload, callback }, { call, put }) {
      const response = yield call(problemPiecesDownload, payload);
      if (callback) callback(response);
    },
    *problemPiecesGetHandleMode({ payload, callback }, { call, put }) {
      const response = yield call(problemPiecesGetHandleMode, payload);
      if (response.success) {
        response.data.map(item => {
          item.name =
            item.name == '客户封存'
              ? (item.name = '弃件')
              : item.name == '退回客户-按司机'
              ? (item.name = '退回-司机')
              : item.name == '退回客户-快递（到付）'
              ? (item.name = '退回-快递到付')
              : item.name == '退回客户-快递（寄付）'
              ? (item.name = '退回-快递寄付')
              : item.name == '退回客户-客户自取'
              ? (item.name = '退回-自取')
              : item.name;
        });
        if (callback) callback(response.data);
      }
    },
    *problemPiecesList({ payload, callback }, { call, put }) {
      const response = yield call(problemPiecesList, payload);
      yield put({
        type: 'savePayload',
        payload: payload,
      });
      if (response.success) {
        if (response.data.data.length > 0) {
          response.data.data.map(item => {
            item.solutionTypeName =
              item.solutionTypeName == '客户封存'
                ? (item.solutionTypeName = '弃件')
                : item.solutionTypeName == '退回客户-按司机'
                ? (item.solutionTypeName = '退回-司机')
                : item.name == '退回客户-快递（到付）'
                ? (item.name = '退回-快递到付')
                : item.name == '退回客户-快递（寄付）'
                ? (item.name = '退回-快递寄付')
                : item.solutionTypeName == '退回客户-客户自取'
                ? (item.solutionTypeName = '退回-自取')
                : item.solutionTypeName;
          });

          response.data.data.map(item => {
            const indexs = editNewsArray.filter(function(x) {
              if (x === item.exceptionTypeId) {
                item.solutionTypeName =
                  item.solutionTypeId == '12' ? '修改信息' : item.solutionTypeName;
              }
            });
            const indexe = recognitionExpense.filter(function(x) {
              if (x === item.exceptionTypeId) {
                item.solutionTypeName =
                  item.solutionTypeId == '12' ? '确认费用' : item.solutionTypeName;
              }
            });
            const indexi = invoiceArr.filter(function(x) {
              if (x === item.exceptionTypeId) {
                item.solutionTypeName =
                  item.solutionTypeId == '12' ? '提供新发票' : item.solutionTypeName;
              }
            });
            const indexis = Issueagain.filter(function(x) {
              if (x === item.exceptionTypeId) {
                item.solutionTypeName =
                  item.solutionTypeId == '12' ? '重出签发' : item.solutionTypeName;
              }
            });
            item.solutionTypeName =
              item.solutionTypeId == '12' && item.exceptionTypeId == '1063'
                ? '修改客户号'
                : item.solutionTypeId == '12' && item.exceptionTypeId == '1126'
                ? '提供资料'
                : item.solutionTypeId == '12' && item.exceptionTypeId == '1100'
                ? '取消截留'
                : item.solutionTypeId == '12' && item.exceptionTypeId == '1184'
                ? '补齐税号'
                : item.solutionTypeName;
          });

          yield put({
            type: 'saveTableData',
            payload: response.data,
          });
          if (callback) callback(response.data);
        } else {
          yield put({
            type: 'saveTableData',
            payload: response.data,
          });
          if (callback) callback(response.data);
        }
      }
    },
    *problemPiecesOldList({ payload, callback }, { call, put }) {
      const response = yield call(problemPiecesOldList, payload);
      yield put({
        type: 'saveOldPayload',
        payload: payload,
      });
      if (response.success) {
        yield put({
          type: 'saveOldTableData',
          payload: response,
        });
        if (callback) callback(response);
      }
    },
    *problemPiecesOrderinformation({ payload, callback }, { call, put }) {
      const response = yield call(problemPiecesOrderinformation, payload);
      if (callback) callback(response);
    },
    *problemPiecesStatisticsnumber({ payload, callback }, { call, put }) {
      const response = yield call(problemPiecesStatisticsnumber, payload);
      if (callback) callback(response);
    },
    *problemPieceschooseHandleType({ payload, callback }, { call, put }) {
      const response = yield call(problemPieceschooseHandleType, payload);
      if (callback) callback(response);
      if (response.data > 0) {
        yield put({
          type: 'saveFailureList',
          payload: response,
        });
      }
    },
    *problemPiecesGetByOrderId({ payload, callback }, { call, put }) {
      const response = yield call(problemPiecesGetByOrderId, payload);
      // console.log('异常工单信息====',payload,response);
      response['identifier'] = payload.identifier;
      if (response.success) {
        yield put({
          type: 'saveOrderInfoArray',
          payload: response,
        });
        if (payload.identifier !== 'abnormalSearch') {
          if (callback) callback(response);
        }
      } else if (response.code === '3') {
        message.error('查询失败，工单编号不存在', 2);
      } else {
        message.error(response.message, 2);
      }
      if (payload.identifier === 'abnormalSearch') {
        if (callback) callback(response);
      }
    },
    *returnReceiptBatchConfirmation({ payload, callback }, { call, put }) {
      const response = yield call(returnReceiptBatchConfirmation, payload);
      if (callback) callback(response);
    },
    *returnReceiptExcelDownload({ payload, callback }, { call, put }) {
      const response = yield call(returnReceiptExcelDownload, payload);
      if (callback) callback(response);
    },
    *returnReceiptList({ payload, callback }, { call, put }) {
      const response = yield call(returnReceiptList, payload);
      yield put({
        type: 'saveReturnPayload',
        payload: payload,
      });
      if (callback) callback(response);
    },
    *returnReceiptNumbers({ payload, callback }, { call, put }) {
      const response = yield call(returnReceiptNumbers, payload);
      if (callback) callback(response);
    },
    *outsideTheWarehouseCustomerBatchHandle({ payload, callback }, { call, put }) {
      const response = yield call(outsideTheWarehouseCustomerBatchHandle, payload);
      if (callback) callback(response);
    },
    *outsideTheWarehouseCustomerHandle({ payload, callback }, { call, put }) {
      const response = yield call(outsideTheWarehouseCustomerHandle, payload);
      if (callback) callback(response);
    },
    *outsideTheWarehouseDownload({ payload, callback }, { call, put }) {
      const response = yield call(outsideTheWarehouseDownload, payload);
      if (callback) callback(response);
    },
    *outsideTheWarehouseGetAbnormalType({ payload, callback }, { call, put }) {
      const response = yield call(outsideTheWarehouseGetAbnormalType, payload);
      if (callback) callback(response);
    },
    *outsideTheWarehouseSelectPage({ payload, callback }, { call, put }) {
      const response = yield call(outsideTheWarehouseSelectPage, payload);
      if (callback) callback(response);
    },
    *outsideTheWarehousePortionDownload({ payload, callback }, { call, put }) {
      const response = yield call(outsideTheWarehousePortionDownload, payload);
      if (callback) callback(response);
    },
    *calculate({ payload, callback }, { call, put }) {
      const response = yield call(calculate, payload);
      if (callback) callback(response);
    },
  },
  reducers: {
    saveHistoryPayload(state, action) {
      return {
        ...state,
        historyPayload: action.payload,
      };
    },
    saveindentifier(state, action) {
      return {
        ...state,
        identifier: action.payload.identifier,
      };
    },
    saveReturnPayload(state, action) {
      return {
        ...state,
        parameter: action.payload,
      };
    },
    // ------------------------------
    saveTableData(state, action) {
      return {
        ...state,
        tableList: action.payload.data,
        totalNum: action.payload.total,
      };
    },
    savePayload(state, action) {
      return {
        ...state,
        pageNum: action.payload.PageIndex,
        para: action.payload,
      };
    },
    saveOldTableData(state, action) {
      return {
        ...state,
        OldtableList: action.payload.data.data,
        OldtotalNum: action.payload.data.totalNumber,
      };
    },
    saveOldPayload(state, action) {
      return {
        ...state,
        OldpageNum: action.payload.currentPage,
        Oldpara: action.payload,
      };
    },
    // ---------------------------------------------

    saveListData(state, action) {
      return {
        ...state,
        orderList: action.payload.data,
        currentPage: action.payload.pageon,
        saveListPara: action.payload.ListPara,
        formPage: 'abnormalList',
        changeFiter: action.payload.ListPara.changeFiter,
      };
    },
    saveDisposeCodeArray(state, action) {
      return {
        ...state,
        disposeCodeArray: action.payload.data,
      };
    },
    saveOrderInfoArray(state, action) {
      return {
        ...state,
        orderInfoArray: action.payload.data,
        formPage: action.payload.identifier,
      };
    },
    saveFailureList(state, action) {
      return {
        ...state,
        failureList: action.payload.data,
      };
    },
    saveListInfo(state, action) {
      return {
        ...state,
        saveListPara: action.payload,
      };
    },
  },

  subscriptions: {},
};
