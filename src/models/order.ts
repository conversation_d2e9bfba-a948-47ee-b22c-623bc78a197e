import {
  cancelConfirmDelivery,
  cancelWaybill,
  changeWaybill,
  chargePriceSimple,
  clickEJFGenericTemplate,
  CliskApplyWithholding,
  confirmDelivery,
  createOrders,
  deleteOrder,
  downloadSignature,
  exportData,
  getCountriesName,
  getCurrency,
  getEJFGenericTemplate,
  getOrderDetailById,
  getOrderDetails,
  getOrderLists,
  getPrintLabelData,
  HKPrintLabelData,
  getProductName,
  getShippingAccount,
  getSignature,
  getVoucherIsEditData,
  importOrderList,
  interceptCancel,
  submitVoucher,
  submitVoucherAll,
  temporaryData,
  updateOrder,
  whetherTheOrderNumberExists,
  whetherTheOrderNumberExistsList,
  getCheckPoint,
  getHandoverCodes,
} from '@/services/order';
import { getWarehouseByType } from '@/services/common';

export default {
  namespace: 'Order',

  state: {
    status: undefined,
    copyData: {},
  },

  effects: {
    *getCheckPoint({ payload, callback }, { call }) {
      const response = yield call(getCheckPoint, payload);
      if (callback) callback(response);
    },
    *getHandoverCodes({ payload, callback }, { call }) {
      const response = yield call(getHandoverCodes, payload);
      if (callback) callback(response);
    },
    *getShippingAccount({ payload, callback }, { call }) {
      const response = yield call(getShippingAccount, payload);
      if (callback) callback(response);
    },
    *getOrderLists({ payload, callback }, { call }) {
      const response = yield call(getOrderLists, payload);
      if (callback) callback(response);
    },
    *getProductName({ payload, callback }, { call }) {
      const response = yield call(getProductName, payload);
      if (callback) callback(response);
    },
    *getCountriesName({ payload, callback }, { call }) {
      const response = yield call(getCountriesName, payload);
      if (callback) callback(response);
    },
    *getCurrency({ payload, callback }, { call }) {
      const response = yield call(getCurrency, payload);
      if (callback) callback(response);
    },
    *getOrderDetails({ payload, callback }, { call, put }) {
      const response = yield call(getOrderDetails, payload);
      if (response.success) {
        yield put({
          type: 'saveCopysData',
          payload: response,
        });
      }
      if (callback) callback(response);
    },
    *createOrders({ payload, callback }, { call }) {
      const response = yield call(createOrders, payload);
      if (callback) callback(response);
    },
    *confirmDelivery({ payload, callback }, { call }) {
      const response = yield call(confirmDelivery, payload);
      if (callback) callback(response);
    },
    *cancelWaybill({ payload, callback }, { call }) {
      const response = yield call(cancelWaybill, payload);
      if (callback) callback(response);
    },
    *updateOrder({ payload, callback }, { call }) {
      const response = yield call(updateOrder, payload);
      if (callback) callback(response);
    },
    *importOrderList({ payload, callback }, { call }) {
      const response = yield call(importOrderList, payload);
      if (callback) callback(response);
    },
    *deleteOrder({ payload, callback }, { call }) {
      const response = yield call(deleteOrder, payload);
      if (callback) callback(response);
    },
    *CliskApplyWithholding({ payload, callback }, { call }) {
      const response = yield call(CliskApplyWithholding, payload);
      if (callback) callback(response);
    },
    *submitVoucher({ payload, callback }, { call }) {
      const response = yield call(submitVoucher, payload);
      if (callback) callback(response);
    },
    *submitVoucherAll({ payload, callback }, { call }) {
      const response = yield call(submitVoucherAll, payload);
      if (callback) callback(response);
    },
    *getOrderDetailById({ payload, callback }, { call }) {
      const response = yield call(getOrderDetailById, payload);
      if (callback) callback(response);
    },
    *interceptCancel({ payload, callback }, { call }) {
      const response = yield call(interceptCancel, payload);
      if (callback) callback(response);
    },
    *getEJFGenericTemplate({ payload, callback }, { call }) {
      const response = yield call(getEJFGenericTemplate, payload);
      if (callback) callback(response);
    },
    *clickEJFGenericTemplate({ payload, callback }, { call }) {
      const response = yield call(clickEJFGenericTemplate, payload);
      if (callback) callback(response);
    },
    *exportData({ payload, callback }, { call }) {
      const response = yield call(exportData, payload);
      if (callback) callback(response);
    },
    *getPrintLabelData({ payload, callback }, { call }) {
      const response = yield call(getPrintLabelData, payload);
      if (callback) callback(response);
    },
    *HKPrintLabelData({ payload, callback }, { call }) {
      const response = yield call(HKPrintLabelData, payload);
      if (callback) callback(response);
    },
    *temporaryData({ payload, callback }, { call }) {
      const response = yield call(temporaryData, payload);
      if (callback) callback(response);
    },
    *chargePriceSimple({ payload, callback }, { call }) {
      const response = yield call(chargePriceSimple, payload);
      if (callback) callback(response);
    },
    *getVoucherIsEditData({ payload, callback }, { call }) {
      const response = yield call(getVoucherIsEditData, payload);
      if (callback) callback(response);
    },
    *changeWaybill({ payload, callback }, { call }) {
      const response = yield call(changeWaybill, payload);
      if (callback) callback(response);
    },
    *cancelConfirmDelivery({ payload, callback }, { call }) {
      const response = yield call(cancelConfirmDelivery, payload);
      if (callback) callback(response);
    },
    *getSignature({ payload, callback }, { call }) {
      const response = yield call(getSignature, payload);
      if (callback) callback(response);
    },
    *downloadSignature({ payload, callback }, { call }) {
      const response = yield call(downloadSignature, payload);
      if (callback) callback(response);
    },
    *whetherTheOrderNumberExists({ payload, callback }, { call }) {
      const response = yield call(whetherTheOrderNumberExists, payload);
      if (callback) callback(response);
    },
    *whetherTheOrderNumberExistsList({ payload, callback }, { call }) {
      const response = yield call(whetherTheOrderNumberExistsList, payload);
      if (callback) callback(response);
    },
    *getWarehouseByType({ payload, callback }, { call }) {
      const response = yield call(getWarehouseByType, payload);
      if (callback) callback(response);
    },
  },

  reducers: {
    saveCopysData(state, action) {
      let obj = action.payload.data;
      let param = {
        receiverInfo: obj.receiverInfo,
        poPStation: {
          pointId: obj.poPStation?.pointId,
        },
        handoverCode: obj.handoverCode,
        waybillNumber: obj.waybillNumber,
        importCustomsInfo: obj.importCustomsInfo,
        parcelInfo: {
          currency: obj.parcelInfo.currency,
          totalPrice: obj.parcelInfo.totalPrice,
          totalQuantity: obj.parcelInfo.totalQuantity,
          totalWeight: obj.parcelInfo.totalWeight,
          height: obj.parcelInfo.height,
          width: obj.parcelInfo.width,
          length: obj.parcelInfo.length,
          pickingOrders: obj.remark,
          productList: obj.parcelInfo.productList,
          isBattery: obj.parcelInfo.hasBattery,
          IOSS: obj.parcelInfo.ioss,
        },
        senderInfo: {
          senderTaxNumber: obj.senderInfo.taxNumber,
        },
        channelId: obj.channelId,
        userId: [obj.userId],
        orderNumber: obj.orderNumber,
        companyCode: obj.companyCode,
        dateOfReceipt: obj.dateOfReceipt,
        transactionNumber: obj.transactionNumber,
      };
      return {
        ...state,
        copyData: param,
      };
    },
  },
};
