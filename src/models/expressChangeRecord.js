import { getExpressChannels, changeRecordList, extract } from '../services/changeRecord';

export default {
  namespace: 'ExpressRecord',

  state: {},

  effects: {
    *getExpressChannels({ payload, callback }, { call, put }) {
      const response = yield call(getExpressChannels, payload);
      if (callback) callback(response);
    },
    *changeRecordList({ payload, callback }, { call, put }) {
      const response = yield call(changeRecordList, payload);
      if (callback) callback(response);
    },
    *extract({ payload, callback }, { call, put }) {
      const response = yield call(extract, payload);
      if (callback) callback(response);
    },
  },
};
