import { routerRedux } from 'dva/router';

import {
  getPrescriptionList, // 获取时效统计列表数据
  getTimeBaseReport, // 获取时效报表
  getTimeBaseDetail, // 获取时效报表详情
  getWaybillCollectedList, // 获取待领取运单列表
  notTakeByGroup, // 待领取运单数据汇总
  addPlatformAccount, // 新增平台账号
  addPlatformAccountPage, // 获取新增平台账号需要的参数
  getPlatformAccounts, // 获取平台账号列表
  makeAccountEdit, // 编辑制单账号
  makeAccountSave, // 新增制单账号
  makeAccountLoad, // 获取制单账号列表
  recordTheWaybill,
  trackingCheckPoint,
  getBankAccountList, //查询结算银行信息
  getExpresses, // 小包首页数量统计
  getPacketBaseInfo, //小包首页获取商户基础信息
  getBillBalance, //获取账单账户信息
  getPacketApplyStatus, // 获取小包业务状态
  getPacketMerchantCode, //获取小包业务账号
  getPacketMessageNumber, // 异常包裹数量
  toTakeDetail, // 待领取运单导出
  getPayableAmount,
  getCustomerData,
  unFreezeMerchant,
  contractSign,
  updateContact,
  updatePlatformAccount,
  unFreezeNotice,
  validateCustomerPayment,
  getOrderLabelDetail,
  getOrderLabelList,
  getProductList,
  importLabel,
  exportLabelTemplate,
  deleteLabel,
} from '@/services/smallBag';
import { getLoadAccounts } from '@/services/personnelManagement';
export default {
  namespace: 'smallBag',

  state: {},

  effects: {
    *deleteLabel({ payload, callback }, { call }) {
      const response = yield call(deleteLabel, payload);
      if (callback) callback(response);
    },
    *exportLabelTemplate({ payload, callback }, { call }) {
      const response = yield call(exportLabelTemplate, payload);
      if (callback) callback(response);
    },
    *importLabel({ payload, callback }, { call }) {
      const response = yield call(importLabel, payload);
      if (callback) callback(response);
    },
    *getProductList({ payload, callback }, { call }) {
      const response = yield call(getProductList, payload);
      if (callback) callback(response);
    },
    *getOrderLabelList({ payload, callback }, { call }) {
      const response = yield call(getOrderLabelList, payload);
      if (callback) callback(response);
    },
    *getOrderLabelDetail({ payload, callback }, { call }) {
      const response = yield call(getOrderLabelDetail, payload);
      if (callback) callback(response);
    },
    *validateCustomerPayment({ payload, callback }, { call }) {
      const response = yield call(validateCustomerPayment, payload);
      if (callback) callback(response);
    },
    *unFreezeNotice({ payload, callback }, { call }) {
      const response = yield call(unFreezeNotice, payload);
      if (callback) callback(response);
    },
    *updatePlatformAccount({ payload, callback }, { call }) {
      const response = yield call(updatePlatformAccount, payload);
      if (callback) callback(response);
    },
    *updateContact({ payload, callback }, { call }) {
      const response = yield call(updateContact, payload);
      if (callback) callback(response);
    },
    *contractSign({ payload, callback }, { call }) {
      const response = yield call(contractSign, payload);
      if (callback) callback(response);
    },
    *getCustomerData({ payload, callback }, { call }) {
      const response = yield call(getCustomerData, payload);
      if (callback) callback(response);
    },
    *unFreezeMerchant({ payload, callback }, { call }) {
      const response = yield call(unFreezeMerchant, payload);
      if (callback) callback(response);
    },
    *getPayableAmount({ payload, callback }, { call, put }) {
      const response = yield call(getPayableAmount, payload);
      if (callback) callback(response);
    },
    *getPrescriptionList({ payload, callback }, { call, put }) {
      const response = yield call(getPrescriptionList, payload);
      if (callback) callback(response);
    },
    *getTimeBaseReport({ payload, callback }, { call, put }) {
      const response = yield call(getTimeBaseReport, payload);
      if (callback) callback(response);
    },
    *getTimeBaseDetail({ payload, callback }, { call, put }) {
      const response = yield call(getTimeBaseDetail, payload);
      if (callback) callback(response);
    },
    *getWaybillCollectedList({ payload, callback }, { call, put }) {
      const response = yield call(getWaybillCollectedList, payload);
      if (callback) callback(response);
    },
    *getLoadAccounts({ payload, callback }, { call, put }) {
      const response = yield call(getLoadAccounts, payload);
      if (callback) callback(response);
    },
    *addPlatformAccount({ payload, callback }, { call, put }) {
      const response = yield call(addPlatformAccount, payload);
      if (callback) callback(response);
    },
    *addPlatformAccountPage({ payload, callback }, { call, put }) {
      const response = yield call(addPlatformAccountPage, payload);
      if (callback) callback(response);
    },
    *getPlatformAccounts({ payload, callback }, { call, put }) {
      const response = yield call(getPlatformAccounts, payload);
      if (callback) callback(response);
    },
    *makeAccountEdit({ payload, callback }, { call, put }) {
      const response = yield call(makeAccountEdit, payload);
      if (callback) callback(response);
    },
    *makeAccountSave({ payload, callback }, { call, put }) {
      const response = yield call(makeAccountSave, payload);
      if (callback) callback(response);
    },
    *makeAccountLoad({ payload, callback }, { call, put }) {
      const response = yield call(makeAccountLoad, payload);
      if (callback) callback(response);
    },
    *notTakeByGroup({ payload, callback }, { call, put }) {
      const response = yield call(notTakeByGroup, payload);
      if (callback) callback(response);
    },
    *recordTheWaybill({ payload, callback }, { call, put }) {
      const response = yield call(recordTheWaybill, payload);
      if (callback) callback(response);
    },
    *trackingCheckPoint({ payload, callback }, { call, put }) {
      const response = yield call(trackingCheckPoint, payload);
      if (callback) callback(response);
    },
    *getBankAccountList({ payload, callback }, { call, put }) {
      const response = yield call(getBankAccountList, payload);
      if (callback) callback(response);
    },
    *getExpresses({ payload, callback }, { call, put }) {
      const response = yield call(getExpresses, payload);
      if (callback) callback(response);
    },
    *getPacketBaseInfo({ payload, callback }, { call, put }) {
      const response = yield call(getPacketBaseInfo, payload);
      if (callback) callback(response);
    },
    *getBillBalance({ payload, callback }, { call, put }) {
      const response = yield call(getBillBalance, payload);
      if (callback) callback(response);
    },
    *getPacketApplyStatus({ payload, callback }, { call, put }) {
      const response = yield call(getPacketApplyStatus, payload);
      if (callback) callback(response);
    },
    *getPacketMerchantCode({ payload, callback }, { call, put }) {
      const response = yield call(getPacketMerchantCode, payload);
      if (callback) callback(response);
    },
    *getPacketMessageNumber({ payload, callback }, { call, put }) {
      const response = yield call(getPacketMessageNumber, payload);
      if (callback) callback(response);
    },
    *toTakeDetail({ payload, callback }, { call, put }) {
      const response = yield call(toTakeDetail, payload);
      if (callback) callback(response);
    },
  },

  reducers: {},

  subscriptions: {},
};
