@import '~antd/lib/style/themes/default.less';
@import '~antd/dist/antd.less';
@tailwind utilities;

@border-radius-base: 4px;

html,
body,
#root {
  height: 100%;
  min-width: 1500px;
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.globalSpin {
  width: 100%;
  margin: 40px 0 !important;
}

ul,
ol {
  list-style: none;
}

@media (max-width: @screen-xs) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;
        > span {
          display: block;
        }
      }
    }
  }
}

.antd-pro-components-global-footer-index-copyright {
  margin-bottom: 20px;
}

:global {
  .ant-tabs-top > .ant-tabs-nav::before {
    bottom: 0;
    border-bottom: 1px solid transparent !important;
  }
  .ant-tab-no-bottom-border {
    .ant-tabs-nav {
      margin-bottom: 0px !important;
    }
  }
}

:global {
  .page-tab-card {
    .ant-page-header {
      padding-block-end: 0px !important;
    }
    .ant-pro-page-container-children-content {
      padding-block-start: 0px !important;
    }
  }
}

:global {
  .no-page-padding {
    .ant-pro-page-container-children-content {
      padding-block: 0px !important;
      padding-inline: 0px !important;
    }
  }
}

.bqHzuc {
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  --antd-wave-shadow-color: #52c41a;
  --scroll-bar: 0;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  font-size: 14px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
  font-variant: tabular-nums;
  line-height: 1.5715;
  font-feature-settings: 'tnum';
  --reactour-accent: #007aff;
  color: inherit;
  text-align: left;
  --tw-shadow: 0 0 #0000;
  --tw-ring-inset: var(--tw-empty, /*!*/ /*!*/);
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgba(59, 130, 246, 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  box-sizing: border-box;
  display: flex;
  margin-top: 10px !important;
  -webkit-box-align: center;
  align-items: center;
  -webkit-box-pack: center;
  justify-content: center;
}

.diKbXs {
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  --antd-wave-shadow-color: #52c41a;
  --scroll-bar: 0;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  font-size: 14px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
  font-variant: tabular-nums;
  line-height: 1.5715;
  font-feature-settings: 'tnum';
  --reactour-accent: #007aff;
  color: inherit;
  text-align: left;
  --tw-shadow: 0 0 #0000;
  --tw-ring-inset: var(--tw-empty, /*!*/ /*!*/);
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgba(59, 130, 246, 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  box-sizing: border-box;
  display: flex;
  margin-top: 10px !important;
  -webkit-box-align: center;
  align-items: center;
  -webkit-box-pack: center;
  justify-content: center;
}

// 添加更高优先级的选择器
html,
body,
#root.no-min-width {
  min-width: unset !important;
}
