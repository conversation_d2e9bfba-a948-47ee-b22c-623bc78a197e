import robotHide from '@/assets/robotHide.png';
import robotShow from '@/assets/robotShow.png';

function createFloatingButton(webPluginId, customer) {
  // 检查是否已存在按钮
  const existingButton = document.querySelector('.floating-robot-button');
  if (existingButton) {
    return existingButton;
  }

  const button = document.createElement('div');
  button.className = 'floating-robot-button udesk-client-btn';
  button.style.cssText = `
    position: fixed;
    right: -20px;
    bottom: 100px;
    width: 80px;
    height: 80px;
    cursor: pointer;
    background-image: url(${robotHide});
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    transition: all 0.3s ease;
    z-index: 1000;
  `;

  let mouseDownTime = 0;
  let isHovering = false; // 追踪悬停状态

  button.addEventListener('mouseenter', () => {
    isHovering = true;
    button.style.backgroundImage = `url(${robotShow})`;
    // 根据当前是左隐藏还是右隐藏来决定显示方式
    if (button.style.left === '-20px') {
      button.style.left = '0px';
    } else if (button.style.right === '-20px') {
      button.style.right = '0px';
    }
    // 如果不在边缘，也确保显示 robotShow (虽然 dragEnd 应该已经处理了)
  });

  button.addEventListener('mouseleave', () => {
    isHovering = false;
    // 如果按钮是从边缘展开的，则恢复隐藏
    if (button.style.left === '0px') {
      button.style.backgroundImage = `url(${robotHide})`;
      button.style.left = '-20px';
    } else if (button.style.right === '0px') {
      button.style.backgroundImage = `url(${robotHide})`;
      button.style.right = '-20px';
    }
    // 如果按钮在中间（left 和 right 都不是 0px 或 -20px），则保持显示状态
  });

  button.addEventListener('mousedown', () => {
    mouseDownTime = Date.now();
  });

  button.addEventListener('click', e => {
    // 如果按下时间超过200ms，认为是在拖动而不是点击
    if (Date.now() - mouseDownTime > 200) {
      return;
    }
    let url = 'https://intelcc-user.icsoc.net/?channelKey=4acdc6f883564e80a119aecdbe156462&init=1';
    // 只有在customer.userId存在时才拼接thirdId参数
    if (customer.userId) {
      url += `&thirdId=${customer.userId}&username=${customer.c_name}_${customer.c_cf_商户号}`;
    }
    window.open(url, '_blank', 'width=1100,height=670');
  });

  document.body.appendChild(button);
  return button;
}

export function initializeUdeskChat(webPluginId, customer) {
  // 检查是否已存在按钮
  const existingButton = document.querySelector('.floating-robot-button');
  if (existingButton) {
    draggable(existingButton);
    return;
  }

  const floatingButton = createFloatingButton(webPluginId, customer);

  setTimeout(() => {
    draggable(floatingButton);
  }, 1000);
}

function draggable(element) {
  let isDragging = false;
  let startX, startY, startLeft, startTop;
  const container = document.getElementById('root');
  const containerRect = container.getBoundingClientRect();
  const containerTop = containerRect.top;
  const containerHeight = containerRect.height;
  const containerLeft = containerRect.left;
  const containerWidth = containerRect.width;

  const dragElement = element || document.querySelector('.udesk-client-btn');
  if (!dragElement) return;

  dragElement.addEventListener('mousedown', dragStart);
  document.addEventListener('mousemove', drag);
  document.addEventListener('mouseup', dragEnd);

  function dragStart(e) {
    e.preventDefault();
    startX = e.clientX;
    startY = e.clientY;
    const rect = dragElement.getBoundingClientRect();
    startLeft = rect.left - containerLeft;
    startTop = rect.top - containerTop;
    isDragging = true;
    dragElement.style.transition = 'none'; // 拖动时禁用过渡效果
  }

  function drag(e) {
    if (!isDragging) return;
    e.preventDefault();

    // 计算垂直和水平方向的移动
    const dx = e.clientX - startX;
    const dy = e.clientY - startY;
    const newLeft = startLeft + dx;
    const newTop = startTop + dy;

    // 限制在容器范围内
    const updatedLeft = Math.max(0, Math.min(newLeft, containerWidth - dragElement.offsetWidth));
    const updatedTop = Math.max(0, Math.min(newTop, containerHeight - dragElement.offsetHeight));

    dragElement.style.left = `${updatedLeft}px`;
    dragElement.style.top = `${updatedTop}px`;
  }

  function dragEnd() {
    if (isDragging) {
      dragElement.style.transition = 'all 0.3s ease'; // 恢复过渡效果
      isDragging = false;

      const rect = dragElement.getBoundingClientRect();
      const currentLeft = rect.left - containerLeft;
      const threshold = 10; // 边缘吸附阈值

      // 检查是否靠近左边缘
      if (currentLeft <= threshold) {
        dragElement.style.left = '-20px';
        dragElement.style.right = '';
        dragElement.style.top = `${rect.top - containerTop}px`; // 保持垂直位置
        dragElement.style.backgroundImage = `url(${robotHide})`;
        dragElement.style.transform = 'scaleX(-1)'; // 水平翻转
      }
      // 检查是否靠近右边缘
      else if (currentLeft >= containerWidth - dragElement.offsetWidth - threshold) {
        dragElement.style.left = '';
        dragElement.style.right = '-20px';
        dragElement.style.top = `${rect.top - containerTop}px`; // 保持垂直位置
        dragElement.style.backgroundImage = `url(${robotHide})`;
        dragElement.style.transform = 'scaleX(1)'; // 取消翻转
      }
      // 不在边缘附近
      else {
        // 保持由 drag 函数计算出的位置
        dragElement.style.right = ''; // 确保 right 被清除
        dragElement.style.backgroundImage = `url(${robotShow})`;
        dragElement.style.transform = 'scaleX(1)'; // 确保取消翻转
      }
    }
  }
}

export function jsonToUrlParams(json) {
  const params = new URLSearchParams();
  for (const [key, value] of Object.entries(json)) {
    if (Array.isArray(value)) {
      // 如果值是数组，则为每个元素添加一个键值对
      value.forEach(item => params.append(key, item));
    } else {
      // 否则直接添加键值对
      params.append(key, value);
    }
  }
  return params.toString();
}
