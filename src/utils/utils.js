import moment from 'moment';
import React from 'react';
import nzh from 'nzh/cn';
import { parse, stringify } from 'qs';
import Fingerprint2 from 'fingerprintjs2';
import { Button, Space, Upload, message, notification, Modal } from 'antd';
import { whiteListPage, specialRoute, foreignKeyEnum } from './commonConstant';
import CA from '@/assets/ca.png';
import US from '@/assets/us.png';
import RMB from '@/assets/rmb.png';
import { router } from 'umi';
import { setLocale } from 'umi-plugin-react/locale';
import { Decimal } from 'decimal.js';
import { formatMessage } from 'umi-plugin-react/locale';
import { businessIntroduction } from '@/utils/commonConstant';
import useOpenBusinessStatusDispatch from '@/hooks/useOpenBusinessStatusDispatch';

// 根据币种返回对应的图片
export function currencyToPng(currency) {
  const currencyTypeMap = {
    美元: US,
    欧元: CA,
    人民币: RMB,
  };

  return currencyTypeMap[currency];
}

export function fixedZero(val) {
  return val * 1 < 10 ? `0${val}` : val;
}

export function getTimeDistance(type) {
  const now = new Date();
  const oneDay = 1000 * 60 * 60 * 24;

  if (type === 'today') {
    now.setHours(0);
    now.setMinutes(0);
    now.setSeconds(0);
    return [moment(now), moment(now.getTime() + (oneDay - 1000))];
  }

  if (type === 'week') {
    let day = now.getDay();
    now.setHours(0);
    now.setMinutes(0);
    now.setSeconds(0);

    if (day === 0) {
      day = 6;
    } else {
      day -= 1;
    }

    const beginTime = now.getTime() - day * oneDay;

    return [moment(beginTime), moment(beginTime + (7 * oneDay - 1000))];
  }

  if (type === 'month') {
    const year = now.getFullYear();
    const month = now.getMonth();
    const nextDate = moment(now).add(1, 'months');
    const nextYear = nextDate.year();
    const nextMonth = nextDate.month();

    return [
      moment(`${year}-${fixedZero(month + 1)}-01 00:00:00`),
      moment(moment(`${nextYear}-${fixedZero(nextMonth + 1)}-01 00:00:00`).valueOf() - 1000),
    ];
  }

  const year = now.getFullYear();
  return [moment(`${year}-01-01 00:00:00`), moment(`${year}-12-31 23:59:59`)];
}

export function getPlainNode(nodeList, parentPath = '') {
  const arr = [];
  nodeList.forEach(node => {
    const item = node;
    item.path = `${parentPath}/${item.path || ''}`.replace(/\/+/g, '/');
    item.exact = true;
    if (item.children && !item.component) {
      arr.push(...getPlainNode(item.children, item.path));
    } else {
      if (item.children && item.component) {
        item.exact = false;
      }
      arr.push(item);
    }
  });
  return arr;
}

export function digitUppercase(n) {
  return nzh.toMoney(n);
}

function getRelation(str1, str2) {
  if (str1 === str2) {
    console.warn('Two path are equal!'); // eslint-disable-line
  }
  const arr1 = str1.split('/');
  const arr2 = str2.split('/');
  if (arr2.every((item, index) => item === arr1[index])) {
    return 1;
  }
  if (arr1.every((item, index) => item === arr2[index])) {
    return 2;
  }
  return 3;
}

function getRenderArr(routes) {
  let renderArr = [];
  renderArr.push(routes[0]);
  for (let i = 1; i < routes.length; i += 1) {
    // 去重
    renderArr = renderArr.filter(item => getRelation(item, routes[i]) !== 1);
    // 是否包含
    const isAdd = renderArr.every(item => getRelation(item, routes[i]) === 3);
    if (isAdd) {
      renderArr.push(routes[i]);
    }
  }
  return renderArr;
}

/**
 * Get router routing configuration
 * { path:{name,...param}}=>Array<{name,path ...param}>
 * @param {string} path
 * @param {routerData} routerData
 */
export function getRoutes(path, routerData) {
  let routes = Object.keys(routerData).filter(
    routePath => routePath.indexOf(path) === 0 && routePath !== path
  );
  // Replace path to '' eg. path='user' /user/name => name
  routes = routes.map(item => item.replace(path, ''));
  // Get the route to be rendered to remove the deep rendering
  const renderArr = getRenderArr(routes);
  // Conversion and stitching parameters
  const renderRoutes = renderArr.map(item => {
    const exact = !routes.some(route => route !== item && getRelation(route, item) === 1);
    return {
      exact,
      ...routerData[`${path}${item}`],
      key: `${path}${item}`,
      path: `${path}${item}`,
    };
  });
  return renderRoutes;
}

export function getPageQuery() {
  return parse(window.location.href.split('?')[1]);
}

export function getQueryPath(path = '', query = {}) {
  const search = stringify(query);
  if (search.length) {
    return `${path}?${search}`;
  }
  return path;
}

/* eslint no-useless-escape:0 */
const reg = /(((^https?:(?:\/\/)?)(?:[-;:&=\+\$,\w]+@)?[A-Za-z0-9.-]+(?::\d+)?|(?:www.|[-;:&=\+\$,\w]+@)[A-Za-z0-9.-]+)((?:\/[\+~%\/.\w-_]*)?\??(?:[-\+=&;%@.\w_]*)#?(?:[\w]*))?)$/;

export function isUrl(path) {
  return reg.test(path);
}

export function formatWan(val) {
  const v = val * 1;
  if (!v || Number.isNaN(v)) return '';

  let result = val;
  if (val > 10000) {
    result = Math.floor(val / 10000);
    result = (
      <span>
        {result}
        <span
          style={{
            position: 'relative',
            top: -2,
            fontSize: 14,
            fontStyle: 'normal',
            marginLeft: 2,
          }}
        >
          万
        </span>
      </span>
    );
  }
  return result;
}

// 给官方演示站点用，用于关闭真实开发环境不需要使用的特性
export function isAntdPro() {
  return window.location.hostname === 'preview.pro.ant.design';
}

export const importCDN = (url, name) =>
  new Promise(resolve => {
    const dom = document.createElement('script');
    dom.src = url;
    dom.type = 'text/javascript';
    dom.onload = () => {
      resolve(window[name]);
    };
    document.head.appendChild(dom);
  });

export function split_array(arr, len) {
  let a_len = arr.length;
  let result = [];
  for (let i = 0; i < a_len; i += len) {
    result.push(Object.assign({}, arr.slice(i, i + len)));
  }
  return result;
}

// 保留两位小数
export function keepTwoFloat(value, n) {
  let f = Math.ceil(value * Math.pow(10, n)) / Math.pow(10, n);
  let s = f.toString();
  let rs = s.indexOf('.');
  if (rs < 0) {
    s += '.';
  }
  for (let i = s.length - s.indexOf('.'); i <= n; i++) {
    s += '0';
  }
  return s;
}

/**
 * 是否有权限
 * @param {*} key
 */
export function isAuth(key) {
  return JSON.parse(localStorage.getItem('permissions') || '[]').indexOf(key) !== -1 || false;
}

/**
 * 是否有路径权限
 * @param {*} key
 */
export function isRouteAuth(key) {
  const array = JSON.parse(localStorage.getItem('menuList') || '[]');
  if (array.length === 0) return true;
  if (specialRoute.find(route => key.includes(route))) return true;
  let map = array.map(item => {
    if (item !== null) {
      return item.indexOf(':') !== -1 ? item.substring(0, item.indexOf(':') - 1) : item;
    }
  });
  return map.indexOf(key) !== -1 || false;
}

/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} id id字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 孩子节点字段 默认 'children'
 * @param {*} rootId 根Id 默认 0
 */
export function handleTree(data, id, parentId, children, rootId) {
  id = id || 'id';
  parentId = parentId || 'parentId';
  children = children || 'children';
  rootId =
    rootId ||
    Math.min.apply(
      Math,
      data.map(item => {
        return item[parentId];
      })
    ) ||
    0;
  // 对源数据深度克隆
  const cloneData = JSON.parse(JSON.stringify(data));
  // 循环所有项
  const treeData = cloneData.filter(father => {
    var branchArr = cloneData.filter(child => {
      // 返回每一项的子级数组
      return father[id] === child[parentId];
    });
    if (branchArr.length > 0) {
      father.children = branchArr;
    } else {
      father.children = '';
    }
    // 返回第一层
    return father[parentId] === rootId;
  });
  return treeData !== '' ? treeData : data;
}

// 递增001这种
export function computedNumber(params) {
  let name = params.substring(0, params.length - 3); // 保留除后三位的字符串
  let number = params.substring(params.length - 3, params.length); // 截取后三位
  let result;
  if (
    typeof parseInt(number) === 'number' &&
    !Object.is(+number, NaN) &&
    parseInt(number) !== 999
  ) {
    let s = parseInt(number);
    s = ++s;
    s = s == 1000 ? 1 : s;
    result = `${name}${s <= 999 ? (s < 10 ? '00' + s : s < 100 ? '0' + s : '' + s) : '001'}`; // 计算 转型
  } else {
    result = undefined;
  }

  return result;
}

// 树形结构扁平化
export function TreeToFlat(data) {
  let formatData = [];
  for (var i = 0; i < data.length; i++) {
    formatData.push(data[i].path);
    if (data[i].routes) {
      formatData = formatData.concat(TreeToFlat(data[i].routes));
    }
  }
  return formatData;
}

// 处理替换高德key
export const mapApiReplace = (newKey, code) => {
  const mapSecurityJsCodeValue = document.getElementById('mapSecurityJsCode');
  const mapKeyValue = document.getElementById('_react_amap_plugin');
  if (mapKeyValue && mapSecurityJsCodeValue) {
    const oldKey = mapKeyValue.src.split('key=')[1].split('&')[0];
    const key = mapKeyValue.src.replace(oldKey, newKey);
    mapKeyValue.src = key;
    let docScript = document.createElement('script');
    let securityScript = document.createElement('script');
    docScript.src = mapKeyValue.src;
    docScript.async = mapKeyValue.async;
    docScript.type = mapKeyValue.type;
    docScript.defer = mapKeyValue.defer;
    docScript.id = '_react_amap_plugin_new';
    securityScript.id = 'mapSecurityJsCodeNew';
    securityScript.type = 'text/javascript';
    securityScript.innerHTML = `window._AMapSecurityConfig = {
        securityJsCode: '${code}'
    };`;
    document.head.removeChild(mapSecurityJsCodeValue);
    document.head.removeChild(mapKeyValue);
    document.head.appendChild(securityScript);
    document.head.appendChild(docScript);
  } else {
    const mapSecurityJsCodeValue = document.getElementById('mapSecurityJsCodeNew');
    const mapKeyValue = document.getElementById('_react_amap_plugin_new');
    const oldKey = mapKeyValue.src.split('key=')[1].split('&')[0];
    const key = mapKeyValue.src.replace(oldKey, newKey);
    mapKeyValue.src = key;
    let docScript = document.createElement('script');
    let securityScript = document.createElement('script');
    docScript.src = mapKeyValue.src;
    docScript.async = mapKeyValue.async;
    docScript.type = mapKeyValue.type;
    docScript.defer = mapKeyValue.defer;
    docScript.id = '_react_amap_plugin';
    securityScript.id = 'mapSecurityJsCode';
    securityScript.type = 'text/javascript';
    securityScript.innerHTML = `window._AMapSecurityConfig = {
        securityJsCode: '${code}'
    };`;
    document.head.removeChild(mapSecurityJsCodeValue);
    document.head.removeChild(mapKeyValue);
    document.head.appendChild(securityScript);
    document.head.appendChild(docScript);
  }
};

// 获取设备号
export const getDeviceId = () => {
  const map = (obj, iterator) => {
    let results = [];
    if (obj == null) {
      return results;
    }
    if (Array.prototype.map && obj.map === Array.prototype.map) {
      return obj.map(iterator);
    }
    each(obj, function(value, index, list) {
      results.push(iterator(value, index, list));
    });
    return results;
  };
  const deviceId = Fingerprint2.get(components => {
    let value = Fingerprint2.x64hash128(
      map(components, function(component) {
        return component.value;
      }).join(''),
      31
    );
    return value;
  });
  return deviceId;
};

/**
 *@description: 上传前校验文件大小和类型
 *@methodAuthor: dangh
 *@date: 2023-03-08 10:21:45
 *@async
 *@param {RcFile} file 上传的文件
 *@param {number} size - 文件允许的最大大小（以字节为单位）。
 *@param {string[]} typeList - 允许的文件类型列表。
 *@param {string} typeErrorMsg - 当文件类型不匹配时显示的错误消息。
 *@param {number} [jpgSize=undefined] - 如果文件是 JPG 类型，允许的最大大小（以字节为单位）。
 *@param {boolean} [needFile=true] - 是否需要文件对象作为回调参数。
 *@param {function} [callback] - 处理完成后调用的回调函数。
 *@returns {Promise<void>} 当操作完成时解决的 Promise。
 */
export const changeBeforeUpload = async (
  file,
  size,
  typeList,
  typeErrorMsg,
  jpgSize = undefined,
  needFile = true,
  callback
) => {
  const isJpgOrPng = typeList.includes(file.type);
  if (!isJpgOrPng) {
    // 不包含在内的文件
    if (!needFile) {
      message.error(typeErrorMsg);
      return false || Upload.LIST_IGNORE;
    }
    // 非图片的文件类型，判断大小
    const isLt2M = file.size / 1024 / 1024 < size;
    if (!isLt2M) {
      message.error(typeErrorMsg);
      return false || Upload.LIST_IGNORE;
    }
  } else {
    // 如果是jpg类型的图片，判断大小
    if (jpgSize) {
      const isLt2M = file.size / 1024 / 1024 < jpgSize;
      if (!isLt2M) {
        message.error(typeErrorMsg);
        return false || Upload.LIST_IGNORE;
      }
    } else {
      // 如果未传jpgSize，判断大小就用size
      const isLt2M = file.size / 1024 / 1024 < size;
      if (!isLt2M) {
        message.error(typeErrorMsg);
        return false || Upload.LIST_IGNORE;
      }
    }
  }
  if (callback) {
    const result = await callback(file);
    if (result) return true || Upload.LIST_IGNORE;
    else return false || Upload.LIST_IGNORE;
  }

  return true || Upload.LIST_IGNORE;
};

export const getBase64 = file =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });

// 给图片添加水印
export const setBase64ImageWaterMark = file =>
  new Promise(resolve => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      const img = new Image();
      img.src = reader.result;
      img.onload = () => {
        const canvas = document.createElement('canvas');
        canvas.width = img.naturalWidth;
        canvas.height = img.naturalHeight;
        const ctx = canvas.getContext('2d');
        ctx.drawImage(img, 0, 0);
        // 计算适应的字体大小
        const minSize = 12;
        const maxSize = 60;
        const scaleFactor = Math.min(canvas.width, canvas.height) / 500; // 假设500px为基准尺寸
        let fontSize = Math.max(minSize, Math.min(maxSize, 30 * scaleFactor));
        ctx.fillStyle = 'red';
        ctx.textBaseline = 'middle';
        ctx.font = `${fontSize}px Arial`;
        const text = 'Watermark';
        const textWidth = ctx.measureText(text).width;
        const x = canvas.width - textWidth - 20;
        const y = canvas.height - fontSize - 20;
        ctx.fillText(text, x, y);
        canvas.toBlob(result => resolve(result));
        // resolve(canvas.toDataURL());
      };
    };
    reader.onerror = error => reject(error);
  });

export const disabledDate = current => {
  // Can not select days before today and today
  return current && current < moment().endOf('day');
};

export const changeBreadcrumb = breadcrumnb => {
  const data = breadcrumnb.split('.');
  data.shift();
  return data;
};

// form表单中错误信息提示
export const onFormErrorMessage = errorInfo => {
  if (errorInfo?.errorFields) {
    message.error(errorInfo.errorFields?.[0].errors[0]);
  }
};

export function generateUUID() {
  var d = new Date().getTime();
  if (typeof performance !== 'undefined' && typeof performance.now === 'function') {
    d += performance.now(); // use high-precision timer if available
  }
  var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    var r = (d + Math.random() * 16) % 16 | 0;
    d = Math.floor(d / 16);
    return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);
  });
  return uuid;
}

// 回显Form表单中的文件
export const setUploadImage = url => {
  return url
    ? [
        {
          uid: '-1',
          name: 'image.png',
          status: 'done',
          url: url,
        },
      ]
    : undefined;
};

// 处理付款账号中企业和个人的名字
// q:对比的英文单词是什么
// a:对比的英文单词是compare
export const returnPersonNameOrCompanyName = merchantDetails => {
  if (merchantDetails?.customerType === 0) {
    // 个人本人
    return merchantDetails?.personName;
  } else if (merchantDetails?.customerType === 1) {
    // 企业本人
    return merchantDetails?.companyName;
  }
  return '';
};

// 返回是企业还是个人
export const returnMerchantType = merchantDetails =>
  merchantDetails?.customerType === 0 ? '个人' : '企业';

/**
 *
 * @param {string} path 路由地址
 * @returns boolean true 为需要验证 false 为不需要验证
 */
export const pathIsNeedVerify = path => {
  const pathArray = path.split('/');
  const customerManagerIndex = whiteListPage.indexOf(pathArray[1]);

  if (pathArray[1] === whiteListPage[customerManagerIndex]) {
    return false;
  } else {
    return true;
  }
};

/**
 *
 * @param {string} url 文件地址
 * @param {string} fileName 文件名
 */
export const downloadUrlFile = (url, fileName) => {
  const a = document.createElement('a');
  a.style.display = 'none';
  a.download = fileName;
  a.href = url;
  const body = document.getElementsByTagName('body');
  body[0].appendChild(a);
  a.click();
  document.body.removeChild(a);
  message.success('下载成功!');
};

/**
 *
 * @param {string} base64Data  base64字符串
 * @param {string} fileName  文件名
 */
export const downloadBase64File = (base64Data, fileName) => {
  let bstr = atob(base64Data), //解析 base-64 编码的字符串
    n = bstr.length,
    u8arr = new Uint8Array(n); //创建初始化为0的，包含length个元素的无符号整型数组
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n); //返回字符串第一个字符的 Unicode 编码
  }
  let blob = new Blob([u8arr]); //转化成blob
  let blobUrl = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.style.display = 'none';
  a.download = fileName;
  a.href = blobUrl;
  const body = document.getElementsByTagName('body');
  body[0].appendChild(a);
  a.click();
  document.body.removeChild(a);
};

/**
 * 点击一键复制内容
 * @param {string} context 复制的内容
 */
export const clickCopy = context => {
  var ele = document.createElement('input'); //创建一个input标签
  ele.setAttribute('value', context); // 设置改input的value值
  document.body.appendChild(ele); // 将input添加到body
  ele.select(); // 获取input的文本内容
  document.execCommand('copy'); // 执行copy指令
  document.body.removeChild(ele); // 删除input标签
  message.success('复制成功!');
};
/**
 * 根据特定字段合并单元格数据。
 *
 * @param {Array} data - 要合并的数据数组。
 * @param {string} field - 用于确定是否应合并数据的字段。
 * @return {undefined} 此函数不返回任何值。
 */
export const mergeCellData = (data, field) => {
  let count = 0; //重复项的第一项
  let indexCount = 1; //下一项
  while (indexCount < data.length) {
    let item = data.slice(count, count + 1)[0]; //获取没有比较的第一个对象
    if (!item.rowSpan) {
      item.rowSpan = 1; //初始化为1
    }
    if (item[field] === data[indexCount][field]) {
      //第一个对象与后面的对象相比，有相同项就累加，并且后面相同项设置为0
      item.rowSpan++;
      data[indexCount].rowSpan = 0;
    } else {
      count = indexCount;
    }
    indexCount++;
  }
};

/**
 * Merges cell data for a specified field in a dataset.
 *
 * @param {Array} data - The dataset array containing objects to be merged.
 * @param {string} field - The key used to determine if two rows should be merged.
 * @returns {Array} An array with merged cell data where applicable.
 */
export const mergeRowsByField = (data, field) => {
  // Validate input to ensure `data` is an array and `field` is a string
  if (!Array.isArray(data)) {
    return data;
  }

  if (typeof field !== 'string') {
    return data;
  }

  // Return early if the array is empty
  if (data.length === 0) return data;

  // Group the items by the specified field
  const groupedItems = data.reduce((acc, item) => {
    const key = item[field];
    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(item);
    return acc;
  }, {});

  // Prepare a new array with calculated rowSpan
  const result = [];

  for (const key in groupedItems) {
    const group = groupedItems[key];
    const groupSize = group.length;

    // Set rowSpan for the first item of each group
    group.forEach((item, index) => {
      if (index === 0) {
        item.rowSpan = groupSize;
      } else {
        item.rowSpan = 0;
      }
      result.push(item);
    });
  }

  return result;
};

// 校验企户名称正则
export const validateMerchantName = value => {
  return value == 1 || value == 3
    ? /^(?:[\u4e00-\u9fa5]{2,30}|[\u4e00-\u9fa5]{2,}（[\u4e00-\u9fa5]{2,10}）{1,2}[\u4e00-\u9fa5]{2,}|[\u4e00-\u9fa5]{2,}（[\u4e00-\u9fa5]{2,10}）{1,2}|（[\u4e00-\u9fa5]{2,10}）{1,2}[\u4e00-\u9fa5]{2,})$/
    : null;
};

// 校验特殊身份证正则
export const validateOtherIdCardRegExp = type => {
  if (type == 1) {
    return /^[A-Z]{1}[0-9]{6}(?:（[A-Z0-9]{1}）){1}?$/g;
  } else if (type == 2) {
    return /^\d{7}(?:（[A-Z0-9]{1}）)$/g;
  } else if (type == 3) {
    return /^[A-Z]\d{9}$/g;
  } else if (type == 7) {
    return /^\w{6,9}$/g;
  }
};

/*
 *@Description: 根据输入内容在数组中进行匹配
 *@MethodAuthor: dangh
 *@Date: 2023-10-12 14:53:33
 */
export const findMatch = (arr, searchValue) => {
  const index = arr.indexOf(searchValue);
  if (index !== -1) {
    return 1;
  }
  // No match
  return 0;
};

/**
 * 需要替换base64前缀
 * @param {string} data 需要替换的base64文件
 * @returns 替换后的文件地址
 */
export const replaceBase64Str = async data => {
  const imageBase64 = await getBase64(data);
  return imageBase64.replace(/^data:image\/(png|jpeg|jpg);base64,/, '');
};
// 校验运单号
export const textWaybill = (value, numberList = 50, needArray = false) => {
  if (value === undefined || value === '') {
    message.error(
      `请输入要搜索的运单编号，最多输入${numberList}个运单号，多单号请以逗号、空格或回车隔开`,
      2
    );
    return undefined;
  }
  value = value.replace(/(^\s*)|(\s*$)/g, '');
  value = value.replace(/[\s+|\t+|,]/g, '，');
  const reg = /[`~!@#$%^&*()_\-+=<>?:"{}|.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'。、]/im;
  const lowercase = new RegExp('[a-z]+', 'g');
  const chineseReg = /^[^\u4E00-\u9FA5]+$/g;
  if (!chineseReg.test(value)) {
    message.error('运单号不能输入汉字');
    return;
  }

  const waybillArray = value.split('，');
  let tempArray = [];
  for (let i = 0; i < waybillArray.length; i++) {
    if (waybillArray[i].length !== 0) {
      tempArray.push(waybillArray[i]);
    }
  }
  if (tempArray.length > numberList) {
    if (needArray) {
      return tempArray;
    } else {
      message.error(
        `订单/运单号搜索最多输入${numberList}个订单/运单号，多单号请以逗号、空格或回车隔开`
      );
      return;
    }
  }
  // if (this.state.account.length === 0) {
  //   message.error('暂无制单账号，请稍后重试');
  //   return;
  // }
  return tempArray;
};

/**
 * Returns a different label based on the payment type
 * @param {string} type
 * @param {string} label
 * @returns {string}
 */
export const paymentTypeDiffLabel = (type, label) => {
  const labelMappings = {
    '0': {
      付款账户名称: '付款账户名称',
      银行账号: '银行账号',
      支付宝账号: '支付宝账号',
    },
    '1': {
      付款账户名称: '企业付款账号名称',
      银行账号: '对公银行账号',
      支付宝账号: '对公支付宝账号',
    },
    '2': {
      付款账户名称: '付款账户名称',
      银行账号: '银行账号',
      支付宝账号: '支付宝账号',
    },
    '3': {
      付款账户名称: '企业付款账号名称',
      银行账号: '对公银行账号',
      支付宝账号: '对公支付宝账号',
    },
  };

  return labelMappings?.[type]?.[label] ?? label;
};

/**
 * Flattens a tree structure using a specified key for children nodes.
 *
 * @param {Array} nodes - the array of nodes representing the tree
 * @param {string} childrenKey - the key to access the children nodes (default is 'children')
 * @return {Array} the flattened tree structure
 */
export const flattenTreeWithOtherKey = (nodes, childrenKey = 'children') => {
  return nodes.reduce((acc, node) => {
    const children = node[childrenKey] || [];
    return acc.concat(
      { ...node, [childrenKey]: undefined },
      flattenTreeWithOtherKey(children, childrenKey)
    );
  }, []);
};

/**
 *
 * @param {Object} dispatch 请求方式 params 请求参数 pageSize 用于判断是否需要后台进入队列轮询下载 callback 不需要轮询的下载
 */
export const downloadExport = ({ dispatch, params, type, pageSize = -1, callback }) => {
  let timeoutId;
  const TIME_COUNT = 2000;
  const queueDownload = data => {
    dispatch({
      type: 'common/queryTaskState', // 用于轮询下载的接口,
      payload: data,
      callback: response => {
        console.log(response);
        if (response.success) {
          if (response.data.res) {
            // 成功后需要通知消息通知
            notification.success({
              message: `${response.data.fileName}已生成，请前往下载中心进行下载`,
              btn: (
                <Space>
                  <Button
                    size="small"
                    type="primary"
                    onClick={() => {
                      notification.close(`open${Date.now()}`);
                      router.push('/serviceManagement/messageCenter/downloadCenter');
                    }}
                  >
                    下载中心
                  </Button>
                </Space>
              ),
            });
            if (timeoutId) clearTimeout(timeoutId);
            dispatch({
              type: 'homePage/handleDownloadMessage',
            });
          } else {
            timeoutId = setTimeout(() => {
              queueDownload(data);
            }, TIME_COUNT);
          }
        }
      },
    });
  };

  if (pageSize > 500 || pageSize == -1) {
    // 需要后台进入队列轮询下载
    dispatch({
      type: 'common/createTask',
      payload: {
        type,
        parameter: JSON.stringify(params),
      },
      callback: response => {
        if (response.success) {
          const data = response?.data;
          if (data) {
            // notification.success({
            //   message: '导出成功',
            //   description: response.message,
            // });
            message.success('下载任务已提交，请前往(用户管理-消息中心-下载中心 下载)');
            queueDownload(data);
          }
        }
      },
    });
  } else {
    // 直接导出
    if (callback) return callback();
  }
};

/**
 * 根据不同的type返回不同的提示信息
 * @param {string} type
 * @returns {string}
 */
export const returnTitleByType = type => {
  const titleMap = {
    4: '香港',
    5: '澳门',
    6: '台湾',
  };

  return titleMap?.[type] ?? '';
};

export const byWebLanguageChange = () => {
  // en --- 英文  zh-CN --- 中文  zh-TW --- 繁体
  const browserLanguage = navigator.language || navigator.userLanguage;
  if (browserLanguage === 'zh-TW') {
    setLocale('zh-TW', false);
  } else if (browserLanguage === 'en') {
    setLocale('en-US', false);
  } else {
    setLocale('zh-CN', false);
  }
};

export const changeColumnState = (tabNumber, allTabs) => {
  // tabNumber
  // -2     全部   6  已截留
  // 9      未制单  10 制单失败
  // 0      已制单  1  已确认发货
  // 2      已收货  3  运输途中
  // 4      已妥投  5  已取消
  // 8      异常/退件 7  追踪结束

  // -2 All 6 Intercepted
  // 9 No Voucher 10 Voucher Failed
  // 0 Voucher Created 1 Confirmed Shipment
  // 2 Goods Received 3 In Transit
  // 4 Delivered 5 Cancelled
  // 8 Abnormal/Returned 7 Tracking Ended
  // allTabs -2  已制单  -1 未制单
  // withholdingArPrice:{ 预扣金额
  //   show:false
  // },
  // unshippedArPrice:{  未出账单金额
  //   show:false
  // },
  const persistenceKeyMap = {
    '-2': {
      '-2': 'AllCreated',
      '-1': 'AllNoCreated',
    },
    '6': 'Intercepted',
    '9': 'NoVoucher',
    '10': 'VoucherFailed',
    '0': 'VoucherCreated',
    '1': 'ConfirmedShipment',
    '2': 'GoodsReceived',
    '3': 'InTransit',
    '4': 'Delivered',
    '5': 'Cancelled',
    '8': 'Abnormal',
    '7': 'Tracking',
  };

  const persistenceValueMap = {
    '-2': {
      '-2': {
        orderNumber: {
          disable: true,
        },
        waybillNumber: {
          disable: true,
        },
        channelName: {
          disable: true,
        },
        status: {
          disable: true,
        },
        createTime: {
          disable: true,
        },
        withholdingArPrice: {
          show: false,
        },
        unshippedArPrice: {
          show: false,
        },
        action: {
          disable: true,
        },
      },
      '-1': {
        orderNumber: {
          disable: true,
        },
        channelName: {
          disable: true,
        },
        createTime: {
          disable: true,
        },
        withholdingArPrice: {
          show: false,
          hideInSetting: true,
          hideInTable: true,
        },
        unshippedArPrice: {
          show: false,
          hideInSetting: true,
          hideInTable: true,
        },
      },
    },
    '9': {
      id: {
        disable: true,
      },
      orderNumber: {
        disable: true,
      },
      waybillNumber: {
        disable: true,
      },
      channelName: {
        disable: true,
      },
      createTime: {
        disable: true,
      },
      withholdingArPrice: {
        show: false,
        hideInSetting: true,
        hideInTable: true,
      },
      unshippedArPrice: {
        show: false,
        hideInSetting: true,
        hideInTable: true,
      },
      action: {
        disable: true,
      },
    },
    '10': {
      id: {
        disable: true,
      },
      orderNumber: {
        disable: true,
      },
      waybillNumber: {
        disable: true,
      },
      channelName: {
        disable: true,
      },
      createTime: {
        disable: true,
      },
      withholdingArPrice: {
        show: false,
        hideInSetting: true,
        hideInTable: true,
      },
      unshippedArPrice: {
        show: false,
        hideInSetting: true,
        hideInTable: true,
      },
      errorMessage: {
        disable: true,
      },
      action: {
        disable: true,
      },
    },
    '0': {
      orderNumber: {
        disable: true,
      },
      waybillNumber: {
        disable: true,
      },
      channelName: {
        disable: true,
      },
      createTime: {
        disable: true,
      },
      withholdingArPrice: {
        show: false,
      },
      unshippedArPrice: {
        show: false,
        hideInSetting: true,
        hideInTable: true,
      },
      action: {
        disable: true,
      },
    },
    '1': {
      orderNumber: {
        disable: true,
      },
      waybillNumber: {
        disable: true,
      },
      channelName: {
        disable: true,
      },
      createTime: {
        disable: true,
      },
      withholdingArPrice: {
        show: false,
      },
      unshippedArPrice: {
        show: false,
        hideInSetting: true,
        hideInTable: true,
      },
      action: {
        disable: true,
      },
    },
    '2': {
      orderNumber: {
        disable: true,
      },
      waybillNumber: {
        disable: true,
      },
      channelName: {
        disable: true,
      },
      withholdingArPrice: {
        show: false,
        hideInSetting: true,
        hideInTable: true,
      },
      unshippedArPrice: {
        show: false,
      },
      createTime: {
        disable: true,
      },
    },
    '3': {
      orderNumber: {
        disable: true,
      },
      waybillNumber: {
        disable: true,
      },
      channelName: {
        disable: true,
      },
      createTime: {
        disable: true,
      },
      withholdingArPrice: {
        show: false,
        hideInSetting: true,
        hideInTable: true,
      },
      unshippedArPrice: {
        show: false,
      },
      action: {
        disable: true,
      },
    },
    '4': {
      orderNumber: {
        disable: true,
      },
      waybillNumber: {
        disable: true,
      },
      channelName: {
        disable: true,
      },
      createTime: {
        disable: true,
      },
      withholdingArPrice: {
        show: false,
        hideInSetting: true,
        hideInTable: true,
      },
      unshippedArPrice: {
        show: false,
        hideInSetting: true,
        hideInTable: true,
      },
      action: {
        disable: true,
      },
    },
    '5': {
      orderNumber: {
        disable: true,
      },
      waybillNumber: {
        disable: true,
      },
      channelName: {
        disable: true,
      },
      cancelNote: {
        disable: true,
      },
      createTime: {
        disable: true,
      },
      withholdingArPrice: {
        show: false,
        hideInSetting: true,
        hideInTable: true,
      },
      unshippedArPrice: {
        show: false,
        hideInSetting: true,
        hideInTable: true,
      },
      action: {
        disable: true,
      },
    },
    '6': {
      orderNumber: {
        disable: true,
      },
      waybillNumber: {
        disable: true,
      },
      channelName: {
        disable: true,
      },
      howToHandle: {
        disable: true,
      },
      createTime: {
        disable: true,
      },
      withholdingArPrice: {
        show: false,
        hideInSetting: true,
        hideInTable: true,
      },
      unshippedArPrice: {
        show: false,
        hideInSetting: true,
        hideInTable: true,
      },
      action: {
        disable: true,
      },
    },
    '8': {
      orderNumber: {
        disable: true,
      },
      waybillNumber: {
        disable: true,
      },
      channelName: {
        disable: true,
      },
      status: {
        disable: true,
      },
      createTime: {
        disable: true,
      },
      withholdingArPrice: {
        show: false,
        hideInSetting: true,
        hideInTable: true,
      },
      unshippedArPrice: {
        show: false,
        hideInSetting: true,
        hideInTable: true,
      },
      action: {
        disable: true,
      },
    },
    '7': {
      orderNumber: {
        disable: true,
      },
      waybillNumber: {
        disable: true,
      },
      channelName: {
        disable: true,
      },
      withholdingArPrice: {
        show: false,
        hideInSetting: true,
        hideInTable: true,
      },
      unshippedArPrice: {
        show: false,
        hideInSetting: true,
        hideInTable: true,
      },
      createTime: {
        disable: true,
      },
    },
  };

  const allTypeColumnMap = {
    transactionNumber: {
      show: false,
    },
    hasBattery: {
      show: false,
    },
    currencyName: {
      show: false,
    },
  };

  const allTypeColumnMapNoShow = {
    transactionNumber: {
      show: false,
      hideInSetting: true,
      hideInTable: true,
    },
    hasBattery: {
      show: false,
      hideInSetting: true,
      hideInTable: true,
    },
    currencyName: {
      show: false,
      hideInSetting: true,
      hideInTable: true,
    },
  };

  const tempNoAddType = ['9', '10'];

  return {
    persistenceKey:
      tabNumber === '-2'
        ? persistenceKeyMap?.[tabNumber]?.[allTabs]
        : persistenceKeyMap?.[tabNumber],
    defaultValue:
      tabNumber === '-2'
        ? allTabs === '-2'
          ? { ...persistenceValueMap?.[tabNumber]?.[allTabs], ...allTypeColumnMap }
          : {
              ...persistenceValueMap?.[tabNumber]?.[allTabs],
              ...allTypeColumnMapNoShow,
            }
        : tempNoAddType?.includes(tabNumber)
        ? { ...persistenceValueMap?.[tabNumber], ...allTypeColumnMapNoShow }
        : {
            ...persistenceValueMap?.[tabNumber],
            ...allTypeColumnMap,
          },
  };
};

// 用于处理数组转换的函数
export function transformOrderData(originalData, labelname) {
  // 检查输入数据是否为数组
  if (!Array.isArray(originalData)) {
    // console.warn('输入数据不是数组，返回空数组');
    return [];
  }

  const result = [];

  originalData.forEach((order, index) => {
    // 检查order是否存在
    if (!order) {
      // console.warn(`第 ${index} 条数据为空，已跳过`);
      return;
    }

    // 使用可选链检查parcel
    if (!order?.parcel) {
      // console.warn(`第 ${index} 条数据缺少parcel属性，已跳过`);
      return;
    }

    // 获取所有parcel之外的属性
    const baseOrderInfo = Object.entries(order).reduce((acc, [key, value]) => {
      if (key !== 'parcel') {
        acc[key] = value;
      }
      return acc;
    }, {});

    // 获取parcel中除productList之外的所有属性
    const parcelInfo = Object.entries(order?.parcel || {}).reduce((acc, [key, value]) => {
      if (key !== 'productList') {
        acc[key] = value;
      }
      return acc;
    }, {});

    // 使用可选链检查productList
    const productList = order?.parcel?.productList;

    // 检查productList是否存在且为数组
    if (!Array.isArray(productList) || productList.length === 0) {
      // 如果不存在productList或为空数组，创建一个只包含基本信息的对象
      result.push({
        ...parcelInfo,
        ...baseOrderInfo,
      });
      // console.warn(`第 ${index} 条数据的productList不存在或为空，已创建包含基本信息的记录`);
      return;
    }

    if (labelname === '收件人信息修改') {
      result.push({
        ...baseOrderInfo, // 订单基本信息
        ...parcelInfo, // parcel信息
        productList, // 产品信息
        rowSpan: 1,
      });
    } else {
      // 为每个产品创建新对象
      productList.forEach((product, productIndex) => {
        result.push({
          ...baseOrderInfo, // 订单基本信息
          ...parcelInfo, // parcel信息
          ...product, // 产品信息
          // 只有第一个产品保留原始rowSpan，其他设为0
          rowSpan: productIndex === 0 ? parcelInfo.rowSpan : 0,
        });
      });
    }
  });

  return result;
}

// Function to reverse the structure of transformed order data
export function reverseOrderData(inputData, currencies, labelname) {
  if (!Array.isArray(inputData) || inputData.length === 0) {
    console.error('Invalid input data');
    return [];
  }

  const fields = {
    poPStation: ['pointId'],
    parcel: [
      'declaredCurrency',
      'companyCodeName',
      'receiverName',
      'receiverPhone',
      'receiverMobile',
      'receiverState',
      'receiverCity',
      'receiverCompany',
      'receiverHouse',
      'receiverAddress1',
      'receiverAddress2',
      'postcode',
      'receiverEmail',
      'dutyParagraph',
      'taxationNumber',
      'companyCode',
      'receiverTaxNumber',
      'totalPrice',
      'declareValue',
      'totalQuantity',
      'totalWeight',
      'rowSpan',
      'receiverPinflNumber',
    ],
    product: [
      'waybillNumber',
      'id',
      'goodsNameCh',
      'goodsNameEn',
      'price',
      'hscode',
      'url',
      'weight',
      'material',
      'quantity',
      'sku',
      'imei',
    ],
  };

  const groupedByWaybill = inputData.reduce((acc, item) => {
    (acc[item.waybillNumber] = acc[item.waybillNumber] || []).push(item);
    return acc;
  }, {});

  return Object.values(groupedByWaybill).map(group => {
    const firstItem = group[0];

    // Helper function to extract fields into an object
    const extractFields = (fieldsToExtract, source) =>
      fieldsToExtract.reduce((obj, field) => {
        obj[field] = source[field];
        return obj;
      }, {});

    const poPStation = extractFields(fields.poPStation, firstItem);
    const parcelInfo = extractFields(fields.parcel, firstItem);
    const productList = group.map(item => ({
      ...extractFields(fields.product, item),
    }));

    const transformedOrder = {
      ...extractFields(
        Object.keys(firstItem).filter(
          key => !fields.parcel.includes(key) && !fields.product.includes(key)
        ),
        firstItem
      ),
      waybillNumber: firstItem.waybillNumber,
      currencyId: currencies?.[firstItem.declaredCurrency]?.id,
      currency: firstItem.declaredCurrency,
      currencyName: currencies?.[firstItem.declaredCurrency]?.name,
      declareValue: firstItem.declareValue,
      receiverInfo: {
        name: parcelInfo.receiverName,
        company: parcelInfo.receiverCompany,
        state: parcelInfo.receiverState,
        city: parcelInfo.receiverCity,
        zipCode: parcelInfo.postcode,
        houseNumber: parcelInfo.receiverHouse,
        phone: parcelInfo.receiverPhone,
        mobile: parcelInfo.receiverMobile,
        email: parcelInfo.receiverEmail,
        taxNumber: parcelInfo.receiverTaxNumber,
        address: parcelInfo.receiverAddress1,
        address2: parcelInfo.receiverAddress2,
        pinflNumber: parcelInfo?.receiverPinflNumber,
      },
      poPStation,
      productList: labelname === '收件人信息修改' ? firstItem.productList : productList,
      parcel: {
        ...parcelInfo,
        productList: labelname === '收件人信息修改' ? firstItem.productList : productList,
      },
    };

    return transformedOrder;
  });
}

export const calculateTotalSum = (customsInfo, fieldsToSum) => {
  // 如果 customsInfo 是未定义的，直接返回 0
  if (!customsInfo || typeof customsInfo !== 'object') {
    return 0;
  }

  // 初始化总和为 0 使用 Decimal
  let totalSum = new Decimal(0);

  // 计算总和
  fieldsToSum.forEach(field => {
    // 取出字段值，使用 hasOwnProperty 确定字段是否存在
    const value = customsInfo.hasOwnProperty(field) ? customsInfo[field] : 0;

    // 只有在字段值有效的情况下进行计算
    if (value !== undefined && value !== null && value !== '') {
      const decimalValue = new Decimal(value);

      // 检查是否是有效的数字
      if (!decimalValue.isNaN()) {
        totalSum = totalSum.plus(decimalValue);
      }
    }
  });

  // 转换总和为数字
  return totalSum.toNumber();
};

// 邮箱格式验证
export const validatorEmail = (value, formRef, type) => {
  if (!value) return Promise.resolve();
  const trimmed = value.trim();
  // 检查是否包含内部空格
  if (trimmed.includes(' ')) {
    return Promise.reject(formatMessage({ id: '邮箱地址含有空格，请修正后再提交！' }));
  }
  // 自动修正前后空格
  if (value !== trimmed) {
    formRef?.current
      ? formRef.current?.setFieldsValue({ [type]: trimmed })
      : formRef?.setFieldsValue({ [type]: trimmed });
  }

  // 邮箱格式验证
  const emailRegex = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
  if (!emailRegex.test(trimmed)) {
    return Promise.reject(formatMessage({ id: '请输入正确邮箱' }));
  }
  return Promise.resolve();
};

export const freightCodes = ['F01', 'F12', 'F10', 'F100'];

// 计算运费金额和其他费用
export const calculateFreightAmount = moneyItem => {
  // 运费是由公斤处理费（F01）+燃油费（F12）+干线调拨费（F10）+旺季附加费（F100） itemCode
  let freightAmount = new Decimal(0);

  // 计算运费总额
  const freightItems = moneyItem.filter(item => freightCodes.includes(item.itemCode));
  freightItems.forEach(item => {
    freightAmount = freightAmount.add(new Decimal(item.money));
  });

  // 过滤出非运费项目
  const otherItems = moneyItem.filter(item => !freightCodes.includes(item.itemCode));

  return [
    {
      itemName: '运费',
      cnyMoney: freightAmount.toNumber(),
      itemCode: 'freight',
    },
    ...otherItems,
  ];
};

export const handleLinkParam = (props, deleteParams) => {
  // 移除问号后边参数
  let params = new URLSearchParams(props.location.search);
  for (let i = 0; i < deleteParams.length; i++) {
    params.delete(deleteParams[i]);
  }
  // 更新 URL，但不刷新页面
  props.history.replace({
    pathname: props.location.pathname,
    search: params.toString() ? '?' + params.toString() : '',
  });
};

export const openSignModal = params => {
  const { url, contractName, closable = true, func } = params;
  Modal.confirm({
    title: '签署合同',
    icon: null,
    content: (
      <p>
        您即将签署合同
        <span style={{ color: '#299956' }}>《{contractName?.replace(/V\d+/g, '')}》</span>
      </p>
    ),
    closable,
    cancelText: '预览下载',
    okText: '立即签署',
    zIndex: 1001,
    cancelButtonProps: {
      onClick: e => {
        e.stopPropagation();
        window.open(url, '_blank');
      },
    },
    onOk: () => {
      // window.location.href = url;
      func(url => {
        window.location.href = url;
      });
    },
  });
};

// 点击菜单开通业务线情况
export const handleBusinessStatus = async (key, dispatch) => {
  const businessObject = {
    '0': {
      title: '小包专线业务介绍',
      // url: ''
      okText: '去开通',
      onOk: () => {
        dispatch({
          type: 'homePage/handleOpenStatus',
          payload: {
            smallBagStatus: true, // 开通小包专线状态
            fbaStatus: false, // 开通FBA专线状态
          },
        });
        router.push('/homePageList');
      },
    },
    '1': {
      title: 'FBA专线业务介绍',
      okText: '去开通',
      onOk: () => {
        dispatch({
          type: 'homePage/handleOpenStatus',
          payload: {
            smallBagStatus: false, // 开通小包专线状态
            fbaStatus: true, // 开通FBA专线状态
          },
        });
        router.push('/homePageList');
      },
    },
    '2': {
      title: '海外派业务介绍',
      okText: '去开通',
      onOk: async () => {
        const res = await useOpenBusinessStatusDispatch(dispatch, +key);
        if (!res.status && res.key === '-2') {
          return Modal.info({
            title: '海外派业务介绍',
            content: '目前仅境内用户可启用该业务，详情请咨询销售！',
            icon: null,
          });
        }
        dispatch({
          type: 'homePage/handleOpenStatus',
          payload: {
            smallBagStatus: false, // 开通小包专线状态
            fbaStatus: false, // 开通FBA专线状态
            overseasStatus: true,
          },
        });
        router.push('/homePageList');
      },
    },
    '3': {
      title: '中国仓业务介绍',
      okText: '知道了',
      onOk: () => {},
    },
  };

  // 业务介绍
  Modal.confirm({
    title: businessObject[key]?.title ?? '',
    content: businessIntroduction[key],
    icon: null,
    okText: businessObject[key]?.okText ?? '去开通',
    cancelText: '暂不开通',
    onOk: businessObject[key]?.onOk ?? (() => {}),
  });
};

// 企业名称格式验证
export const checkCompanyNameRegex = /^(?:[\u4e00-\u9fa5a-zA-Z]+(?:（[\u4e00-\u9fa5]+）)?)+$/;

export const openPreviewModal = params => {
  const { url, closable = true } = params;
  const dataBase64Data = `data:application/pdf;base64,${url}`;
  const parts = dataBase64Data.split(';base64,');
  const contentType = parts[0].split(':')[1];
  const raw = window.atob(parts[1]);
  const rawLength = raw.length;
  const uInt8Array = new Uint8Array(rawLength);
  for (let i = 0; i < rawLength; ++i) {
    uInt8Array[i] = raw.charCodeAt(i);
  }
  const blob = new Blob([uInt8Array], { type: contentType });
  const blobUrl = URL.createObjectURL(blob);
  Modal.info({
    title: null,
    icon: null,
    closable,
    width: '70%',
    zIndex: 1001,
    content: <iframe src={blobUrl} width="100%" height="600px" frameBorder="0" />,
  });
};

export const hongKongDHLOpenWindow = () => {
  //  生产
  const url = `/serviceManagement/helpCenter?type=3&id=7173581784381132800&selectId=361`;
  window.open(url);
};
