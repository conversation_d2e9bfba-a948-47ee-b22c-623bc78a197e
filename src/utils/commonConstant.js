import moment from 'moment';

// temu展示产品
export const temuShowList = ['1828', '1829', '1830', '1846'];

// 客户经营平台
export const customerPlatformSources = [
  '亚马逊',
  '速卖通',
  '沃尔玛',
  'wish',
  'eBay',
  'TikTok',
  'coupang',
  'shopee',
  '自建站',
  '京东',
  '敦煌',
  'DSers点石',
];

// 税号备案中的平台数据 Fruugo、etsy、阿里巴巴、其他（
export const platformData = [
  { label: '个人', value: '个人' },
  { label: '速卖通', value: '速卖通' },
  { label: '亚马逊', value: '亚马逊' },
  { label: '敦煌', value: '敦煌' },
  { label: '阿里巴巴', value: '阿里巴巴' },
  { label: 'Wish', value: 'Wish' },
  { label: 'VOVA', value: 'VOVA' },
  { label: 'Tophatter', value: 'Tophatter' },
  { label: 'eBay', value: 'eBay' },
  { label: 'Shopify', value: 'Shopify' },
  { label: 'TikTok', value: 'TikTok' },
  { label: 'Joom', value: 'Joom' },
  { label: 'Fruugo', value: 'Fruugo' },
  { label: 'etsy', value: 'etsy' },
  { label: '其他', value: '其他' },
];

// 修改商户与商户关系
export const merchantRelation = [
  '法定代表人',
  '控股股东或实际控制人',
  '实际控制人控制的其他公司',
  '控股子公司',
  '直系亲属（父母、配偶、子女）',
  '其他亲属',
  '员工',
  '其他',
];

export const pageRouters = {
  '400': '/exception/400',
  '500': '/exception/400',
  '-1': '/userInitPassword',
  '0': '/homePageList', // 未开通业务
  '1': '/customerManager/customerInformation/realName', // 未实名认证
  '3': '/homePageList', // 待审核业务
  '4': '/homePageList', // 审核失败业务
  '5': '/homePageList', // 业务待签约
  '9': '/financialManagement/rechargeManagement/merchantUpdatePaymentAccount', // 客服补齐
  '10': '/financialManagement/rechargeManagement/merchantConfirmPaymentAccount', // 确认付款
  '11': '/customerManager/customerInformation/CompleteAttach', // 补齐证件照
  // '0': 'merchant',
  // '1': '/registerMerchant/register/authIndex',
  // '2': '/registerMerchant/register/registerIndex',
  // '3': '/registerMerchant/register/companyBankRegister',
  // '4': '/registerMerchant/register/companyBankVerify',
  // '5': '/registerMerchant/register/shipperAndContactRegister',
  // '6': '/registerMerchant/registerToAudit',
  // '7': '/registerMerchant/registerToAudit',
  // '8': '/merchant/unSignedMerchantInfo',
  // '9': '/merchant/waiting',
  // '10': '/merchant/authIndex',
  // '11': '/merchant/personalMerchantInfoUpdate',
  // '12': '/merchant/companyMerchantInfoUpdate',
  // '13': '/merchant/companyBankUpdate',
  // '14': '/merchant/companyBankVerify',
  // '15': '/merchant/shipperAndContactUpdate',
  // '16': '/merchant/unSignedMerchantInfo',
  // '17': '/merchant/unSignedMerchantInfo',
  // '18': '/merchant/merchantInfoList',
  // '19': '/merchant/merchantUpdateIdentification',
  // '20': '/merchant/unSignedMerchantInfo',
  // '21': '/merchant/serviceConfirmation',
  // '22': '/merchant/merchantUpdatePaymentInfo',
  // '23': '/financialManagement/rechargeManagement/merchantConfirmPaymentAccount',
  // '24': 'merchant',
  // '25': '/merchant/companyOverseaMerchantInfoUpdate',
};

// 业务状态
export const businessStatus = ['0', '1', '3', '4', '5', '-2'];

export const relationOption = [
  {
    value: '法定代表人',
    title: '法定代表人',
  },
  {
    value: '控股股东或实际控制人',
    title: '控股股东或实际控制人',
  },
  {
    value: '员工',
    title: '员工',
  },
  {
    value: '其他',
    title: '其他',
  },
];
// 个人商户
export const personalPayRelationArray = [
  {
    value: 0,
    title: '个人付款',
    relation: [
      {
        value: '直系亲属（父母、配偶、子女）',
        title: '直系亲属（父母、配偶、子女）',
      },
      {
        value: '其他亲属',
        title: '其他亲属',
      },
      {
        value: '员工',
        title: '员工',
      },
      {
        value: '其他',
        title: '其他',
      },
    ],
  },
  {
    value: 1,
    title: '企业付款',
    relation: [
      {
        value: '法定代表人',
        title: '法定代表人',
      },
      {
        value: '控股股东或实际控制人',
        title: '控股股东或实际控制人',
      },
      {
        value: '员工',
        title: '员工',
      },
      {
        value: '其他',
        title: '其他',
      },
    ],
  },
];
// 企业商户
export const companyPayRelationArray = [
  {
    value: 2,
    title: '个人付款',
    relation: [
      {
        value: '法定代表人',
        title: '法定代表人',
      },
      {
        value: '控股股东或实际控制人',
        title: '控股股东或实际控制人',
      },
      {
        value: '员工',
        title: '员工',
      },
      {
        value: '其他',
        title: '其他',
      },
    ],
  },
  {
    value: 3,
    title: '企业付款',
    relation: [
      {
        value: '控股股东或实际控制人',
        title: '控股股东或实际控制人',
      },
      {
        value: '实际控制人控制的其他公司',
        title: '实际控制人控制的其他公司',
      },
      {
        value: '控股子公司',
        title: '控股子公司',
      },
      {
        value: '其他',
        title: '其他',
      },
    ],
  },
];
// 支付宝
export const alipayPayRelationArray = [
  {
    value: 0,
    title: '个人付款',
    relation: [
      {
        value: '直系亲属（父母、配偶、子女）',
        title: '直系亲属（父母、配偶、子女）',
      },
      {
        value: '其他亲属',
        title: '其他亲属',
      },
      {
        value: '员工',
        title: '员工',
      },
      {
        value: '其他',
        title: '其他',
      },
    ],
  },
  {
    value: 1,
    title: '企业付款',
    relation: [
      {
        value: '法定代表人',
        title: '法定代表人',
      },
      {
        value: '控股股东或实际控制人',
        title: '控股股东或实际控制人',
      },
      {
        value: '员工',
        title: '员工',
      },
      {
        value: '其他',
        title: '其他',
      },
    ],
  },
];
export const alipayCompanyPayRelationArray = [
  {
    value: 2,
    title: '个人付款',
    relation: [
      {
        value: '法定代表人',
        title: '法定代表人',
      },
      {
        value: '控股股东或实际控制人',
        title: '控股股东或实际控制人',
      },
      {
        value: '员工',
        title: '员工',
      },
      {
        value: '其他',
        title: '其他',
      },
    ],
  },
  {
    value: 3,
    title: '企业付款',
    relation: [
      {
        value: '控股股东或实际控制人',
        title: '控股股东或实际控制人',
      },
      {
        value: '实际控制人控制的其他公司',
        title: '实际控制人控制的其他公司',
      },
      {
        value: '控股子公司',
        title: '控股子公司',
      },
      {
        value: '其他',
        title: '其他',
      },
    ],
  },
];

export const otherPayRelationArray = [
  {
    value: '其他（本人）',
    title: '其他（本人）',
    relation: [
      {
        value: '其他（本人）',
        title: '其他（本人）',
      },
    ],
  },
];

// 认证方式
export const authOptions = [
  {
    label: '人脸识别',
    value: 0,
  },
  {
    label: '银行卡认证',
    value: 1,
  },
  {
    label: '其他',
    value: 2,
  },
];
// 身份证;
// 香港来往大陆通行证;
// 澳门来往大陆通行证;
// 台湾来往大陆通行证;
// 护照;

export const idCardOptions = [
  {
    label: '身份证',
    value: 0,
  },
  {
    label: '香港来往大陆通行证',
    value: 4,
  },
  {
    label: '澳门来往大陆通行证',
    value: 5,
  },
  {
    label: '台湾来往大陆通行证',
    value: 6,
  },
  {
    label: '护照',
    value: 7,
  },
];

// 高德地图key
export const aMapKeys = [
  {
    key: '1f9b587255c4147ee3a632f067a579ad',
    code: '3ac3c62cdea0eb150ec3a181ea97921c',
  },
  {
    key: '530f3c8e62422a3dda16873a1fe47bf7',
    code: 'b3d8bedc7f586995413d4ce30e9e0f0e',
  },
  {
    key: 'e1c141ec5cdd5ff378c8f2e12447552a',
    code: '8239d20f81bba7aa5af78c2b01f4a9db',
  },
  {
    key: 'c704d2fdd4d97d18828470c435306e60',
    code: '4482db49e7efcc91ac26cbc4f69c54f8',
  },
  {
    key: 'ffed9ce08418a4d483db38de751953c0',
    code: '9a8ea6b37887bb48b571d965996bab77',
  },
];

// 图片占位符
export const errorImageUrl =
  'data:image/png;base64,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';

// 长期证件有效期
export const dateLongCheck = '3000-01-01 00:00:00';

// position 岗位
export const positionList = [
  { value: '员工', label: '员工' },
  { value: '部门经理', label: '部门经理' },
  { value: '部门总监', label: '部门总监' },
  { value: '总经理', label: '总经理' },
];

// relationShip 与商户关系
export const relationShipList = [
  { value: '亲属', label: '亲属' },
  { value: '同事', label: '同事' },
  { value: '同学', label: '同学' },
  { value: '邻居', label: '邻居' },
  { value: '朋友', label: '朋友' },
  { value: '其他', label: '其他' },
];

// 退件方式
export const returnWayList = [
  { value: '0', label: '司机带回' },
  { value: '3', label: '快递到付' },
  { value: '2', label: '快递寄付' },
  { value: '1', label: '自取退回' },
];

// 工单中的新建时间列表
export const createTimerList = [
  { value: 'bx', label: '不限' },
  {
    label: '今天',
    value: moment()
      .endOf('day')
      .format('YYYY-MM-DD'),
  },
  {
    label: '3天内',
    value: moment()
      .subtract(2, 'day')
      .format('YYYY-MM-DD'),
  },
  {
    label: '7天内',
    value: moment()
      .subtract(6, 'day')
      .format('YYYY-MM-DD'),
  },
  {
    label: '14天内',
    value: moment()
      .subtract(13, 'day')
      .format('YYYY-MM-DD'),
  },
  {
    label: '28天内',
    value: moment()
      .subtract(27, 'day')
      .format('YYYY-MM-DD'),
  },
  { label: '自定义', value: 'zdy' },
];

export const queryReasonList = [
  {
    value: 1,
    label: '退回原因',
  },
  {
    value: 2,
    label: '退件时间',
  },
  {
    value: 3,
    label: '包裹未上网',
  },
  {
    value: 4,
    label: '包裹长时间未更新',
  },
  {
    value: 5,
    label: '网上显示签收，但是收件人未收到',
  },

  {
    value: 6,
    label: '签收地址不正确',
  },
  {
    value: 7,
    label: '包裹是否有揽收',
  },
  {
    value: 8,
    label: '燕文官网没有追踪信息',
  },
  {
    value: 9,
    label: '包裹具体位置',
  },
  {
    value: 10,
    label: '收件人收到包裹破损、丢件',
  },
  {
    value: 12,
    label: '申请POD（签收图片）',
  },
  {
    value: 13,
    label: '派送失败原因',
  },
  {
    value: 11,
    label: '其他',
  },
];

// 申报价值确认
export const declareValueList = ['1307'];
// 修改信息中收件人信息
export const editNewsRecipientArray = [
  '1129',
  '1119',
  '1281',
  '1071',
  '1058',
  '1117',
  '1115',
  '1120',
  '1070',
  '1127',
  '1061',
  '1298',
  '1067',
  '1118',
  '1179',
  '1072',
  '1066',
  '1073',
  '1063',
  '1060',
  '1299',
];
// 修改信息中申报信息
export const editNewsDeclarationArray = [
  '1065',
  '1122',
  '1121',
  '1238',
  '1128',
  '1269',
  '1178',
  '1282',
];

// 修改信息
export const editNewsArray = [
  '1056',
  '1058',
  '1060',
  '1061',
  '1062',
  '1065',
  '1066',
  '1067',
  '1070',
  '1071',
  '1073',
  '1115',
  '1117',
  '1118',
  '1119',
  '1120',
  '1121',
  '1122',
  '1128',
  '1127',
  '1129',
  '1178',
  '1184',
  '1207',
  '1233',
  '1238',
  '1281',
  '1298',
];
// 确认费用
export const recognitionExpense = [
  '1081',
  '1082',
  '1083',
  '1084',
  '1085',
  '1086',
  '1087',
  '1088',
  '1089',
  '1092',
  '1093',
  '1072',
  '1256',
  '1257',
  '1258',
  '1259',
  '1260',
  '1261',
  '1283',
  '1303',
  '1304',
  '1305',
];
// 提供新发票
export const invoiceArr = ['1074', '1075', '1076', '1077', '1078', '1079', '1080'];
// 重出签发
export const Issueagain = ['1109', '1110', '1112'];
// 提供资料
export const provideInformation = ['1126'];
// 取消截留
export const CancelIntercept = ['1100'];
// 修改客户号
export const Modifycustomernum = ['1063'];
// 展示泡重重量和运费
export const showWeightAndFreight = ['1093', '1265', '1256', '1258', '1259', '1283'];

// 仓外异常件中匹配的处理类型
export const wareTypeList = [
  '客户IOSS税号错误',
  '申报价值过低',
  '国外海关扣关',
  '国内海关扣关',
  '国外海关扣关并销毁',
  '国内海关扣关并销毁',
  '收件人地址不正确',
  '收件人地址不详',
  '收件人待支付关税',
  '收件人不在家',
  '收件人拒收',
  '联系不到收件人',
  '包裹破损',
  '包裹退回',
  '无人认领',
  '电话无法接通',
  '邮箱不正确',
  '无法进入收件人住宅区',
  '重派失败',
  '电话不正确',
  '邮编错误',
  '其他',
  '重量确认',
];

export const businessTypeList = [
  { label: '小包专线', value: '0' },
  { label: 'FBA专线', value: '1' },
  { label: '海外派', value: '2' },
  // { label: '中国仓', value: '3' },
];

export const AlipayArr = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];

// 处理成select下拉框适合的数据格式 ， 付款类型和付款人与商户关系都可以用这个方法, 可以当作处理数据的模板
export const renderSelectOptions = (dataArray, optionsFields) => {
  return (
    dataArray?.map((item, index) => {
      item.label = item[optionsFields.label];
      item.value = item[optionsFields?.value ?? 'value'];
      return item;
    }) ?? []
  );
};

// 美国加拿大澳大利亚只允许选择包税
export const taxModeChooseData = [
  115,
  45,
  92,
  30,
  53,
  70,
  82,
  94,
  99,
  102,
  127,
  154,
  165,
  192,
  242,
  243,
  254,
  262,
  272,
  309,
  314,
  315,
  334,
  338,
  345,
  350,
  356,
  379,
];

// 处理成select下拉框适合的数据格式 ， 付款类型和付款人与商户关系都可以用这个方法, 可以当作处理数据的模板
export const renderSelectOptionsMulti = (dataArray, optionsFields, split) => {
  return dataArray.map((item, index) => {
    item.label = optionsFields.label.map(l => item[l]).join(split ? split : '/');
    item.value = item[optionsFields?.value ?? 'value'];
    return item;
  });
};

// 根据付款方式和商户类型获取付款类型和付款人与商户关系
export const PAY_RELATION_MAP = {
  '0-个人': personalPayRelationArray,
  '0-企业': companyPayRelationArray,
  '1-个人': alipayPayRelationArray,
  '1-企业': alipayCompanyPayRelationArray,
  '2-个人': personalPayRelationArray,
  '2-企业': companyPayRelationArray,
};

// 默认付款类型和付款人与商户关系
export const DEFAULT_PAY_TYPE_MAP = {
  '0-个人': personalPayRelationArray[0].value,
  '0-企业': companyPayRelationArray[1].value,
  '1-个人': alipayPayRelationArray[0].value,
  '1-企业': alipayCompanyPayRelationArray[1].value,
  '2-个人': personalPayRelationArray[0].value,
  '2-企业': companyPayRelationArray[1].value,
};

// 默认付款类型和付款人与商户关系
export const DEFAULT_PAY_RELATION_MAP = {
  '0-个人': personalPayRelationArray[0].relation,
  '0-企业': companyPayRelationArray[1].relation,
  '1-个人': alipayPayRelationArray[0].relation,
  '1-企业': alipayCompanyPayRelationArray[1].relation,
  '2-个人': personalPayRelationArray[0]?.relation,
  '2-企业': companyPayRelationArray[1].relation,
};

export const otherOption = '其他（本人）';

/**
 *  白名单页面
 *  1. 无需权限
 */
export const whiteListPage = [
  'index',
  'homePageList',
  'financialManagement',
  'customerManager',
  'serviceManagement',
  'content',
];

// 业务介绍
export const businessIntroduction = {
  '0':
    '燕文整合全球快递资源推出的跨境小包出口电子商务领域的一站式物流服务。服务内容包括首公里上门揽收、分拨仓订单履约作业、国际干线运输、出入境清关、末端配送、退运等物流相关服务，将各类包裹从启运国直运到目的国，进行末端派送的一项综合性服务。', // 小包专线
  '1':
    '燕文物流FBA大货业务，专为跨境电商卖家提供大包裹一站式门到门跨境物流服务。全国24城可揽收调拨，设有上海、义乌、深圳三大操作中心，物流服务涵盖欧盟、英国、美国、加拿大、澳大利亚等三十余国。可为商家、个人支持送达FBA仓库，海外仓，私人地址提供空派、海运、铁路、卡航等全渠道服务。', // FBA业务
  '2':
    'YWE 是燕文物流美国最后一公里自营派送品牌，全美已建设洛杉矶、旧金山、芝加哥、纽约、达拉斯、迈阿密，亚特兰大7大分拣中心，在主要核心城市圈基本完成全覆盖，部分城市已开启一票上门取件的揽收服务，随着揽收区域以及派送区域的逐步拓展，持续为客户提供安全，高效，极具性价比的最后一公里派送服务。',
  '3':
    '燕文中国仓是位于中国境内的仓储物流中心，仓选址于电商货源地，节省头程转运时间，如深圳、东莞、义乌、杭州和郑州等地。可为用户提供专业的货物验收、集成、存储和出库打包等仓储服务，以及匹配通达全球200多个国家和地区的进出口通关、干线运输和目的国最后一公里配送服务。', // 中国仓
};

// 特殊路由
export const specialRoute = [
  '/financialManagement/invoice/invoiceReapply/',
  '/financialManagement/invoice/invoiceDetail/',
  '/financialManagement/invoice/invoiceSearchList/',
  '/serviceManagement/content/noticeList/',
  '/smallBag/collectManagement/material/materSearchlist/',
  '/smallBag/collectManagement/material/materdetail/',
];

// https://track.yw56.com.cn/cn/querydel       生产
// http://************       测试
export const trackUrl = 'http://************/cn/querydel';

/**
 * 发货方式
1:客户自送 2:燕文揽收 3:客户自寄；
 */
export const shipperMethodList = [
  { label: '燕文揽收', value: '2' },
  { label: '客户自送', value: '1' },
  { label: '客户自寄', value: '3' },
];

/**
 * 商业快递文件类型
 * @type {[{label: string, value: string}, {label: string, value: string}, {label: string, value: string}, {label: string, value: string}]}
 */
export const businessFileType = [
  {
    label: '报关资料',
    value: '1',
  },
  {
    label: '商业发票',
    value: '2',
  },
  {
    label: '交易证明',
    value: '3',
  },
  {
    label: '其他资料',
    value: '4',
  },
];

// 办公地址正则校验
export const officeAddressRegex = /^[^0-9]+[0-9]+号(\d+栋(\d+?|(\d+)?[^0-9]+)?|(\d+)?楼整层|[A-Za-z0-9]+室?)?$|^\d+栋（([\u4e00-\u9fa5]+|\d+)单元）?\d+$|^[^0-9]+\d+栋(\d+单元)?\d+$|[\u4e00-\u9fa5]+[A-Za-z0-9\u4e00-\u9fa5]+$|([A-Za-z]|\d+)栋[A-Za-z0-9]+号[A-Za-z0-9]+室?$/g;
// 手机号正则校验
export const mobileValidateRegex = /^(?:852)?[5689]\d{7}$|^(?:853)?[66|68]\d{5}$|^(?:886)?9\d{8}$|^1\d{10}$/;
export const DownloadTaskType = {
  BILL_DETAIL: 1,
  E_BILL: 2,
};

// 付款账号证件类型
export const AddPaymentCertificateTypeData = [
  {
    label: '身份证',
    value: 0,
  },
  {
    label: '香港来往大陆通行证',
    value: 4,
  },
  {
    label: '澳门来往大陆通行证',
    value: 5,
  },
  {
    label: '台湾来往大陆通行证',
    value: 6,
  },
  {
    label: '护照',
    value: 7,
  },
];

export const contract = {
  directContractVersion: 8,
  fbaContractVersion: 4,
};

export const billApi = {
  settleApiUrl: 'https://fs2.yanwentech.com:443/downloadfile/14873836/1846743392940244994.pdf',
  unSettleApiUrl: 'https://fs2.yanwentech.com:443/downloadfile/14748538/1843483075711909890.pdf',
};

/**
 * 认证类型 0 个人认证 1 企业认证
 * 客户地区 0 大陆 1 境外 3 港澳台
 */
export const customerAreaData = {
  大陆个人: {
    customerArea: 0,
    customerType: 0,
  },
  大陆企业: {
    customerArea: 0,
    customerType: 1,
  },
  港澳台个人: {
    customerArea: 3,
    customerType: 0,
  },
  港澳台企业: {
    customerArea: 3,
    customerType: 1,
  },
  境外个人: {
    customerArea: 1,
    customerType: 0,
  },
  境外企业: {
    customerArea: 1,
    customerType: 1,
  },
};

export const foreignKeyEnum = {
  foreignKey: 'foreignKey',
  robot: 'robot',
  billDownload: 'billDownload',
  crm: 'crm',
};

/**
 * 香港DHL产品代码
 */
export const hongKongDHLProductCode = ['45', '1155', '1291'];
