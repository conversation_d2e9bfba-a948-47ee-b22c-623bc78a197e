import { ProFormInstance } from '@ant-design/pro-components';
import { useMount, useUpdateEffect } from 'ahooks';
import { useRef, useState } from 'react';
import SearchComponent from './search';
import TablesComponents from './tables';
import moment from 'moment';
import React from 'react';
import { getOverseaRecordRequest, getOverseaRecordResponse } from './types';
import { connect } from 'dva';
import PageContainerComponent from '@/components/PageContainer';
import PageHeaderBreadcrumb from '@/components/PageHeaderBreadcrumb';
import { textWaybill } from '@/utils/utils';

type TAccountDataInfo = USER_API.getOverseaAccountsData & {
  label?: string;
  value?: string;
};

type TAccountData = Array<TAccountDataInfo>;

const startTimeDate = moment()
  .subtract(6, 'months')
  .format('YYYY-MM-DD');
const endTimeDate = moment().format('YYYY-MM-DD');

const Index = (props: any) => {
  const { dispatch, tableLoading } = props;
  const formRef = useRef<ProFormInstance<getOverseaRecordRequest>>();
  const [pageSize, setPageSize] = useState(10);
  const [current, setCurrent] = useState(1);
  const [total, setTotal] = useState(0);
  const [dataList, setDataList] = useState<getOverseaRecordResponse['rows']>([]);
  const [tabActiveKey, setTabActiveKey] = useState('-2');
  const [accountData, setAccountData] = useState<TAccountData>([]);
  const [showCancel, setShowCancel] = useState(false);
  useUpdateEffect(() => {
    getList();
  }, [pageSize, current, tabActiveKey]);

  useMount(() => {
    getInit();
  });

  const getInitFunc = async () =>
    new Promise((resolve, reject) => {
      dispatch({
        type: 'overseas/getOverseaAccounts',
        payload: '0',
        callback: response => {
          if (response.success) {
            const data = response.data?.map(item => ({
              ...item,
              label: `${item?.accountCode}-${item?.currencyCode}`,
              value: item?.accountCode,
            }));
            if (data?.length > 0 && data[0].value.startsWith('8')) {
              setShowCancel(true);
            }
            setAccountData(data || []);
            resolve(data || []);
          } else {
            reject([]);
          }
        },
      });
    });

  const getInit = async () => {
    Promise.all([getInitFunc()]).then(res => {
      formRef.current?.setFieldsValue({
        userId: res[0]?.[0]?.accountCode,
        dateTime: [startTimeDate, endTimeDate],
      });
      getList();
    });
  };

  const getList = async () => {
    try {
      const values = await formRef.current?.validateFields();
      const params: Partial<typeof values> = {
        ...values,
        pageSize,
        pageNum: current,
        listNumber: values?.listNumber ? textWaybill(values?.listNumber, 500, true) : undefined,
        startTime:
          typeof values?.dateTime?.[0] === 'string'
            ? values?.dateTime?.[0]
            : values?.dateTime?.[0]?.format('YYYY-MM-DD') ?? undefined,
        endTime:
          typeof values?.dateTime?.[1] === 'string'
            ? values?.dateTime?.[1]
            : values?.dateTime?.[1]?.format('YYYY-MM-DD') ?? undefined,
        status: tabActiveKey,
      };
      dispatch({
        type: 'overseas/getOversea',
        payload: params,
        callback: response => {
          if (response.success) {
            setDataList(response.data?.records ?? []);
            setTotal(response.data?.totalRecord ?? 0);
          } else {
            setDataList([]);
            setTotal(0);
          }
        },
      });
    } catch (error) {
      console.error(error);
    }
  };

  const resetPageSize = () => {
    setCurrent(1);
    setPageSize(10);
  };

  const pagination = {
    pageSize,
    current,
    total,
    showSizeChanger: true,
    pageSizeOptions: ['5', '10', '20', '50', '100', '500', '1000'],
    onChange: (page: number, pageSize: number) => {
      setCurrent(page);
      setPageSize(pageSize);
    },
    showTotal: () => {
      return `共- ${total} -条记录`;
    },
  };

  const handleSearch = (values: getOverseaRecordRequest) => {
    if (current === 1 && pageSize === 10) {
      getList();
    } else {
      setCurrent(1);
      setPageSize(10);
    }
  };

  return (
    <PageContainerComponent
      {...props}
      header={{
        title: null,
        breadcrumb: {},
        breadcrumbRender: props => <PageHeaderBreadcrumb {...props} />,
      }}
    >
      <SearchComponent
        formRef={formRef}
        {...props}
        accountData={accountData}
        onSearch={handleSearch}
        buttonLoading={tableLoading}
        dateTime={[startTimeDate, endTimeDate]}
        setShowCancel={setShowCancel}
      />
      <TablesComponents
        {...props}
        tabActiveKey={tabActiveKey}
        setTabActiveKey={setTabActiveKey}
        pagination={pagination}
        data={dataList}
        resetPageSize={resetPageSize}
        formRef={formRef}
        showCancel={showCancel}
      />
    </PageContainerComponent>
  );
};

export default connect(({ loading }) => ({
  tableLoading: loading.effects['overseas/getOversea'],
  cancelLoading: loading.effects['overseas/cancelOverseaOrder'],
}))(Index);
