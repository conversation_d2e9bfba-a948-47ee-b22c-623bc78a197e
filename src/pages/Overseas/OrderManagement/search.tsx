import '@/pages/index.less';
import {
  ProCard,
  ProForm,
  ProFormDateRangePicker,
  ProFormRadio,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Button, Col, Row, Space } from 'antd';
import React from 'react';
import { SearchIProps, getOverseaRecordRequest } from './types';
import moment from 'moment';
const SearchComponent = ({
  formRef,
  accountData,
  onSearch,
  buttonLoading,
  dateTime,
  setShowCancel,
}: SearchIProps<getOverseaRecordRequest>) => {
  const handleReset = () => {
    formRef.current?.resetFields();
  };

  const handleSearch = async () => {
    try {
      const values = await formRef.current?.validateFields();
      onSearch(values);
    } catch (error) {
      console.error(error);
    }
  };

  const disabledDate = current => {
    return current && current > moment();
  };

  return (
    <ProCard ghost>
      <ProCard bordered>
        <ProForm
          formRef={formRef}
          labelAlign="left"
          layout="horizontal"
          labelCol={{ flex: '70px' }}
          initialValues={{
            dateTime,
            userId: accountData[0]?.accountCode,
          }}
          submitter={{
            render: () => {
              return [];
            },
          }}
        >
          <Row gutter={20} className="pt-2">
            <Col span={24}>
              <div className="ant-radio-button-part">
                <ProFormRadio.Group
                  name="userId"
                  label="业务账号"
                  radioType="button"
                  options={accountData}
                  fieldProps={{
                    buttonStyle: 'solid',
                    onChange: e => {
                      if (e.target.value.startsWith('8')) {
                        setShowCancel(true);
                      } else {
                        setShowCancel(false);
                      }
                      handleSearch();
                    },
                  }}
                />
              </div>
            </Col>

            <Col span={6}>
              <ProFormTextArea
                labelCol={{ flex: '105px' }}
                name="listNumber"
                label="运单号/订单号"
                placeholder="请输入查询运单号/订单号，最多支持500条"
              />
            </Col>
            <Col span={6}>
              <ProFormDateRangePicker
                fieldProps={{
                  style: { width: '100%' },
                  disabledDate,
                }}
                name={'dateTime'}
                label="创建时间"
                placeholder={['请选择开始时间', '请选择结束时间']}
              />
            </Col>
            <Col span={6}>
              <Space className="pl-2">
                <Button type="primary" onClick={handleSearch} loading={buttonLoading}>
                  查询
                </Button>
                <Button onClick={handleReset}>重置</Button>
              </Space>
            </Col>
          </Row>
        </ProForm>
      </ProCard>
    </ProCard>
  );
};

export default SearchComponent;
