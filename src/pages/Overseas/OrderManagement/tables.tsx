import '@/pages/index.less';
import { ProCard, ProColumnType, ProFormInstance, ProTable } from '@ant-design/pro-components';
import { useUpdateEffect } from 'ahooks';
import { Button, Space, TablePaginationConfig, message, Tabs, Modal } from 'antd';
import { useState, useRef } from 'react';
import React from 'react';
import Detail from './detail';
import { downloadBase64File } from '@/utils/utils';
import TextArea from 'antd/es/input/TextArea';

interface IProps {
  pagination: TablePaginationConfig;
  data: any[];
  tabActiveKey: string;
  setTabActiveKey: (key: string) => void;
  resetPageSize: () => void;
  dispatch: any;
  formRef: React.MutableRefObject<ProFormInstance | undefined>;
  [key: string]: any;
}

const TablesComponents = ({
  pagination,
  data,
  tabActiveKey,
  setTabActiveKey,
  resetPageSize,
  dispatch,
  formRef,
  tableLoading,
  cancelMakeOrder,
  cancelLoading,
  showCancel,
}: IProps) => {
  const modalRef = useRef<{ open: (record) => void }>();
  const [dataSource, setDataSource] = useState<Array<any>>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [cancelFlag, setCancelFlag] = useState(false);
  const [cancelReason, setCancelReason] = useState('');

  useUpdateEffect(() => {
    setDataSource(data);
  }, [data]);

  const columns = [
    {
      title: '业务账号',
      dataIndex: 'userId',
    },
    {
      title: '订单号',
      dataIndex: 'orderNumber',
    },
    {
      title: '运单号',
      dataIndex: 'waybillNumber',
      render: (text, record) => {
        return (
          <a
            onClick={() => {
              const userId = formRef.current?.getFieldValue('userId');
              modalRef.current?.open({ ...record, userId });
            }}
          >
            {text}
          </a>
        );
      },
    },
    {
      title: '产品名称',
      dataIndex: 'channelName',
    },
    {
      title: '目的国',
      dataIndex: 'receiverCountryName',
    },
    {
      title: '收件人名称',
      dataIndex: 'receiverName',
    },

    {
      title: '创建时间',
      dataIndex: 'createTime',
    },
    {
      title: '状态',
      dataIndex: 'status',
      hideItem: !showCancel ? true : undefined,
      render: text => {
        let stateName = '已制单';
        if (text === '5') {
          stateName = '已取消';
        }
        return <div dangerouslySetInnerHTML={{ __html: stateName }} />;
      },
    },
  ];

  const tabItem = [
    {
      key: '-2',
      label: '全部',
    },
    {
      key: '0',
      label: '已制单',
      // children: 'Content of Tab Pane 1',
    },
    {
      key: '5',
      label: '已取消',
      // children: 'Content of Tab Pane 2',
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(selectedRowKeys);
    },
  };

  const tableAlertRender = ({
    selectedRowKeys,
    selectedRows,
    onCleanSelected,
  }: {
    selectedRowKeys: React.Key[];
    selectedRows: typeof data | Array<any>;
    onCleanSelected: () => void;
  }) => {
    // console.log(selectedRowKeys, selectedRows);
    return (
      <Space size={24}>
        <span>已选 {selectedRowKeys.length} 项</span>
        <span>{`容器数量: ${selectedRows.reduce(
          (pre, item) => pre + item.containers,
          0
        )} 个`}</span>
        <span>{`调用量: ${selectedRows.reduce((pre, item) => pre + item.callNumber, 0)} 次`}</span>
        <a className="mt-2" onClick={onCleanSelected}>
          清空
        </a>
      </Space>
    );
  };

  const handleOptions = () => {
    const userId = formRef.current?.getFieldValue('userId');
    dispatch({
      type: 'overseas/exportOverseaDetail',
      payload: {
        userId,
        listNumber: selectedRowKeys,
      },
      callback: response => {
        if (response.success) {
          downloadBase64File(response?.data?.base64, response?.data?.fileName);
          message.success('导出成功');
        }
        setSelectedRowKeys([]);
      },
    });
  };

  const cancelOrder = () => {
    let userId = formRef.current?.getFieldValue('userId');
    let param = { waybillNumbers: selectedRowKeys, userId: userId, note: cancelReason };
    dispatch({
      type: 'overseas/cancelOverseaOrder',
      payload: param,
      callback: result => {
        if (result.success) {
          cancelModal();
          message.success(result.message);
          resetPageSize();
        }
      },
    });
  };

  const cancelOptions = () => {
    setCancelFlag(true);
  };

  const cancelModal = () => {
    setCancelFlag(false);
    setCancelReason('');
  };

  const changeCancelOrderReason = e => {
    setCancelReason(e.target.value);
  };

  return (
    <ProCard ghost gutter={[0, 20]}>
      <ProCard
        bordered
        title={
          showCancel && (
            <Tabs
              className="ant-tab-no-bottom-border"
              activeKey={tabActiveKey}
              items={tabItem}
              onChange={key => {
                setTabActiveKey(key);
                resetPageSize();
                setSelectedRowKeys([]);
              }}
            />
          )
        }
        extra={
          <Space>
            <Button onClick={() => handleOptions()} disabled={selectedRowKeys.length == 0}>
              导出
            </Button>
            {showCancel && (
              <Button
                onClick={() => cancelOptions()}
                disabled={selectedRowKeys.length == 0 || tabActiveKey !== '0'}
              >
                取消
              </Button>
            )}
          </Space>
        }
      >
        <ProTable
          rowKey="waybillNumber"
          search={false}
          rowSelection={rowSelection}
          toolBarRender={false}
          dataSource={dataSource}
          columns={columns.filter(item => item.hideItem === undefined)}
          pagination={pagination}
          loading={tableLoading}
          // tableAlertRender={tableAlertRender}
          // tableAlertOptionRender={() => {
          //   return (
          //     <Space size={16}>
          //       <a onClick={handleOptions}>批量取消</a>
          //     </Space>
          //   );
          // }}
        />
      </ProCard>
      <Detail modalRef={modalRef} dispatch={dispatch} />
      <Modal
        title="取消订单"
        open={cancelFlag}
        onCancel={cancelModal}
        footer={[
          <Button key="submit" type="primary" loading={cancelLoading} onClick={cancelOrder}>
            确定
          </Button>,
          <Button key="back" onClick={cancelModal}>
            取消
          </Button>,
        ]}
      >
        <p>请输入取消订单原因（最多支持100个字符）</p>
        <TextArea
          rows={4}
          value={cancelReason}
          onChange={changeCancelOrderReason}
          placeholder="请输入取消运单原因(最多支持100个字符)"
          maxLength={100}
        />
      </Modal>
    </ProCard>
  );
};

export default TablesComponents;
