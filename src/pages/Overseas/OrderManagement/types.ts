import moment from 'moment';
import { ProFormInstance } from '@ant-design/pro-components';

export type DataSourceInfo = {
  id: number;
  name: string;
  desc: string;
  url: string;
  userName: string;
  password: string;
  createTime: string;
  updateTime: string;
};

export type getOverseaRecordRequest = {
  startTime: string;
  endTime: string;
  pageNum: number;
  pageSize: number;
  userId: string;
  listNumber: string[];
  dateTime: Array<moment.Moment>;
  status: string;
};
export interface getOverseaRecordResponse {
  rows: Array<
    Partial<{
      merchantCode: string;
      merchantName: string;
      customerCode: string;
      waybillNumber: string;
      exchangeNumber: string;
      orderStatus: string;
      orderNumber: string;
      transTime: string;
      expenseType: string;
      transNo: string;
      currency: string;
      moneyToFc: number;
      moneyToFcDif: number;
      memo: string;
    }>
  >;
}

export interface SearchIProps<T> {
  formRef: React.MutableRefObject<ProFormInstance<T> | undefined>;
  accountData: Array<{ [key: string]: any } & { label: 'string'; value: any }>;
  onSearch: (values: T | undefined) => void;
  buttonLoading: boolean;
  [key: string]: any;
}

export interface DetailInfo {
  waybillNumber: string;
  orderNumber: string;
  userId: string;
  epServiceCode: string;
  routingCode: string;
  epHandoverPlace: string;
  createTime: string;
  orderSource: string;
  channelId: string;
  channelName: string;
  inductionFacility: string;
  status: number;
  isPrint: number;
  receiverInfo: ReceiverInfo;
  senderInfo: SenderInfo;
  returnInfo: ReturnInfo;
  parcelInfo: ParcelInfo;
}

interface ReceiverInfo {
  name: string;
  company: string;
  phone: string;
  mobile: string;
  email: string;
  zipCode: string;
  countryId: string;
  countryName: string;
  state: string;
  city: string;
  district: string;
  address1: string;
  address2: string;
  houseNumber: string;
  taxNumber: string;
}

interface SenderInfo {
  name: string;
  phone: string;
  mobile: string;
  company: string;
  email: string;
  zipCode: string;
  countryId: string;
  countryName: string;
  state: string;
  city: string;
  district: string;
  address1: string;
  address2: string;
  houseNumber: string;
}

interface ReturnInfo {
  name: string;
  phone: string;
  mobile: string;
  company: string;
  email: string;
  zipCode: string;
  countryId: string;
  countryName: string;
  state: string;
  city: string;
  district: string;
  address1: string;
  address2: string;
  houseNumber: string;
}

interface ParcelInfo {
  totalWeight: string;
  weightUnit: string;
  length: string;
  width: string;
  height: string;
  dimensionUnit: string;
  totalDeclareValue: string;
  declareCurrency: string;
  totalQuantity: number;
  hasBattery: string;
  productList: ProductList[];
}

interface ProductList {
  sku: string;
  goodsNameCn: string;
  goodsNameEn: string;
  price: string;
  quantity: string;
  hsCode: string;
  weight: string;
  material: string;
  url: string;
}
