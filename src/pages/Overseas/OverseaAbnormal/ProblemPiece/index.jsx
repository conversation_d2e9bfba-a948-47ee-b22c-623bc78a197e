import { Form } from '@ant-design/compatible';
import React, { Component, Fragment } from 'react';
import { Button, Card, Col, DatePicker, message, Row, Select, Upload, UploadProps } from 'antd';
import { formatMessage } from 'umi-plugin-react/locale';
import StandardFormRow from '@/components/StandardFormRow';
import SingleTagSelect from '@/components/SingleTagSelect';
import TagSelect from '@/components/TagSelect';
import { connect } from 'dva';
import TextArea from 'antd/es/input/TextArea';
import ProTableList from '@/components/ProTable';
import AbnormalOverDetail from './components/AbnormalOverDetail';
import AbnormalBatchOver from './components/AbnormalBatchOver';
import { outsideOverseaExportHandle } from '@/services/problemPieces';
import { logSave, LogType } from '@/utils/logSave';

const { RangePicker } = DatePicker;

const FormItem = Form.Item;

@connect(({ anomalyOrder, loading }) => ({
  anomalyOrder,
  outsideAndWareExportLoading:
    loading.effects['anomalyOrder/outsideTheWarehousePortionDownload'] ||
    loading.effects['anomalyOrder/outsideTheWarehouseDownload'],
  wareLoading: loading.effects['anomalyOrder/outsideTheWarehouseSelectPage'],
  uploadLoading: loading.effects['anomalyOrder/importOverseaBatch'],
}))
@Form.create()
class ProblemPiece extends Component {
  constructor(props) {
    super(props);
    this.state = {
      shipperInitOpt: [],
      shippers: [],
      validateStatus: 'success',
      abnormalTypeList: [],
      untreatedCount: 0,
      completeCount: 0,
      selectOverseas: '1',
      overDataList: [],
      selectedRowOver: [],
      overPageIndex: 1,
      pageSize: 10,
      fileList: [],
    };
  }

  wareBatchModal = React.createRef(); // {formatMessage({id: '海外重派处理'})}
  wareDetailModal = React.createRef(); // {formatMessage({id: '海外重派详情'})}

  componentDidMount() {
    this.getShippers();
    this.getAbnormalTypeList();
  }

  getShippers = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'overseas/getOverseaAccounts',
      payload: '0',
      callback: response => {
        if (response.success) {
          let zhs = new Array();
          let shippers = response.data;
          for (let i = 0; i < shippers.length; i++) {
            zhs.push(shippers[i].accountCode);
          }
          this.setState(
            {
              shippers: response.data,
              shipperInitOpt: zhs,
            },
            this.getOverseasList
          );
        }
      },
    });
  };

  getAbnormalTypeList = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'anomalyOrder/outsideTheWarehouseGetAbnormalType',
      callback: response => {
        if (response.success) {
          this.setState({
            abnormalTypeList: response.data,
          });
        }
      },
    });
  };

  onChangeTagSelect = v => {
    this.setState({
      shipperInitOpt: v,
    });
    if (v.length >= 1) {
      this.setState({
        validateStatus: 'success',
      });
      // this.getList(v)
    } else {
      this.setState({
        validateStatus: 'error',
      });
    }
  };

  handleTabChange = v => {
    this.setState(
      {
        selectOverseas: v,
        overPageIndex: 1,
      },
      this.getOverseasList
    );
  };

  detailOver = record => {
    this.wareDetailModal.current.initalFun(record);
    // sessionStorage.setItem('over', JSON.stringify(record));
    // router.push(`/express/anomalyOrder/abnormalOverDetail?data=${'over'}`);
  };

  batchSingleOver = record => {
    this.wareBatchModal.current.initalFun(record);
    // sessionStorage.setItem('overBatch', JSON.stringify(record));
    // router.push(`/express/anomalyOrder/abnormalBatchOver?data=${'overBatch'}`);
  };

  getOverseasList = () => {
    const { dispatch, form } = this.props;
    const { selectOverseas, validateStatus, pageSize, overPageIndex } = this.state;
    let { createOverseas, exceptionTypeId3, shippers, waybillNumbers3 } = form.getFieldsValue();
    let params = {
      pageIndex: overPageIndex,
      pageSize,
      customerCode: shippers.toString(),
      createTime:
        createOverseas?.length > 0
          ? `${createOverseas[0].format('YYYY-MM-DD')} 00:00:00`
          : undefined,
      endTime:
        createOverseas?.length > 0
          ? `${createOverseas[1].format('YYYY-MM-DD')} 23:59:59`
          : undefined,
      abnormalTypeName:
        typeof exceptionTypeId3 == 'string' ? exceptionTypeId3 : exceptionTypeId3?.join(','),
      status: selectOverseas === '0' ? undefined : selectOverseas === '1' ? 0 : 1, // 0 {formatMessage({id: '全部等于不穿'})}  1 {formatMessage({id: '待处理等于'})} 0  2 {formatMessage({id: '已处理等于'})}1
      overseas: '1',
      export: 0,
      businessType: 2,
      waybillNumbers:
        waybillNumbers3 !== undefined && waybillNumbers3 !== ''
          ? this.textWaybill(waybillNumbers3, 50)?.join(',')
          : undefined,
    };
    if (validateStatus == 'success') {
      dispatch({
        type: 'anomalyOrder/outsideTheWarehouseSelectPage',
        payload: params,
        callback: result => {
          if (result.success) {
            this.setState({
              overDataList: result.data.outsideWarehouseList,
              completeCount: +result.data.endCountInteger,
              untreatedCount: +result.data.pendingCountInteger,
              overTotalNum:
                selectOverseas === '0'
                  ? +result.data.totalCountInteger
                  : selectOverseas === '1'
                  ? +result.data.pendingCountInteger
                  : +result.data.endCountInteger,
            });
          } else {
            this.setState({
              overDataList: [],
              completeCount: 0,
              untreatedCount: 0,
              overTotalNum: 0,
            });
          }
        },
      });
    } else {
      message.error(formatMessage({ id: '请选择制单账号' }));
    }
  };

  // {formatMessage({id: '重置按钮'})}
  resetFormData = () => {
    const { form } = this.props;
    // {formatMessage({id: '海外重派'})}
    form.setFieldsValue({
      createOverseas: [],
      exceptionTypeId3: undefined,
      waybillNumbers3: undefined,
    });
  };

  textWaybill = (value, number = 500) => {
    // || value === ''
    if (value == '') return undefined;
    if (value === undefined) {
      message.error(
        `${formatMessage({ id: '请输入要搜索的运单编号' })}，${formatMessage({
          id: '最多输入',
        })}50${formatMessage({ id: '个运单号' })}，${formatMessage({
          id: '多单号请以逗号',
        })}、${formatMessage({ id: '空格或回车隔开' })}`,
        2
      );
      return false;
    }
    value = value.replace(/(^\s*)|(\s*$)/g, '');
    value = value.replace(/[\s+|\t+|,]/g, '，');
    const reg = /[`~!@#$%^&*()_\-+=<>?:"{}|.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'。、]/im;
    const lowercase = new RegExp('[a-z]+', 'g');
    const chineseReg = /^[^\u4E00-\u9FA5]+$/g;
    if (!chineseReg.test(value)) {
      message.error(formatMessage({ id: '运单号不能输入汉字' }));
      return false;
    }
    if (lowercase.test(value)) {
      message.error(formatMessage({ id: '运单号不能输入小写字母' }));
      return false;
    }
    if (reg.test(value)) {
      message.error(formatMessage({ id: '运单号不能输入特殊字符' }));
      return false;
    }
    const waybillarray = value.split('，');
    let tempArray = [];
    for (let i = 0; i < waybillarray.length; i++) {
      if (waybillarray[i].length !== 0) {
        tempArray.push(waybillarray[i]);
      }
    }
    if (tempArray.length > number) {
      message.error(
        `${formatMessage({ id: '运单号搜索最多输入' })}${number}${formatMessage({
          id: '个运单号',
        })}，${formatMessage({ id: '多单号请以逗号' })}、${formatMessage({ id: '空格或回车隔开' })}`
      );
      return false;
    }
    if (this.state.shippers.length === 0) {
      message.error(
        `${formatMessage({ id: '暂无制单账号' })}，${formatMessage({ id: '请稍后重试' })}`
      );
      return false;
    }

    return tempArray;
  };

  handleOverExport = () => {
    const { form, dispatch } = this.props;
    const { selectOverseas, validateStatus, pageSize, overPageIndex, selectedRowOver } = this.state;
    if (validateStatus != 'success') {
      message.error(formatMessage({ id: '请选择制单账号' }));
      return;
    }
    let { createOverseas, exceptionTypeId3, shippers, waybillNumbers3 } = form.getFieldsValue();
    let params = {
      pageIndex: overPageIndex,
      pageSize,
      customerCode: shippers.toString(),
      createTime:
        createOverseas?.length > 0
          ? `${createOverseas[0].format('YYYY-MM-DD')} 00:00:00`
          : undefined,
      endTime:
        createOverseas?.length > 0
          ? `${createOverseas[1].format('YYYY-MM-DD')} 23:59:59`
          : undefined,
      abnormalTypeName: exceptionTypeId3,
      status: selectOverseas === '0' ? undefined : selectOverseas === '1' ? 0 : 1, // 0 {formatMessage({id: '全部等于不穿'})}  1 {formatMessage({id: '待处理等于'})} 0  2 {formatMessage({id: '已处理等于'})}1
      overseas: '1',
      export: 1,
      businessType: 2,
      waybillNumbers:
        waybillNumbers3 !== undefined && waybillNumbers3 !== ''
          ? this.textWaybill(waybillNumbers3, 50)?.join(',')
          : undefined,
    };
    dispatch({
      type: `anomalyOrder/outsideOverseaExportHandle`,
      payload: params,
      callback: response => {
        if (response.success) {
          message.success(response.message);
          this.dataURLtoDownload(response.data.url, response.data.fileName);
        }
      },
    });
  };

  dataURLtoDownload = (dataurl, name) => {
    let bstr = atob(dataurl), //{formatMessage({id: '解析'})} base-64 {formatMessage({id: '编码的字符串'})}
      n = bstr.length,
      u8arr = new Uint8Array(n); //{formatMessage({id: '创建初始化为'})}0{formatMessage({id: '的'})}，{formatMessage({id: '包含'})}length{formatMessage({id: '个元素的无符号整型数组'})}
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n); //{formatMessage({id: '返回字符串第一个字符的'})} Unicode {formatMessage({id: '编码'})}
    }
    let blob = new Blob([u8arr]); //{formatMessage({id: '转化成'})}blob
    let url = URL.createObjectURL(blob); //{formatMessage({id: '这个新的'})}URL {formatMessage({id: '对象表示指定的'})} File {formatMessage({id: '对象或'})} Blob {formatMessage({id: '对象'})}
    let a = document.createElement('a'); //{formatMessage({id: '创建一个'})}a{formatMessage({id: '标签'})}
    a.href = url;
    a.download = name;
    a.click();
    URL.revokeObjectURL(a.href); //{formatMessage({id: '释放之前创建的'})}url{formatMessage({id: '对象'})}
  };

  batchHandle = () => {
    const { selectedRowOver } = this.state;
    if (selectedRowOver.length < 1) {
      message.error('请勾选择至少一条数据');
      return;
    }
    if (selectedRowOver.length > 30) {
      message.error('批量处理一次最多支持30条数据');
    }
    this.setState({ selectedRowKeys: [], selectedRowOver: [] });
    this.batchSingleOver(selectedRowOver);
  };

  uploadFile = file => {
    if ('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' !== file.type) {
      message.error(`文件格式不正确,请根据模板上传正确文件`);
      return;
    }
    const { dispatch } = this.props;
    const formData = new FormData();
    formData.append('file', file);
    dispatch({
      type: 'anomalyOrder/importOverseaBatch',
      payload: formData,
      callback: result => {
        if (result.success) {
          message.success('处理成功!');
          this.getOverseasList();
        }
      },
    });
  };

  render() {
    const abnormalList = [
      {
        id: 3,
        name: formatMessage({ id: '海外重派' }),
      },
    ];
    const actionsTextMap = {
      selectAllText: formatMessage({ id: '全部' }),
    };

    const {
      shippers,
      shipperInitOpt,
      validateStatus,
      abnormalTypeList,
      untreatedCount,
      completeCount,
      selectOverseas,
      overDataList,
      selectedRowKeys,
      selectedRowOver,
      overPageIndex,
      pageSize,
      overTotalNum,
    } = this.state;

    const {
      form: { getFieldDecorator, setFieldsValue, getFieldsValue },
      outsideAndWareExportLoading,
      wareLoading,
      uploadLoading,
    } = this.props;

    const tabLists = [
      { key: '1', tab: `${formatMessage({ id: '待处理' })} (${untreatedCount})` },
      { key: '2', tab: `${formatMessage({ id: '已处理' })} (${completeCount})` },
      {
        key: '0',
        tab: `${formatMessage({ id: '全部' })} (${completeCount + untreatedCount})`,
      },
    ];

    const rowOverSelection = {
      selectedRowKeys,
      onChange: (selectedRowKeys, selectedRows) => {
        this.setState({ selectedRowKeys, selectedRowOver: selectedRows });
      },
    };

    const filterTabButton = () => {
      return (
        <>
          <Button
            type="primary"
            style={{ marginLeft: '10px' }}
            onClick={this.batchHandle}
            loading={outsideAndWareExportLoading}
          >
            {formatMessage({ id: '批量处理' })}
          </Button>
          <Button
            type="primary"
            style={{ marginLeft: '10px' }}
            onClick={this.handleOverExport}
            loading={outsideAndWareExportLoading}
          >
            {formatMessage({ id: '导出' })}
          </Button>
          <Upload
            showUploadList={false}
            beforeUpload={this.uploadFile}
            customRequest={({ file }) => {}}
            accept=".xlsx,.xls"
          >
            <Button type="primary" style={{ marginLeft: '10px' }} loading={uploadLoading}>
              导入处理
            </Button>
          </Upload>
        </>
      );
    };

    // {formatMessage({id: '海外重派'})}
    const overPageProps = {
      pageSize: pageSize,
      showSizeChanger: true,
      pageSizeOptions: ['10', '20', '50', '100', '500'],
      current: overPageIndex,
      total: overTotalNum,

      showTotal: () => {
        return `${formatMessage({ id: '共' })}- ${overTotalNum} -${formatMessage({
          id: '条记录',
        })}`;
      },
      onChange: (page, pageSize) => {
        this.setState(
          { overPageIndex: page, pageSize, selectedRowKeys: [], selectedRowOver: [] },
          this.getOverseasList
        );
      },
    };

    const columns = [
      {
        title: formatMessage({ id: '制单账号' }),
        dataIndex: 'customerCode',
        width: 80,
      },
      {
        title: formatMessage({ id: '运单号' }),
        dataIndex: 'waybillNumber',
        // render: (text, record) => <a onClick={() => this.detailWare(record)}>{text}</a>,
        width: 100,
      },
      {
        title: formatMessage({ id: '订单号' }),
        dataIndex: 'orderCode',
        width: 100,
      },
      {
        title: formatMessage({ id: '新转单号' }),
        dataIndex: 'newNumber',
        width: 100,
        filterType: selectOverseas == '1' ? true : undefined,
      },
      {
        title: formatMessage({ id: '目的地' }),
        dataIndex: 'regionCh',
        width: 80,
      },
      {
        title: formatMessage({ id: '产品名称' }),
        dataIndex: 'productName',
        width: 100,
      },
      {
        title: formatMessage({ id: '异常原因' }),
        dataIndex: 'abnormalType',
        width: 100,
      },
      {
        title: '异常详情',
        dataIndex: 'abnormalityDetails',
        width: 100,
      },
      {
        title: formatMessage({ id: '处理方式' }),
        dataIndex: 'handleType',
        width: 140,
        filterType: selectOverseas == '1' ? true : undefined,
      },
      {
        title: formatMessage({ id: '创建时间' }),
        dataIndex: 'createTime',
        width: 140,
      },
      {
        title: formatMessage({ id: '截止处理时间' }),
        dataIndex: 'deadline',
        width: 140,
      },
      {
        title: '获取重派单号时间',
        dataIndex: 'newNumberTime',
        width: 140,
        filterType: selectOverseas == '2' ? true : undefined,
      },
      {
        title: formatMessage({ id: '操作' }),
        dataIndex: 'wldo',
        fixed: 'right',
        width: '100px',
        render: (text, record) => {
          return record.status == 0 ? (
            <span>
              <Button type="link" onClick={() => this.batchSingleOver([record])}>
                {formatMessage({ id: '处理' })}
              </Button>
            </span>
          ) : (
            <a onClick={() => this.detailOver(record)}>{formatMessage({ id: '详情' })}</a>
          );
        },
      },
    ];

    return (
      <div>
        <Fragment>
          <Card
            bordered={false}
            className="listShipper"
            bodyStyle={{
              padding: '24px 24px 0px 24px',
            }}
          >
            <Form layout="horizontal">
              <StandardFormRow title={formatMessage({ id: '异常类型' })} block>
                <FormItem style={{ marginRight: 70, marginBottom: '0px' }}>
                  {getFieldDecorator('exceptionType', { initialValue: [3] })(
                    <SingleTagSelect
                      hideCheckAll
                      className="open"
                      // onChange={value => {
                      //   this.onChangeTagTypeSelect(value);
                      // }}
                      style={{ maxHeight: '100%' }}
                    >
                      {abnormalList &&
                        abnormalList.map((element, index) => (
                          <SingleTagSelect.Option key={index} value={element.id}>
                            <span style={{ fontWeight: '900' }}>{element.name}</span>
                          </SingleTagSelect.Option>
                        ))}
                    </SingleTagSelect>
                  )}
                </FormItem>
              </StandardFormRow>
              <StandardFormRow title={formatMessage({ id: '业务账号' })} block>
                <FormItem
                  style={{ marginRight: 70, marginBottom: '0px' }}
                  validateStatus={validateStatus}
                  help={
                    validateStatus == 'error' ? (
                      <div style={{ textAlign: 'left' }}>
                        {formatMessage({ id: '请至少选择一个业务账号' })}
                      </div>
                    ) : null
                  }
                >
                  {getFieldDecorator('shippers', { initialValue: shipperInitOpt })(
                    <TagSelect
                      actionsText={actionsTextMap}
                      className="open"
                      onChange={this.onChangeTagSelect}
                      style={{ maxHeight: '100%' }}
                    >
                      {shippers &&
                        shippers.map((element, index) => (
                          <TagSelect.Option key={index} value={element.accountCode}>
                            {element.accountCode}
                          </TagSelect.Option>
                        ))}
                    </TagSelect>
                  )}
                </FormItem>
              </StandardFormRow>
              <div
                style={{
                  marginLeft: '24px',
                }}
              >
                <Row>
                  <Col span={6} xl={6} xxl={6}>
                    <FormItem
                      label={formatMessage({ id: '运单号' })}
                      style={{ marginBottom: '0px' }}
                    >
                      {getFieldDecorator(
                        'waybillNumbers3',
                        {}
                      )(
                        <TextArea
                          style={{ width: 300, verticalAlign: 'top' }}
                          placeholder={`${formatMessage({
                            id: '请输入运单编号',
                          })}，${formatMessage({ id: '最多输入' })}50${formatMessage({
                            id: '个运单号',
                          })}，${formatMessage({ id: '多单号请以逗号' })}、${formatMessage({
                            id: '空格或回车隔开',
                          })}`}
                          rows={4}
                        ></TextArea>
                      )}
                    </FormItem>
                  </Col>
                  <Col span={6} xl={6} xxl={6}>
                    <FormItem label={formatMessage({ id: '创建时间' })}>
                      {getFieldDecorator('createOverseas', {})(<RangePicker format="YYYY-MM-DD" />)}
                    </FormItem>
                  </Col>
                  <Col span={6} xl={6} xxl={6}>
                    <FormItem label={formatMessage({ id: '异常原因' })}>
                      {getFieldDecorator(
                        'exceptionTypeId3',
                        {}
                      )(
                        <Select
                          style={{ width: '200px' }}
                          allowClear
                          showSearch
                          mode="multiple"
                          maxTagCount={3}
                          onSelect={value => {
                            const { exceptionTypeId3 } = getFieldsValue(['exceptionTypeId3']);
                            if (value === 'all') {
                              setFieldsValue({
                                exceptionTypeId3: ['all'].concat(
                                  abnormalTypeList.map(item => item)
                                ),
                              });
                            } else if (exceptionTypeId3?.length === abnormalTypeList?.length) {
                              setFieldsValue({
                                exceptionTypeId3: ['all'].concat(exceptionTypeId3),
                              });
                            }
                          }}
                          onDeselect={value => {
                            const { exceptionTypeId3 } = getFieldsValue(['exceptionTypeId3']);
                            if (value === 'all') {
                              setFieldsValue({
                                exceptionTypeId3: [],
                              });
                            } else if (
                              exceptionTypeId3?.includes('all') &&
                              exceptionTypeId3?.filter(item => item !== 'all')?.length <
                                abnormalTypeList?.length
                            ) {
                              setFieldsValue({
                                exceptionTypeId3: exceptionTypeId3?.filter(item => item !== 'all'),
                              });
                            }
                          }}
                        >
                          <Select.Option value="all">全部</Select.Option>
                          {abnormalTypeList.map((item, index) => {
                            return (
                              <Select.Option key={index} value={item}>
                                {item}
                              </Select.Option>
                            );
                          })}
                        </Select>
                      )}
                    </FormItem>
                  </Col>
                  <Col span={6} xl={6} xxl={6}>
                    <div style={{ display: 'inline-block', lineHeight: '40px' }}>
                      <Button
                        onClick={() => {
                          this.getOverseasList();
                        }}
                        type="primary"
                      >
                        {formatMessage({ id: '查询' })}
                      </Button>
                      <Button
                        onClick={() => {
                          this.resetFormData();
                        }}
                        style={{ marginLeft: '10px' }}
                      >
                        {formatMessage({ id: '重置' })}
                      </Button>
                    </div>
                  </Col>
                </Row>
                <div style={{ color: '#ff6161', padding: '10px 0px' }}>
                  {formatMessage({ id: '请及时处理工单' })},{formatMessage({ id: '若在' })}【
                  {formatMessage({ id: '截止处理时间' })}】
                  {formatMessage({ id: '前未提供处理方式' })},
                  {formatMessage({ id: '则默认做销毁处理' })}
                </div>
              </div>
            </Form>
          </Card>
          <Card
            style={{ marginTop: 24 }}
            bordered={false}
            className="listCard"
            tabList={tabLists}
            activeTabKey={selectOverseas}
            tabProps={{
              size: 'small',
              style: {
                marginTop: '10px',
              },
            }}
            onTabChange={this.handleTabChange}
            tabBarExtraContent={filterTabButton()}
          >
            <ProTableList
              size="middle"
              columns={columns}
              bordered
              rowSelection={rowOverSelection}
              rowKey={(record, index) => index}
              dataSource={overDataList}
              pagination={overPageProps}
              scroll={{ x: 1500 }}
              search={false}
              toolBarRender={false}
              loading={wareLoading}
            />
          </Card>
          <AbnormalOverDetail modalRef={this.wareDetailModal} />
          <AbnormalBatchOver
            modalRef={this.wareBatchModal}
            onCancel={() => this.handleTabChange(selectOverseas)}
          />
        </Fragment>
      </div>
    );
  }
}

export default ProblemPiece;
