import '@/pages/index.less';
import { currencyToPng } from '@/utils/utils';
import { ProCard, ProColumnType, ProTable } from '@ant-design/pro-components';
import { useUpdateEffect } from 'ahooks';
import { Button, Space, TablePaginationConfig, Tabs } from 'antd';
import { useState, useRef } from 'react';
import React from 'react';
import { PlusOutlined } from '@ant-design/icons';
import AddOrder from './components/AddOrder';

interface IProps {
  pagination: TablePaginationConfig;
  data: any[];
  tabActiveKey: string;
  setTabActiveKey: (key: string) => void;
  resetPageSize: () => void;
  dispatch: any;
  [key: string]: any;
}

const TablesComponents = ({
  pagination,
  data,
  tabActiveKey,
  setTabActiveKey,
  resetPageSize,
  dispatch,
  accountData,
  getList,
  tableLoading,
  exportData,
  exportLoading,
  addFunc,
}: IProps) => {
  const addOrderModalRef = useRef<{ open: () => void }>();
  const [dataSource, setDataSource] = useState<Array<any>>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  useUpdateEffect(() => {
    setDataSource(data);
  }, [data]);

  const columns: ProColumnType<any>[] = [
    {
      title: '业务账号',
      dataIndex: 'accountCode',
    },
    {
      title: '订单号',
      dataIndex: 'orderNumber',
    },
    {
      title: '运单号',
      dataIndex: 'waybillNumber',
    },

    {
      title: '索赔原因',
      dataIndex: 'note',
    },
    {
      title: '拒绝原因',
      dataIndex: 'reason',
      hideInTable: tabActiveKey !== '3',
    },

    {
      title: '状态',
      dataIndex: 'stateName',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
    },
    // {
    //   title: '操作',
    //   dataIndex: 'operation',
    //   render: (text, record) => {
    //     return (
    //       <>
    //         <Space>
    //           <img
    //             src={currencyToPng(record.currencyName)}
    //             style={{ width: '14px', height: '12px' }}
    //           />
    //           {`${record?.currencyName}(${record?.currencyCode})`}
    //         </Space>
    //       </>
    //     );
    //   },
    // },
  ];

  const tabItem = [
    {
      key: 'all',
      label: '全部',
    },
    {
      key: '1',
      label: '待处理',
      // children: 'Content of Tab Pane 1',
    },
    {
      key: '2',
      label: '已完成',
      // children: 'Content of Tab Pane 2',
    },
    {
      key: '3',
      label: '已拒绝',
      // children: 'Content of Tab Pane 3',
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(selectedRowKeys);
    },
  };

  const tableAlertRender = ({
    selectedRowKeys,
    selectedRows,
    onCleanSelected,
  }: {
    selectedRowKeys: React.Key[];
    selectedRows: typeof data | Array<any>;
    onCleanSelected: () => void;
  }) => {
    // console.log(selectedRowKeys, selectedRows);
    return (
      <Space size={24}>
        <span>已选 {selectedRowKeys.length} 项</span>
        <span>{`容器数量: ${selectedRows.reduce(
          (pre, item) => pre + item.containers,
          0
        )} 个`}</span>
        <span>{`调用量: ${selectedRows.reduce((pre, item) => pre + item.callNumber, 0)} 次`}</span>
        <a className="mt-2" onClick={onCleanSelected}>
          清空
        </a>
      </Space>
    );
  };

  const handleOptions = type => {
    // console.log(selectedRowKeys);
    if (type === 'add') {
      addOrderModalRef?.current?.open();
    } else {
      // 取消
    }
  };

  return (
    <ProCard ghost gutter={[0, 20]}>
      <ProCard
        bordered
        title={
          <Tabs
            className="ant-tab-no-bottom-border"
            activeKey={tabActiveKey}
            items={tabItem}
            onChange={key => {
              setTabActiveKey(key);
              resetPageSize();
            }}
          />
        }
        extra={
          <Space>
            {/* {tabActiveKey === '1' && <Button onClick={() => handleOptions('cancel')}>取消</Button>} */}
            <Button onClick={() => handleOptions('add')} type="primary" icon={<PlusOutlined />}>
              新建
            </Button>
            <Button onClick={exportData} loading={exportLoading}>
              导出
            </Button>
            <Button onClick={() => addFunc()}>批量导入</Button>
          </Space>
        }
      >
        <ProTable
          search={false}
          rowSelection={false}
          toolBarRender={false}
          dataSource={dataSource}
          columns={columns}
          pagination={pagination}
          loading={tableLoading}
          // tableAlertRender={tableAlertRender}
          // tableAlertOptionRender={() => {
          //   return (
          //     <Space size={16}>
          //       <a onClick={handleOptions}>批量取消</a>
          //     </Space>
          //   );
          // }}
        />
      </ProCard>
      <AddOrder
        accountData={accountData}
        dispatch={dispatch}
        modalRef={addOrderModalRef}
        onSubmit={getList}
      />
    </ProCard>
  );
};

export default TablesComponents;
