import { Modal, Row, Col, Avatar, Upload, UploadProps, message } from 'antd';
import React, { useState, useImperativeHandle } from 'react';
import IconFont from '@/components/IconFont';
import { PlusOutlined } from '@ant-design/icons';
import { downloadBase64File } from '@/utils/utils';

const { Dragger } = Upload;

const BatchClaimModal = ({ modalRef, dispatch, onSubmit, setTabActiveKey, tabActiveKey }) => {
  const [open, setOpen] = useState(false);

  useImperativeHandle(modalRef, () => ({
    open: () => {
      setOpen(true);
    },
  }));

  const reset = () => {
    setOpen(false);
  };

  const uploadProps: UploadProps = {
    name: 'file',
    action: '/csc/oversea/importClaimData',
    showUploadList: false,
    onChange(info) {
      const { status } = info.file;
      if (status !== 'uploading') {
        console.log(info.file, info.fileList);
      }
      if (status === 'done') {
        if (info.file.response.success) {
          message.success(`${info.file.name} 文件上传成功.`);
          onSubmit();
          reset();
        } else {
          message.error(info.file.response.message);
        }
      } else if (status === 'error') {
        message.error(`${info.file.name} 文件上传失败.`);
      }
    },
    onDrop(e) {
      console.log('Dropped files', e.dataTransfer.files);
    },
  };

  const clickDownload = () => {
    dispatch({
      type: 'overseas/exportClaimTemplate',
      callback: response => {
        if (response.success) {
          downloadBase64File(response?.data?.base64, response?.data?.fileName);
          message.success('导出成功');
        }
      },
    });
  };

  return (
    <Modal title="批量索赔工单申请" footer={null} open={open} onCancel={reset} width="35%">
      <Row gutter={20}>
        <Col span={12}>
          <div className="">
            <div style={{ marginBottom: '10px' }}>下载模板后填写相关信息，拖动或点击上传文件</div>
            <div className="template" style={{ display: 'flex', flexWrap: 'wrap' }}>
              <a
                href="/#"
                onClick={e => {
                  e.preventDefault();
                  clickDownload();
                }}
                className="template-list"
                style={{
                  marginRight: '10px',
                  textAlign: 'center',
                }}
              >
                <Avatar
                  size={50}
                  icon={<IconFont type="icon-Microsoft-Excel" />}
                  style={{
                    backgroundColor: '#fff',
                    verticalAlign: 'middle',
                    marginLeft: 20,
                    border: '1px solid #ccc',
                  }}
                />
                <div
                  style={{
                    color: '#52c41a',
                    marginTop: 10,
                    fontSize: 12,
                    marginLeft: 20,
                  }}
                >
                  下载模板
                </div>
              </a>
            </div>
          </div>
        </Col>
        <Col span={12}>
          <Dragger {...uploadProps}>
            <p className="ant-upload-drag-icon mb-3">
              <PlusOutlined
                style={{
                  fontSize: '20px',
                }}
              />
            </p>
            <p className="ant-upload-text">拖拽或点击上传文件</p>
            <p className="ant-upload-hint">Click or drag file to this area to upload</p>
          </Dragger>
        </Col>
      </Row>
    </Modal>
  );
};

export default BatchClaimModal;
