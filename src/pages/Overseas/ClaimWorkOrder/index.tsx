import { ProFormInstance } from '@ant-design/pro-components';
import { useMount, useUpdateEffect } from 'ahooks';
import { useRef, useState } from 'react';
import SearchComponent from './search';
import TablesComponents from './tables';
import moment from 'moment';
import React from 'react';
import { getOverseaRecordRequest, getOverseaRecordResponse } from './types';
import { connect } from 'dva';
import PageContainerComponent from '@/components/PageContainer';
import PageHeaderBreadcrumb from '@/components/PageHeaderBreadcrumb';
import { downloadBase64File, textWaybill } from '@/utils/utils';
import { message } from 'antd';
import BatchClaimModal from '@/pages/Overseas/ClaimWorkOrder/components/BatchClaimModal';

type TAccountDataInfo = USER_API.getOverseaAccountsData & {
  label?: string;
  value?: string;
};

type TAccountData = Array<TAccountDataInfo>;

const startTimeDate = moment()
  .subtract(6, 'months')
  .format('YYYY-MM-DD 00:00:00');
const endTimeDate = moment().format('YYYY-MM-DD 23:59:59');

const Index = (props: any) => {
  const { dispatch, tableLoading } = props;
  const formRef = useRef<ProFormInstance<getOverseaRecordRequest>>();
  const [pageSize, setPageSize] = useState(10);
  const [current, setCurrent] = useState(1);
  const [total, setTotal] = useState(0);
  const [dataList, setDataList] = useState<getOverseaRecordResponse['rows']>([]);
  const [tabActiveKey, setTabActiveKey] = useState('all');
  const [accountData, setAccountData] = useState<TAccountData>([]);
  const uploadModalRef = useRef<{
    open: () => void;
  }>();

  useUpdateEffect(() => {
    getList();
  }, [pageSize, current, tabActiveKey]);

  useMount(() => {
    getInit();
  });

  const getInitFunc = async () =>
    new Promise((resolve, reject) => {
      dispatch({
        type: 'overseas/getOverseaAccounts',
        payload: '0',
        callback: response => {
          if (response.success) {
            const data = response.data?.map(item => ({
              ...item,
              label: `${item?.accountCode}-${item?.currencyCode}`,
              value: item?.accountCode,
            }));
            setAccountData(data || []);
            resolve(data || []);
          } else {
            reject([]);
          }
        },
      });
    });

  const getInit = async () => {
    Promise.all([getInitFunc()]).then(res => {
      formRef.current?.setFieldsValue({
        accountCode: res[0]?.[0]?.accountCode,
        dateTime: [startTimeDate, endTimeDate],
      });
      getList();
    });
  };

  const getList = async () => {
    try {
      const values = await formRef.current?.validateFields();
      const params: Partial<typeof values> = {
        ...values,
        pageSize,
        current,
        waybillNumbers: values?.waybillNumbers ? textWaybill(values?.waybillNumbers) : undefined,
        state: tabActiveKey === 'all' ? undefined : tabActiveKey,
        startTime:
          typeof values?.dateTime?.[0] === 'string'
            ? values?.dateTime?.[0]
            : values?.dateTime?.[0]?.format('YYYY-MM-DD 00:00:00') ?? undefined,
        endTime:
          typeof values?.dateTime?.[1] === 'string'
            ? values?.dateTime?.[1]
            : values?.dateTime?.[1]?.format('YYYY-MM-DD 23:59:59') ?? undefined,
      };
      if (params.waybillNumbers != undefined) {
        // @ts-ignore
        params.waybillNumbers = params.waybillNumbers.join(',');
      }
      dispatch({
        type: 'overseas/getClaimDamageList',
        payload: params,
        callback: response => {
          if (response.success) {
            setDataList(response.data?.list ?? []);
            setTotal(response.data?.total ?? 0);
          }
        },
      });
    } catch (error) {
      console.error(error);
    }
  };

  const exportData = async () => {
    try {
      const values = await formRef.current?.validateFields();
      const params: Partial<typeof values> = {
        ...values,
        pageSize,
        current,
        waybillNumbers: values?.waybillNumbers ? textWaybill(values?.waybillNumbers) : undefined,
        state: tabActiveKey === 'all' ? undefined : tabActiveKey,
        startTime:
          typeof values?.dateTime?.[0] === 'string'
            ? values?.dateTime?.[0]
            : values?.dateTime?.[0]?.format('YYYY-MM-DD 00:00:00') ?? undefined,
        endTime:
          typeof values?.dateTime?.[1] === 'string'
            ? values?.dateTime?.[1]
            : values?.dateTime?.[1]?.format('YYYY-MM-DD 23:59:59') ?? undefined,
      };
      if (params.waybillNumbers != undefined) {
        // @ts-ignore
        params.waybillNumbers = params.waybillNumbers.join(',');
      }
      dispatch({
        type: 'overseas/exportClaimOrder',
        payload: params,
        callback: response => {
          if (response.success) {
            downloadBase64File(response?.data?.base64, response?.data?.fileName);
            message.success('导出成功');
          }
        },
      });
    } catch (error) {
      console.error(error);
    }
  };

  const resetPageSize = () => {
    if (current !== 1 || pageSize !== 10) {
      setCurrent(1);
      setPageSize(10);
    } else {
      getList();
    }
  };

  const pagination = {
    pageSize,
    current,
    total,
    showSizeChanger: true,
    pageSizeOptions: ['5', '10', '20', '50', '100'],
    onChange: (page: number, pageSize: number) => {
      setCurrent(page);
      setPageSize(pageSize);
    },
    showTotal: () => {
      return `共- ${total} -条记录`;
    },
  };

  const handleSearch = (values: getOverseaRecordRequest) => {
    resetPageSize();
  };

  return (
    <PageContainerComponent
      {...props}
      header={{
        title: null,
        breadcrumb: {},
        breadcrumbRender: props => <PageHeaderBreadcrumb {...props} />,
      }}
    >
      <SearchComponent
        formRef={formRef}
        {...props}
        accountData={accountData}
        onSearch={handleSearch}
        buttonLoading={tableLoading}
        dateTime={[startTimeDate, endTimeDate]}
      />
      <TablesComponents
        {...props}
        tabActiveKey={tabActiveKey}
        setTabActiveKey={setTabActiveKey}
        pagination={pagination}
        data={dataList}
        accountData={accountData}
        resetPageSize={resetPageSize}
        getList={getList}
        exportData={exportData}
        addFunc={() => {
          uploadModalRef.current?.open();
        }}
      />
      <BatchClaimModal
        modalRef={uploadModalRef}
        {...props}
        onSubmit={() => {
          if (tabActiveKey !== '1' || current !== 1 || pageSize !== 10) {
            setTabActiveKey('all');
            resetPageSize();
          } else {
            getList();
          }
        }}
      />
    </PageContainerComponent>
  );
};

export default connect(({ loading }) => ({
  tableLoading: loading.effects['overseas/getClaimDamageList'],
  exportLoading: loading.effects['overseas/exportClaimOrder'],
}))(Index);
