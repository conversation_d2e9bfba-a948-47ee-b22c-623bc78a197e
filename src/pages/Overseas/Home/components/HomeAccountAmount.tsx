import React, { useState } from 'react';
import { ProCard, ProColumnType, ProTable } from '@ant-design/pro-components';
import { useMount } from 'ahooks';
import { Space } from 'antd';
import { currencyToPng } from '@/utils/utils';

/**
 * @returns 账户余额组件
 */
const HomeAccountAmount = ({ dispatch, getOverseaAccountsLoading }) => {
  const [dataSource, setDataSource] = useState<USER_API.getOverseaAccountsData[]>([]);

  useMount(() => {
    initialFunc();
  });

  const initialFunc = async () => {
    // run({ type: '1' });
    dispatch({
      type: 'overseas/getOverseaAccounts',
      payload: '1',
      callback: (response: API.Result<Array<USER_API.getOverseaAccountsData>>) => {
        if (response.success) {
          setDataSource(response.data);
        }
      },
    });
  };

  const columns: ProColumnType<any>[] = [
    {
      title: '业务账号',
      dataIndex: 'accountCode',
    },
    {
      title: '币种',
      dataIndex: 'currencyName',
      render: (text, record) => {
        return (
          <>
            <Space>
              {/* <img
                src={currencyToPng(record.currencyName)}
                style={{ width: '14px', height: '12px' }}
              /> */}
              {`${record?.currencyName}(${record?.currencyCode})`}
            </Space>
          </>
        );
      },
    },
    {
      title: '账户余额',
      dataIndex: 'balance',
    },
    {
      title: '授信额度',
      dataIndex: 'exemptMoney',
    },
    {
      title: '可用额度',
      dataIndex: 'availableBalance',
    },
    {
      title: '账号状态',
      dataIndex: 'ejfStatusName',
      render: text => <span style={{ color: text === '冻结' ? 'red' : '' }}>{text}</span>,
    },
  ];

  return (
    <ProCard ghost gutter={[20, 40]}>
      <ProCard
        bordered
        bodyStyle={{
          height: '25rem',
        }}
      >
        <ProTable
          rowKey={(_, index) => index + ''}
          loading={getOverseaAccountsLoading}
          search={false}
          rowSelection={false}
          toolBarRender={false}
          dataSource={dataSource}
          columns={columns}
          pagination={false}
        />
      </ProCard>
    </ProCard>
  );
};

export default HomeAccountAmount;
