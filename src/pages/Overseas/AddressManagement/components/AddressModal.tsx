import React, { useState, useEffect, forwardRef, useImperativeHandle, useRef } from 'react';
import { Modal, message, Button, Row, Space, Col } from 'antd';
import ProForm, {
  ProFormText,
  ProFormSelect,
  ProFormInstance,
  ProFormGroup,
} from '@ant-design/pro-form';
import PageLoading from '@/components/PageLoading';

// 假设 AddressItem 类型在父组件或全局类型中定义
// 如果没有，需要在此处定义或导入
// 定义 Ref 暴露的方法类型
export interface AddressModalRef {
  open: (record?: USER_API.AddressItem | null) => void;
}

// 定义组件的 Props 接口 - 移除了 visible, onCancel, initialValues
// Define AddressItem type if not globally available
// Example structure, adjust based on actual USER_API.AddressItem
interface AddressItem extends Partial<USER_API.AddressItem> {
  provinceCode?: string;
  provinceName?: string;
  cityCode?: string;
  cityName?: string;
  areaCode?: string;
  areaName?: string;
  address?: string;
  contactName?: string;
  contactPhone?: string;
  orderAccount?: string; // 制单账号
  warehouse?: string; // 交货仓代码
  warehouseName?: string; // 交货仓名称
  returnMethod?: string; // 退件方式
  // Ensure other necessary fields for submission are included if any
}

// Define component props
interface AddressModalProps {
  onSuccess: () => void; // Success callback
  dispatch: Function; // Receive dispatch
  // Pass other necessary props if BindAddress requires them, e.g., URLs
  checkAddressUrl?: string;
  getCoordsUrl?: string;
  mapListUrl?: string;
  mapTagUrl?: string;
  accountData: any[];
  modalLoading: boolean;
}

const AddressModalComponent = forwardRef<AddressModalRef, AddressModalProps>(
  ({ onSuccess, dispatch, accountData, modalLoading }, ref) => {
    const formRef = useRef<ProFormInstance<AddressItem>>();
    const [submitting, setSubmitting] = useState(false);
    const [visible, setVisible] = useState(false); // Internal state for visibility
    const [currentRecord, setCurrentRecord] = useState<AddressItem | null>(null); // Internal state for current record
    const [loading, setLoading] = useState(false);
    const [formKey, setFormKey] = useState(0); // State for forcing form remount
    const [paramsMap, setParamsMap] = useState<any>();
    const [warehouseList, setWarehouseList] = useState<{ label: string; value: string }[]>([]); // Add warehouse state

    // Use useImperativeHandle to expose the open method
    useImperativeHandle(ref, () => ({
      open: record => {
        setLoading(true);
        setCurrentRecord(record || null); // 设置当前记录
        setVisible(true); // 打开弹窗
      },
    }));

    // Internal close handler
    const handleClose = () => {
      setVisible(false);
      setLoading(false);
      // formRef.current?.resetFields(); // Resetting handled by key change/initialValues
      setCurrentRecord(null); // Clear current record
      setWarehouseList([]); // Reset warehouse list on close
      setFormKey(prevKey => prevKey + 1); // Change key on close to ensure clean state next time
      // Reset other states if necessary
    };
    // Fetch warehouse list and return a promise
    const getWarehouseList = () => {
      return new Promise((resolve, reject) => {
        dispatch({
          type: 'homePage/getAllWarehouseByType',
          payload: 4,
          callback: response => {
            if (response.success && Array.isArray(response.data)) {
              setWarehouseList(
                response.data.map(item => ({
                  label: item.name,
                  value: item.code,
                }))
              );
              resolve(true); // Resolve the promise on success
            } else {
              setWarehouseList([]); // Clear list on failure or empty data
              reject(new Error('Failed to fetch warehouse list')); // Reject the promise on failure
            }
          },
          // Consider adding an error callback if the dispatch itself can fail
          // errorCallback: (error) => {
          //   setWarehouseList([]);
          //   message.error('获取注入仓列表时出错');
          //   reject(error);
          // }
        });
      });
    };

    // Effect to fetch initial data and set form values when modal opens or record changes
    useEffect(() => {
      if (visible) {
        setLoading(true); // Set loading at the beginning
        // Fetch initial data using Promise.all
        Promise.all([getWarehouseList()]) // Wrap the call in Promise.all
          .then(() => {
            // Warehouse list state is now updated
            // Data is fetched, warehouseList state is updated.
            // Set loading to false and update the key to force remount with new initialValues.
            setLoading(false);
            setFormKey(prevKey => prevKey + 1); // Increment key to trigger remount
          })
          .catch(() => {
            // Error is handled within getWarehouseList or here if needed
            setLoading(false); // Ensure loading is turned off on error even if promise rejects
            // Optionally reset key or handle error state differently
          });
      }
    }, [visible, currentRecord]); // Rerun effect if visible or currentRecord changes

    // 提交处理
    const handleSubmit = async (values: AddressItem) => {
      // Use the updated AddressItem type
      setSubmitting(true);
      try {
        // 根据 currentRecord 判断是新增还是编辑

        // Prepare payload, map fields if backend expects different names
        const payload = {
          ...currentRecord, // Include existing data for update
          ...values, // Include form values
          method: currentRecord ? 'U' : 'A',
          type: '1',
          // Example mapping: if backend expects warehouseCode instead of warehouse
          // warehouseCode: values.warehouse,
          // returnType: values.returnMethod, // Example mapping
          // Remove frontend-only fields if necessary
          // warehouseName: undefined, // If only warehouse code is needed
        };

        // Adjust dispatch type and payload structure according to your actual API endpoint for overseas addresses
        // e.g., 'overseasAddress/add' or 'overseasAddress/update'
        const result = await dispatch({
          type: `overseas/${currentRecord ? 'update' : 'add'}Address`, // Replace with your actual dispatch type
          payload,
        });

        if (result?.success) {
          message.success(currentRecord ? '编辑成功' : '新增成功');
          onSuccess(); // 调用成功回调刷新列表
          handleClose(); // 成功后关闭弹窗
        }
      } catch (error) {
        console.error('保存地址时出错:', error);
        message.error('保存地址时发生错误');
      } finally {
        setSubmitting(false);
      }
    };

    return (
      <Modal
        title={currentRecord ? '编辑地址' : '新增地址'}
        open={visible}
        onCancel={handleClose}
        footer={null}
        destroyOnClose
        maskClosable={false}
        width="50%" // Match width with AddSiteModal
      >
        {loading ? (
          <PageLoading paddingTop={10} />
        ) : (
          <ProForm<AddressItem> // Specify form item type
            key={formKey} // Add key prop
            formRef={formRef} // Use formRef
            layout="horizontal"
            labelAlign="left"
            initialValues={currentRecord || {}} // Set initialValues based on currentRecord
            labelCol={{ flex: '75px' }} // Match label width
            onFinish={handleSubmit}
            submitter={{
              // Render custom submit/cancel buttons
              render: _ => (
                <Row justify="center">
                  <Space>
                    <Button className="w-32" onClick={handleClose}>
                      取消
                    </Button>
                    <Button type="primary" htmlType="submit" className="w-32" loading={submitting}>
                      提交
                    </Button>
                  </Space>
                </Row>
              ),
            }}
            // Ensure destroyOnClose works as expected for form reset
          >
            {/* 制单账号 */}
            <ProFormSelect
              name="accountCode"
              label="制单账号"
              width="sm"
              options={accountData}
              rules={[
                {
                  required: true,
                  message: '请选择制单账号',
                },
              ]}
              placeholder="请选择制单账号"
              fieldProps={{
                onChange: () => {
                  // queryList(value);
                },
              }}
            />

            {/* 注入仓选择 */}
            <ProFormSelect
              label="注入仓"
              width="sm"
              name="warehouse"
              rules={[
                {
                  required: true,
                  message: '请选择注入仓',
                },
              ]}
              options={warehouseList}
              fieldProps={{
                showSearch: true,
                onChange: (value: any, option: any) => {
                  if (formRef.current?.getFieldValue('optWay') != 1) {
                    formRef.current?.resetFields(['address']);
                  }
                  setParamsMap({
                    ...paramsMap,
                    cityCode: value,
                    cityName:
                      typeof option?.label == 'object'
                        ? option?.title?.replace('燕文', '')?.replace('虚拟仓', '')
                        : option?.label?.replace('燕文', '')?.replace('虚拟仓', ''),
                  });
                },
              }}
            />

            {/* Return Address Section */}
            <div className="flex flex-col justify-start items-start ">
              <div className="ant-col ant-form-item-label mb-3">
                <label htmlFor="upload" className="ant-form-item-required" title="退件地址及联系人">
                  退件地址
                </label>
              </div>
              <Row gutter={[8, 0]} className="w-full">
                <Col span={8}>
                  <ProFormText
                    labelCol={{
                      flex: '70px',
                    }}
                    label="省/州"
                    name="provinceName"
                    placeholder="请输入省/州"
                    rules={[
                      {
                        required: true,
                        message: '请输入省/州',
                      },
                    ]}
                  />
                </Col>
                <Col span={8}>
                  <ProFormText
                    labelCol={{
                      flex: '70px',
                    }}
                    label="城市"
                    name="cityName"
                    placeholder="请输入城市"
                    rules={[
                      {
                        required: true,
                        message: '请输入城市',
                      },
                      {
                        max: 50,
                        message: '城市名称最多50个字符',
                      },
                    ]}
                  />
                </Col>
                <Col span={8}>
                  <ProFormText
                    labelCol={{
                      flex: '70px',
                    }}
                    label="邮编"
                    name="contactZipCode"
                    placeholder="请输入邮编"
                    rules={[
                      {
                        required: true,
                        message: '请输入邮编',
                      },
                    ]}
                  />
                </Col>
                <Col span={16}>
                  <ProFormText
                    labelCol={{
                      flex: '50px',
                    }}
                    label="地址"
                    name="address"
                    placeholder="请输入详细地址"
                    rules={[
                      {
                        required: true,
                        message: '请输入详细地址',
                      },
                      {
                        max: 100,
                        message: '详细地址最多100个字符',
                      },
                      {
                        pattern: /^[\u4e00-\u9fa5a-zA-Z0-9\-\.\(\)\（\）\x20]+$/,
                        message: '包含非法字符',
                      },
                    ]}
                  />
                </Col>
              </Row>

              <Row gutter={[8, 0]} className="w-full">
                <Col span={8}>
                  <ProFormText
                    name="contactName"
                    label="联系人姓名"
                    labelCol={{
                      flex: '100px',
                    }}
                    placeholder="请输入负责人姓名"
                    rules={[
                      {
                        required: true,
                        message: '请输入退件负责人姓名',
                      },
                    ]}
                  />
                </Col>
                <Col span={8}>
                  <ProFormText
                    name="contactPhone"
                    label="联系人手机号"
                    labelCol={{
                      flex: '108px',
                    }}
                    placeholder="请输入负责人手机号"
                    rules={[
                      {
                        required: true,
                        message: '请输入退件负责人手机号',
                      },
                    ]}
                  />
                </Col>

                <Col span={8}>
                  <ProFormText
                    label="联系人邮箱"
                    labelCol={{
                      flex: '100px',
                    }}
                    name="contactEmail" // 需要确保后端字段名称对应
                    placeholder="请输入邮箱"
                    rules={[{ required: true, message: '请输入邮箱' }]}
                  />
                </Col>
              </Row>
            </div>
          </ProForm>
        )}
      </Modal>
    );
  }
);

// 使用 connect 将 dispatch 注入到 props
export default AddressModalComponent;
