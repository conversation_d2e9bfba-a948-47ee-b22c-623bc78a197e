import React, { useState, useEffect, useRef } from 'react';
import PageContainerComponent from '@/components/PageContainer';
import PageHeaderBreadcrumb from '@/components/PageHeaderBreadcrumb';
import { connect } from 'dva';
import ProTableList from '@/components/ProTable';
import { Button, Popconfirm, message, Space } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { ProColumns, ActionType } from '@ant-design/pro-components';
import AddressModal, { AddressModalRef } from './components/AddressModal'; // 导入新的 Modal 组件 和 Ref 类型

// 注意：AddressItem 接口需要包含以下 dataIndex 对应的字段
// 例如: businessAccount, warehouse, email, zipCode, updateTime

const Index = props => {
  const { dispatch, tableLoading } = props;
  const modalRef = useRef<AddressModalRef>(null); // 创建 Ref
  // 移除 modalVisible 和 currentRecord state
  // const [modalVisible, setModalVisible] = useState(false);
  // const [currentRecord, setCurrentRecord] = useState<AddressItem | null>(null);
  const [dataSource, setDataSource] = useState<USER_API.AddressItem[]>([]); // 使用本地定义的类型

  const [pagination, setPagination] = useState<Common.PaginationState>({
    // 新增 pagination state
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const actionRef = useRef<ActionType>(); // 可选：如果需要手动触发刷新等操作
  const [accountData, setAccountData] = useState<any[]>([]);
  const [businessAccount, setBusinessAccount] = useState<any[]>([]);

  const makeAccountLoad = () => {
    dispatch({
      type: 'smallBag/makeAccountLoad',
      payload: {
        accountType: 4,
        scene: 1,
      },
      callback: response => {
        if (response.success) {
          const data = response.data?.map(item => ({
            ...item,
            label: item?.accountCode,
            value: item?.accountCode,
          }));
          setAccountData(data);
        } else {
          setAccountData([]);
        }
      },
    });
  };

  const fetchAccountList = async () => {
    try {
      const result = await dispatch({
        type: 'overseas/getOverseaAccounts',
        payload: '0',
      });

      if (result?.success) {
        const normalizedData =
          result?.data?.map(item => ({
            ...item,
            label: `${item?.accountCode}`,
            value: item?.accountCode,
          })) ?? [];
        setBusinessAccount(normalizedData);
      } else {
        setBusinessAccount([]);
      }
    } catch (error) {
      setBusinessAccount([]);
    }
  };

  // --- 数据获取逻辑 ---
  const fetchAddressList = async (pageInfo?: Partial<Common.PaginationState>) => {
    const currentPage = pageInfo?.current || pagination.current;
    const currentPageSize = pageInfo?.pageSize || pagination.pageSize;
    // 这里可以添加业务账号等过滤参数
    const payload = {
      type: 1,
      currentPage: currentPage,
      pageSize: currentPageSize,
      // businessAccountId: selectedBusinessAccount, // 示例：添加业务账号过滤
    };

    try {
      // 确保这里的 API 调用返回的数据包含新 columns 定义中 dataIndex 指定的所有字段
      const result = await dispatch({
        type: 'overseas/getAddressList', // 假设 model 和 type 名称正确
        payload,
      });

      if (result?.success) {
        setDataSource(result?.data?.data || []);
        setPagination(prev => ({
          ...prev,
          current: currentPage,
          pageSize: currentPageSize,
          total: result?.data?.totalCount || 0,
        }));
      } else {
        // 处理请求失败的情况
        setDataSource([]);
        setPagination(prev => ({
          ...prev,
          current: 1, // 失败时重置回第一页
          pageSize: currentPageSize, // 保留 pageSize
          total: 0,
        }));
      }
    } catch (error) {
      console.error('获取地址列表时出错:', error);
      setDataSource([]);
      setPagination(prev => ({
        ...prev,
        current: 1,
        pageSize: 10, // 错误时重置分页
        total: 0,
      }));
    }
  };

  // --- 使用 useEffect 获取初始数据 ---
  useEffect(() => {
    makeAccountLoad();
    // fetchAccountList();
    fetchAddressList(); // 组件挂载时获取第一页数据
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // 空依赖数组确保只在挂载时执行一次

  // --- 业务账号变化时重新获取数据 ---
  // useEffect(() => {
  //   // 如果 selectedBusinessAccount 不是初始状态，则获取数据
  //   // 这可以防止在组件首次加载时触发两次 fetchAddressList (一次来自上面的 useEffect，一次来自这里)
  //   // 如果希望选择账号后立即加载，可以移除这个判断或调整逻辑
  //   if (selectedBusinessAccount !== undefined) {
  //      fetchAddressList({ current: 1 }); // 切换账号时回到第一页
  //   }
  // }, [selectedBusinessAccount]); // 依赖于业务账号状态

  // 打开编辑/新增弹窗的逻辑 (使用 ref)
  const showModal = (record?: USER_API.AddressItem | null) => {
    modalRef.current?.open(record); // 通过 ref 打开弹窗
  };

  const handleDelete = async (addressId: string) => {
    try {
      const result = await dispatch({
        type: 'overseas/deleteAddress', // 假设 model 和 type 名称正确
        payload: { addressId },
      });
      if (result?.success) {
        // 假设接口返回 success 字段
        message.success('删除成功');
        // 删除成功后刷新列表
        // 检查删除后当前页是否还有数据，如果没有，则请求前一页，除非已经是第一页
        const newTotal = pagination.total - 1;
        const newCurrent =
          dataSource.length === 1 && pagination.current > 1
            ? pagination.current - 1
            : pagination.current;

        fetchAddressList({ current: newCurrent }); // 刷新调整后的当前页
      } else {
        message.error(result?.message || '删除失败');
      }
    } catch (error) {
      console.error('删除地址时出错:', error);
    }
  };

  // --- 处理 Modal 成功回调 ---
  const handleModalSuccess = () => {
    // Modal 内部处理了消息提示和关闭，这里只需刷新列表
    // 新增通常建议回到第一页，编辑刷新当前页
    // 为了简化，这里统一刷新当前页，如果需要根据新增/编辑区分刷新页码，可以在 onSuccess 回调中传递信息
    fetchAddressList({ current: pagination.current, pageSize: pagination.pageSize });
    // 如果新增后需要强制跳回第一页，可以在这里判断 modalRef.current?.operationType === 'create' 等方式 (需要 modal 暴露更多信息)
    // 或者简单地：
    // fetchAddressList({ current: 1, pageSize: pagination.pageSize }); // 总是刷新第一页
  };

  const columns: ProColumns<USER_API.AddressItem>[] = [
    // 使用本地定义的类型
    {
      title: '序号',
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      render: (_, record, index) => (pagination.current - 1) * pagination.pageSize + index + 1, // 计算正确序号
    },
    {
      title: '业务账号',
      dataIndex: 'merchantCode',
      key: 'merchantCode',
    },
    {
      title: '注入仓',
      dataIndex: 'warehouseName',
      key: 'warehouseName',
    },
    {
      title: '联系人姓名',
      dataIndex: 'contactName',
      key: 'contactName',
    },
    {
      title: '手机号',
      dataIndex: 'contactPhone',
      key: 'contactPhone',
    },
    {
      title: '邮箱',
      dataIndex: 'contactEmail',
      key: 'contactEmail',
    },
    {
      title: '地址',
      dataIndex: 'address',
      key: 'address',
      ellipsis: true,
    },
    {
      title: '邮编',
      dataIndex: 'contactZipCode',
      key: 'contactZipCode',
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      valueType: 'dateTime',
    },
    {
      title: '操作',
      key: 'action',
      valueType: 'option',
      render: (_, record) => {
        return (
          <Space>
            <a
              key="edit"
              type="link"
              onClick={() => showModal(record)} // 调用新的 showModal
            >
              编辑
            </a>
            <Popconfirm
              key="delete"
              title="确定要删除该地址吗？"
              onConfirm={() => handleDelete(record.addressId)}
            >
              <a type="link" style={{ color: '#f00' }}>
                删除
              </a>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  // --- 处理分页变化 ---
  const handleTableChange = (page: number, pageSize?: number) => {
    // 更新 pagination state 会触发 useEffect (如果 pagination 是依赖项)
    // 或者直接调用 fetchAddressList
    fetchAddressList({ current: page, pageSize: pageSize || pagination.pageSize });
  };

  // --- 移除 handleFormFinish，逻辑已移至 AddressModal ---

  return (
    <PageContainerComponent
      header={{
        title: null,
        breadcrumb: {},
        breadcrumbRender: props => <PageHeaderBreadcrumb {...props} />,
      }}
    >
      <ProTableList // 显式指定泛型类型
        actionRef={actionRef} // 可选
        columns={columns}
        dataSource={dataSource} // --- 添加 dataSource ---
        loading={tableLoading} // --- 添加 loading ---
        rowKey="id"
        // headerTitle={
        //   <Space key="toolbar-space">
        //     <span>业务账号:</span>
        //     <Select
        //       placeholder="请选择业务账号"
        //       style={{ width: 200 }}
        //       allowClear // 允许清除选择
        //       options={accountData}
        //       // value={selectedBusinessAccount} // 绑定状态
        //       // onChange={(value) => {
        //       //   setSelectedBusinessAccount(value); // 更新状态
        //       //   // 状态更新后，上面的 useEffect 会自动触发数据获取
        //       //   // 或者直接在这里调用 fetchAddressList({ current: 1 });
        //       // }}
        //     />
        //   </Space>
        // }
        options={{
          reload: () =>
            fetchAddressList({ current: pagination.current, pageSize: pagination.pageSize }), // --- 修改刷新逻辑，刷新当前页 ---
          density: false, // 允许调整密度
          setting: false, // 允许列设置
        }}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => showModal()} // 调用新的 showModal，不传参数表示新增
          >
            新增地址
          </Button>,
        ]}
        // rowSelection={{}} // 如果不需要行选择，可以移除此项
        pagination={{
          // --- 修改 pagination ---
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showSizeChanger: true,
          showQuickJumper: true,
          onChange: handleTableChange, // --- 添加 onChange 处理分页变化 ---
          onShowSizeChange: (current, size) => handleTableChange(1, size), // 切换 pageSize 时回到第一页
          pageSizeOptions: ['10', '20', '50', '100'], // 定义可选的 pageSize
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条 / 总共 ${total} 条`, // 显示总数
        }}
        search={false} // 如果不需要搜索表单，可以设置为 false
        rowSelection={false}
      />
      {/* 使用 ref 控制的 AddressModal */}
      {/* 移除 visible, onCancel, initialValues props */}
      <AddressModal
        ref={modalRef} // 传递 ref
        // 传递成功回调
        onSuccess={handleModalSuccess}
        accountData={accountData}
        {...props}
      />
    </PageContainerComponent>
  );
};

// 移除 connect 中的映射，除非确实需要从 model 获取数据
// 如果需要 loading 状态，可以这样连接:
export default connect(({ loading }) => ({
  tableLoading: loading.effects['overseas/getAddressList'], // 将 dva model 的 loading 状态映射到 props
  modalLoading: loading.effects['overseas/getInjectWarehouseList'],
}))(Index);
// 然后在组件中使用 props.tableLoading 代替 useState 的 loading
// 但当前实现使用了 useState 管理 loading，所以保持简单 connect() 即可
