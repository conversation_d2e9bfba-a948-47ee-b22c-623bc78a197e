import React, { useState, useRef } from 'react';
import PageContainerComponent from '@/components/PageContainer';
import PageHeaderBreadcrumb from '@/components/PageHeaderBreadcrumb';
import {
  ProForm,
  ProFormText,
  ProFormDatePicker,
  ProFormSelect,
  ProFormRadio,
  EditableProTable,
  ProFormInstance,
} from '@ant-design/pro-components';
import { Row, Col, Space, Button, message, Upload } from 'antd';
import { useMount } from 'ahooks';
import { connect } from 'dva';
import { downloadFile } from '@/utils/download';
import { overseas } from '@/services/overseas';
import { router } from 'umi';

const Index = props => {
  const { dispatch, pageLoading, submitLoading } = props;
  const formRef = useRef<ProFormInstance>();
  const [editableKeys, setEditableRowKeys] = useState([]);
  const [isEdit, setIsEdit] = useState(false);
  const [showRecordCreator, setShowRecordCreator] = useState(true);
  const [creatorButtonText, setCreatorButtonText] = useState('新增');
  const [accountData, setAccountData] = useState<Array<any>>([]);
  const [loading, setLoading] = useState(false); // 新增 loading 状态
  const [uploadLoading, setUploadLoading] = useState(false);
  const [channelList, setChannelList] = useState([]);
  const [flightInformation, setFlightInformation] = useState('0');
  const [warehouseList, setWarehouseList] = useState([]);

  useMount(() => {
    initialFunc();
  });

  const initialFunc = async () => {
    try {
      Promise.all([getForecastChannelList(), getInitFunc(), getWarehouseList()]).then(
        ([channelList, accountData, warehouseList]) => {
          const detailsInfo = Array.from({ length: 5 }, () => ({
            tableKey: (Math.random() * 1000000).toFixed(0),
          }));
          setEditableRowKeys(detailsInfo?.map(item => item.tableKey));
          formRef.current?.setFieldsValue({
            detailsInfo,
            customerCode: accountData[0]?.accountCode,
            hasFlightInformation: flightInformation,
          });
        }
      );
    } catch (error) {
      console.log(error);
    }
  };

  const getInitFunc = async () =>
    new Promise((resolve, reject) => {
      dispatch({
        type: 'overseas/getOverseaAccounts',
        payload: '0',
        callback: response => {
          if (response.success) {
            const data = response.data?.map(item => ({
              ...item,
              label: `${item?.accountCode}`,
              value: item?.accountCode,
            }));
            setAccountData(data || []);
            resolve(data || []);
          } else {
            reject([]);
          }
        },
      });
    });

  const getForecastChannelList = async () => {
    return new Promise((resolve, reject) => {
      dispatch({
        type: 'overseas/getForecastChannelList',
        callback: response => {
          if (response.success) {
            setChannelList(
              response.data?.map(v => ({
                label: v?.productCnName,
                value: v?.productNumber,
              }))
            );
            resolve(response.data);
          } else {
            reject([]);
          }
        },
      });
    });
  };

  const getWarehouseList = () => {
    return new Promise((resolve, reject) => {
      dispatch({
        type: 'homePage/getAllWarehouseByType',
        payload: 4,
        callback: response => {
          if (response.success && Array.isArray(response.data)) {
            setWarehouseList(
              response.data.map(item => ({
                label: item.name,
                value: item.code,
              }))
            );
            resolve(true); // Resolve the promise on success
          } else {
            setWarehouseList([]); // Clear list on failure or empty data
            reject(new Error('Failed to fetch warehouse list')); // Reject the promise on failure
          }
        },
      });
    });
  };

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      valueType: 'index',
      width: 80,
      readonly: true,
      align: 'center',
    },
    {
      title: (
        <>
          运单号<span style={{ color: 'red' }}>*</span>
        </>
      ),
      dataIndex: 'waybillNumber',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项是必填项',
          },
        ],
      },
    },
    {
      title: (
        <>
          大包号<span style={{ color: 'red' }}>*</span>
        </>
      ),
      dataIndex: 'bagNo',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项是必填项',
          },
        ],
      },
    },
    // {
    //   title: '订单号',
    //   key: 'orderNumber',
    //   dataIndex: 'orderNumber',
    //   formItemProps: {
    //     rules: [
    //       {
    //         required: false,
    //         message: '此项是必填项',
    //       },
    //     ],
    //   },
    // },
    {
      title: '操作',
      width: '10%',
      valueType: 'option',
      render: (text, record, _, action) =>
        isEdit
          ? []
          : [
              <a
                key="delete"
                onClick={() => {
                  const tableDataSource = formRef.current?.getFieldValue('detailsInfo');
                  const data = tableDataSource.filter(item => item.tableKey !== record.tableKey);
                  formRef?.current?.setFieldsValue({
                    detailsInfo: data,
                  });
                }}
              >
                删除
              </a>,
            ],
    },
  ];

  function handleUploadChange(info: any): void {
    if (info.file.status === 'uploading') {
      setUploadLoading(true);
      return;
    }
    if (info.file.status === 'done') {
      setUploadLoading(false);
      if (info.file.response.success) {
        const data = info.file.response.data?.detailsInfo?.map((t, i) => ({
          ...t,
          tableKey: (Math.random() * 1000000).toFixed(0),
          realIndex: i,
          index: i + 1,
        }));
        let importData = info.file.response.data;
        if (importData && importData.hasFlightInformation) {
          setFlightInformation(importData && importData.hasFlightInformation);
        }
        formRef.current?.setFieldsValue({
          ...info.file.response.data,
          detailsInfo: data,
        });
        setEditableRowKeys(data.map(t => t.tableKey));
      } else {
        message.error(info.file.response.message);
      }
    }
  }

  const reset = () => {
    formRef.current?.resetFields();
    setEditableRowKeys([]);
    setFlightInformation('0');
    setShowRecordCreator(true);
    setCreatorButtonText('新增');
  };

  const handleSubmit = async values => {
    values.channelName = channelList.find(item => item.value === values.channelId)?.label;
    dispatch({
      type: 'overseas/addForecast',
      payload: values,
      callback: response => {
        if (response.success) {
          message.success(response.message);
          reset();
          router.push('/overseas/finalWarehousing/query');
        }
      },
    });
  };

  return (
    <PageContainerComponent
      loading={pageLoading}
      header={{
        title: null,
        breadcrumb: {},
        breadcrumbRender: props => <PageHeaderBreadcrumb {...props} />,
      }}
    >
      <div>
        <ProForm
          formRef={formRef}
          labelAlign="right"
          layout="horizontal"
          labelCol={{ flex: '120px' }}
          onFinish={handleSubmit}
          submitter={{
            render: () => {
              return [];
            },
          }}
        >
          {/* 基础信息 */}
          <div className="bg-white p-6 rounded-lg mb-6">
            <div className="flex items-center justify-between">
              <div
                className="text-lg font-bold"
                style={{
                  color: '#52c41a',
                }}
              >
                基础信息
              </div>
              <Space>
                <Button
                  type="primary"
                  loading={loading}
                  onClick={() => {
                    setLoading(true);
                    downloadFile({ url: overseas.getForecastTemplate })
                      .then(() => setLoading(false))
                      .catch(() => setLoading(false));
                  }}
                >
                  下载模板
                </Button>
                <Upload
                  accept=".csv,.xlsx,.xls"
                  maxCount={1}
                  action="/csc/oversea/importForecast"
                  name="file"
                  showUploadList={false}
                  onChange={handleUploadChange}
                >
                  <Button type="primary" loading={uploadLoading}>
                    上传
                  </Button>
                </Upload>
              </Space>
            </div>
            <Row gutter={20} className="pt-6">
              <Col span={24}>
                <div className="ant-radio-button-part">
                  <ProFormRadio.Group
                    name="customerCode"
                    label="业务账号"
                    radioType="button"
                    options={accountData}
                    fieldProps={{
                      buttonStyle: 'solid',
                    }}
                    rules={[{ required: true, message: '请选择制单账号' }]}
                  />
                </div>
              </Col>
              <Col span={8}>
                <ProFormText
                  rules={[{ required: true, message: '请输入送货批次标识' }]}
                  name="batchCode"
                  label="送货批次标识"
                />
              </Col>
              <Col span={8}>
                <ProFormDatePicker
                  fieldProps={{
                    style: {
                      width: '100%',
                    },
                  }}
                  rules={[{ required: true, message: '请输入预计送达时间' }]}
                  name="predictArriveDate"
                  label="预计送达时间"
                />
              </Col>
              <Col span={8}>
                <ProFormSelect
                  label="产品名称"
                  name="channelId"
                  rules={[{ required: true, message: '请选择产品名称' }]}
                  options={channelList}
                />
              </Col>
              <Col span={8}>
                <ProFormSelect
                  label="注入仓信息"
                  name="companyCode"
                  placeholder="请选择注入仓"
                  rules={[
                    {
                      required: true,
                      message: '请选择注入仓',
                    },
                  ]}
                  options={warehouseList}
                  fieldProps={{
                    showSearch: true,
                  }}
                />
              </Col>
              <Col span={8}>
                <ProFormText
                  label="运输方式"
                  name="transportWay"
                  rules={[{ required: false, message: '请输入运输方式' }]}
                />
              </Col>
              <Col span={8}>
                <ProFormRadio.Group
                  label="是否有航班信息"
                  name="hasFlightInformation"
                  options={[
                    {
                      label: '是',
                      value: '1',
                    },
                    {
                      label: '否',
                      value: '0',
                    },
                  ]}
                  fieldProps={{
                    onChange: e => {
                      const value = e.target.value;
                      setFlightInformation(value);
                    },
                  }}
                />
              </Col>
            </Row>
          </div>
          {/* 航班信息 */}
          {flightInformation === '1' && (
            <div className="bg-white p-6 rounded-lg mb-6">
              <div className="flex items-center justify-between">
                <div
                  className="text-lg font-bold"
                  style={{
                    color: '#52c41a',
                  }}
                >
                  航班信息
                </div>
              </div>
              <Row gutter={20} className="pt-6">
                <Col span={8}>
                  <ProFormText
                    rules={[{ required: false, message: '请输入主单号' }]}
                    name="mawbNo"
                    label="主单号"
                  />
                </Col>
                <Col span={8}>
                  <ProFormText
                    rules={[{ required: false, message: '请输入起始港' }]}
                    name="pol"
                    label="起始港"
                  />
                </Col>
                <Col span={8}>
                  <ProFormText
                    rules={[{ required: false, message: '请输入目的港' }]}
                    name="pod"
                    label="目的港"
                  />
                </Col>

                <Col span={8}>
                  <ProFormDatePicker
                    fieldProps={{
                      style: {
                        width: '100%',
                      },
                    }}
                    rules={[{ required: false, message: '请输入预计起飞时间' }]}
                    name="etd"
                    label="预计起飞时间"
                  />
                </Col>
                <Col span={8}>
                  <ProFormDatePicker
                    fieldProps={{
                      style: {
                        width: '100%',
                      },
                    }}
                    rules={[{ required: false, message: '请输入预计到达时间' }]}
                    name="eta"
                    label="预计到达时间"
                  />
                </Col>
                <Col span={8}>
                  <ProFormText
                    rules={[{ required: false, message: '请输入时区' }]}
                    name="timeZone"
                    label="时区"
                  />
                </Col>
              </Row>
            </div>
          )}
          {/* 批次明细 */}
          <div className="bg-white p-6 rounded-lg mb-6">
            <div className="flex items-center justify-between">
              <div
                className="text-lg font-bold"
                style={{
                  color: '#52c41a',
                }}
              >
                批次明细
              </div>
            </div>
            <Row gutter={20} className="pt-6">
              <Col span={24}>
                <EditableProTable
                  rowKey="tableKey"
                  loading={false}
                  columns={columns as any}
                  name="detailsInfo"
                  toolBarRender={false}
                  recordCreatorProps={
                    !showRecordCreator
                      ? false
                      : {
                          newRecordType: 'dataSource',
                          record: () => ({ tableKey: (Math.random() * 1000000).toFixed(0) }),
                          creatorButtonText,
                        }
                  }
                  editable={{
                    type: 'multiple',
                    editableKeys,
                    actionRender: (row, config, defaultDoms) => {
                      return isEdit ? null : [defaultDoms.delete];
                    },
                    onValuesChange: (record, recordList) => {},
                    onChange: setEditableRowKeys,
                  }}
                />
              </Col>
            </Row>
          </div>

          <div className="flex justify-center">
            <Button type="primary" htmlType="submit" loading={submitLoading}>
              提交
            </Button>
          </div>
        </ProForm>
      </div>
    </PageContainerComponent>
  );
};

export default connect(({ loading }) => ({
  pageLoading: loading.effects['overseas/getOverseaAccounts'],
  submitLoading: loading.effects['overseas/addForecast'],
}))(Index);
