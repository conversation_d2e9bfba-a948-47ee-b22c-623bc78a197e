import React, { useState, useImperativeHandle } from 'react';
import { Drawer, Card, Descriptions, Divider, Button } from 'antd';
// import { DetailInfo } from './types';
import ProTableList from '@/components/ProTable';
import { CloseOutlined } from '@ant-design/icons';

const Detail = ({ dispatch, modalRef }) => {
  const [open, setOpen] = useState(false);
  const [params, setParams] = useState<Partial<any>>({});

  useImperativeHandle(modalRef, () => ({
    open: record => {
      fetchData(record);
    },
  }));

  const fetchData = record => {
    dispatch({
      type: 'overseas/getForecastDetail',
      payload: {
        batchCode: record?.batchCode,
        customerCode: record?.userId,
      },
      callback: response => {
        if (response.success) {
          setParams({
            ...params,
            ...record,
            ...response?.data,
          });
          setOpen(true);
        }
      },
    });
  };

  const reset = () => {
    setOpen(false);
    setParams({});
  };

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
    },
    {
      title: '运单号',
      dataIndex: 'waybillNumber',
      key: 'waybillNumber',
    },
    {
      title: '大包号',
      dataIndex: 'bagNo',
      key: 'bagNo',
    },
    // {
    //   title: '订单号',
    //   dataIndex: 'orderNumber',
    //   key: 'orderNumber',
    // },
  ];

  return (
    <Drawer
      title="详情"
      width="50%"
      open={open}
      onClose={reset}
      closable={false}
      extra={<Button type="text" icon={<CloseOutlined />} onClick={reset} />}
      footer={null}
    >
      <Card>
        <Descriptions
          title={<span style={{ fontSize: '16px', color: '#52C41A' }}>基础信息</span>}
          column={3}
        >
          <Descriptions.Item label="业务账号">{params?.userId}</Descriptions.Item>
          <Descriptions.Item label="送货批次标识">{params?.batchCode}</Descriptions.Item>
          <Descriptions.Item label="预计送达时间">{params?.predictArriveDate}</Descriptions.Item>
          <Descriptions.Item label="产品名称">{params?.channelName}</Descriptions.Item>
          <Descriptions.Item label="注入仓信息">{params?.companyCode}</Descriptions.Item>
          <Descriptions.Item label="运输方式">{params?.transportWay}</Descriptions.Item>
          {/* <Descriptions.Item label="是否有航班信息">
            {params?.isPrint == 1 ? '是' : params?.isPrint == 0 ? '否' : ''}
          </Descriptions.Item> */}
        </Descriptions>
        <Divider />
        <Descriptions
          title={<span style={{ fontSize: '16px', color: '#52C41A' }}>航班信息</span>}
          column={3}
        >
          <Descriptions.Item label="主单号">{params?.mawbNo}</Descriptions.Item>
          <Descriptions.Item label="起始港">{params?.pol}</Descriptions.Item>
          <Descriptions.Item label="目的港">{params?.pod}</Descriptions.Item>
          <Descriptions.Item label="预计起飞时间">{params?.etd}</Descriptions.Item>
          <Descriptions.Item label="预计到达时间">{params?.eta}</Descriptions.Item>
          <Descriptions.Item label="时区">{params?.timeZone}</Descriptions.Item>
        </Descriptions>
        <Divider />
        <Descriptions
          title={<span style={{ fontSize: '16px', color: '#52C41A' }}>批次明细</span>}
          column={1}
        >
          <Descriptions.Item>
            <ProTableList
              columns={columns}
              style={{
                width: '100%',
              }}
              dataSource={params?.detailsInfo?.map((v, i) => ({
                ...v,
                index: i + 1,
                waybillNumber: v?.parcels?.[0]?.waybillNumber,
              }))}
              pagination={false}
              rowSelection={false}
              rowKey={(record, index) => index}
              search={false}
              toolBarRender={false}
            />
          </Descriptions.Item>
        </Descriptions>
      </Card>
    </Drawer>
  );
};

export default Detail;
