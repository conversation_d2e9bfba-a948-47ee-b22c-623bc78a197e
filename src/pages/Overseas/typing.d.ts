declare namespace USER_API {
  interface getOverseaAccountsData {
    // 查询海外派制单账号
    accountCode: string;
    currencyId: number;
    currencyName: string;
    createTime: string;
    ejfStatus: number;
    ejfStatusName: string;
    balance: number;
  }

  interface TAccountDataInfo {
    label?: string;
    value?: string;
  }

  interface getOverseaMerchantInfoData {
    // 查询海外派客户信息
    merchantName: string;
    userCode: string;
    officeProvinceName: string;
    officeCityName: string;
    officeAreaName: string;
    officeAddress: string;
    merchantType: number;
  }

  interface getUserInfo {
    mobile: string;
    isAdmin: boolean;
    accessToken: string;
    userName: string;
    userId: number;
    userCode: string;
    customerName: string;
    merchantNo: string;
    email: string;
  }

  interface AddressItem {
    id: string; // 主键ID
    addressId: string; // 地址ID
    userCode: string; // 用户代码
    merchantCode: string; // 商户代码
    type: string; // 类型 "1"
    optWay: string; // 操作方式 "2"
    warehouse: string; // 注入仓 "13"
    provinceCode: string; // 省份代码 "310000"
    provinceName: string; // 省份名称 "上海市"
    cityCode: string; // 城市代码 "**********00"
    cityName: string; // 城市名称 "市辖区"
    areaCode: string; // 区域代码 "310101000000"
    areaName: string; // 区域名称 "黄浦区"
    address: string; // 详细地址 "新北大道1号楼302室123"
    contactName: string; // 联系人姓名 "联系人名称"
    contactPhone: string; // 联系人电话 "15876565454"
    contactPost: string; // 联系人职位 "职员"
    frequency: string; // 揽收频率 "揽收频率"
    isDel: string; // 是否删除 "0"
    isBind: string; // 是否绑定 "0"
    updateAddressId?: string; // 修正地址ID "202204241636112233002" (可能为空)
    updateAddress?: string; // 修正地址 "修正地址" (可能为空)
    createUser: string; // 创建用户 "YWILUɡDI"
    source: string; // 来源 "4"
    createTime: string; // 创建时间 "2023-05-06 10:13:14"
    updateTime: string; // 更新时间 "2023-05-06 10:13:14"
    contactEmail?: string; // 联系人邮箱 (可能为空)
    contactZipCode?: string; // 联系人邮编 (可能为空)
  }
}

declare namespace API {
  interface Result<T> {
    success?: boolean;
    errorMessage?: string;
    message?: string;
    data?: T;
  }
}
