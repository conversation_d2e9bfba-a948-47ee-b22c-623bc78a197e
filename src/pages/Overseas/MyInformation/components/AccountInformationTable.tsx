import { currencyToPng } from '@/utils/utils';
import { ProCard, ProColumnType, ProTable } from '@ant-design/pro-components';
import { useMount } from 'ahooks';
import { Modal, Space, message, Switch } from 'antd';
import React, { useRef, useState } from 'react';
import SwitchModal from './SwitchModal';
// const { getOverseaAccounts, getOrResetKey } = UserServices.Controller;

const AccountInformationTable = props => {
  const { dispatch, getOverseaAccountsLoading, userInfo } = props;
  const [dataSource, setDataSource] = useState<Array<USER_API.getOverseaAccountsData>>([]);
  const [loadingRowId, setLoadingRowId] = useState<string | null>(null);
  const switchModalRef = useRef<any>();
  // 复制密钥
  const handleCopy = (context: string) => {
    var ele = document.createElement('input'); //创建一个input标签
    ele.setAttribute('value', context); // 设置改input的value值
    document.body.appendChild(ele); // 将input添加到body
    ele.select(); // 获取input的文本内容
    document.execCommand('copy'); // 执行copy指令
    document.body.removeChild(ele); // 删除input标签
    message.success('复制成功!');
  };

  const handleSwitchCancel = (values?: any, checked?: any) => {
    if (values) {
      updateEncryptInfo(values, checked);
    }
  };

  const getOrResetKey = params =>
    new Promise<API.Result<any>>((resolve, reject) => {
      dispatch({
        type: 'overseas/getOrResetKey',
        payload: {
          ...params,
        },
        callback: (response: API.Result<any>) =>
          response.success ? resolve(response) : reject(response),
      });
    });

  const handleKey = async (accountCode: string, reset: boolean) => {
    try {
      if (reset) {
        Modal.confirm({
          title: '确定重置秘钥吗？',
          okText: '确定',
          cancelText: '取消',
          onOk: async () => {
            const response = await getOrResetKey({ accountCode, reset });
            if (response.success) {
              message.success(response.message);
              initialFunc();
            }
          },
        });
      } else {
        const response = await getOrResetKey({ accountCode, reset });
        if (response.success) {
          const apiToken = response.data;
          Modal.confirm({
            title: '秘钥',
            okText: '确定',
            cancelText: '复制',
            cancelButtonProps: {
              type: 'primary',
            },
            content: <div>{apiToken}</div>,
            onOk() {},
            onCancel: () => handleCopy(apiToken),
          });
        }
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleChangeSwitch = (checked: boolean, record: any) => {
    switchModalRef.current?.openModal(checked, record);
  };

  const updateEncryptInfo = (record, checked) => {
    setLoadingRowId(record.accountCode);
    let param = {
      merchantCode: record.accountCode,
      hasS3Encrypt: checked ? '1' : '0',
      encryptField: 'senderInfo.taxNumber',
    };
    dispatch({
      type: 'overseas/updateEncryptInfo',
      payload: param,
      callback: result => {
        setLoadingRowId(null);
        if (result.success) {
          initialFunc();
        }
      },
    });
  };

  const lookSecret = merchantCode => {
    dispatch({
      type: 'overseas/getPublicKey',
      payload: { merchantCode: merchantCode },
      callback: result => {
        if (result.success) {
          Modal.confirm({
            title: '秘钥',
            okText: '确定',
            cancelText: '复制',
            cancelButtonProps: {
              type: 'primary',
            },
            content: <div>{result.data}</div>,
            onOk() {},
            onCancel: () => handleCopy(result.data),
          });
        }
      },
    });
  };

  const columns: ProColumnType<any>[] = [
    {
      title: '业务账号',
      dataIndex: 'accountCode',
    },
    {
      title: '币种',
      dataIndex: 'currencyName',
      render: (text, record) => {
        return (
          <>
            <Space>
              {/* <img
                src={currencyToPng(record.currencyName)}
                style={{ width: '14px', height: '12px' }}
              /> */}
              {`${record?.currencyName}(${record?.currencyCode})`}
            </Space>
          </>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
    },
    {
      title: '清关数据加密发件人税号',
      dataIndex: 'encrypt',
      render: (text, record) => (
        <div>
          <Switch
            checked={record?.encryptInfo?.hasS3Encrypt == '1'}
            onChange={checked => handleChangeSwitch(checked, record)}
            loading={loadingRowId === record.accountCode} // 行级loading判断
            checkedChildren="否"
            unCheckedChildren="是"
            // disabled={userInfo?.isAdmin === false}
          />
          {record?.encryptInfo?.hasS3Encrypt == '1' && (
            <Space style={{ marginLeft: '8px' }}>
              <a onClick={() => lookSecret(record?.accountCode)}>查看YW公钥</a>
            </Space>
          )}
        </div>
      ),
    },
    {
      title: '操作',
      dataIndex: 'operation',
      render: (text, record) => {
        return (
          <Space>
            <a onClick={() => handleKey(record?.accountCode, false)}>查看密钥</a>
            <a onClick={() => handleKey(record?.accountCode, true)}>重置密钥</a>
          </Space>
        );
      },
    },
  ];

  useMount(() => {
    initialFunc();
  });

  const initialFunc = () => {
    // run({ type: '0' });
    dispatch({
      type: 'overseas/getOverseaAccounts',
      payload: '0',
      callback: (response: API.Result<USER_API.getOverseaAccountsData[]>) => {
        if (response.success) {
          setDataSource(response.data);
        }
      },
    });
  };

  return (
    <ProCard ghost gutter={[20, 40]}>
      <ProCard bordered title={<span className="text-sm">账号信息</span>}>
        <ProTable
          rowKey={(_, index) => index + ''}
          search={false}
          rowSelection={false}
          toolBarRender={false}
          pagination={false}
          dataSource={dataSource}
          loading={getOverseaAccountsLoading}
          columns={columns}
        />
      </ProCard>
      <SwitchModal modalRef={switchModalRef} onCancel={handleSwitchCancel} {...props} />
    </ProCard>
  );
};
export default AccountInformationTable;
