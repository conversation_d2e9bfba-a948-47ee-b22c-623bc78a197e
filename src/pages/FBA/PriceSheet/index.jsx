/*
 * @Description: {formatMessage({id: '报价单'})}
 * @Author:  {formatMessage({id: '李苏亚'})}
 * @Date: 2023-03-08 11:06:30
 * @LastEditTime: 2023-03-08 11:07:22
 * @LastEditors:
 */
import React, { useState } from 'react';
import { Card, Space, Typography, Button, message } from 'antd';
import moment from 'moment';
import { connect } from 'dva';
import { useMount } from 'ahooks';
import PageContainerComponent from '@/components/PageContainer';
import PageHeaderBreadcrumb from '@/components/PageHeaderBreadcrumb';
import ModalImage from '@/components/ModalImage';
import { formatMessage } from 'umi-plugin-react/locale';

const PriceSheet = ({ pageLoading, dispatch }) => {
  const currentDate = moment().format('YYYY-MM-DD');
  const [imgUrl, setImgUrl] = useState();
  const [previewOpen, setPreviewOpen] = useState(false);
  const [localFileUrl, setLocalFileUrl] = useState();
  const [uploadTime, setUploadTime] = useState(currentDate);
  const [remark, setRemark] = useState();
  const testUrl = 'https://csc-fat.yanwentech.com/csc';

  useMount(() => {
    initialFunc();
  });

  const initialFunc = () => {
    dispatch({
      type: 'fba/fbaOfferMessage',
      callback: response => {
        if (response.success) {
          setImgUrl(response.data.imgUrl);
          setLocalFileUrl(response.data.localFileUrl);
          setUploadTime(response.data.uploadTime);
          setRemark(response.data.remark);
        } else {
          setImgUrl();
          setLocalFileUrl();
          setUploadTime();
          setRemark();
        }
      },
    });
  };

  const handleOnlineLook = () => {
    if (!localFileUrl) return message.error(formatMessage({ id: '暂无报价单' }));
    window.open(`https://view.officeapps.live.com/op/view.aspx?src=${testUrl}${localFileUrl}`);
  };

  const handleDownLoad = () => {
    if (!localFileUrl) return message.error(formatMessage({ id: '暂无报价单' }));
    window.open(`${testUrl}${localFileUrl}`);
  };

  return (
    <>
      <Card loading={pageLoading}>
        <Card
          style={{ boxShadow: '0 4px 4px 0 rgba(190, 190, 190, 0.5)' }}
          title={
            <span style={{ color: '#52c41a' }}>{formatMessage({ id: '报价单更新说明' })}</span>
          }
          extra={`${formatMessage({ id: '更新时间' })}：${uploadTime}`}
        >
          <Space direction="vertical">
            <Typography.Text>{remark}</Typography.Text>
          </Space>
        </Card>
        <Space direction="vertical" className="mt-3">
          <Typography.Text type="success">{formatMessage({ id: '报价单文件' })}</Typography.Text>
          <div style={{ display: 'flex', flexDirection: 'column', width: '15rem' }}>
            {imgUrl && (
              <img
                src={imgUrl}
                alt=""
                onClick={() => setPreviewOpen(true)}
                className="cursor-pointer"
              />
            )}
            <div
              style={{
                width: '100%',
                display: 'flex',
                justifyContent: 'space-between',
                marginTop: '20px',
              }}
            >
              <Button type="primary" onClick={() => handleOnlineLook()}>
                {formatMessage({ id: '在线预览' })}
              </Button>
              <Button type="primary" onClick={() => handleDownLoad()}>
                {formatMessage({ id: '下载' })}
              </Button>
            </div>
          </div>
        </Space>
      </Card>
      <ModalImage
        width={'57%'}
        url={imgUrl}
        open={previewOpen}
        onCancel={() => setPreviewOpen(false)}
      />
    </>
  );
};

export default connect(({ loading }) => ({
  pageLoading: loading.effects['fba/fbaOfferMessage'],
}))(PriceSheet);
