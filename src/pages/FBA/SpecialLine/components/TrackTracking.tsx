import React, { useRef } from 'react';
import { TFBAPageProps } from '@/types/fba';
import { ProCard, ProForm, ProFormTextArea, ProFormInstance } from '@ant-design/pro-components';
import { Row, Space, Button } from 'antd';
import { useMount } from 'ahooks';
import { trackUrl } from '@/utils/commonConstant';
import { formatMessage } from 'umi-plugin-react/locale';

const TrackTracking = ({ dispatch }: TFBAPageProps) => {
  const formRef = useRef<ProFormInstance>();
  const textAreaRef = useRef<any>();

  useMount(() => {
    textAreaRef.current?.blur();
  });

  const handleSubmit = async (values: any) => {
    const trimmedValue = values?.nums.trim();
    const replacedValue = trimmedValue.replace(/[\s,\t]+/g, ',');
    // const nums = replacedValue.split('，');
    // https://track.yw56.com.cn/cn/querydel       {formatMessage({id: '生产'})}
    // http://10.10.144.16       {formatMessage({id: '测试'})}
    window.open(`${trackUrl}?nums=${replacedValue}`);
  };
  return (
    <ProCard title={formatMessage({ id: '轨迹查询' })} headerBordered>
      <div className="h-80">
        <ProForm formRef={formRef} submitter={false} onFinish={handleSubmit}>
          <ProFormTextArea
            name="nums"
            placeholder={`${formatMessage({ id: '请输入订单' })}/${formatMessage({
              id: '运单号',
            })}，${formatMessage({ id: '最多输入' })}500${formatMessage({
              id: '个运单号',
            })}，${formatMessage({ id: '多单号' })}
                ${formatMessage({ id: '请以逗号' })}、${formatMessage({ id: '空格或回车隔开' })}`}
            rules={[
              {
                required: true,
                message: `${formatMessage({ id: '请输入订单' })}/${formatMessage({
                  id: '运单号',
                })}`,
              },
            ]}
            fieldProps={{
              style: {
                height: '255px',
              },
              ref: textAreaRef,
            }}
          />
          <Row justify="end">
            <Space>
              <Button onClick={() => formRef.current?.resetFields()}>
                {formatMessage({ id: '重置' })}
              </Button>
              <Button type="primary" htmlType="submit">
                {formatMessage({ id: '查询' })}
              </Button>
            </Space>
          </Row>
        </ProForm>
      </div>
    </ProCard>
  );
};

export default TrackTracking;
