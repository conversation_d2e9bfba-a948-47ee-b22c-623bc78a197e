import React, { useState, useRef } from 'react';
import PageContainerComponent from '@/components/PageContainer';
import PageHeaderBreadcrumb from '@/components/PageHeaderBreadcrumb';
import { Tabs, <PERSON><PERSON>, Card, Alert, Steps } from 'antd';
import SmallBagOfflineTopUp from './components/SmallBagOfflineTopUp';
import FBAOfflineTopUp from './components/FBAOfflineTopUp';
import OverseasOfflineTopUp from './components/OverseasOfflineTopUp';
import { connect } from 'dva';
import { message } from 'antd';
import { useMount, useUpdateEffect } from 'ahooks';
import { businessStatus } from '@/utils/commonConstant';
import usePaymentAccountStatus from '@/hooks/usePaymentAccountStatus';
import AuthStatusNode from '@/components/AuthStatusNode';
import RecordPaymentTips from './components/SmallBagOfflineTopUp/RecordPaymentTips';
import { isAuth } from '@/utils/utils';
import { formatMessage } from 'umi-plugin-react/locale';

const Index = ({ dispatch, location }) => {
  const [current, setCurrent] = useState(0);
  const [activeKey, setActiveKey] = useState('0');
  const [smallBagStatus, setSmallBagStatus] = useState(false);
  const [fbaStatus, setFbaStatus] = useState(false);
  const [overseasStatus, setOverseasStatus] = useState(false);
  const smallBagOfflineRef = useRef();

  const tabItems = [
    {
      label: formatMessage({ id: '小包专线' }),
      key: '0',
      children: (
        <SmallBagOfflineTopUp businessType={activeKey} smallBagOfflineRef={smallBagOfflineRef} />
      ),
    },
    {
      label: `FBA${formatMessage({ id: '专线' })}`,
      key: '1',
      children: (
        <FBAOfflineTopUp businessType={activeKey} smallBagOfflineRef={smallBagOfflineRef} />
      ),
    },
    {
      label: '海外派',
      key: '2',
      children: (
        <OverseasOfflineTopUp businessType={activeKey} smallBagOfflineRef={smallBagOfflineRef} />
      ),
    },
    // { label: '中国仓', key: '3', children: <WaybillCollected /> },
    // { label: '海外派', key: '4', children: <ExpressProductChangeOrderRecord /> },
  ];

  useMount(() => {
    initialFunc();
  });

  const initialFunc = () => {
    getOpenStatusResult();
  };

  useUpdateEffect(() => {
    if (location?.query?.paymentAdd && (smallBagStatus || fbaStatus)) {
      handleAdd();
    }
  }, [smallBagStatus, fbaStatus]);

  const handleAdd = () => {
    // , getAuthorityStatus(1)
    Promise.allSettled([getAuthorityStatus(+activeKey)]).then(res => {
      const fulfilledInfo = res.find(item => item.status === 'fulfilled');
      if (fulfilledInfo) {
        const {
          value: { type, value },
        } = fulfilledInfo;
        if (value === '6') {
          smallBagOfflineRef.current?.openAddModal();
        }
      } else {
        const rejectedInfo = res.find(item => item.status === 'rejected');
        const {
          reason: { type, value },
        } = rejectedInfo;
        if (value === '1') {
          message.error('请先实名认证');
          return;
        }
        if (value === '5') {
          message.error('此功能暂无法使用，请前往首页签署合同');
          return;
        } else if (value !== '6') {
          message.error(
            `您尚未开通${type == 0 ? '小包业务线' : 'FBA业务线'}，请开通成功后请再进行此操作！`
          );
        }
      }
    });
  };

  const getAuthorityStatus = type =>
    new Promise((resolve, reject) => {
      dispatch({
        type: 'user/getAuthorityStatus',
        payload: { type },
        callback: response => {
          if (response.success) {
            const key = `${response.data}`;
            if (key === '6') {
              resolve({
                type,
                value: key,
              });
            } else if (key === '1') {
              // 未实名认证
              reject({
                type,
                value: key,
              });
            } else {
              // 未开通小包服务
              reject({
                type,
                value: key,
              });
            }
          }
        },
      });
    });

  // 获取开通小包专线和FBA专线的状态结果
  const getOpenStatusResult = async () => {
    try {
      const result = await Promise.allSettled([
        getOpenStatus(0),
        getOpenStatus(1),
        getOpenStatus(2),
      ]);

      let isSetActiveKeyExecuted = false; // 添加一个变量来记录是否已经设置过 activeKey
      result.forEach(item => {
        if (item.status === 'fulfilled') {
          const { type, status } = item.value;
          if (!isSetActiveKeyExecuted) {
            // 判断是否已经设置过 activeKey
            if (location?.query?.type == 1) {
              setActiveKey('1');
              handleChangeTabs('1');
            } else {
              setActiveKey(
                type === 0 && status
                  ? '0'
                  : type === 1 && status
                  ? '1'
                  : type === 2 && status
                  ? '2'
                  : '0'
              );
              handleChangeTabs(
                type === 0 && status
                  ? '0'
                  : type === 1 && status
                  ? '1'
                  : type === 2 && status
                  ? '2'
                  : '0'
              );
            }
            isSetActiveKeyExecuted = status; // 设置为已经执行过 setActiveKey
          }
          if (type === 0) {
            setSmallBagStatus(status);
          } else if (type === 1) {
            setFbaStatus(status);
          } else if (type === 2) {
            setOverseasStatus(status);
          }
        }
      });
    } catch (error) {
      console.log(error);
    }
  };

  // 获取小包专线和FBA专线开通接口
  const getOpenStatus = type => {
    return new Promise((resolve, reject) => {
      const result = isAuth(type === 0 ? 'smallPackage' : type === 1 ? 'fbaOrder' : 'overseas');
      resolve({ type, status: result });
    });
  };

  const handleChangeTabs = async key => {
    setActiveKey(key);
    try {
      await usePaymentAccountStatus(dispatch, key, { viewContract: true });
    } catch (error) {
      console.log('error', error);
    }
  };

  return (
    <PageContainerComponent
      header={{
        title: null,
        breadcrumb: {},
        breadcrumbRender: props => <PageHeaderBreadcrumb {...props} />,
      }}
    >
      <RecordPaymentTips activeKey={activeKey} />

      <Tabs
        tabBarStyle={{ margin: '0' }}
        activeKey={activeKey}
        onChange={handleChangeTabs}
        type="card"
        items={tabItems.filter(item => {
          if (item.key === '0' && smallBagStatus) {
            return true;
          }
          if (item.key === '1' && fbaStatus) {
            return true;
          }
          if (item.key === '2' && overseasStatus) {
            return true;
          }
          return false;
        })}
        tabBarGutter={20}
        tabBarExtraContent={
          <AuthStatusNode authKey="mymessage:merchant:addPayment">
            <Button type="primary" onClick={() => handleAdd()} style={{ margin: 15 }}>
              {formatMessage({ id: '新增付款账号' })}
            </Button>
          </AuthStatusNode>
        }
      />
    </PageContainerComponent>
  );
};

export default connect()(Index);
