import React, { useState } from 'react';
import { ProCard } from '@ant-design/pro-components';
import { CopyOutlined } from '@ant-design/icons';
import { Row, Col, Space } from 'antd';
import { useMount } from 'ahooks';
import { clickCopy } from '@/utils/utils';
import { formatMessage } from 'umi-plugin-react/locale';

const YanwenPaymentAccount = props => {
  const { dispatch, bankInfoLoading, businessType } = props;
  const [bankInfo, setBankInfo] = useState({});
  const [alipayInfo, setAlipayInfo] = useState({});

  useMount(() => {
    getBankAccountList();
  });

  const getBankAccountList = () => {
    dispatch({
      type:
        businessType == 0
          ? 'smallBag/getBankAccountList'
          : businessType == 1
          ? 'bill/getFbaBankAccountList'
          : 'overseas/getYWEBankAccountList',
      callback: response => {
        if (response.success) {
          const bankDataInfo = response.data.filter(item => item.bankType === 1)[0];
          const alipayDataInfo = response.data.filter(item => item.bankType === 0)[0];
          setBankInfo({
            ...bankInfo,
            ...bankDataInfo,
          });
          setAlipayInfo({
            ...alipayInfo,
            ...alipayDataInfo,
          });
        } else {
          setAlipayInfo({});
          setBankInfo({});
        }
      },
    });
  };

  return (
    <>
      <Row style={{ borderRadius: 6, background: '#fff' }}>
        {/* <Col span={24}>
          <p
            className="text-red-500"
            style={{
              marginBottom: 0,
              paddingInline: '24px',
              paddingBlock: '16px',
              paddingBlockEnd: 0,
            }}
          >
            {formatMessage({id:'温馨提示'})}：{formatMessage({id:'线下支付需进行境内银行'})}/{formatMessage({id:'支付宝备案'})}，{formatMessage({id:'备案完成后直接转账至下列账户'})}，{formatMessage({id:'即可自动完成入账'})}
          </p>
        </Col> */}
        <Col span={12}>
          <ProCard title={formatMessage({ id: '银行收款账户信息' })}>
            <Space direction="vertical">
              <Space>
                <span>
                  {formatMessage({ id: '账户名' })}：{bankInfo?.bankAccountName ?? ''}
                </span>
                {bankInfo?.bankAccountName && (
                  <CopyOutlined
                    style={{ color: '#52c41a' }}
                    onClick={event => clickCopy(bankInfo?.bankAccountName)}
                  />
                )}
              </Space>
              <Space>
                <span>
                  {formatMessage({ id: '开户行' })}：{bankInfo?.bankAddr ?? ''}
                </span>
                {bankInfo?.bankAddr && (
                  <CopyOutlined
                    style={{ color: '#52c41a' }}
                    onClick={event => clickCopy(bankInfo?.bankAddr)}
                  />
                )}
              </Space>
              <Space>
                <span>
                  {formatMessage({ id: '账号' })}：{bankInfo?.account ?? ''}
                </span>
                {bankInfo?.account && (
                  <CopyOutlined
                    style={{ color: '#52c41a' }}
                    onClick={event => clickCopy(bankInfo?.account)}
                  />
                )}
              </Space>
            </Space>
          </ProCard>
        </Col>
        <Col span={12}>
          <ProCard title={formatMessage({ id: '支付宝收款账户信息' })}>
            <Space direction="vertical">
              <Space>
                <span>
                  {formatMessage({ id: '支付宝账户名' })}：{alipayInfo?.bankAccountName ?? ''}
                </span>
                {alipayInfo?.bankAccountName && (
                  <CopyOutlined
                    style={{ color: '#52c41a' }}
                    onClick={event => clickCopy(alipayInfo?.bankAccountName)}
                  />
                )}
              </Space>
              <Space>
                <span>
                  {formatMessage({ id: '支付宝账户号' })}：{alipayInfo?.account ?? ''}
                </span>
                {alipayInfo?.account && (
                  <CopyOutlined
                    style={{ color: '#52c41a' }}
                    onClick={event => clickCopy(alipayInfo?.account)}
                  />
                )}
              </Space>
            </Space>
          </ProCard>
        </Col>
      </Row>
    </>
  );
};

export default YanwenPaymentAccount;
