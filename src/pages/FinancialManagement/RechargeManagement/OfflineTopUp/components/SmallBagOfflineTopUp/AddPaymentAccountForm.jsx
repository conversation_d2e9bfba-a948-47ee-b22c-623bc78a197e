import React, { useState, useImperativeHandle, useEffect, useRef } from 'react';
import {
  Form,
  Button,
  Select,
  Radio,
  Input,
  Upload,
  Modal,
  message,
  Row,
  Space,
  Typography,
  Alert,
  Tag,
} from 'antd';
import {
  businessTypeList,
  personalPayRelationArray,
  companyPayRelationArray,
  alipayPayRelationArray,
  alipayCompanyPayRelationArray,
  otherPayRelationArray,
  AlipayArr,
  renderSelectOptions,
  relationOption,
  AddPaymentCertificateTypeData,
} from '@/utils/commonConstant';
import { SwitcherFilled } from '@ant-design/icons';
import { ProFormUploadButton } from '@ant-design/pro-components';
import {
  changeBeforeUpload,
  disabledDate,
  getBase64,
  returnMerchantType,
  returnPersonNameOrCompanyName,
  validateMerchantName,
  paymentTypeDiffLabel,
} from '@/utils/utils';
import {
  PAY_RELATION_MAP,
  DEFAULT_PAY_RELATION_MAP,
  DEFAULT_PAY_TYPE_MAP,
  otherOption,
} from '@/utils/commonConstant';
import ModalImage from '@/components/ModalImage';
import VerifyAdminPhoneModal from '@/components/VerifyAdminPhoneModal';
import ElectronicReceiptDemo from '@/assets/electronicReceiptDemo.png';
import ModalDemoImage from '@/components/ModalDemoImage';

let timeout;
let currentValue;

/*
 *@Description: 新增付款账号Form表单弹窗
 *@MethodAuthor: dangh
 *@Date: 2023-05-09 17:00:22
 */
const AddPaymentAccountForm = props => {
  const {
    addFormRef,
    form,
    dispatch,
    onCancel,
    merchantDetail,
    bankAccountOptions,
    alipayAccountOptions,
    platformOptions,
    bankOptions,
    createLoading,
    businessType,
  } = props;

  const verifyAdminPhoneRef = useRef();

  const [open, setOpen] = useState(false); // 新增付款账号内容弹窗
  const [mode, setMode] = useState(0); // 付款方式
  const [paymentTypeOptions, setPaymentTypeOptions] = useState([]); // 付款类型
  const [payRelationOptions, setPayRelationOptions] = useState([]); // 付款人与商户关系
  const [isMerchantSelf, setIsMerchantSelf] = useState(false); // 是否是商户本身
  const [isSelectOther, setIsSelectOther] = useState(false); // 是否选择其他
  const [isCompany, setIsCompany] = useState(false); // 是否是企业
  const [isHaveAgree, setIsHaveAgree] = useState(1); // 是否同意协议
  const [previewOpen, setPreviewOpen] = useState(false); // 是否显示打开链接
  const [previewImage, setPreviewImage] = useState(); // 打开链接
  const [bankCardId, setBankCardId] = useState(); // 用于文件上传id使用
  const [addBusinessTypeList, setAddBusinessTypeList] = useState(businessTypeList); // 用于新增商户类型使用
  const [flowId, setFlowId] = useState(); // 用于新增商户类型使用
  const [showBankBranch, setShowBankBranch] = useState(false); // 是否显示支行信息
  const [bankBranchList, setBankBranchList] = useState([]); // 支行信息
  const [paymentType, setPaymentType] = useState(); // 付款类型  0、2 个人付款  1、3企业付款
  const [certificateType, setCertificateType] = useState(0); // 证件类型
  const modalDemoRef = useRef();
  // 用于父组件调用子组件方法
  useImperativeHandle(addFormRef, () => ({
    showModal: () => {
      initialFunc();
    },
  }));

  const initialFunc = () => {
    Promise.allSettled([getAuthorityStatus(0), getAuthorityStatus(1), getAuthorityStatus(2)]).then(
      res => {
        setOpen(true);
        handleModeChange({ target: { value: 0 } });
        const data = addBusinessTypeList.filter(val => {
          for (const item of res) {
            if (item?.status === 'fulfilled') {
              const { type, value } = item?.value;
              if (value === '6' && type == val?.value) {
                return true;
              }
            }
          }
        });
        setAddBusinessTypeList(data);
        setTimeout(() => {
          form.setFieldsValue({
            businessTypes: data.length === 1 ? data?.[0]?.value : undefined,
            certificateType: 0,
          });
        }, 300);
      }
    );
  };

  // 根据输入的内容查询支行
  const handleSearch = newValue => {
    if (newValue) {
      const values = form.getFieldsValue(['bankCode']);
      // const value = `${newValue}${
      //   bankOptions?.find(item => item.value === values?.bankCode)?.label
      // }`;
      const value = newValue;
      getBankBranch(value, setBankBranchList);
    } else {
      setBankBranchList([]);
    }
  };

  // 查询支行信息
  const getBankBranch = (keyword, callback) => {
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }
    currentValue = keyword;
    const request = () => {
      dispatch({
        type: 'paymentAccount/branchBankList',
        payload: {
          flowId,
          keyword,
        },
        callback: response => {
          if (response.success) {
            if (currentValue === keyword) {
              callback(response.data);
            }
          }
        },
      });
    };
    timeout = setTimeout(request, 300);
  };

  const getAuthorityStatus = type =>
    new Promise((resolve, reject) => {
      dispatch({
        type: 'user/getAuthorityStatus',
        payload: { type },
        callback: response => {
          if (response.success) {
            const key = `${response.data}`;
            if (key === '6') {
              resolve({
                type,
                value: key,
              });
            } else if (key === '1') {
              // 未实名认证
              reject({
                type,
                value: key,
              });
            } else {
              // 未开通小包服务
              reject({
                type,
                value: key,
              });
            }
          }
        },
      });
    });

  const handleCancel = values => {
    form.resetFields();
    setAddBusinessTypeList(businessTypeList);
    setMode(0);
    setOpen(false);
    setFlowId(undefined);
    setShowBankBranch(false);
    setBankBranchList([]);
    setCertificateType(0);
    timeout = undefined;
    currentValue = undefined;
    if (values) {
      onCancel(values);
    }
  };

  // 选择不同的付款方式处理不同的逻辑
  const handleModeChange = e => {
    form.resetFields([
      'accountName',
      'businessLicence',
      'idCard',
      'bankCode',
      'bankCard',
      'legalPersonName',
      'relationShip',
      'relationShipInput',
      'platformName',
      'protocol',
      'platformAgreement',
      'digitalReceipt',
      'businessTypes',
    ]);
    // form.resetFields(['relationShip', 'relationShipInput']);
    setTimeout(() => {
      form.setFieldsValue({
        accountName: returnPersonNameOrCompanyName(merchantDetail),
        businessTypes:
          addBusinessTypeList.length === 1 ? addBusinessTypeList?.[0]?.value : undefined,
      });
    }, 300);
    setIsMerchantSelf(true);
    // if (e.target.value === 1 && businessType == 1) {
    //   // form.setFieldsValue({
    //   //   paymentType: 0,
    //   // });
    //   handlePaymentChange({ target: { value: 0 } });
    //   return message.warning('FBA业务不支持支付宝付款方式');
    // }
    handlePaymentChange(e);
  };

  // TODO:: 处理不同付款方式下的付款类型和付款人与商户关系选择框里的内容
  const handlePaymentChange = async e => {
    const accountName = form.getFieldValue(['accountName']);
    const value = e.target.value;
    const key = `${value}-${returnMerchantType(merchantDetail)}`;
    const isHimself = accountName === returnPersonNameOrCompanyName(merchantDetail);
    const paymentTypeArray = PAY_RELATION_MAP[key]; // 用于渲染付款类型使用
    const defaultType = DEFAULT_PAY_TYPE_MAP[key]; // 用于回显付款方式使用
    const relationList = isHimself
      ? otherPayRelationArray[0].relation
      : DEFAULT_PAY_RELATION_MAP[key]; // 用于渲染付款人与商户关系使用
    setPaymentType(defaultType);
    setPayRelationOptions(
      renderSelectOptions(relationList, {
        label: 'title',
        value: 'value',
      })
    );
    setPaymentTypeOptions(
      renderSelectOptions(paymentTypeArray, {
        label: 'title',
        value: 'value',
      })
    );
    setIsCompany(returnMerchantType(merchantDetail) === '企业');
    setMode(value);
    form.setFieldsValue({
      paymentTypes: defaultType,
      paymentType: value,
      relationShip:
        value == 2 && accountName === returnPersonNameOrCompanyName(merchantDetail)
          ? otherOption
          : undefined,
    });
  };

  // 新增时的账户明和平台实名认证名称时 输入的内容为当前商户一致时的逻辑
  const inputChange = () => {
    form.resetFields(['relationShip', 'relationShipInput']);
    const data = form.getFieldsValue(['accountName', 'accountName', 'paymentTypes']);
    if (mode === 2) {
      form.setFieldsValue({
        relationShip:
          data?.accountName === returnPersonNameOrCompanyName(merchantDetail)
            ? otherOption
            : undefined,
      });
      let array = null;
      if (data?.accountName === returnPersonNameOrCompanyName(merchantDetail)) {
        array = otherPayRelationArray[0].relation;
        setIsMerchantSelf(true);
      } else if (data?.paymentTypes === 2 && returnMerchantType(merchantDetail) === '企业') {
        array = handleCompanyRelationOptions(data?.accountName, merchantDetail.corporateName);
        setIsMerchantSelf(false);
      } else {
        array = findPaymentTypeOption(data?.paymentTypes)?.relation;
        setIsMerchantSelf(false);
      }
      setPayRelationOptions(
        renderSelectOptions(array, {
          label: 'title',
          value: 'value',
        })
      );
    } else {
      if (data?.accountName === returnPersonNameOrCompanyName(merchantDetail)) {
        setIsMerchantSelf(true);
      } else {
        setIsMerchantSelf(false);
        // 非商户本人
        let relation = null;
        if (returnMerchantType(merchantDetail) === '企业' && data?.paymentTypes === 2) {
          relation = handleCompanyRelationOptions(data?.accountName, merchantDetail.corporateName);
        } else {
          relation = findPaymentTypeOption(data?.paymentTypes)?.relation;
        }
        setPayRelationOptions(
          renderSelectOptions(relation, {
            label: 'title',
            value: 'value',
          })
        );
      }
    }
  };

  // 上传文件处理数据逻辑
  const handleOnChange = (info, type) => {
    if (info.file.status === 'uploading') {
      return;
    }
    if (info.file.status === 'done') {
      // Get this url from response in real world.
      if (info.file.response.success) {
        const uploadList = form.getFieldValue([type]);
        const url = info.file.response.data;
        const data = uploadList.map(item => {
          item.url = url;
          return item;
        });
        form.setFieldsValue({
          [type]: data,
        });
      } else {
        form.resetFields([type]);
        message.error(info.file.response.message);
      }
    }
  };

  // 选择付款类型
  const selectPaymentType = value => {
    form.resetFields(['relationShipInput']);
    const data = form.getFieldsValue(['accountName']);
    setPaymentType(value);
    if (mode === 2) {
      // 选择第三方
      let array = null;
      if (value === 2 && returnMerchantType(merchantDetail) === '企业') {
        array = handleCompanyRelationOptions(data?.accountName, merchantDetail.corporateName);
        setIsCompany(false);
      } else if (data?.accountName === returnPersonNameOrCompanyName(merchantDetail)) {
        array = otherPayRelationArray[0].relation;
        setIsCompany(value == 1 || value == 3);
      } else {
        array = findPaymentTypeOption(value)?.relation;
        setIsCompany(returnMerchantType(merchantDetail) === '企业' || value == 1 || value == 3);
      }
      setPayRelationOptions(
        renderSelectOptions(array, {
          label: 'title',
          value: 'value',
        })
      );
    } else {
      // 选择银行或者支付宝
      let relation = null;
      if (value === 2 && returnMerchantType(merchantDetail) === '企业') {
        relation = handleCompanyRelationOptions(data?.accountName, merchantDetail.corporateName);
        setIsCompany(false);
      } else {
        relation = findPaymentTypeOption(value)?.relation;
        setIsCompany(returnMerchantType(merchantDetail) === '企业' || value == 1 || value == 3);
      }
      setPayRelationOptions(
        renderSelectOptions(relation, {
          label: 'title',
          value: 'value',
        })
      );
    }
  };

  // 选择付款人与商户关系
  const selectRelationShip = value => {
    setIsSelectOther(value === '其他');
    form.resetFields(['relationShipInput']);
  };

  const findPaymentTypeOption = value => {
    return paymentTypeOptions.find(item => item.value === value);
  };

  // 选择付款类型时处理商户是企业的逻辑
  const handleCompanyRelationOptions = (data, corporateName) => {
    if (data === corporateName) {
      return [
        {
          label: '法定代表人',
          value: '法定代表人',
        },
      ];
    }
    return renderSelectOptions(relationOption, {
      label: 'title',
      value: 'value',
    });
  };

  const getBankCardId = values =>
    new Promise(resolve => {
      dispatch({
        type: 'paymentAccount/getBankCardId',
        payload: {
          paymentTopicType: values?.paymentTypes,
          bankCard: values?.bankCard,
          paymentType: 2,
          accountName: values?.accountName,
          businessTypes:
            typeof values?.businessTypes === 'string'
              ? [values?.businessTypes]
              : values?.businessTypes,
        },
        callback: response => {
          if (response.success) {
            setBankCardId(response.data);
            resolve(response.data);
          } else {
            resolve(null);
          }
        },
      });
    });

  const beforeUploadFunc = async file => {
    try {
      const values = await form.validateFields([
        'paymentTypes',
        'bankCard',
        'accountName',
        'businessTypes',
      ]);
      const bankCardId = await getBankCardId(values);
      if (bankCardId) {
        return changeBeforeUpload(
          file,
          5,
          ['image/jpeg', 'image/png'],
          '请上传小于1M的图片或小于5M的文件',
          1
        );
      } else {
        return false || Upload.LIST_IGNORE;
      }
    } catch (error) {
      return false || Upload.LIST_IGNORE;
    }
  };

  // 银行支付宝渲染
  const bankAndAliPayRender = () => (
    <>
      <Form.Item
        label="付款类型"
        name="paymentTypes"
        rules={[
          {
            required: true,
            message: '请选择付款类型',
          },
        ]}
      >
        <Select
          options={paymentTypeOptions}
          showSearch
          disabled={paymentTypeOptions.length === 1 || showBankBranch}
          placeholder={'-请选择-'}
          onChange={selectPaymentType}
          allowClear
        />
      </Form.Item>
      {(paymentType == 1 || paymentType == 3) && (
        <div
          style={{
            paddingLeft: '48px',
            marginTop: '-4px',
            marginBottom: '24px',
          }}
        >
          <Typography.Text
            style={{
              padding: '8px 8px 8px 8px',
              background: '#e7f4ff',
              border: '1px solid #c0e1ff',
              borderRadius: '5px',
            }}
          >
            温馨提示：仅支持大陆企业备案，非大陆企业请联系客服线下备案
          </Typography.Text>
        </div>
      )}
      <Form.Item
        label={paymentTypeDiffLabel(paymentType, '付款账户名称')}
        name="accountName"
        rules={[
          {
            required: true,
            message: `请输入${paymentTypeDiffLabel(paymentType, '付款账户名称')}`,
          },
          {
            whitespace: true,
            message: '不能输入空格',
          },
          {
            pattern: validateMerchantName(form.getFieldValue(['paymentTypes'])),
            message: `${paymentTypeDiffLabel(paymentType, '付款账户名称')}格式错误`,
          },
        ]}
      >
        <Input
          placeholder={`请填写${paymentTypeDiffLabel(paymentType, '付款账户名称')}`}
          onBlur={inputChange}
          allowClear
          disabled={showBankBranch}
        />
      </Form.Item>
      {isCompany ? (
        <Form.Item
          label="统一社会信用代码"
          name="businessLicence"
          rules={[
            {
              required: true,
              message: '请输入统一社会信用代码',
            },
            {
              whitespace: true,
              message: '不能输入空格',
            },
            {
              pattern: /^([0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}|[1-9]\d{14})|(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)$/,
              message: '统一社会信用代码格式错误',
            },
          ]}
        >
          <Input placeholder="请填写统一社会信用代码" allowClear disabled={showBankBranch} />
        </Form.Item>
      ) : (
        <>
          {mode === 0 && (paymentType == 0 || paymentType == 2) && (
            <Form.Item
              label="证件类型"
              name="certificateType"
              rules={[
                {
                  required: true,
                  message: '请选择证件类型',
                },
              ]}
            >
              <Select
                showSearch
                options={AddPaymentCertificateTypeData}
                notFoundContent={'请选择证件类型'}
                placeholder={'-请选择-'}
                onChange={value => setCertificateType(value)}
              />
            </Form.Item>
          )}
          <Form.Item
            label="证件号码"
            name="idCard"
            rules={[
              {
                required: true,
                message: '请输入证件号码',
              },
              {
                whitespace: true,
                message: '不能输入空格',
              },
              certificateType === 0
                ? {
                    pattern: /^(^\d{8}(0\d|10|11|12)([0-2]\d|30|31)\d{3}$)|(^\d{6}(18|19|20)\d{2}(0\d|10|11|12)([0-2]\d|30|31)\d{3}(\d|X|x)$)$/,
                    message: '证件号码格式错误',
                  }
                : null,
            ]}
          >
            <Input placeholder="请填写证件号码" allowClear />
          </Form.Item>
        </>
      )}
      {mode === 0 ? (
        <>
          <Form.Item
            label="付款银行"
            name="bankCode"
            rules={[
              {
                required: true,
                message: '请选择对应银行',
              },
            ]}
          >
            <Select
              options={bankOptions}
              showSearch
              filterOption={(input, option) =>
                (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
              }
              placeholder="-请选择-"
              allowClear
              disabled={showBankBranch}
            />
          </Form.Item>
          <Form.Item
            label={paymentTypeDiffLabel(paymentType, '银行账号')}
            name="bankCard"
            rules={[
              {
                required: true,
                message: `请输入${paymentTypeDiffLabel(paymentType, '银行账号')}`,
              },
              {
                whitespace: true,
                message: '不能输入空格',
              },
              {
                pattern: /^([0-9]{1})(\d{5,29})$/,
                message: `${paymentTypeDiffLabel(paymentType, '银行账号')}格式错误`,
              },
            ]}
          >
            <Input
              placeholder={`请填写${paymentTypeDiffLabel(paymentType, '银行账号')}`}
              allowClear
              disabled={showBankBranch}
            />
          </Form.Item>
        </>
      ) : (
        <Form.Item
          label="支付宝账户"
          name="bankCard"
          rules={[
            {
              required: true,
              message: '请输入账户',
            },
            {
              whitespace: true,
              message: '不能输入空格',
            },
            {
              pattern: /(^(?:(?:\+|00)86)?1[3-9]\d{9}$)|(^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$)/,
              message: '支付宝账号格式错误',
            },
            {
              validator: (rule, value, callback) => {
                if (AlipayArr.findIndex(item => item == value) > -1) {
                  message.error(`请填写您本人相关的平台账号！`);
                  callback(`请填写您本人相关的平台账号！`);
                  return;
                }
                callback();
              },
            },
          ]}
        >
          <Input placeholder="请输入支付宝账户" allowClear />
        </Form.Item>
      )}
      {isCompany && (
        <Form.Item
          name="legalPersonName"
          label="法人姓名"
          rules={[
            {
              required: true,
              message: '请输入法人姓名',
            },
            {
              whitespace: true,
              message: '不能输入空格',
            },
          ]}
        >
          <Input placeholder="请输入法人姓名" allowClear disabled={showBankBranch} />
        </Form.Item>
      )}
      {showBankBranch && (
        <Form.Item
          label="付款银行支行"
          name="bankBranchName"
          rules={[
            {
              required: true,
              message: '请输入关键字后选择支行名称',
            },
          ]}
        >
          <Select
            options={bankBranchList}
            showSearch
            filterOption={false}
            onSearch={handleSearch}
            placeholder="请输入关键字后选择支行名称"
            allowClear
            fieldNames={{
              label: 'bankName',
              value: 'bankName',
            }}
          />
        </Form.Item>
      )}
      {!isMerchantSelf && (
        <>
          <Form.Item
            label="付款人与商户关系"
            name="relationShip"
            rules={[
              {
                required: true,
                message: '请选择付款人和商户关系',
              },
            ]}
          >
            <Select
              options={payRelationOptions}
              notFoundContent={'请先选择付款类型'}
              showSearch
              placeholder={'-请选择-'}
              onChange={selectRelationShip}
              allowClear
              disabled={showBankBranch}
            />
          </Form.Item>
          {isSelectOther && (
            <Form.Item
              label="与商户的具体关系"
              name="relationShipInput"
              rules={[
                {
                  required: true,
                  message: '请输入与商户的具体关系',
                },
              ]}
            >
              <Input placeholder="请输入与商户的具体关系" allowClear disabled={showBankBranch} />
            </Form.Item>
          )}
          {/* <Form.Item
            label="燕文收款账户"
            name="accountsReceivable"
            rules={[
              {
                required: true,
                message: '请选择燕文收款账户',
              },
            ]}
          >
            <Select
              options={mode === 0 ? bankAccountOptions : alipayAccountOptions}
              showSearch
              notFoundContent="请选择燕文收款账户"
              placeholder="-请选择-"
              allowClear
            />
          </Form.Item> */}
        </>
      )}
      {certificateType !== 0 && (
        <Form.Item
          label="手机号"
          name="mobile"
          rules={[
            { required: true, message: '请输入手机号' },
            {
              pattern: /^1\d{10}$/,
              message: '请输入正确手机号',
            },
          ]}
        >
          <Input placeholder="请输入手机号" allowClear />
        </Form.Item>
      )}
    </>
  );

  // 第三方渲染
  const otherPayRender = () => (
    <>
      <div
        style={{
          marginTop: '-15px',
          marginBottom: '15px',
        }}
      >
        <Alert
          style={{
            paddingTop: '3px',
            paddingLeft: '10px',
            paddingBottom: '3px',
            background: '#e7f4ff',
            border: '1px solid #c0e1ff',
          }}
          message={
            <p style={{ margin: 0, fontSize: '13px' }}>
              温馨提示：
              <br />
              ①转账/汇款前请提前备案付款账号，未备案无法入账；
              <br />
              ②转账/汇款前付款账号未备案，请务必将
              <span style={{ color: '#FF6666' }}>业务账号+商户名</span>填写在汇款单 “用途/备注/摘要”
              一栏，便于燕文联系您
            </p>
          }
        />
      </div>
      <Form.Item
        label="付款类型"
        name="paymentTypes"
        rules={[
          {
            required: true,
            message: '请选择付款类型',
          },
        ]}
      >
        <Select
          options={paymentTypeOptions}
          showSearch
          filterOption={(input, option) =>
            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
          }
          disabled={paymentTypeOptions.length === 1}
          placeholder={'-请选择-'}
          onChange={selectPaymentType}
        />
      </Form.Item>
      {(paymentType == 1 || paymentType == 3) && (
        <div
          style={{
            paddingLeft: '48px',
            marginTop: '-4px',
            marginBottom: '24px',
          }}
        >
          <Typography.Text
            style={{
              padding: '8px 8px 8px 8px',
              background: '#e7f4ff',
              border: '1px solid #c0e1ff',
              borderRadius: '5px',
            }}
          >
            温馨提示：仅支持大陆企业备案，非大陆企业请联系客服线下备案
          </Typography.Text>
        </div>
      )}
      <Form.Item
        label="平台名称"
        name="platformName"
        rules={[
          {
            required: true,
            message: '请输入平台名称',
          },
          {
            whitespace: true,
            message: '不能输入空格',
          },
        ]}
      >
        <Select
          options={platformOptions}
          showSearch
          filterOption={(input, option) =>
            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
          }
          placeholder={'-请选择平台名称-'}
        />
      </Form.Item>
      <Form.Item
        label="平台账号"
        name="bankCard"
        rules={[
          {
            required: true,
            message: '请输入平台账号',
          },
          {
            whitespace: true,
            message: '不能输入空格',
          },
          {
            max: 50,
            message: '平台账号过于复杂，请输入50位以下字符',
          },
          {
            pattern: /^[a-zA-Z0-9][a-zA-Z0-9.\s-@.]*$/,
            message: '平台账号格式错误',
          },
        ]}
      >
        <Input placeholder="请输入在平台注册的账号" />
      </Form.Item>
      <Form.Item
        label="平台实名认证名称"
        name="accountName"
        rules={[
          {
            required: true,
            message: '请输入平台实名认证名称',
          },
          {
            whitespace: true,
            message: '不能输入空格',
          },
          {
            max: 50,
            message: '平台实名认证名称过于复杂，请输入50位以下字符',
          },
          {
            pattern: /^(^[\u4e00-\u9fa5（）()]*$)|(^[a-zA-Z0-9][a-zA-Z0-9.()（）\s-]*$)$/,
            message: '平台实名认证名称格式错误',
          },
        ]}
      >
        <Input onBlur={inputChange} placeholder={'请输入平台实名认证名称'} />
      </Form.Item>
      {isCompany ? (
        <Form.Item
          label="统一社会信用代码"
          name="businessLicence"
          rules={[
            {
              required: true,
              message: '请输入统一社会信用代码',
            },
            {
              whitespace: true,
              message: '不能输入空格',
            },
            {
              pattern: /^([0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}|[1-9]\d{14})|(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)$/,
              message: '统一社会信用代码格式错误',
            },
          ]}
        >
          <Input placeholder="请填写统一社会信用代码" />
        </Form.Item>
      ) : (
        <>
          <Form.Item
            label="身份证号"
            name="idCard"
            rules={[
              {
                required: true,
                message: '请输入身份证号',
              },
              {
                whitespace: true,
                message: '不能输入空格',
              },
              {
                pattern: /^(^\d{8}(0\d|10|11|12)([0-2]\d|30|31)\d{3}$)|(^\d{6}(18|19|20)\d{2}(0\d|10|11|12)([0-2]\d|30|31)\d{3}(\d|X|x)$)$/,
                message: '身份证号格式错误',
              },
            ]}
          >
            <Input placeholder="请填写证件号码" />
          </Form.Item>
        </>
      )}
      {isCompany && (
        <Form.Item
          name="legalPersonName"
          label="法人姓名"
          rules={[
            {
              required: true,
              message: '请输入法人姓名',
            },
            {
              whitespace: true,
              message: '不能输入空格',
            },
          ]}
        >
          <Input placeholder="请输入法人姓名" />
        </Form.Item>
      )}
      {!isMerchantSelf && (
        <>
          <Form.Item
            label="付款人与商户关系"
            name="relationShip"
            rules={[
              {
                required: true,
                message: '请选择付款人和商户关系',
              },
            ]}
          >
            <Select
              showSearch
              options={payRelationOptions}
              notFoundContent={'请先选择付款类型'}
              placeholder={'-请选择-'}
              onChange={selectRelationShip}
            />
          </Form.Item>
          {isSelectOther && (
            <Form.Item
              label="与商户的具体关系"
              name="relationShipInput"
              rules={[
                {
                  required: true,
                  message: '请输入与商户的具体关系',
                },
              ]}
            >
              <Input placeholder="请输入与商户的具体关系" />
            </Form.Item>
          )}
          {/* <Form.Item
            label="燕文收款账户"
            name="accountsReceivable"
            rules={[
              {
                required: true,
                message: '请选择燕文收款账户',
              },
            ]}
          >
            <Select
              options={bankAccountOptions}
              showSearch
              notFoundContent="请选择燕文收款账户"
              placeholder="-请选择-"
            />
          </Form.Item> */}
        </>
      )}
      <Form.Item
        label="是否有平台协议"
        name="protocol"
        initialValue={isHaveAgree}
        rules={[
          {
            required: true,
            message: '请选择是否有平台协议',
          },
        ]}
      >
        <Radio.Group onChange={e => setIsHaveAgree(e.target.value)}>
          <Radio value={1}>是</Radio>
          <Radio value={0}>否</Radio>
        </Radio.Group>
      </Form.Item>
      {isHaveAgree === 1 && (
        <ProFormUploadButton
          name="platformAgreement"
          label="与平台协议"
          max={1}
          help="提示信息：请上传小于1M的图片或小于5M的文件;上传平台协议之前，请先填写平台账号"
          action="/csc/file/upload/attachment"
          title="上传"
          fieldProps={{
            name: 'attach',
            listType: 'picture-card',
            data: {
              objName: 'thirdAgreement',
              objId: bankCardId,
              appId: 'customerinfo',
              singleFile: false,
            },
            beforeUpload: file => beforeUploadFunc(file),
            onChange: info => handleOnChange(info, 'platformAgreement'),
            onPreview: async file => {
              if (!file.url && !file.preview) {
                file.preview = await getBase64(file.originFileObj);
              }
              setPreviewImage(file.url || file.preview);
              setPreviewOpen(true);
            },
          }}
          rules={[
            {
              required: true,
              message: '图片不能为空',
            },
          ]}
        />
      )}
      <ProFormUploadButton
        name="digitalReceipt"
        label="电子回单"
        max={1}
        help={[
          '提示信息：①请先充值到燕文收款账号，再上传电子回单',
          '②请上传小于1M的图片或小于5M的文件',
        ]}
        action="/csc/file/upload/attachment"
        title="上传"
        fieldProps={{
          name: 'attach',
          listType: 'picture-card',
          data: {
            objName: 'digitalReceipt',
            objId: bankCardId,
            appId: 'customerinfo',
            singleFile: false,
          },
          beforeUpload: file => beforeUploadFunc(file),
          onChange: info => handleOnChange(info, 'digitalReceipt'),
          onPreview: async file => {
            if (!file.url && !file.preview) {
              file.preview = await getBase64(file.originFileObj);
            }
            setPreviewImage(file.url || file.preview);
            setPreviewOpen(true);
          },
        }}
        rules={[
          {
            required: true,
            message: '图片不能为空',
          },
        ]}
        addonAfter={
          <div className="flex items-end">
            <a
              className="underline underline-offset-4"
              style={{
                textDecoration: 'underline',
              }}
              onClick={() => {
                modalDemoRef.current?.open({
                  url: ElectronicReceiptDemo,
                  params: {
                    personOrLegal: 'person',
                    area: '',
                    desc: false,
                  },
                });
              }}
            >
              (查看示例)
            </a>
          </div>
        }
      />
      <ModalDemoImage modalRef={modalDemoRef}></ModalDemoImage>
    </>
  );

  // 公用渲染
  const commonRender = () => {
    switch (+mode) {
      case 0: // 选择银行,支付宝
        return bankAndAliPayRender();
      case 1:
        return bankAndAliPayRender();
      case 2: // 选择第三方平台
        return otherPayRender();
    }
  };

  const sendMessage = async callback => {
    dispatch({
      type: `myProfile/sendSmsCode`,
      payload: { type: 36 },
      callback: response => {
        if (!response.success) {
          message.error(response.message);
        } else {
          if (callback) callback();
        }
      },
    });
  };

  // 校验支付宝账号
  const checkAliAccount = (params, callback) => {
    if (params?.paymentType == 1) {
      dispatch({
        type: 'paymentAccount/alipayCheck',
        payload: params,
        callback: response => {
          if (response.success) {
            if (callback) callback();
          } else {
            if (response.code == -1) {
              message.error(response.message);
            } else if (response.code == -2) {
              Modal.confirm({
                title: '温馨提示',
                content: response.message,
                okText: '是',
                cancelText: '否',
                onOk() {
                  if (callback) callback();
                },
                onCancel() {
                  handleCancel();
                },
              });
            }
          }
        },
      });
    } else {
      if (callback) callback();
    }
  };

  // 提交
  const handleSubmit = async values => {
    try {
      let paramsValues = values?.verifyPhone
        ? { ...values, ...(await form?.validateFields()) }
        : values;
      // ... 提交完成后的操作
      const params = {
        ...paramsValues,
        businessTypes:
          mode === 1
            ? [values?.businessTypes]
            : typeof values?.businessTypes === 'string'
            ? [values?.businessTypes]
            : values?.businessTypes,
        // aliPayCard: mode === 1 ? values?.bankCard : undefined,
        // bankCard: mode === 0 ? values?.bankCard : undefined,
        paymentTopicType: values?.paymentTypes,
        platformAgreement: values?.platformAgreement?.[0]?.url?.split('?')?.[0] ?? undefined,
        digitalReceipt: values?.digitalReceipt?.[0]?.url?.split('?')?.[0] ?? undefined,
        depositBank:
          mode === 0
            ? bankOptions?.find(item => item.value === values?.bankCode)?.label
            : undefined,
        platformName:
          mode === 1
            ? '支付宝'
            : platformOptions?.find(item => item.value === values?.platformName)?.label ??
              undefined,
        relationShip:
          values?.accountName === returnPersonNameOrCompanyName(merchantDetail)
            ? otherOption
            : !isSelectOther
            ? values.relationShip
            : values.relationShipInput,
        flowId,
      };
      // 企业银行卡走另外的方法
      if (
        mode === 0 &&
        (values?.paymentTypes == 1 || values?.paymentTypes == 3) &&
        !showBankBranch
      ) {
        dispatch({
          type: 'paymentAccount/businessVerification',
          payload: {
            ...params,
            companyName: params?.accountName,
            legalName: params?.legalPersonName,
            businessLicense: params?.businessLicence,
          },
          callback: response => {
            if (response.success) {
              message.success('提交成功,请选择银行支行');
              // getBankBranch(
              //   response?.data?.flowId,
              //   bankOptions?.find(item => item.value === values?.bankCode)?.label
              // );
              setFlowId(response?.data?.flowId);
              setShowBankBranch(true);
              // handleCancel({
              //   ...values,
              //   id: response?.data,
              // });
            }
          },
        });
      } else {
        checkAliAccount({ ...params, businessType: params?.businessTypes?.[0] }, () => {
          dispatch({
            type: 'paymentAccount/create',
            payload: params,
            callback: response => {
              if (response.success) {
                if (params?.certificateType !== undefined && params?.certificateType !== 0) {
                  if (!params?.verifyPhone) {
                    setFlowId(response?.data);
                    return verifyAdminPhoneRef.current?.showModal({
                      phone: params?.mobile,
                      title: '验证手机号',
                    });
                  }
                }
                handleCancel({
                  ...params,
                  businessType: params?.businessTypes?.[0],
                  id: response?.data,
                  flowId,
                });
              }
            },
          });
        });
      }
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <Modal
      title={
        <span>
          新增付款账户
          <span className="text-xs text-red-900 ml-1">
            {showBankBranch ? '企业信息校验成功，请选择支行进行对公银行信息验证' : ''}
          </span>
        </span>
      }
      open={open}
      footer={null}
      onCancel={() => handleCancel()}
      destroyOnClose
    >
      <Form form={form} labelAlign="right" labelCol={{ flex: '130px' }} onFinish={handleSubmit}>
        <Form.Item
          label="业务类型"
          name="businessTypes"
          rules={[{ required: true, message: '请选择业务类型' }]}
        >
          <Select
            options={addBusinessTypeList}
            // disabled={mode === 1}
            allowClear
          />
        </Form.Item>
        <Form.Item
          label="付款方式"
          name="paymentType"
          initialValue={mode}
          rules={[{ required: true, message: '请选择付款方式' }]}
        >
          <Radio.Group
            onChange={handleModeChange}
            size="small"
            style={{ marginBottom: 8 }}
            disabled={showBankBranch}
          >
            <Radio.Button value={0}>银 行</Radio.Button>
            <Radio.Button value={1}>支付宝</Radio.Button>
            <Radio.Button value={2}>第三方平台</Radio.Button>
          </Radio.Group>
        </Form.Item>
        {commonRender()}
        <Row justify="center">
          <Space>
            <Button onClick={() => handleCancel()}>取消</Button>
            <Button type="primary" htmlType="submit" loading={createLoading}>
              提交
            </Button>
          </Space>
        </Row>
      </Form>
      {previewOpen && (
        <ModalImage open={previewOpen} url={previewImage} onCancel={() => setPreviewOpen(false)} />
      )}
      <VerifyAdminPhoneModal
        {...props}
        modalRef={verifyAdminPhoneRef}
        needLogout={false}
        noSendSms
        phoneTitle="验证手机号"
        onSubmit={handleSubmit}
        onCancel={() => {
          form.resetFields();
          setAddBusinessTypeList(businessTypeList);
          setMode(0);
          setFlowId(undefined);
          setShowBankBranch(false);
          setBankBranchList([]);
          setCertificateType(0);
          timeout = undefined;
          currentValue = undefined;
        }}
      />
    </Modal>
  );
};

export default AddPaymentAccountForm;
