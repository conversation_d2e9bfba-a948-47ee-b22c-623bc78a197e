import React, { useState, useImperativeHandle, MutableRefObject, useRef } from 'react';
import {
  FormInstance,
  Modal,
  Form,
  Input,
  Select,
  Radio,
  message,
  Space,
  Button,
  Row,
  Upload,
  Alert,
} from 'antd';
import { ProFormUploadButton } from '@ant-design/pro-components';
import {
  changeBeforeUpload,
  setUploadImage,
  getBase64,
  returnMerchantType,
  returnPersonNameOrCompanyName,
  validateMerchantName,
  paymentTypeDiffLabel,
  checkCompanyNameRegex,
} from '@/utils/utils';
import {
  renderSelectOptions,
  relationOption,
  otherPayRelationArray,
  PAY_RELATION_MAP,
  otherOption,
  //   DEFAULT_PAY_RELATION_MAP,
} from '@/utils/commonConstant';
import ModalImage from '@/components/ModalImage';
import moment from 'moment';
import ModalDemoImage from '@/components/ModalDemoImage';
import ElectronicReceiptDemo from '@/assets/electronicReceiptDemo.png';

type MakeUpModalRef = {
  showModal: (record: any) => void;
};

type TProps = {
  dispatch: Function;
  makeUpModalRef: MutableRefObject<MakeUpModalRef>;
  form: FormInstance;
  onCancel: (value?: any) => void;
  merchantDetail: any;
  bankAccountOptions: Array<any>;
  alipayAccountOptions: Array<any>;
  bankOptions: Array<any>;
  platformOptions: Array<any>;
  businessType: string;
};

let timeout;
let currentValue;

/*
 *@Description: 补录付款账户
 *@MethodAuthor: dangh
 *@Date: 2023-05-12 10:05:44
 */
const MakeUpPaymentAccount = ({
  dispatch,
  makeUpModalRef,
  form,
  onCancel,
  merchantDetail,
  bankAccountOptions,
  alipayAccountOptions,
  bankOptions,
  platformOptions,
  businessType,
}: TProps) => {
  const [open, setOpen] = useState(false);
  const [paymentTypeOptions, setPaymentTypeOptions] = useState([]); // 付款类型
  const [payRelationOptions, setPayRelationOptions] = useState([]); // 付款人与商户关系
  const [isMerchantSelf, setIsMerchantSelf] = useState(false); // 是否是商户本身
  const [isSelectOther, setIsSelectOther] = useState(false); // 是否选择其他
  const [isCompany, setIsCompany] = useState(false); // 是否是企业
  const [isHaveAgree, setIsHaveAgree] = useState(1); // 是否同意协议
  const [previewOpen, setPreviewOpen] = useState(false); // 是否显示打开链接
  const [previewImage, setPreviewImage] = useState<any>(); // 打开链接
  const [bankCardId, setBankCardId] = useState(); // 用于文件上传id使用
  const [flowId, setFlowId] = useState(); // 用于新增商户类型使用
  const [showBankBranch, setShowBankBranch] = useState(false); // 是否显示支行信息
  const [bankBranchList, setBankBranchList] = useState([]); // 支行信息

  const [paymentInfo, setPaymentInfo] = useState<{
    [key: string]: any;
  }>({
    paymentType: undefined,
    id: undefined,
  });
  const [paymentType, setPaymentType] = useState(); // 付款类型  0、2 个人付款  1、3企业付款
  const modalDemoRef = useRef();

  useImperativeHandle(makeUpModalRef, () => ({
    showModal: (record: any) => {
      initialFunc(record);
    },
  }));

  const initialFunc = record => {
    setPaymentInfo({
      ...paymentInfo,
      ...record,
    });

    const key = `${record?.paymentType}-${returnMerchantType(merchantDetail)}`;
    const isHimself = record?.accountName === returnPersonNameOrCompanyName(merchantDetail);
    const paymentTypeArray = PAY_RELATION_MAP[key]; // 用于渲染付款类型使用
    // const defaultType = DEFAULT_PAY_TYPE_MAP[key]; // 用于回显付款方式使用
    const relationList = isHimself
      ? otherPayRelationArray[0].relation
      : paymentTypeArray?.find(item => item.value === record?.paymentTopicType)?.relation ?? []; // 用于渲染付款人与商户关系使用
    setPayRelationOptions(renderSelectOptions(relationList, { label: 'title', value: 'value' }));
    setPaymentTypeOptions(
      renderSelectOptions(paymentTypeArray, { label: 'title', value: 'value' })
    );
    setIsMerchantSelf(isHimself);
    setIsCompany(record?.paymentTopicType == 3 || record?.paymentTopicType == 1);
    setIsHaveAgree(+record?.protocol);
    setPaymentType(record?.paymentTopicType);
    setTimeout(() => {
      form.setFieldsValue({
        ...record,
        protocol: +record?.protocol,
        paymentTypes: record?.paymentTopicType,
        platformAgreement: setUploadImage(record?.platformAgreement),
        digitalReceipt: setUploadImage(record?.digitalReceipt),
        relationShip:
          record?.paymentType === 2 &&
          record?.accountName === returnPersonNameOrCompanyName(merchantDetail)
            ? otherOption
            : record?.relationShip,
      });
    }, 300);
    setOpen(true);
  };

  const handleCancel = (value?: any) => {
    form.resetFields();
    setOpen(false);
    setShowBankBranch(false);
    setBankBranchList([]);
    setFlowId(undefined);
    timeout = null;
    currentValue = null;
    if (value) {
      onCancel(value);
    }
  };

  // 根据输入的内容查询支行
  const handleSearch = newValue => {
    if (newValue) {
      // const values = form.getFieldsValue(['bankCode']);
      // const value = `${newValue}${
      //   bankOptions?.find(item => item.value === values?.bankCode)?.label
      // }`;
      const value = newValue;
      getBankBranch(value, setBankBranchList);
    } else {
      setBankBranchList([]);
    }
  };

  // 查询支行信息
  const getBankBranch = (keyword, callback) => {
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }
    currentValue = keyword;
    const request = () => {
      dispatch({
        type: 'paymentAccount/branchBankList',
        payload: {
          flowId,
          keyword,
        },
        callback: response => {
          if (response.success) {
            if (currentValue === keyword) {
              callback(response.data);
            }
          }
        },
      });
    };
    timeout = setTimeout(request, 300);
  };

  // 上传文件处理数据逻辑
  const handleOnChange = (info, type) => {
    if (info.file.status === 'uploading') {
      return;
    }
    if (info.file.status === 'done') {
      // Get this url from response in real world.
      if (info.file.response.success) {
        const uploadList = form.getFieldValue([type]);
        const url = info.file.response.data;
        const data = uploadList.map(item => {
          item.url = url;
          return item;
        });
        form.setFieldsValue({
          [type]: data,
        });
      } else {
        form.resetFields([type]);
        message.error(info.file.response.message);
      }
    }
  };

  // 选择付款类型
  const selectPaymentType = value => {
    const data = form.getFieldsValue(['accountName']);
    setPaymentType(value);
    if (paymentInfo?.paymentType === 2) {
      // 选择第三方
      let array = null;
      if (value === 2 && returnMerchantType(merchantDetail) === '企业') {
        array = handleCompanyRelationOptions(data?.accountName, merchantDetail.corporateName);
        setIsMerchantSelf(false);
        setIsCompany(false);
      } else if (data?.accountName === returnPersonNameOrCompanyName(merchantDetail)) {
        array = otherPayRelationArray[0].relation;
        setIsMerchantSelf(true);
        setIsCompany(value == 1 || value == 3);
      } else {
        array = findPaymentTypeOption(value)?.relation;
        setIsMerchantSelf(false);
        setIsCompany(returnMerchantType(merchantDetail) === '企业' || value == 1 || value == 3);
      }
      setPayRelationOptions(renderSelectOptions(array, { label: 'title', value: 'value' }));
    } else {
      // 选择银行或者支付宝
      let relation = null;
      if (data?.accountName === returnPersonNameOrCompanyName(merchantDetail)) {
        relation = [];
        setIsMerchantSelf(true);
      } else if (value === 2 && returnMerchantType(merchantDetail) === '企业') {
        relation = handleCompanyRelationOptions(data?.accountName, merchantDetail.corporateName);
        setIsMerchantSelf(false);
        setIsCompany(false);
      } else {
        relation = findPaymentTypeOption(value)?.relation;
        setIsMerchantSelf(false);
        setIsCompany(returnMerchantType(merchantDetail) === '企业' || value == 1 || value == 3);
      }
      setPayRelationOptions(renderSelectOptions(relation, { label: 'title', value: 'value' }));
    }
  };

  // 选择付款人与商户关系
  const selectRelationShip = value => {
    setIsSelectOther(value === '其他');
    form.resetFields(['relationShipInput']);
  };

  const findPaymentTypeOption = value => {
    return paymentTypeOptions.find(item => item.value === value);
  };

  // 选择付款类型时处理商户是企业的逻辑
  const handleCompanyRelationOptions = (data, corporateName) => {
    if (data === corporateName) {
      return [
        {
          label: '法定代表人',
          value: '法定代表人',
        },
      ];
    }
    return renderSelectOptions(relationOption, { label: 'title', value: 'value' });
  };

  //输入用户名时处理商户是否是本人之类
  const inputChangeBu = () => {
    form.resetFields(['relationShip', 'relationShipInput']);
    const data = form.getFieldsValue(['accountName', 'paymentTypes']);
    if (paymentInfo?.paymentType === 2) {
      form.setFieldsValue({
        relationShip:
          data?.accountName === returnPersonNameOrCompanyName(merchantDetail)
            ? otherOption
            : undefined,
      });
    } else {
      form.setFieldsValue({
        relationShip: data.accountName === merchantDetail.corporateName ? '法定代表人' : undefined,
      });
    }

    if (paymentInfo?.paymentType === 2) {
      // 选择第三方
      let array = null;
      if (data?.accountName === returnPersonNameOrCompanyName(merchantDetail)) {
        array = otherPayRelationArray[0].relation;
        setIsMerchantSelf(true);
      } else if (data?.paymentTypes === 2 && returnMerchantType(merchantDetail) === '企业') {
        array = handleCompanyRelationOptions(data?.accountName, merchantDetail.corporateName);
        setIsMerchantSelf(false);
      } else {
        array = findPaymentTypeOption(data?.paymentTypes)?.relation;
        setIsMerchantSelf(false);
      }
      setPayRelationOptions(renderSelectOptions(array, { label: 'title', value: 'value' }));
    } else {
      // 选择银行或者支付宝
      let relation = null;
      if (data?.accountName === returnPersonNameOrCompanyName(merchantDetail)) {
        relation = [];
        setIsMerchantSelf(true);
      } else if (data?.paymentTypes === 2 && returnMerchantType(merchantDetail) === '企业') {
        relation = handleCompanyRelationOptions(data?.accountName, merchantDetail.corporateName);
        setIsMerchantSelf(false);
      } else {
        relation = findPaymentTypeOption(data?.paymentTypes)?.relation;
        setIsMerchantSelf(false);
      }
      setPayRelationOptions(renderSelectOptions(relation, { label: 'title', value: 'value' }));
    }
  };

  const getBankCardId = values =>
    new Promise(resolve => {
      dispatch({
        type: 'paymentAccount/getBankCardId',
        payload: {
          paymentTopicType: values?.paymentTypes,
          bankCard: values?.bankCard,
          paymentType: 2,
          accountName: values?.accountName,
          businessType: paymentInfo?.businessType,
          businessTypes: paymentInfo?.businessTypes,
        },
        callback: response => {
          if (response.success) {
            setBankCardId(response.data);
            resolve(response.data);
          } else {
            resolve(null);
          }
        },
      });
    });

  const beforeUploadFunc = async file => {
    try {
      const values = await form.validateFields(['paymentTypes', 'bankCard', 'accountName']);
      const bankCardId = await getBankCardId(values);
      if (bankCardId) {
        return changeBeforeUpload(
          file,
          5,
          ['image/jpeg', 'image/png'],
          '请上传小于1M的图片或小于5M的文件',
          1
        );
      } else {
        return false || Upload.LIST_IGNORE;
      }
    } catch (error) {
      return false || Upload.LIST_IGNORE;
    }
  };

  // 补齐提交时的公用方法
  const confirmPolishing = values => {
    let param = null;
    // 是否是客服发起补齐
    const isCustomerServiceMakeUp = paymentInfo?.applicationType === '3';
    if (paymentInfo?.paymentType === 0) {
      // 银行
      param = {
        id: paymentInfo?.id,
        depositBank: bankOptions.find(item => item.value === values.bankCode)?.label || '',
        paymentTopicType: values.paymentTypes,
        paymentType: paymentInfo?.paymentType,
        bankCard: values?.bankCard,
        originalAccountName: paymentInfo?.accountName,
        accountName: values.accountName,
        bankCode: values.bankCode,
        idCard: values.idCard,
        payType: values?.payType,
        relationShip:
          values?.accountName === returnPersonNameOrCompanyName(merchantDetail)
            ? otherOption
            : !isSelectOther
            ? values.relationShip
            : values.relationShipInput,
        // accountsReceivable: values.accountsReceivable,
        businessLicence: values.businessLicence,
        legalPersonName: !isCompany ? '' : values.legalPersonName,
        applicationType: paymentInfo?.applicationType ? paymentInfo?.applicationType : '1',
        bankCardId: paymentInfo?.bankCardId ?? ' ',
        accountTime: paymentInfo?.createTime
          ? moment(paymentInfo?.createTime).format('YYYY-MM-DD')
          : moment().format('YYYY-MM-DD'),
        businessType: paymentInfo?.businessType,
        flowId,
        bankBranchName: values?.bankBranchName,
      };
    } else if (paymentInfo?.paymentType === 1) {
      // 支付宝
      param = {
        id: paymentInfo?.id,
        paymentTopicType: values.paymentTypes,
        paymentType: paymentInfo?.paymentType,
        bankCard: values?.bankCard,
        originalAccountName: paymentInfo?.accountName,
        accountName: values.accountName,
        bankCode: '',
        idCard: values.idCard,
        payType: values?.payType,
        relationShip:
          values?.accountName === returnPersonNameOrCompanyName(merchantDetail)
            ? otherOption
            : !isSelectOther
            ? values.relationShip
            : values.relationShipInput,
        bankCardId: paymentInfo?.bankCardId ?? ' ',
        businessLicence: values.businessLicence,
        legalPersonName: !isCompany ? '' : values.legalPersonName,
        applicationType: paymentInfo?.applicationType ? paymentInfo?.applicationType : '1',
        platformName: '支付宝',
        accountTime: paymentInfo?.createTime
          ? moment(paymentInfo?.createTime).format('YYYY-MM-DD')
          : moment().format('YYYY-MM-DD'),
        businessType: paymentInfo?.businessType,
      };
    } else {
      // 第三方
      param = {
        id: paymentInfo?.id,
        paymentTopicType: values.paymentTypes,
        paymentType: paymentInfo?.paymentType,
        bankCard: values?.bankCard, // 平台账号
        originalAccountName: paymentInfo?.accountName,
        accountName: values.accountName, // 平台实名认证
        idCard: values.idCard,
        payType: values?.paymentTypes,
        relationShip:
          values?.accountName === returnPersonNameOrCompanyName(merchantDetail)
            ? otherOption
            : !isSelectOther
            ? values.relationShip
            : values.relationShipInput,
        platformName: values.platformName, // 平台名称
        // accountsReceivable: values.accountsReceivable,
        businessLicence: values.businessLicence,
        legalPersonName: !isCompany ? '' : values.legalPersonName,
        applicationType: paymentInfo?.applicationType ? paymentInfo?.applicationType : '1',
        bankCardId: paymentInfo?.bankCardId ?? ' ',
        platformAgreement:
          values.protocol === 1 ? values?.platformAgreement?.[0].url.split('?')[0] : undefined, // 平台协议
        digitalReceipt: values?.digitalReceipt?.[0].url.split('?')[0], // 电子回单
        accountTime: paymentInfo?.createTime
          ? moment(paymentInfo?.createTime).format('YYYY-MM-DD')
          : moment().format('YYYY-MM-DD'),
        protocol: values.protocol + '',
        businessType: paymentInfo?.businessType,
      };
    }
    if (
      paymentInfo?.paymentType === 0 &&
      (values.paymentTypes == 1 || values.paymentTypes == 3) &&
      !showBankBranch
    ) {
      dispatch({
        type: 'paymentAccount/businessVerification',
        payload: {
          ...param,
          companyName: param?.accountName,
          legalName: param?.legalPersonName,
          businessLicense: param?.businessLicence,
        },
        callback: response => {
          if (response.success) {
            message.success('提交成功,请选择银行支行');
            // getBankBranch(
            //   response?.data?.flowId,
            //   bankOptions?.find(item => item.value === values?.bankCode)?.label
            // );
            setFlowId(response?.data?.flowId);
            setShowBankBranch(true);
            // handleCancel({
            //   ...values,
            //   id: response?.data,
            // });
          }
        },
      });
    } else {
      dispatch({
        type: `paymentAccount/${isCustomerServiceMakeUp ? 'serviceSubmit' : 'customerUpdate'}`,
        payload: param,
        callback: result => {
          if (result.success) {
            if (paymentInfo?.paymentType === 1) {
              handleCancel({
                ...param,
                id: result?.data?.id,
                paymentType: paymentInfo?.paymentType,
              });
            } else if (
              paymentInfo?.paymentType === 0 &&
              returnPersonNameOrCompanyName(merchantDetail) !== values.accountName
            ) {
              handleCancel({
                ...param,
                id: result?.data?.id,
                paymentType: paymentInfo?.paymentType,
                flowId,
              });
            } else {
              handleCancel({
                ...param,
                id: result?.data?.id,
                paymentType: paymentInfo?.paymentType,
                flowId,
              });
            }
          }
        },
      });
    }
  };

  // 补齐提交
  const handleSubmit = values => {
    const params = {
      ...values,
      businessType: paymentInfo?.businessType,
      paymentTopicType: values.paymentTypes,
      platformAgreement:
        values.protocol === 1 ? values?.platformAgreement?.[0]?.url?.split('?')[0] : undefined, // 平台协议
      digitalReceipt: values?.digitalReceipt?.[0]?.url?.split('?')[0], // 电子回单
      relationShip:
        values?.accountName === returnPersonNameOrCompanyName(merchantDetail)
          ? otherOption
          : !isSelectOther
          ? values.relationShip
          : values.relationShipInput,
    };
    // if (paymentInfo?.paymentType === 2 && paymentInfo?.id) {
    //   handleReverseDesensitization(values, res => {
    //     if (res.success) {
    //       // 第三方补齐
    //       // 是否是客服发起补齐
    //       const isCustomerServiceMakeUp =
    //         paymentInfo?.applicationType === '3' && paymentInfo?.processStatus === '0';
    //       dispatch({
    //         type: `paymentAccount/${isCustomerServiceMakeUp ? 'serviceSubmit' : 'customerUpdate'}`,
    //         payload: {
    //           ...params,
    //           bankCard: res?.data?.bankCard,
    //         },
    //         callback: response => {
    //           if (response.success) {
    //             handleCancel({
    //               ...params,
    //               paymentType: paymentInfo?.paymentType,
    //               bankCard: res?.data?.bankCard,
    //             });
    //           }
    //         },
    //       });
    //     }
    //   });
    // } else
    if (paymentInfo?.paymentType === 2) {
      handleReverseDesensitization(values, res => {
        if (res.success) {
          confirmPolishing({ ...values, bankCard: res?.data?.bankCard });
        }
      });
    } else {
      // 银行和支付宝补齐
      handleReverseDesensitization(values, res => {
        if (res.success) {
          // if (values.accountName === returnPersonNameOrCompanyName(merchantDetail)) {
          //   message.warning('填写的账户名称不能与当前商户名称一致，如有疑问请联系客服');
          //   return;
          // }
          confirmPolishing({ ...values, bankCard: res?.data?.bankCard });
        }
      });
    }
  };

  // 调取脱密接口
  const handleReverseDesensitization = (values, callback) => {
    dispatch({
      type: 'paymentAccount/reverseDesensitization',
      payload: {
        id: paymentInfo?.id,
        bankName: paymentInfo.accountName,
        bankCard: values.bankCard,
        source: paymentInfo?.source,
        paymentType: paymentInfo?.paymentType,
        businessType: businessType,
        alipayCode: paymentInfo?.alipayCode,
        bankCardId: paymentInfo?.bankCardId,
      },
      callback: res => {
        if (res.success) {
          if (callback) callback(res);
          // if (values.accountName === returnPersonNameOrCompanyName(merchantDetail)) {
          //   message.warning('填写的账户名称不能与当前商户名称一致，如有疑问请联系客服');
          //   return;
          // }
          // confirmPolishing({ ...values, bankCard: res?.data?.bankCard });
        }
      },
    });
  };

  const bankAndAliMakeUpRender = () => (
    <>
      {paymentInfo?.paymentType !== 2 && (
        <Form.Item
          label={paymentTypeDiffLabel(paymentType, '付款账户名称')}
          name="accountName"
          rules={[
            {
              required: true,
              message: `请填写${paymentTypeDiffLabel(paymentType, '付款账户名称')}`,
            },
            {
              pattern: validateMerchantName(form.getFieldValue('paymentTypes')),
              message: `${paymentTypeDiffLabel(paymentType, '付款账户名称')}格式错误`,
            },
          ]}
        >
          <Input onBlur={inputChangeBu} disabled={showBankBranch} />
        </Form.Item>
      )}

      {paymentInfo?.paymentType === 0 && (
        <Form.Item
          label="银行"
          name="bankCode"
          rules={[
            {
              required: true,
              message: '请选择对应银行',
            },
          ]}
        >
          <Select
            options={bankOptions}
            showSearch
            filterOption={(input, option) =>
              (option?.label?.toLowerCase() ?? '').includes(input.toLowerCase())
            }
            placeholder="-请选择-"
            disabled={showBankBranch}
          />
        </Form.Item>
      )}
      {isCompany ? (
        <>
          <Form.Item
            name="businessLicence"
            label="统一社会信用代码"
            rules={[
              {
                required: true,
                message: '请输入统一社会信用代码',
              },
              {
                whitespace: true,
                message: '不能输入空格',
              },
              {
                pattern: /^([0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}|[1-9]\d{14})|(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)$/,
                message: '统一社会信用代码格式错误',
              },
            ]}
          >
            <Input placeholder="请填写统一社会信用代码" disabled={showBankBranch} />
          </Form.Item>
          <Form.Item
            name="legalPersonName"
            label="法人姓名"
            rules={[
              {
                required: true,
                message: '请填写法人姓名',
              },
              {
                whitespace: true,
                message: '不能输入空格',
              },
            ]}
          >
            <Input disabled={showBankBranch} />
          </Form.Item>
        </>
      ) : (
        <Form.Item
          name="idCard"
          label="身份证号"
          rules={[
            {
              required: true,
              message: '请输入身份证号',
            },
            {
              whitespace: true,
              message: '不能输入空格',
            },
            {
              pattern: /^(^\d{8}(0\d|10|11|12)([0-2]\d|30|31)\d{3}$)|(^\d{6}(18|19|20)\d{2}(0\d|10|11|12)([0-2]\d|30|31)\d{3}(\d|X|x)$)$/,
              message: '身份证号格式错误',
            },
          ]}
        >
          <Input placeholder="请填写身份证号" />
        </Form.Item>
      )}
      {showBankBranch && (
        <Form.Item
          label="付款银行支行"
          name="bankBranchName"
          rules={[
            {
              required: true,
              message: '请输入关键字后选择支行名称',
            },
          ]}
        >
          <Select
            options={bankBranchList}
            showSearch
            filterOption={false}
            onSearch={handleSearch}
            placeholder="请输入关键字后选择支行名称"
            allowClear
            fieldNames={{
              label: 'bankName',
              value: 'bankName',
            }}
          />
        </Form.Item>
      )}
    </>
  );

  return (
    <Modal
      title={
        <span>
          补齐付款账户
          <span className="text-xs text-red-900 ml-1">
            {showBankBranch ? '企业信息校验成功，请选择支行进行对公银行信息验证' : ''}
          </span>
        </span>
      }
      open={open}
      footer={null}
      onCancel={() => handleCancel()}
      destroyOnClose
    >
      <Form
        form={form}
        labelAlign="right"
        labelCol={{
          flex: '130px',
        }}
        onFinish={handleSubmit}
      >
        <Form.Item
          label={
            paymentInfo?.paymentType === 2
              ? '平台账号'
              : paymentInfo?.paymentType === 1
              ? paymentTypeDiffLabel(paymentType, '支付宝账号')
              : paymentTypeDiffLabel(paymentType, '银行账号')
          }
          name="bankCard"
          rules={[
            {
              required: true,
              message:
                paymentInfo?.paymentType === 2
                  ? '请输入平台账号'
                  : paymentInfo?.paymentType === 1
                  ? `请输入${paymentTypeDiffLabel(paymentType, '支付宝账号')}`
                  : `请输入${paymentTypeDiffLabel(paymentType, '银行账号')}`,
            },
          ]}
        >
          <Input disabled={true} />
        </Form.Item>
        {paymentInfo?.paymentType === 2 && (
          <>
            <div
              style={{
                marginTop: '-15px',
                marginBottom: '15px',
              }}
            >
              <Alert
                style={{
                  paddingTop: '3px',
                  paddingLeft: '10px',
                  paddingBottom: '3px',
                  background: '#e7f4ff',
                  border: '1px solid #c0e1ff',
                }}
                message={
                  <p style={{ margin: 0, fontSize: '13px' }}>
                    温馨提示：
                    <br />
                    ①转账/汇款前请提前备案付款账号，未备案无法入账；
                    <br />
                    ②转账/汇款前付款账号未备案，请务必将
                    <span style={{ color: '#FF6666' }}>业务账号+商户名</span>填写在汇款单
                    “用途/备注/摘要” 一栏，便于燕文联系您
                  </p>
                }
                type="success"
              />
            </div>
            <Form.Item
              label="平台名称"
              name="platformName"
              rules={[
                {
                  required: true,
                  message: '请输入平台名称',
                },
              ]}
            >
              <Select options={platformOptions} showSearch placeholder={'-请选择平台名称-'} />
            </Form.Item>
            <Form.Item
              label="平台实名认证"
              name="accountName"
              rules={[
                {
                  required: true,
                  message: '请输入在平台注册的账号',
                },
                {
                  pattern: checkCompanyNameRegex,
                  message: '平台实名认证名称格式错误',
                },
              ]}
            >
              <Input onBlur={inputChangeBu} />
            </Form.Item>
          </>
        )}
        <Form.Item
          label="付款类型"
          name="paymentTypes"
          rules={[
            {
              required: true,
              message: '请选择付款类型',
            },
          ]}
        >
          <Select
            options={paymentTypeOptions}
            showSearch
            disabled={paymentTypeOptions.length === 1 || showBankBranch}
            placeholder={'-请选择-'}
            onChange={selectPaymentType}
          />
        </Form.Item>
        {paymentInfo?.paymentType !== undefined && bankAndAliMakeUpRender()}
        {!isMerchantSelf && (
          <>
            <Form.Item
              label="付款人与商户关系"
              name="relationShip"
              rules={[
                {
                  required: true,
                  message: '请选择付款人和商户关系',
                },
              ]}
            >
              <Select
                options={payRelationOptions}
                notFoundContent={'请先选择付款类型'}
                showSearch
                placeholder={'-请选择-'}
                onChange={selectRelationShip}
                disabled={showBankBranch}
              />
            </Form.Item>
            {isSelectOther && (
              <Form.Item
                label="与商户的具体关系"
                name="relationShipInput"
                rules={[
                  {
                    required: true,
                    message: '请输入与商户的具体关系',
                  },
                ]}
              >
                <Input placeholder="请输入与商户的具体关系" disabled={showBankBranch} />
              </Form.Item>
            )}
            {/* <Form.Item
              label="燕文收款账户"
              name="accountsReceivable"
              rules={[
                {
                  required: true,
                  message: '请选择燕文收款账户',
                },
              ]}
            >
              <Select
                options={paymentInfo?.paymentType !== 2 ? alipayAccountOptions : bankAccountOptions}
                showSearch
                notFoundContent="请选择燕文收款账户"
                placeholder="-请选择-"
              />
            </Form.Item> */}
          </>
        )}
        {paymentInfo?.paymentType === 2 && (
          <>
            <Form.Item
              label="是否有平台协议"
              name="protocol"
              initialValue={isHaveAgree}
              rules={[
                {
                  required: true,
                  message: '请选择是否有平台协议',
                },
              ]}
            >
              <Radio.Group onChange={e => setIsHaveAgree(e.target.value)}>
                <Radio value={1}>是</Radio>
                <Radio value={0}>否</Radio>
              </Radio.Group>
            </Form.Item>
            {isHaveAgree === 1 && (
              <ProFormUploadButton
                name="platformAgreement"
                label="与平台协议"
                max={1}
                help="提示信息：请上传小于1M的图片或小于5M的文件;上传平台协议之前，请先填写平台账号"
                action="/csc/file/upload/attachment"
                title="上传"
                fieldProps={{
                  name: 'attach',
                  listType: 'picture-card',
                  data: {
                    objName: 'thirdAgreement',
                    objId: bankCardId,
                    appId: 'customerinfo',
                    singleFile: false,
                  },
                  beforeUpload: file => beforeUploadFunc(file),
                  onChange: info => handleOnChange(info, 'platformAgreement'),
                  onPreview: async file => {
                    if (!file.url && !file.preview) {
                      file.preview = await getBase64(file.originFileObj);
                    }
                    setPreviewImage(file.url || file.preview);
                    setPreviewOpen(true);
                  },
                }}
                rules={[
                  {
                    required: true,
                    message: '图片不能为空',
                  },
                ]}
              />
            )}
            <ProFormUploadButton
              name="digitalReceipt"
              label="电子回单"
              max={1}
              help={[
                '提示信息：①请先充值到燕文收款账号，再上传电子回单',
                '②请上传小于1M的图片或小于5M的文件',
              ]}
              action="/csc/file/upload/attachment"
              title="上传"
              fieldProps={{
                name: 'attach',
                listType: 'picture-card',
                data: {
                  objName: 'digitalReceipt',
                  objId: bankCardId,
                  appId: 'customerinfo',
                  singleFile: false,
                },
                beforeUpload: file => beforeUploadFunc(file),
                onChange: info => handleOnChange(info, 'digitalReceipt'),
                onPreview: async file => {
                  if (!file.url && !file.preview) {
                    file.preview = await getBase64(file.originFileObj);
                  }
                  setPreviewImage(file.url || file.preview);
                  setPreviewOpen(true);
                },
              }}
              rules={[
                {
                  required: true,
                  message: '图片不能为空',
                },
              ]}
              addonAfter={
                <div className="flex items-end">
                  <a
                    className="underline underline-offset-4"
                    style={{
                      textDecoration: 'underline',
                    }}
                    onClick={() => {
                      modalDemoRef.current?.open({
                        url: ElectronicReceiptDemo,
                        params: {
                          personOrLegal: 'person',
                          area: '',
                          desc: false,
                        },
                      });
                    }}
                  >
                    (查看示例)
                  </a>
                </div>
              }
            />
            <ModalDemoImage modalRef={modalDemoRef}></ModalDemoImage>
          </>
        )}
        <Row justify="center">
          <Space>
            <Button onClick={() => handleCancel()}>取消</Button>
            <Button type="primary" htmlType="submit">
              提交
            </Button>
          </Space>
        </Row>
      </Form>
      {previewOpen && (
        <ModalImage open={previewOpen} url={previewImage} onCancel={() => setPreviewOpen(false)} />
      )}
    </Modal>
  );
};

export default MakeUpPaymentAccount;
