import { Alert, Card, Steps } from 'antd';
import React from 'react';
import { formatMessage } from 'umi-plugin-react/locale';

const RecordPaymentTips = props => {
  const { activeKey } = props;

  const activeKeyMapDescription = {
    '2':
      '我司系统依据备案账号入账，请您在转账前务必确认您的付款账号已在对应业务账号下完成备案，若未备案请点击 “新增付款账号”。另请注意，我司每个业务线的收款相互独立，请分开进行转账，一旦转错账号，系统无法进行调账，只能安排退款。充值流程如下',
    default: `${formatMessage({ id: '因系统识别备案账号入账' })}，${formatMessage({
      id: '为保障您转账的金额能及时入账',
    })} （${formatMessage({ id: '未入账将影响您制单' })})，${formatMessage({
      id: '请在充值时确保您的付款账号已在对应业务账号下备案过',
    })}，${formatMessage({ id: '若未备案请点击新增付款账号' })}，${formatMessage({
      id: '小包和',
    })}FBA${formatMessage({ id: '独立收款' })}，${formatMessage({
      id: '需注意分开转账',
    })}，${formatMessage({ id: '转错账号不支持调账' })}，${formatMessage({
      id: '只能退款',
    })}。${formatMessage({ id: '充值流程如下' })}：`,
  };

  return (
    <>
      <Card style={{ width: '100%', marginBottom: 20 }}>
        <Alert
          message={formatMessage({ id: '充值说明' })}
          description={activeKeyMapDescription[activeKey] ?? activeKeyMapDescription.default}
          type="warning"
          showIcon
          style={{ marginBottom: 20 }}
        />
        <Steps
          style={{ paddingLeft: 30, paddingRight: 30 }}
          current={6} //{formatMessage({id: '解决底部进度条'})}
          type="navigation"
          size="small"
          className="site-navigation-steps"
          items={[
            {
              title: formatMessage({ id: '新增付款账号' }),
              status: 'process',
              // disabled: true,
            },
            {
              title: formatMessage({ id: '备案成功' }),
              status: 'process',
              // disabled: true,
            },
            {
              title: formatMessage({ id: '查看业务线收款账号' }),
              status: 'process',
              // disabled: true,
            },
            {
              title: (
                <span>
                  {formatMessage({ id: '用已备案的付款账号' })}
                  <br />
                  {formatMessage({ id: '转账至燕文收款账号' })}
                </span>
              ),
              status: 'process',
              // disabled: true,
            },
            {
              title: formatMessage({ id: '到账成功' }),
              status: 'process',
              // disabled: true,
            },
          ]}
        />
      </Card>
    </>
  );
};

export default RecordPaymentTips;
