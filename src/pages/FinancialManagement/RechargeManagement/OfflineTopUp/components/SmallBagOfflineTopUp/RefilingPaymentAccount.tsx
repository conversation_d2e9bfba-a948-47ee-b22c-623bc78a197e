import React, { useState, MutableRefObject, useImperativeHandle, useRef } from 'react';
import {
  FormInstance,
  Modal,
  Form,
  Input,
  Select,
  Space,
  Button,
  Row,
  Radio,
  message,
  Upload,
} from 'antd';
import {
  otherPayRelationArray,
  relationOption,
  renderSelectOptions,
  PAY_RELATION_MAP,
  otherOption,
  AddPaymentCertificateTypeData,
} from '@/utils/commonConstant';
import {
  changeBeforeUpload,
  getBase64,
  returnMerchantType,
  returnPersonNameOrCompanyName,
  setUploadImage,
  validateMerchantName,
  paymentTypeDiffLabel,
  checkCompanyNameRegex,
} from '@/utils/utils';
import ModalImage from '@/components/ModalImage';
import { ProFormUploadButton } from '@ant-design/pro-components';
import moment from 'moment';
import VerifyAdminPhoneModal from '@/components/VerifyAdminPhoneModal';

type RefilingModalRef = {
  showModal: (record: any) => void;
};

type TProps = {
  dispatch: Function;
  refilingModalRef: MutableRefObject<RefilingModalRef>;
  form: FormInstance;
  onCancel: (value?: any) => void;
  merchantDetail: any;
  bankAccountOptions: Array<any>;
  alipayAccountOptions: Array<any>;
  platformOptions: Array<any>;
  bankOptions: Array<any>;
};

const { confirm } = Modal;

let timeout;
let currentValue;

/*
 *@Description: 备案失败重新填写付款账户
 *@MethodAuthor: dangh
 *@Date: 2023-05-12 10:13:49
 */
const RefilingPaymentAccount = (props: TProps) => {
  const {
    dispatch,
    refilingModalRef,
    form,
    onCancel,
    merchantDetail,
    bankAccountOptions,
    alipayAccountOptions,
    platformOptions,
    bankOptions,
  } = props;
  const verifyAdminPhoneRef = useRef<any>();

  const [open, setOpen] = useState(false);
  const [paymentTypeOptions, setPaymentTypeOptions] = useState([]); // 付款类型
  const [payRelationOptions, setPayRelationOptions] = useState([]); // 付款人与商户关系
  const [isMerchantSelf, setIsMerchantSelf] = useState(false); // 是否是商户本身
  const [isSelectOther, setIsSelectOther] = useState(false); // 是否选择其他
  const [isCompany, setIsCompany] = useState(false); // 是否是企业
  const [isHaveAgree, setIsHaveAgree] = useState(1); // 是否同意协议

  const [previewOpen, setPreviewOpen] = useState(false); // 是否显示打开链接
  const [previewImage, setPreviewImage] = useState<any>(); // 打开链接
  const [bankCardId, setBankCardId] = useState(); // 用于文件上传id使用
  const [flowId, setFlowId] = useState(); // 用于新增商户类型使用
  const [showBankBranch, setShowBankBranch] = useState(false); // 是否显示支行信息
  const [bankBranchList, setBankBranchList] = useState([]); // 支行信息
  const [paymentType, setPaymentType] = useState(); // 付款类型  0、2 个人付款  1、3企业付款
  const [certificateType, setCertificateType] = useState(0); // 证件类型
  const [paymentInfo, setPaymentInfo] = useState<{
    [key: string]: any;
  }>({
    paymentType: undefined,
    id: undefined,
  });

  useImperativeHandle(refilingModalRef, () => ({
    showModal: (record: any) => {
      initialFunc(record);
    },
  }));

  const initialFunc = async record => {
    try {
      setPaymentInfo({
        ...paymentInfo,
        ...record,
      });

      const key = `${record?.paymentType}-${returnMerchantType(merchantDetail)}`;
      const isHimself = record?.accountName === returnPersonNameOrCompanyName(merchantDetail);
      const paymentTypeArray = PAY_RELATION_MAP[key]; // 用于渲染付款类型使用
      // const defaultType = DEFAULT_PAY_TYPE_MAP[key]; // 用于回显付款方式使用
      const relationList = isHimself
        ? otherPayRelationArray[0].relation
        : paymentTypeArray?.find(item => item.value === record?.paymentTopicType)?.relation ?? []; // 用于渲染付款人与商户关系使用
      setPayRelationOptions(renderSelectOptions(relationList, { label: 'title', value: 'value' }));
      setPaymentTypeOptions(
        renderSelectOptions(paymentTypeArray, { label: 'title', value: 'value' })
      );
      setIsMerchantSelf(isHimself);
      setIsCompany(record?.paymentTopicType == 3 || record?.paymentTopicType == 1);
      setIsHaveAgree(+record?.protocol);
      setPaymentType(record?.paymentTopicType);
      const result = await getRealBankCardValue(record);
      setTimeout(() => {
        form.setFieldsValue({
          ...record,
          bankCard: result?.bankCard,
          protocol: +record?.protocol,
          paymentTypes: record?.paymentTopicType,
          platformAgreement: setUploadImage(record?.platformAgreement),
          digitalReceipt: setUploadImage(record?.digitalReceipt),
          certificateType: 0,
          relationShip:
            record?.paymentType === 2 &&
            record?.accountName === returnPersonNameOrCompanyName(merchantDetail)
              ? otherOption
              : record?.relationShip,
        });
      }, 300);
      setOpen(true);
    } catch (error) {
      console.error(error);
    }
  };

  // 根据输入的内容查询支行
  const handleSearch = newValue => {
    if (newValue) {
      // const values = form.getFieldsValue(['bankCode']);
      // const value = `${newValue}${
      //   bankOptions?.find(item => item.value === values?.bankCode)?.label
      // }`;
      const value = newValue;
      getBankBranch(value, setBankBranchList);
    } else {
      setBankBranchList([]);
    }
  };

  // 查询支行信息
  const getBankBranch = (keyword, callback) => {
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }
    currentValue = keyword;
    const request = () => {
      dispatch({
        type: 'paymentAccount/branchBankList',
        payload: {
          flowId,
          keyword,
        },
        callback: response => {
          if (response.success) {
            if (currentValue === keyword) {
              callback(response.data);
            }
          }
        },
      });
    };
    timeout = setTimeout(request, 300);
  };

  const getRealBankCardValue = paymentInfo =>
    new Promise<any>((resolve, reject) => {
      dispatch({
        type: 'paymentAccount/reverseDesensitization',
        payload: {
          id: paymentInfo?.id,
          bankName: paymentInfo.accountName,
          bankCard: paymentInfo.bankCard,
          source: paymentInfo?.source,
          paymentType: paymentInfo?.paymentType,
          businessType: paymentInfo?.businessType,
        },
        callback: res => {
          if (res.success) {
            const data = res.data;
            resolve(data);
          } else {
            reject();
          }
        },
      });
    });

  // 上传文件处理数据逻辑
  const handleOnChange = (info, type) => {
    if (info.file.status === 'uploading') {
      return;
    }
    if (info.file.status === 'done') {
      // Get this url from response in real world.
      if (info.file.response.success) {
        const uploadList = form.getFieldValue([type]);
        const url = info.file.response.data;
        const data = uploadList.map(item => {
          item.url = url;
          return item;
        });
        form.setFieldsValue({
          [type]: data,
        });
      } else {
        form.resetFields([type]);
        message.error(info.file.response.message);
      }
    }
  };

  // 选择付款类型
  const selectPaymentType = value => {
    const data = form.getFieldsValue(['accountName']);
    setPaymentType(value);
    if (paymentInfo?.paymentType === 2) {
      // 选择第三方
      let array = null;
      if (value === 2 && returnMerchantType(merchantDetail) === '企业') {
        array = handleCompanyRelationOptions(data?.accountName, merchantDetail.corporateName);
        setIsMerchantSelf(false);
        setIsCompany(false);
      } else if (data?.accountName === returnPersonNameOrCompanyName(merchantDetail)) {
        array = otherPayRelationArray[0].relation;
        setIsMerchantSelf(true);
        setIsCompany(value == 1 || value == 3);
      } else {
        array = findPaymentTypeOption(value)?.relation;
        setIsMerchantSelf(false);
        setIsCompany(returnMerchantType(merchantDetail) === '企业' || value == 1 || value == 3);
      }
      setPayRelationOptions(renderSelectOptions(array, { label: 'title', value: 'value' }));
    } else {
      // 选择银行或者支付宝
      let relation = null;
      if (data?.accountName === returnPersonNameOrCompanyName(merchantDetail)) {
        relation = [];
        setIsMerchantSelf(true);
        setIsCompany(value == 1 || value == 3);
      } else if (value === 2 && returnMerchantType(merchantDetail) === '企业') {
        relation = handleCompanyRelationOptions(data?.accountName, merchantDetail.corporateName);
        setIsMerchantSelf(false);
        setIsCompany(false);
      } else {
        relation = findPaymentTypeOption(value)?.relation;
        setIsMerchantSelf(false);
        setIsCompany(returnMerchantType(merchantDetail) === '企业' || value == 1 || value == 3);
      }
      setPayRelationOptions(renderSelectOptions(relation, { label: 'title', value: 'value' }));
    }
  };

  // 选择付款人与商户关系
  const selectRelationShip = value => {
    setIsSelectOther(value === '其他');
    form.resetFields(['relationShipInput']);
  };

  const findPaymentTypeOption = value => {
    return paymentTypeOptions.find(item => item.value === value);
  };

  // 选择付款类型时处理商户是企业的逻辑
  const handleCompanyRelationOptions = (data, corporateName) => {
    if (data === corporateName) {
      return [
        {
          label: '法定代表人',
          value: '法定代表人',
        },
      ];
    }
    return renderSelectOptions(relationOption, { label: 'title', value: 'value' });
  };

  const inputChange = () => {
    form.resetFields(['relationShip', 'relationShipInput']);
    const data = form.getFieldsValue(['accountName', 'paymentTypes']);
    form.setFieldsValue({
      relationShip: data.accountName === merchantDetail.corporateName ? '法定代表人' : undefined,
    });
    // 选择银行或者支付宝
    let relation = null;
    if (data?.accountName === returnPersonNameOrCompanyName(merchantDetail)) {
      setIsMerchantSelf(true);
    } else if (data?.paymentTypes === 2 && returnMerchantType(merchantDetail) === '企业') {
      relation = handleCompanyRelationOptions(data?.accountName, merchantDetail.corporateName);
      setIsMerchantSelf(false);
    } else {
      relation = findPaymentTypeOption(data?.paymentTypes)?.relation;
      setIsMerchantSelf(false);
    }
    setPayRelationOptions(renderSelectOptions(relation, { label: 'title', value: 'value' }));
  };

  const bankAndAliMakeUpRender = () => (
    <>
      {paymentInfo?.paymentType !== 2 && (
        <Form.Item
          label={paymentTypeDiffLabel(paymentType, '付款账户名称')}
          name="accountName"
          rules={[
            {
              required: true,
              message: `请填写${paymentTypeDiffLabel(paymentType, '付款账户名称')}`,
            },
            {
              pattern: validateMerchantName(form.getFieldValue('paymentTypes')),
              message: `${paymentTypeDiffLabel(paymentType, '付款账户名称')}格式错误`,
            },
          ]}
        >
          <Input onBlur={inputChange} disabled={showBankBranch} />
        </Form.Item>
      )}

      {paymentInfo?.paymentType === 0 && (
        <Form.Item
          label="银行"
          name="bankCode"
          rules={[
            {
              required: true,
              message: '请选择对应银行',
            },
          ]}
        >
          <Select
            options={bankOptions}
            showSearch
            placeholder="-请选择-"
            disabled={showBankBranch}
          />
        </Form.Item>
      )}
      {isCompany ? (
        <>
          <Form.Item
            name="businessLicence"
            label="统一社会信用代码"
            rules={[
              {
                required: true,
                message: '请输入统一社会信用代码',
              },
              {
                whitespace: true,
                message: '不能输入空格',
              },
              {
                pattern: /^([0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}|[1-9]\d{14})|(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)$/,
                message: '统一社会信用代码格式错误',
              },
            ]}
          >
            <Input placeholder="请填写统一社会信用代码" disabled={showBankBranch} />
          </Form.Item>
          <Form.Item
            name="legalPersonName"
            label="法人姓名"
            rules={[
              {
                required: true,
                message: '请填写法人姓名',
              },
              {
                whitespace: true,
                message: '不能输入空格',
              },
            ]}
          >
            <Input disabled={showBankBranch} />
          </Form.Item>
        </>
      ) : (
        <>
          {paymentInfo?.paymentType === 0 && (paymentType == 0 || paymentType == 2) && (
            <Form.Item
              label="证件类型"
              name="certificateType"
              rules={[
                {
                  required: true,
                  message: '请选择证件类型',
                },
              ]}
            >
              <Select
                showSearch
                options={AddPaymentCertificateTypeData}
                notFoundContent={'请选择证件类型'}
                placeholder={'-请选择-'}
                onChange={value => setCertificateType(value)}
              />
            </Form.Item>
          )}
          <Form.Item
            name="idCard"
            label="证件号码"
            rules={[
              {
                required: true,
                message: '请输入证件号码',
              },
              {
                whitespace: true,
                message: '不能输入空格',
              },
              certificateType === 0
                ? {
                    pattern: /^(^\d{8}(0\d|10|11|12)([0-2]\d|30|31)\d{3}$)|(^\d{6}(18|19|20)\d{2}(0\d|10|11|12)([0-2]\d|30|31)\d{3}(\d|X|x)$)$/,
                    message: '证件号码格式错误',
                  }
                : null,
            ]}
          >
            <Input placeholder="请填写证件号码" />
          </Form.Item>
        </>
      )}
      {showBankBranch && (
        <Form.Item
          label="付款银行支行"
          name="bankBranchName"
          rules={[
            {
              required: true,
              message: '请输入关键字后选择支行名称',
            },
          ]}
        >
          <Select
            options={bankBranchList}
            showSearch
            filterOption={false}
            onSearch={handleSearch}
            placeholder="请输入关键字后选择支行名称"
            allowClear
            fieldNames={{
              label: 'bankName',
              value: 'bankName',
            }}
          />
        </Form.Item>
      )}
    </>
  );
  const getBankCardId = values =>
    new Promise(resolve => {
      dispatch({
        type: 'paymentAccount/getBankCardId',
        payload: {
          paymentTopicType: values?.paymentTypes,
          bankCard: values?.bankCard,
          paymentType: 2,
          accountName: values?.accountName,
          businessType: paymentInfo?.businessType,
          businessTypes: paymentInfo?.businessTypes,
        },
        callback: response => {
          if (response.success) {
            setBankCardId(response.data);
            resolve(response.data);
          } else {
            resolve(null);
          }
        },
      });
    });

  const beforeUploadFunc = async file => {
    try {
      const values = await form.validateFields(['paymentTypes', 'bankCard', 'accountName']);
      const bankCardId = await getBankCardId(values);
      if (bankCardId) {
        return changeBeforeUpload(
          file,
          5,
          ['image/jpeg', 'image/png'],
          '请上传小于1M的图片或小于5M的文件',
          1
        );
      } else {
        return false || Upload.LIST_IGNORE;
      }
    } catch (error) {
      return false || Upload.LIST_IGNORE;
    }
  };

  const handleCancel = (value?: any) => {
    form.resetFields();
    setOpen(false);
    setShowBankBranch(false);
    setCertificateType(0);
    setBankBranchList([]);
    setFlowId(undefined);
    timeout = null;
    currentValue = null;
    if (value) {
      onCancel(value);
    }
  };

  // 校验支付宝账号
  const checkAliAccount = (params, callback) => {
    if (params?.paymentType == 1) {
      dispatch({
        type: 'paymentAccount/alipayCheck',
        payload: params,
        callback: response => {
          if (response.success) {
            if (callback) callback();
          } else {
            if (response.code == -1) {
              message.error(response.message);
            } else if (response.code == -2) {
              confirm({
                title: '温馨提示',
                content: response.message,
                okText: '是',
                cancelText: '否',
                onOk() {
                  if (callback) callback();
                },
                onCancel() {
                  handleCancel();
                },
              });
            }
          }
        },
      });
    } else {
      if (callback) callback();
    }
  };
  const handleSubmit = values => {
    let params = null;
    if (paymentInfo.paymentType === 0) {
      params = {
        ...values,
        id: paymentInfo.id,
        paymentType: paymentInfo.paymentType,
        type: paymentInfo.paymentType,
        receiptType: paymentInfo.paymentType,
        payment: paymentInfo.paymentType,
        paymentTopicType: values.paymentTypes,
        accountCode: values.bankCard,
        bankCard: values.bankCard,
        bankCode: values.bankCode,
        customerName: returnPersonNameOrCompanyName(merchantDetail),
        originalAccountName: paymentInfo.originalAccountName,
        trustee: values.accountName,
        accountName: values.accountName,
        accountPlatform: bankOptions.find(item => item.value === values.bankCode)?.label || '', // 银行名
        depositBank: bankOptions.find(item => item.value === values.bankCode)?.label || '', // 银行名
        trusteeNumber: values.idCard, // 身份证
        idCard: values.idCard, // 身份证
        relation:
          values?.accountName === returnPersonNameOrCompanyName(merchantDetail)
            ? otherOption
            : !isSelectOther
            ? values.relationShip
            : values.relationShipInput, // 付款与商户的关系
        relationShip:
          values?.accountName === returnPersonNameOrCompanyName(merchantDetail)
            ? otherOption
            : !isSelectOther
            ? values.relationShip
            : values.relationShipInput, // 付款与商户的关系
        paymentTypes: values.paymentTypes, // 付款类型
        // ywAccount: values.accountsReceivable, // 燕文收款账户
        // accountsReceivable: values.accountsReceivable, // 燕文收款账户
        businessLicence: values.businessLicence, // 信用代码
        legalPersonName: values.legalPersonName, //法人姓名
        applicationType: paymentInfo?.applicationType ? paymentInfo?.applicationType : '2',
        createTime: paymentInfo.createTime,
        bankCardId: paymentInfo?.bankCardId ?? ' ',
        // protocol: values?.protocol ? (values?.protocol+ ''):undefined,
        businessType: paymentInfo?.businessType,
        flowId,
        bankBranchName: values?.bankBranchName,
      };
    } else if (paymentInfo.paymentType === 1) {
      params = {
        id: paymentInfo.id,
        paymentTopicType: values.paymentTypes,
        type: paymentInfo.paymentType,
        receiptType: paymentInfo.paymentType,
        payment: paymentInfo.paymentType,
        paymentType: paymentInfo.paymentType,
        accountCode: values.bankCard,
        bankCard: values.bankCard,
        originalAccountName: paymentInfo.originalAccountName,
        trustee: values.accountName,
        bankCode: '',
        customerName: returnPersonNameOrCompanyName(merchantDetail),
        accountPlatform: '支付宝',
        platformName: '支付宝',
        depositBank: '支付宝',
        accountName: values.accountName,
        // aliPayCard: values.bankCard2,
        trusteeNumber: values.idCard, // 身份证
        idCard: values.idCard, // 身份证
        relation:
          values?.accountName === returnPersonNameOrCompanyName(merchantDetail)
            ? otherOption
            : !isSelectOther
            ? values.relationShip
            : values.relationShipInput, // 付款与商户的关系
        relationShip:
          values?.accountName === returnPersonNameOrCompanyName(merchantDetail)
            ? otherOption
            : !isSelectOther
            ? values.relationShip
            : values.relationShipInput, // 付款与商户的关系
        paymentTypes: values.paymentTypes, // 付款类型
        businessLicence: values.businessLicence, //统一信用代码
        // ywAccount: values.accountsReceivable, // 燕文收款账户
        // accountsReceivable: values.accountsReceivable, // 燕文收款账户
        legalPersonName: values.legalPersonName, // 法人姓名
        applicationType: paymentInfo?.applicationType ? paymentInfo?.applicationType : '2',
        createTime: paymentInfo?.createTime,
        bankCardId: paymentInfo?.bankCardId ?? ' ',
        payBatchId: paymentInfo?.payBatchId,
        businessType: paymentInfo?.businessType,
      };
    } else {
      // 第三方
      params = {
        id: paymentInfo.id,
        paymentTopicType: values.paymentTypes,
        paymentType: paymentInfo?.paymentType,
        bankCard: values?.bankCard, // 平台账号
        originalAccountName: paymentInfo?.accountName,
        accountName: values.accountName, // 平台实名认证
        idCard: values.idCard,
        payType: values?.paymentTypes,
        relationShip:
          values?.accountName === returnPersonNameOrCompanyName(merchantDetail)
            ? otherOption
            : !isSelectOther
            ? values.relationShip
            : values.relationShipInput,
        platformName: values.platformName, // 平台名称
        // accountsReceivable: values.accountsReceivable,
        businessLicence: values.businessLicence,
        legalPersonName: !isCompany ? '' : values.legalPersonName,
        applicationType: paymentInfo?.applicationType ? paymentInfo?.applicationType : '2',
        bankCardId: paymentInfo?.bankCardId ?? ' ',
        platformAgreement:
          values.protocol === 1 ? values?.platformAgreement?.[0].url.split('?')[0] : undefined, // 平台协议
        digitalReceipt: values?.digitalReceipt?.[0].url.split('?')[0], // 电子回单
        accountTime: paymentInfo?.createTime
          ? moment(paymentInfo?.createTime).format('YYYY-MM-DD')
          : moment().format('YYYY-MM-DD'),
        protocol: values?.protocol ? values?.protocol + '' : undefined,
        businessType: paymentInfo?.businessType,
      };
    }
    if (
      paymentInfo.paymentType === 0 &&
      (values.paymentTypes == 1 || values.paymentTypes == 3) &&
      !showBankBranch
    ) {
      dispatch({
        type: 'paymentAccount/businessVerification',
        payload: {
          ...params,
          companyName: params?.accountName,
          legalName: params?.legalPersonName,
          businessLicense: params?.businessLicence,
        },
        callback: response => {
          if (response.success) {
            message.success('提交成功,请选择银行支行');
            // getBankBranch(
            //   response?.data?.flowId,
            //   bankOptions?.find(item => item.value === values?.bankCode)?.label
            // );
            setFlowId(response?.data?.flowId);
            setShowBankBranch(true);
            // handleCancel({
            //   ...values,
            //   id: response?.data,
            // });
          }
        },
      });
    } else {
      checkAliAccount(params, () => {
        // 是否是客服发起确认
        const isCustomerServiceConfirm =
          paymentInfo?.applicationType === '4' || paymentInfo?.applicationType === '3';
        dispatch({
          type: `paymentAccount/${isCustomerServiceConfirm ? 'confirmSubmit' : 'editAgain'}`,
          payload: params,
          callback: response => {
            if (response.success) {
              if (params?.certificateType !== undefined && params?.certificateType !== 0) {
                if (!params?.verifyPhone) {
                  setFlowId(response?.data);
                  return verifyAdminPhoneRef.current?.showModal({
                    phone: params?.mobile,
                    title: '验证手机号',
                  });
                }
              }
              handleCancel({
                ...params,
                paymentType: paymentInfo?.paymentType,
                id: response?.data,
                flowId,
              });
            }
          },
        });
      });
    }
  };

  return (
    <Modal
      title={
        <span>
          确认付款账号
          <span className="text-xs text-red-900 ml-1">
            {showBankBranch ? '企业信息校验成功，请选择支行进行对公银行信息验证' : ''}
          </span>
        </span>
      }
      open={open}
      footer={null}
      destroyOnClose
      onCancel={() => handleCancel()}
    >
      <Form
        form={form}
        labelAlign="right"
        labelCol={{
          flex: '130px',
        }}
        onFinish={handleSubmit}
      >
        <Form.Item
          label={
            paymentInfo?.paymentType === 2
              ? '平台账号'
              : paymentInfo?.paymentType === 1
              ? paymentTypeDiffLabel(paymentType, '支付宝账号')
              : paymentTypeDiffLabel(paymentType, '银行账号')
          }
          name="bankCard"
          rules={[
            {
              required: true,
              message:
                paymentInfo?.paymentType === 2
                  ? '请输入平台账号'
                  : paymentInfo?.paymentType === 1
                  ? `请输入${paymentTypeDiffLabel(paymentType, '支付宝账号')}`
                  : `请输入${paymentTypeDiffLabel(paymentType, '银行账号')}`,
            },
          ]}
        >
          <Input />
        </Form.Item>
        {paymentInfo?.paymentType === 2 && (
          <>
            <Form.Item
              label="平台名称"
              name="platformName"
              rules={[
                {
                  required: true,
                  message: '请输入平台名称',
                },
              ]}
            >
              <Select options={platformOptions} showSearch placeholder={'-请选择平台名称-'} />
            </Form.Item>
            <Form.Item
              label="平台实名认证"
              name="accountName"
              rules={[
                {
                  required: true,
                  message: '请输入在平台注册的账号',
                },
                {
                  pattern: checkCompanyNameRegex,
                  message: '平台实名认证名称格式错误',
                },
              ]}
            >
              <Input onBlur={inputChange} />
            </Form.Item>
          </>
        )}
        <Form.Item
          label="付款类型"
          name="paymentTypes"
          rules={[
            {
              required: true,
              message: '请选择付款类型',
            },
          ]}
        >
          <Select
            options={paymentTypeOptions}
            showSearch
            disabled={paymentTypeOptions.length === 1 || showBankBranch}
            placeholder={'-请选择-'}
            onChange={selectPaymentType}
          />
        </Form.Item>
        {paymentInfo?.paymentType !== undefined && bankAndAliMakeUpRender()}
        {!isMerchantSelf && (
          <>
            <Form.Item
              label="付款人与商户关系"
              name="relationShip"
              rules={[
                {
                  required: true,
                  message: '请选择付款人和商户关系',
                },
              ]}
            >
              <Select
                options={payRelationOptions}
                notFoundContent={'请先选择付款类型'}
                showSearch
                placeholder={'-请选择-'}
                onChange={selectRelationShip}
                disabled={showBankBranch}
              />
            </Form.Item>
            {isSelectOther && (
              <Form.Item
                label="与商户的具体关系"
                name="relationShipInput"
                rules={[
                  {
                    required: true,
                    message: '请输入与商户的具体关系',
                  },
                ]}
              >
                <Input placeholder="请输入与商户的具体关系" disabled={showBankBranch} />
              </Form.Item>
            )}
            {/* <Form.Item
              label="燕文收款账户"
              name="accountsReceivable"
              rules={[
                {
                  required: true,
                  message: '请选择燕文收款账户',
                },
              ]}
            >
              <Select
                options={paymentInfo?.paymentType !== 2 ? alipayAccountOptions : bankAccountOptions}
                showSearch
                notFoundContent="请选择燕文收款账户"
                placeholder="-请选择-"
              />
            </Form.Item> */}
            {certificateType !== 0 && (
              <Form.Item
                label="手机号"
                name="mobile"
                rules={[
                  { required: true, message: '请输入手机号' },
                  {
                    pattern: /^1\d{10}$/,
                    message: '请输入正确手机号',
                  },
                ]}
              >
                <Input placeholder="请输入手机号" allowClear />
              </Form.Item>
            )}
          </>
        )}
        {paymentInfo?.paymentType === 2 && (
          <>
            <Form.Item
              label="是否有平台协议"
              name="protocol"
              initialValue={isHaveAgree}
              rules={[
                {
                  required: true,
                  message: '请选择是否有平台协议',
                },
              ]}
            >
              <Radio.Group onChange={e => setIsHaveAgree(e.target.value)}>
                <Radio value={1}>是</Radio>
                <Radio value={0}>否</Radio>
              </Radio.Group>
            </Form.Item>
            {isHaveAgree === 1 && (
              <ProFormUploadButton
                name="platformAgreement"
                label="与平台协议"
                max={1}
                help="提示信息：请上传小于1M的图片或小于5M的文件;上传平台协议之前，请先填写平台账号"
                action="/csc/file/upload/attachment"
                title="上传"
                fieldProps={{
                  name: 'attach',
                  listType: 'picture-card',
                  data: {
                    objName: 'thirdAgreement',
                    objId: bankCardId,
                    appId: 'customerinfo',
                    singleFile: false,
                  },
                  beforeUpload: file => beforeUploadFunc(file),
                  onChange: info => handleOnChange(info, 'platformAgreement'),
                  onPreview: async file => {
                    if (!file.url && !file.preview) {
                      file.preview = await getBase64(file.originFileObj);
                    }
                    setPreviewImage(file.url || file.preview);
                    setPreviewOpen(true);
                  },
                }}
                rules={[
                  {
                    required: true,
                    message: '图片不能为空',
                  },
                ]}
              />
            )}
            <ProFormUploadButton
              name="digitalReceipt"
              label="电子回单"
              max={1}
              help="提示信息：请上传小于1M的图片或小于5M的文件;上传电子回单之前，请先填写平台账号"
              action="/csc/file/upload/attachment"
              title="上传"
              fieldProps={{
                name: 'attach',
                listType: 'picture-card',
                data: {
                  objName: 'digitalReceipt',
                  objId: bankCardId,
                  appId: 'customerinfo',
                  singleFile: false,
                },
                beforeUpload: file => beforeUploadFunc(file),
                onChange: info => handleOnChange(info, 'digitalReceipt'),
                onPreview: async file => {
                  if (!file.url && !file.preview) {
                    file.preview = await getBase64(file.originFileObj);
                  }
                  setPreviewImage(file.url || file.preview);
                  setPreviewOpen(true);
                },
              }}
              rules={[
                {
                  required: true,
                  message: '图片不能为空',
                },
              ]}
            />
          </>
        )}
        <Row justify="center">
          <Space>
            <Button onClick={() => handleCancel()}>取消</Button>
            <Button type="primary" htmlType="submit">
              提交
            </Button>
          </Space>
        </Row>
      </Form>
      {previewOpen && (
        <ModalImage open={previewOpen} url={previewImage} onCancel={() => setPreviewOpen(false)} />
      )}
      <VerifyAdminPhoneModal
        {...props}
        modalRef={verifyAdminPhoneRef}
        needLogout={false}
        noSendSms
        phoneTitle="验证手机号"
        onSubmit={async values => {
          handleSubmit({
            ...values,
            ...(await form?.validateFields()),
          });
        }}
        onCancel={() => {
          form.resetFields();
          setFlowId(undefined);
          setShowBankBranch(false);
          setBankBranchList([]);
          setCertificateType(0);
          timeout = undefined;
          currentValue = undefined;
        }}
      />
    </Modal>
  );
};

export default RefilingPaymentAccount;
