import React from 'react';
import { connect } from 'dva';
import YanwenPaymentAccount from '../SmallBagOfflineTopUp/YanwenPaymentAccount';
import Tables from '../SmallBagOfflineTopUp/Tables';

const Index = props => {
  return (
    <>
      <YanwenPaymentAccount {...props} />
      <Tables {...props} />
    </>
  );
};

export default connect(({ loading }) => ({
  bankInfoLoading: loading.effects['smallBag/getBankAccountList'],
  getPaymentListLoading: loading.effects['paymentAccount/paymentList'],
  createLoading: loading.effects['paymentAccount/create'],
  authLoading: loading.effects['paymentAccount/getSignUrl'],
}))(Index);
