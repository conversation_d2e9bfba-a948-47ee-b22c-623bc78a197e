import React, { useRef, useState } from 'react';
import PageContainerComponent from '@/components/PageContainer';
import PageHeaderBreadcrumb from '@/components/PageHeaderBreadcrumb';
import { Button, Empty, Row, Tabs, Card } from 'antd';
import { connect } from 'dva';
import YanwenAccountInfo from './components/YanwenAccountInfo';
import SmallPacketLineBill from './SmallPacketLine';
import FBABill from './FBA';
import Overseas from './Overseas';
import { businessStatus } from '@/utils/commonConstant';
import { useMount } from 'ahooks';
import { formatMessage } from 'umi-plugin-react/locale';

const Index = (props: any) => {
  const { dispatch, location } = props;
  const modalRef = useRef<any>();

  const [activeKey, setActiveKey] = useState('0');
  const [smallBagStatus, setSmallBagStatus] = useState(false);
  const [fbaStatus, setFbaStatus] = useState(false);
  const [overseaStatus, setOverseaStatus] = useState(false); // 海外派
  const [pageLoading, setPageLoading] = useState(false);

  /**
   * 打开收款账户弹窗
   */
  const handelRecharge = () => {
    modalRef.current?.open();
  };

  const tabItems = [
    {
      label: formatMessage({ id: '小包专线' }),
      key: '0',
      children: <SmallPacketLineBill location={props?.location} {...props} />,
    },
    {
      label: `FBA${formatMessage({ id: '专线' })}`,
      key: '1',
      children: <FBABill location={props?.location} activeKey={activeKey} />,
    },
    {
      label: formatMessage({ id: '海外派' }),
      key: '2',
      children: <Overseas location={props?.location} activeKey={activeKey} {...props} />,
    },
  ];

  useMount(() => {
    initialFunc();
  });

  const initialFunc = () => {
    setPageLoading(true);
    getOpenStatusResult();
  };

  // 获取开通小包专线和FBA专线的状态结果
  const getOpenStatusResult = async () => {
    try {
      const result = await Promise.allSettled([
        getOpenStatus(0),
        getOpenStatus(1),
        getOpenStatus(2),
      ]);
      let isSetActiveKeyExecuted = false; // 添加一个变量来记录是否已经设置过 activeKey
      result.forEach(item => {
        if (item.status === 'fulfilled') {
          const { type, status } = item.value;
          if (!isSetActiveKeyExecuted) {
            // 判断是否已经设置过 activeKey
            if (location?.query?.activeKey == 1) {
              setActiveKey('1');
            } else {
              setActiveKey(
                type === 0 && status
                  ? '0'
                  : type === 1 && status
                  ? '1'
                  : type === 2 && status
                  ? '2'
                  : '0'
              );
            }
            isSetActiveKeyExecuted = status; // 设置为已经执行过 setActiveKey
          }
          if (type === 0) {
            setSmallBagStatus(status);
          } else if (type === 1) {
            setFbaStatus(status);
          } else {
            setOverseaStatus(status);
          }
        }
      });
      setPageLoading(false);
    } catch (error) {
      console.log(error);
      setPageLoading(false);
    }
  };

  // 获取小包专线和FBA专线开通接口
  const getOpenStatus = type => {
    return new Promise<any>((resolve, reject) => {
      dispatch({
        type: 'user/checkIfShowBill',
        payload: { type },
        callback: response => {
          if (response.success) {
            const key = `${response.data}`;
            if (businessStatus.includes(key)) {
              // status 为true 时表示已开通
              resolve({ type, status: false });
            } else {
              resolve({ type, status: true });
            }
          } else {
            reject();
          }
        },
      });
    });
  };

  return (
    <PageContainerComponent
      loading={pageLoading}
      header={{
        title: null,
        breadcrumb: {},
        breadcrumbRender: (props: any) => (
          <PageHeaderBreadcrumb {...props} col>
            {(smallBagStatus || fbaStatus) && (
              <Row justify="end">
                <Button onClick={handelRecharge}>收款账户</Button>
              </Row>
            )}
          </PageHeaderBreadcrumb>
        ),
      }}
    >
      {pageLoading ? null : (
        <>
          {!smallBagStatus && !fbaStatus && !overseaStatus && (
            <Card>
              <Empty
                description={
                  <span>
                    {formatMessage({ id: '暂无数据' })}，{formatMessage({ id: '请先开通服务' })}
                  </span>
                }
              />
            </Card>
          )}
          <Tabs
            activeKey={activeKey}
            onChange={key => setActiveKey(key)}
            tabBarStyle={{ margin: '0' }}
            type="card"
            items={tabItems.filter(item => {
              if (item.key === '0' && smallBagStatus) {
                return true;
              }
              if (item.key === '1' && fbaStatus) {
                return true;
              }
              if (item.key === '2' && overseaStatus) {
                return true;
              }
              return false;
            })}
          />
        </>
      )}

      <YanwenAccountInfo modalRef={modalRef} activeKey={activeKey} {...props} />
    </PageContainerComponent>
  );
};

export default connect(({ loading }) => ({
  getYanwenAccountLoading:
    loading.effects['bill/getBankAccountList'] ||
    loading.effects['bill/getFbaBankAccountList'] ||
    loading.effects['overseas/getYWEBankAccountList'],
  buttonLoading: loading.effects['bill/getOverseaRecord'],
}))(Index);
