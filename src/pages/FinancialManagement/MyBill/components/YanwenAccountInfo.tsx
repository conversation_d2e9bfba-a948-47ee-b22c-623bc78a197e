import { Button, Descriptions, Modal, Spin } from 'antd';
import React, { useState, useImperativeHandle } from 'react';
import { clickCopy } from '@/utils/utils';
import { CopyOutlined } from '@ant-design/icons/lib';

const YanwenAccountInfo = ({
  getYanwenAccountLoading: isMadelLoading,
  modalRef,
  dispatch,
  activeKey,
}) => {
  const [open, setOpen] = useState(false);
  const [bankAccountName, setBankAccountName] = useState('');
  const [bankAddr, setBankAddr] = useState('');
  const [accountBank, setAccountBank] = useState('');
  const [accountAlipay, setAccountAlipay] = useState('');
  const [alipayAccountName, setAlipayAccountName] = useState('');

  useImperativeHandle(modalRef, () => ({
    open: () => {
      initialFunc();
    },
  }));

  const initialFunc = () => {
    setAlipayAccountName('');
    setAccountAlipay('');
    setBankAccountName('');
    setBankAddr('');
    setAccountBank('');
    let path = '';
    if (activeKey == 0) {
      path = 'bill/getBankAccountList';
    } else if (activeKey == 1) {
      path = 'bill/getFbaBankAccountList';
    } else if (activeKey == 2) {
      path = 'overseas/getYWEBankAccountList';
    } else {
      setOpen(true);
      return;
    }
    dispatch({
      type: `${path}`,
      callback: result => {
        if (result.success) {
          const dataLists = result.data;
          if (dataLists.length >= 1) {
            // 银行账号
            setBankAccountName(dataLists[0].bankAccountName);
            setBankAddr(dataLists[0].bankAddr);
            setAccountBank(dataLists[0].account);
            if (dataLists.length > 1) {
              // 支付宝
              setAccountAlipay(dataLists[1].account);
              setAlipayAccountName(dataLists[1].bankAccountName);
            }
          } else {
            setAlipayAccountName('');
            setAccountAlipay('');
            setBankAccountName('');
            setBankAddr('');
            setAccountBank('');
          }
        } else {
          setAlipayAccountName('');
          setAccountAlipay('');
          setBankAccountName('');
          setBankAddr('');
          setAccountBank('');
        }
      },
    });
    setOpen(true);
  };

  const handleCancel = () => {
    setOpen(false);
  };

  return (
    <Modal
      title="燕文收款账户信息"
      open={open}
      width="700px"
      onOk={handleOk => handleCancel()}
      onCancel={handleCancel}
      footer={[
        <Button type="primary" onClick={handleOk => handleCancel()}>
          确定
        </Button>,
        <Button onClick={handleCancel}>取消</Button>,
      ]}
    >
      {isMadelLoading ? (
        <div style={{ textAlign: 'center', fontSize: '22px' }}>
          <Spin tip="Loading..." />
        </div>
      ) : (
        <div style={{ display: 'flex' }}>
          <Descriptions column={1} title="银行收款账户信息">
            <Descriptions.Item label="账户名">
              {bankAccountName}
              {bankAccountName && (
                <CopyOutlined
                  style={{ color: '#52c41a', marginLeft: 5 }}
                  onClick={event => clickCopy(bankAccountName)}
                />
              )}
            </Descriptions.Item>
            <Descriptions.Item label="开户行">
              {bankAddr}
              {bankAddr && (
                <CopyOutlined
                  style={{ color: '#52c41a', marginLeft: 5 }}
                  onClick={event => clickCopy(bankAddr)}
                />
              )}
            </Descriptions.Item>
            <Descriptions.Item label="账号">
              {accountBank}
              {accountBank && (
                <CopyOutlined
                  style={{ color: '#52c41a', marginLeft: 5 }}
                  onClick={event => clickCopy(accountBank)}
                />
              )}
            </Descriptions.Item>
          </Descriptions>
          <Descriptions column={1} title="支付宝收款账户信息">
            <Descriptions.Item label="支付宝账户名">
              {alipayAccountName}
              {alipayAccountName && (
                <CopyOutlined
                  style={{ color: '#52c41a', marginLeft: 5 }}
                  onClick={event => clickCopy(alipayAccountName)}
                />
              )}
            </Descriptions.Item>
            <Descriptions.Item label="支付宝账户号">
              {accountAlipay}
              {accountAlipay && (
                <CopyOutlined
                  style={{ color: '#52c41a', marginLeft: 5 }}
                  onClick={event => clickCopy(accountAlipay)}
                />
              )}
            </Descriptions.Item>
          </Descriptions>
        </div>
      )}
    </Modal>
  );
};

export default YanwenAccountInfo;
