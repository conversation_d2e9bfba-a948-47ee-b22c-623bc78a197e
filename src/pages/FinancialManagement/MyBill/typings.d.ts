import moment from 'moment';
import { ProFormInstance } from '@ant-design/pro-components';

declare namespace BILL_SERVICE {
  interface getOverseaRecordRequest {
    accountCode: string;
    transStartDate: string;
    transEndDate: string;
    transNo: string;
    currentPage: number;
    pageSize: number;
    dateTime: Array<moment.Moment>;
    selectType: string;
    expenseType: string;
  }

  interface getOverseaRecordResponse {
    total: number;
    rows: Array<
      Partial<{
        merchantCode: string;
        merchantName: string;
        customerCode: string;
        waybillNumber: string;
        exchangeNumber: string;
        orderStatus: string;
        orderNumber: string;
        transTime: string;
        expenseType: string;
        transNo: string;
        currency: string;
        moneyToFc: number;
        moneyToFcDif: number;
        memo: string;
      }>
    >;
  }
}

declare namespace Search {
  interface IProps<T> {
    formRef: React.MutableRefObject<ProFormInstance<T> | undefined>;
    accountData: Array<{ [key: string]: any } & { label: 'string'; value: any }>;
    onSearch: (values: T | undefined) => void;
    buttonLoading: boolean;
    dateTime: Array<moment.Moment>;
    [key: string]: any;
  }
}
