/**
 * 未出账单金额
 */
import React, { Component } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {
  Card,
  DatePicker,
  Row,
  Col,
  Button,
  Breadcrumb,
  Table,
  Space,
  Input,
  message,
  Modal,
  Tabs,
  Spin,
} from 'antd';
import moment from 'moment';
import { connect } from 'dva';
import defaultSettings from '../../../../../../config/defaultSettings';
import { downloadFile } from '@/utils/download';
import PageHeaderBreadcrumb from '@/components/PageHeaderBreadcrumb';
import PageContainerComponent from '@/components/PageContainer';
import { formatMessage } from 'umi-plugin-react/locale';
import { mergeCellData } from '@/utils/utils';

const { portalUrl } = defaultSettings;
const { RangePicker } = DatePicker;
let pageSize = 10;
const weekOfday = parseInt(moment().format('E')); // 计算今天是这周第几天  周日为一周中的第一天
let startDate = moment()
  .subtract(weekOfday - 1, 'days')
  .format('YYYY-MM-DD');
let endDate = moment().format('YYYY-MM-DD');
const { TabPane } = Tabs;
@connect(({ bill, loading }) => ({
  bill,
  notBillBillLoading: loading.effects[('bill/unBilledBillList', 'bill/unBilledTaxBill')],
}))
@Form.create()
class NotOutBill extends Component {
  constructor(props) {
    super(props);
    this.state = {
      merchantCode: '',
      total: 0,
      data: [],
      totalAmount: '',
      waybillNumber: '',
      currentPage: 1,
      visible: false,
      tabNum: '0',
      listLoading: false,
    };
  }

  showModelHandler = () => {
    this.setState({
      visible: true,
    });
    this.handleQuery();
  };

  cancelHandle = () => {
    startDate = moment()
      .subtract(weekOfday - 1, 'days')
      .format('YYYY-MM-DD');
    endDate = moment().format('YYYY-MM-DD');
    pageSize = 10;
    const { form } = this.props;
    form.setFieldsValue({
      epCodes: '',
      dateTime: [moment(startDate, 'YYYY-MM-DD'), moment(endDate, 'YYYY-MM-DD')],
    });
    this.setState({
      visible: false,
      data: [],
      currentPage: 1,
      tabNum: '0',
    });
  };

  // 时间选择事件
  onChange = (date, dateString) => {
    startDate = dateString[0];
    endDate = dateString[1];
  };

  handleQuery = key => {
    const { currentPage, tabNum } = this.state;
    const { dispatch, form } = this.props;
    form.validateFields((err, values) => {
      if (!err) {
        let epCodes = values?.epCodes === '' ? undefined : values?.epCodes;
        let nums = epCodes?.split(/[\r\n]/g) ?? [];
        const startDateTime = values?.dateTime
          ? values?.dateTime[0].format('YYYY-MM-DD')
          : undefined;
        const endDateTime = values?.dateTime ? values?.dateTime[1].format('YYYY-MM-DD') : undefined;
        const param = {
          currentPage: currentPage,
          pageSize: pageSize,
          startCalcTime: startDateTime,
          endCalcTime: endDateTime,
          waybillNumbers: nums,
        };
        this.setState({ listLoading: true });
        let activeKey = tabNum;
        if (key === '0' || key === '1') {
          activeKey = key;
        }
        dispatch({
          type: activeKey == '0' ? 'bill/unBilledBillList' : 'bill/unBilledTaxBill',
          payload: param,
          callback: result => {
            this.setState({ listLoading: false });
            if (result.success) {
              let returnlist = result.data.returnlist;
              if (activeKey === '1') {
                mergeCellData(returnlist, 'waybillNumber');
              }
              this.setState({
                data: returnlist,
                total: result.data.totalrows,
                totalAmount: result.data.totalAmount,
              });
            } else {
              this.setState({
                data: [],
                totalAmount: '',
              });
              message.error(result.message);
            }
          },
        });
      }
    });
  };

  handleChange = e => {
    this.setState({
      waybillNumber: e.target.value,
    });
  };

  validateToNextPassword = (rule, value, callback) => {
    const str = value.replace(/[\r\n]/g, '');
    var counts = value.split('\n').length;
    let valrlurs = new RegExp('^[0-9a-zA-Z ]+$');
    if (counts > 50) {
      callback('最多输入50单');
    } else if (value && !valrlurs.test(str)) {
      callback('不能出现数字及字母外的字符');
    } else if (new Set(value.split('\n')).size !== counts) {
      callback('不能出现重复单号');
    } else {
      callback();
    }
  };
  handleButtonQuery = key => {
    this.state.currentPage = 1;
    this.handleQuery(key);
  };
  exportBill = () => {
    const { waybillNumber, merchantCode, currentPage, tabNum } = this.state;
    const { form } = this.props;
    let info = form.getFieldsValue();
    let epCodes = info?.epCodes === '' ? undefined : info?.epCodes;
    let nums = epCodes?.split(/[\r\n]/g) ?? [];
    const startDateTime = info?.dateTime ? info?.dateTime[0].format('YYYY-MM-DD') : [];
    const endDateTime = info?.dateTime ? info?.dateTime[1].format('YYYY-MM-DD') : [];
    let url =
      portalUrl +
      '/bill/unBilledBill/export?currentPage=' +
      currentPage +
      '&type=' +
      tabNum +
      '&pageSize=' +
      pageSize +
      '&startCalcTime=' +
      startDateTime +
      '&endCalcTime=' +
      endDateTime +
      '&waybillNumbers=' +
      nums;
    downloadFile({
      url,
    });
    // window.open(url);
  };

  changeTab = key => {
    this.setState(
      {
        tabNum: key,
      },
      this.handleButtonQuery(key)
    );
  };

  toTaxTab = record => {
    const { form } = this.props;
    form.setFieldsValue({ epCodes: record.waybillNumber, dateTime: undefined });
    this.setState(
      {
        tabNum: '1',
        currentPage: 1,
        pageSize: 10,
      },
      this.handleQuery('1')
    );
  };

  render() {
    const { notBillBillLoading, children, form } = this.props;
    const { getFieldDecorator } = form;
    const { total, data, totalAmount, visible, tabNum } = this.state;
    // 表头
    if (tabNum === '0') {
      this.columns = [
        {
          dataIndex: 'customerCode',
          title: formatMessage({ id: '制单账号' }),
          width: 150,
        },
        {
          title: formatMessage({ id: '发货仓' }),
          dataIndex: 'companyCodeOfFromName',
        },
        {
          title: formatMessage({ id: '运单号' }),
          dataIndex: 'waybillNumber',
        },
        {
          title: formatMessage({ id: '订单号' }),
          dataIndex: 'orderNumber',
        },
        {
          title: formatMessage({ id: '转单号' }),
          dataIndex: 'exchangeNumber',
        },
        {
          title: formatMessage({ id: '流水号' }),
          dataIndex: 'yanwenOrderNumber',
        },
        {
          title: formatMessage({ id: '快递单日期' }),
          dataIndex: 'calcTime',
          render: text => moment(+text).format('YYYY-MM-DD'),
        },
        {
          title: formatMessage({ id: '产品名称' }),
          dataIndex: 'productName',
        },
        {
          title: formatMessage({ id: '目的地' }),
          dataIndex: 'regionName',
        },
        {
          title: formatMessage({ id: '邮编分区' }),
          dataIndex: 'zone',
        },
        {
          title: `${formatMessage({ id: '计费重量' })}(${formatMessage({ id: '克' })})`,
          dataIndex: 'calcWeight',
        },
        {
          title: formatMessage({ id: '产品名称' }),
          dataIndex: 'productName',
        },
        {
          title: `资费(${this.props.currencyName})`,
          dataIndex: 'discountPrice',
        },
        {
          title: `处理费(${this.props.currencyName})`,
          dataIndex: 'proPrice',
        },
        {
          title: `${formatMessage({ id: '干线调拨费' })}(${this.props.currencyName})`,
          dataIndex: 'moneyTrunk',
        },
        {
          title: `${formatMessage({ id: '税费' })}(${this.props.currencyName})`,
          dataIndex: 'taxation',
          render: (text, record) => (
            <a
              onClick={
                text
                  ? e => {
                      e.preventDefault();
                      this.toTaxTab(record);
                    }
                  : undefined
              }
              style={!text ? { cursor: 'default', color: 'inherit', textDecoration: 'none' } : {}}
            >
              {text || '-'}
            </a>
          ),
        },
        {
          title: `${formatMessage({ id: '附加费' })}(${this.props.currencyName})`,
          dataIndex: 'acctachPrice',
        },
        {
          title: `${formatMessage({ id: '未出账单金额' })}(${this.props.currencyName})`,
          dataIndex: 'arPrice',
        },
      ];
    } else {
      this.columns = [
        {
          dataIndex: 'customerCode',
          title: formatMessage({ id: '制单账号' }),
          width: 150,
          render: (text, row) => {
            return {
              children: text,
              props: {
                rowSpan: row.rowSpan,
              },
            };
          },
        },
        {
          title: '计费时间',
          dataIndex: 'calcTime',
          render: (text, row) => {
            return {
              children: moment(+text).format('YYYY-MM-DD'),
              props: {
                rowSpan: row.rowSpan,
              },
            };
          },
        },
        {
          title: '账单日期',
          dataIndex: 'dateOfBill',
          render: (text, row) => {
            return {
              children: text ? moment(+text).format('YYYY-MM-DD') : '-',
              props: {
                rowSpan: row.rowSpan,
              },
            };
          },
        },
        {
          title: formatMessage({ id: '运单号' }),
          dataIndex: 'waybillNumber',
          render: (text, row) => {
            return {
              children: text,
              props: {
                rowSpan: row.rowSpan,
              },
            };
          },
        },
        {
          title: formatMessage({ id: '订单号' }),
          dataIndex: 'orderNumber',
          render: (text, row) => {
            return {
              children: text,
              props: {
                rowSpan: row.rowSpan,
              },
            };
          },
        },
        {
          title: formatMessage({ id: '转单号' }),
          dataIndex: 'exchangeNumber',
          render: (text, row) => {
            return {
              children: text,
              props: {
                rowSpan: row.rowSpan,
              },
            };
          },
        },
        {
          title: formatMessage({ id: '产品名称' }),
          dataIndex: 'productName',
          render: (text, row) => {
            return {
              children: text,
              props: {
                rowSpan: row.rowSpan,
              },
            };
          },
        },
        {
          title: formatMessage({ id: '目的地' }),
          dataIndex: 'regionName',
          render: (text, row) => {
            return {
              children: text,
              props: {
                rowSpan: row.rowSpan,
              },
            };
          },
        },
        {
          title: '原始品名',
          dataIndex: 'itemName',
        },
        {
          title: '海关编码',
          dataIndex: 'hsCode',
        },
        {
          title: 'HTS',
          dataIndex: 'htsTaxCode',
        },
        {
          title: '税类',
          dataIndex: 'taxName',
        },
        {
          title: '税率',
          dataIndex: 'taxRate',
          render: text => {
            if (text == null) return '-';
            const value = Math.round((parseFloat(text) * 100 + Number.EPSILON) * 100) / 100;
            return `${value}%`;
          },
        },
        {
          title: '申报价值',
          dataIndex: 'declaredValue',
        },
        {
          title: '申报币种',
          dataIndex: 'declaredCurrencyName',
        },
        {
          title: '外币税金',
          dataIndex: 'moneyFcTax',
        },
        {
          title: '汇率',
          dataIndex: 'declaredRate',
        },
        {
          title: `税费(${this.props.currencyName})`,
          dataIndex: 'moneyTax',
        },
      ];
    }

    const paginationProps = {
      onChange: (page, size) => {
        this.state.currentPage = page;
        pageSize = size;
        this.handleQuery();
      },
      current: this.state.currentPage,
      pageSize: pageSize,
      showSizeChanger: true,
      pageSizeOptions: ['10', '20', '50', '100', '500'],
      total: total,
    };
    return (
      <span>
        <span onClick={this.showModelHandler}>{children}</span>
        <Modal
          visible={visible}
          style={{
            top: 0,
          }}
          width="100%"
          height="100%"
          footer={false}
          onCancel={this.cancelHandle}
        >
          <PageContainerComponent
            header={{
              title: null,
              breadcrumb: {},
              breadcrumbRender: props => (
                <PageHeaderBreadcrumb {...props} col>
                  <h2 style={{ margin: '16px 0', fontSize: '20px' }}>
                    {formatMessage({ id: '未出账单明细' })}
                  </h2>
                </PageHeaderBreadcrumb>
              ),
            }}
          >
            <div>
              <Card>
                <Form layout="horizontal">
                  <Row gutter={40}>
                    <Col>
                      <Form.Item label={formatMessage({ id: '运单号' })}>
                        {getFieldDecorator('epCodes', {
                          initialValue: '',
                          rules: [
                            {
                              required: false,
                              message: formatMessage({ id: '搜索运单号不能为空' }),
                            },
                            {
                              validator: this.validateToNextPassword,
                            },
                          ],
                        })(
                          <Input.TextArea
                            placeholder="最多50单,每单一行"
                            style={{ display: 'inline-block', height: '120px', width: '260px' }}
                            rows={4}
                          />
                        )}
                      </Form.Item>
                      {/* <StandardFormRow
                  title="运单号"
                  block
                  style={{ marginRight: 40, marginTop: 0, marginBottom: 0, border: 'none' }}
                >
                  <Input placeholder="请输入运单号" onChange={this.handleChange} />
                </StandardFormRow> */}
                    </Col>
                    <Col>
                      <div
                        style={{
                          display: 'flex',
                          flexDirection: 'column',
                          marginBottom: '23px',
                        }}
                      >
                        <Form.Item label={formatMessage({ id: '日期' })}>
                          {getFieldDecorator('dateTime', {
                            initialValue: [
                              moment(startDate, 'YYYY-MM-DD'),
                              moment(endDate, 'YYYY-MM-DD'),
                            ],
                            rules: [
                              {
                                required: false,
                                message: formatMessage({ id: '搜索日期不能为空' }),
                              },
                            ],
                          })(
                            <RangePicker
                            // onChange={this.onChange}
                            // defaultValue={[
                            //   moment(startDate, 'YYYY-MM-DD'),
                            //   moment(endDate, 'YYYY-MM-DD'),
                            // ]}
                            />
                          )}
                        </Form.Item>
                        <Space span={4}>
                          <Button
                            type="primary"
                            style={{ marginRight: 30 }}
                            onClick={this.handleButtonQuery}
                          >
                            {formatMessage({ id: '查询' })}
                          </Button>
                          <Button type="primary" onClick={this.exportBill}>
                            {formatMessage({ id: '下载' })}
                          </Button>
                        </Space>
                      </div>

                      {/* <StandardFormRow
                  title="日期"
                  block
                  style={{ marginTop: 0, marginBottom: 0, border: 'none' }}
                >
                  <RangePicker
                    onChange={this.onChange}
                    defaultValue={[moment(startDate, 'YYYY-MM-DD'), moment(endDate, 'YYYY-MM-DD')]}
                  />
                </StandardFormRow> */}
                    </Col>
                  </Row>
                </Form>
              </Card>
              <Card style={{ marginTop: 20 }} bordered={false} className="listCard">
                <Spin spinning={this.state.listLoading} size="middle">
                  <Tabs activeKey={this.state.tabNum} onChange={this.changeTab}>
                    <TabPane tab={`账单`} key="0"></TabPane>
                    <TabPane tab={'税费'} key="1"></TabPane>
                  </Tabs>
                  <Table
                    size="small"
                    dataSource={data}
                    columns={this.columns.filter(item => item.filterType === undefined)}
                    scroll={{ x: 'max-content' }}
                    pagination={paginationProps}
                  />
                  <div
                    style={{ textAlign: 'right', fontSize: 20, paddingRight: 40, color: '#333' }}
                  >
                    {formatMessage({ id: '筛选合计' })}：&nbsp;&nbsp;&nbsp;&nbsp;
                    {tabNum == '0' && (
                      <>
                        <span style={{ fontSize: 22 }}>{total}票</span>&nbsp;&nbsp;&nbsp;&nbsp;
                      </>
                    )}
                    <span style={{ fontSize: 22 }}>
                      <span style={{ color: 'red' }}>{totalAmount}</span>
                      {this.props.currencyName}
                    </span>
                    &nbsp;&nbsp;&nbsp;&nbsp;
                  </div>
                </Spin>
              </Card>
            </div>
          </PageContainerComponent>
        </Modal>
      </span>
    );
  }
}

export default NotOutBill;
