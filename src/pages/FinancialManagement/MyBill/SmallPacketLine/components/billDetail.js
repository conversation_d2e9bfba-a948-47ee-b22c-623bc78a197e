import React, { Component } from 'react';
import router from 'umi/router';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {
  Card,
  DatePicker,
  Row,
  Col,
  Button,
  Breadcrumb,
  Table,
  message,
  Input,
  Divider,
  List,
  Modal,
} from 'antd';
import StandardFormRow from '@/components/StandardFormRow';
import moment from 'moment';
import { connect } from 'dva';
import defaultSettings from '../../../../../../config/defaultSettings';
import ProTable from '@ant-design/pro-table';
import PageContainerComponent from '@/components/PageContainer';
import PageHeaderBreadcrumb from '@/components/PageHeaderBreadcrumb';
import { DownloadTaskType } from '@/utils/commonConstant';
import { downloadExport } from '@/utils/utils';

const { portalUrl } = defaultSettings;
const { RangePicker } = DatePicker;
let startDate = moment()
  .startOf('month')
  .format('YYYY-MM-DD');
let endDate = moment().format('YYYY-MM-DD');
let transType = '';
let mainNo = '';
const transTypeOptions = [
  'TC01',
  'BTC01',
  'CTC01',
  'TC02',
  'BTC02',
  'CTC02',
  'TC03',
  'BTC03',
  'CTC03',
  'TC04',
  'BTC04',
  'CTC04',
  'TC05',
  'BTC05',
  'CTC05',
  'TC08',
  'BTC08',
  'CTC08',
  'TC15',
  'BTC15',
  'CTC15',
  'BTC35',
  'CTC35',
  'BTC39',
  'TC38',
  'BTC38',
];

@connect(({ bill, loading }) => ({
  bill,
  pageLoading: loading.effects['bill/detail'],
}))
@Form.create()
class BillDetail extends Component {
  constructor(props) {
    super(props);
    this.state = {
      data: [],
      totalAmount: '',
      total: 0,
      billInfo: '',
      waybillNumber: '',
      currentPage: 1,
      pageSize: 10,
      sumRow: 0,
      flag: true,
      billWeightunit: '',
      indicator: undefined,
      transType: '',
      visible: false,
    };
    const { r } = props;

    this.state.indicator = r.indicator ?? 1;
    this.state.transType = r.transType;
  }

  getTableColumns = (transType, indicator) => {
    const { billWeightunit } = this.state;
    let columns;
    // 应收快件
    if (transType === 'TC01' || transType === 'BTC01' || transType === 'CTC01') {
      columns =
        indicator != 2
          ? [
              {
                dataIndex: 'customerCode',
                title: '制单账号',
                width: 150,
                // filterType: transType === 'CTC01' ? true : undefined,
              },
              {
                dataIndex: 'waybillNumber',
                title: '运单号',
                width: 150,
              },
              {
                dataIndex: 'orderNumber',
                title: '订单号',
                width: 150,
              },
              {
                dataIndex: 'exchangeNumber',
                title: '转单号',
                width: 160,
              },
              // {
              //   dataIndex: 'yanwenNumber',
              //   title: '参考号',
              //   width: 150,
              // },
              {
                dataIndex: 'calcTime',
                title: '账单日期',
                render: text => {
                  return moment(text).format('YYYY-MM-DD');
                },
                width: 150,
              },
              {
                dataIndex: 'productName',
                title: '产品名称',
                width: 180,
              },
              {
                dataIndex: 'regionName',
                title: '目的地',
                width: 150,
              },
              {
                dataIndex: 'companyCodeOfFromName',
                title: '发货仓',
                filterType: true,
                width: 150,
              },

              {
                dataIndex: 'calcWeight',
                title: '计费重量(克)',
                width: 150,
              },
              {
                dataIndex: 'discountPrice',
                title: `资费(${this.props.currencyName})`,
                width: 130,

                filterType: transType === 'CTC01' ? true : undefined,
              },
              {
                dataIndex: 'proPrice',
                title: `处理费(${this.props.currencyName})`,
                width: 130,

                filterType: transType === 'CTC01' ? true : undefined,
              },
              // {
              //   dataIndex: 'arPrice',
              //   title: `税费(${this.props.currencyName})`,
              //   width: 130,
              //
              //   filterType: transType === 'CTC01' ? true : undefined,
              // },
              {
                dataIndex: 'acctachPrice',
                title: `附加费(${this.props.currencyName})`,
                width: 130,

                filterType: transType === 'CTC01' ? true : undefined,
              },
              {
                dataIndex: 'moneyTrunk',
                title: `干线费(${this.props.currencyName})`,
                width: 130,

                filterType: transType === 'CTC01' ? true : undefined,
              },
              {
                dataIndex: 'arPrice',
                title: `账单金额(${this.props.currencyName})`,
                width: 130,
                fixed: 'right',
              },
            ]
          : [
              {
                dataIndex: 'customerCode',
                title: '制单账号',
                width: 150,
              },
              {
                dataIndex: 'timeOfCalc',
                title: '计费时间',
                render: text => {
                  return moment(text).format('YYYY-MM-DD');
                },
                width: 150,
              },
              {
                dataIndex: 'waybillNumber',
                title: '运单号',
                width: 150,
              },
              {
                dataIndex: 'exchangeNumber',
                title: '转单号',
                width: 150,
              },
              {
                dataIndex: 'productName',
                title: '产品名称',
                width: 150,
              },
              {
                dataIndex: 'regionName',
                title: '国家',
                width: 150,
              },
              {
                dataIndex: 'postCode',
                title: '邮编',
                width: 150,
              },
              {
                dataIndex: 'piece',
                title: '件数',
                width: 150,
              },
              {
                dataIndex: 'weight',
                title: '实重(g)',
                width: 150,
              },
              {
                dataIndex: 'dimWeight',
                title:
                  billWeightunit === ''
                    ? '体积重'
                    : billWeightunit === 'kg'
                    ? '体积重(kg)'
                    : '体积重(g)',
                width: 150,
              },
              {
                dataIndex: 'calcWeight',
                title: '计费重(g)',
                width: 150,
              },
              // {
              //   dataIndex: 'unitPrice',
              //   title: '单价',
              // },
              {
                dataIndex: 'itemName',
                title: '费用名称',
                width: 150,
              },
              {
                dataIndex: 'companyCodeOfFromName',
                title: '发货仓',
                filterType: true,
                width: 150,
              },

              {
                dataIndex: 'proPrice',
                title: `处理费(${this.props.currencyName})`,
                width: 150,
              },
              {
                dataIndex: 'discountPrice',
                title: `金额(${this.props.currencyName})`,
                width: 150,
                fixed: 'right',
              },
            ];
    } else if (transType === 'TC02' || transType === 'BTC02' || transType === 'CTC02') {
      // 应收退件
      columns =
        indicator != 2
          ? [
              {
                dataIndex: 'rowNum',
                title: '序号 ',
                width: 150,
              },
              {
                dataIndex: 'waybillNumber',
                title: '运单号',
                width: 150,
              },
              {
                dataIndex: 'orderCode',
                title: '订单号 ',
                width: 150,
              },
              {
                dataIndex: 'exchangeNumber',
                title: '转单号',
                filterType: true,
                width: 150,
              },
              {
                dataIndex: 'calcTime',
                title: '账单日期',
                render: text => {
                  return moment(text).format('YYYY-MM-DD');
                },
                width: 150,
              },
              {
                dataIndex: 'productName',
                title: '产品名称',
                width: 150,
              },
              {
                dataIndex: 'regionName',
                title: '目的地',
                width: 150,
              },
              {
                dataIndex: 'companyCodeOfFromName',
                title: '发货仓',
                filterType: true,
                width: 150,
              },
              {
                dataIndex: 'calcWeight',
                title: '计费重量(克)',
                width: 150,
              },
              // {
              //   dataIndex: 'customerCode',
              //   title: '客户号',
              //   width: 150,
              // },
              // {
              //   dataIndex: 'merchantCode',
              //   title: '商户号',
              //   width: 150,
              // },
              // {
              //   dataIndex: 'merchantName',
              //   title: '商户名称',
              //   width: 150,
              // },

              // {
              //   dataIndex: 'salesManagerName',
              //   title: '销售员',
              //   width: 150,
              // },
              // {
              //   dataIndex: 'receivableName',
              //   title: '收款人',
              //   width: 150,
              // },

              {
                dataIndex: 'indemnityTotalPrice',
                title: '赔偿金额',
                filterType: true,
                width: 150,
              },
              {
                dataIndex: 'arPrice',
                title: `运费(${this.props.currencyName})`,
                width: 150,
                fixed: 'right',
              },
            ]
          : [
              {
                dataIndex: 'rowNum',
                title: '序号 ',
                width: 150,
              },
              {
                dataIndex: 'waybillNumber',
                title: '运单号',
                width: 150,
              },
              {
                dataIndex: 'orderCode',
                title: '订单号 ',
                width: 150,
              },
              {
                dataIndex: 'exchangeNumber',
                title: '转单号',
                filterType: true,
                width: 150,
              },
              {
                dataIndex: 'calcTime',
                title: '账单日期',
                render: text => {
                  return moment(text).format('YYYY-MM-DD');
                },
                width: 150,
              },
              {
                dataIndex: 'productName',
                title: '产品名称',
                width: 150,
              },
              {
                dataIndex: 'regionName',
                title: '目的地',
                width: 150,
              },
              {
                dataIndex: 'companyCodeOfFromName',
                title: '发货仓',
                filterType: true,
                width: 150,
              },
              // {
              //   dataIndex: 'transTypeName',
              //   title: '交易类型',
              //   width: 150,
              // },
              {
                dataIndex: 'calcWeight',
                title: '计费重量(克)',
                width: 150,
              },
              // {
              //   dataIndex: 'customerCode',
              //   title: '客户号',
              //   width: 150,
              // },
              // {
              //   dataIndex: 'merchantCode',
              //   title: '商户号',
              //   width: 150,
              // },
              // {
              //   dataIndex: 'merchantName',
              //   title: '商户名称',
              //   width: 150,
              // },

              // {
              //   dataIndex: 'salesManagerName',
              //   title: '销售员',
              //   width: 150,
              // },
              // {
              //   dataIndex: 'receivableName',
              //   title: '收款人',
              //   width: 150,
              // },

              {
                dataIndex: 'indemnityTotalPrice',
                title: '赔偿金额',
                filterType: true,
                width: 150,
              },
              {
                dataIndex: 'arPrice',
                title: `运费(${this.props.currencyName})`,
                width: 150,
                fixed: 'right',
              },
            ];
    } else if (transType === 'TC03' || transType === 'BTC03' || transType === 'CTC03') {
      // 应收赔偿
      columns =
        indicator != 2
          ? [
              {
                dataIndex: 'customerCode',
                title: '制单账号',
                width: 150,
                filterType: true,
              },
              // {
              //   dataIndex: 'rowNum',
              //   title: '序号',
              //   width: 150,
              // },
              {
                dataIndex: 'calcTime',
                title: '账单日期',
                render: text => {
                  return moment(text).format('YYYY-MM-DD');
                },
                width: 150,
              },
              {
                dataIndex: 'waybillNumber',
                title: '运单号',
                width: 150,
              },
              {
                dataIndex: 'orderCode',
                title: '订单号',
                width: 150,
                filterType: true,
              },
              {
                dataIndex: 'exchangeNumber',
                title: '转单号',
                width: 150,
                filterType: true,
              },

              {
                dataIndex: 'productName',
                title: '产品名称',
                width: 150,
              },
              // {
              //   dataIndex: 'merchantCode',
              //   title: '商户号',
              //   width: 150,
              // },
              // {
              //   dataIndex: 'merchantName',
              //   title: '商户名称',
              //   width: 150,
              // },
              {
                dataIndex: 'regionName',
                title: '目的地',
                width: 150,
              },
              {
                dataIndex: 'companyCodeOfFromName',
                title: '发货仓',
                filterType: true,
                width: 150,
              },
              {
                dataIndex: 'calcWeight',
                title: '计费重量(克)',
                width: 150,
              },
              // {
              //   dataIndex: 'transTypeName',
              //   title: '交易类型',
              //   width: 150,
              // },
              {
                dataIndex: 'arPrice',
                title: `运费(${this.props.currencyName})`,
                width: 150,
              },
              {
                dataIndex: 'indemnityTotalPrice',
                title: `赔偿金额(${this.props.currencyName})`,
                width: 150,
                fixed: 'right',
              },
            ]
          : [
              {
                dataIndex: 'customerCode',
                title: '制单账号',
                width: 150,
                // filterType: transType === 'CTC01' ? true : undefined,
                filterType: true,
              },
              // {
              //   dataIndex: 'rowNum',
              //   title: '序号',
              //   width: 150,
              // },
              {
                dataIndex: 'calcTime',
                title: '账单日期',
                render: text => {
                  return moment(text).format('YYYY-MM-DD');
                },
                width: 150,
              },
              {
                dataIndex: 'waybillNumber',
                title: '运单号',
                width: 150,
              },
              {
                dataIndex: 'orderCode',
                title: '订单号',
                width: 150,
                filterType: true,
              },
              {
                dataIndex: 'exchangeNumber',
                title: '转单号',
                width: 150,
                filterType: true,
              },

              {
                dataIndex: 'productName',
                title: '产品名称',
                width: 150,
              },
              // {
              //   dataIndex: 'merchantCode',
              //   title: '商户号',
              //   width: 150,
              // },
              // {
              //   dataIndex: 'merchantName',
              //   title: '商户名称',
              //   width: 150,
              // },
              {
                dataIndex: 'regionName',
                title: '目的地',
                width: 150,
              },
              {
                dataIndex: 'companyCodeOfFromName',
                title: '发货仓',
                filterType: true,
                width: 150,
              },
              {
                dataIndex: 'calcWeight',
                title: '计费重量(克)',
                width: 150,
              },
              // {
              //   dataIndex: 'transTypeName',
              //   title: '交易类型',
              //   width: 150,
              // },
              {
                dataIndex: 'arPrice',
                title: `运费(${this.props.currencyName})`,
                width: 150,
              },
              {
                dataIndex: 'indemnityTotalPrice',
                title: `赔偿金额(${this.props.currencyName})`,
                width: 150,
                fixed: 'right',
              },
            ];
    } else if (transType === 'TC04' || transType === 'BTC04' || transType === 'CTC04') {
      // 二次费用
      columns =
        indicator != 2
          ? [
              {
                dataIndex: 'customerCode',
                title: '制单账号',
                width: 150,
                // filterType: transType === 'CTC01' ? true : undefined,
                // filterType: true,
              },
              {
                dataIndex: 'calcTime',
                title: '账单日期',
                render: text => {
                  return moment(text).format('YYYY-MM-DD');
                },
                width: 150,
              },
              {
                dataIndex: 'waybillNumber',
                title: '运单号',
                width: 150,
              },
              {
                dataIndex: 'orderCode',
                title: '订单号',
                width: 150,
              },
              {
                dataIndex: 'exchangeNumber',
                title: '转单号',
                filterType: true,
                width: 150,
              },
              {
                dataIndex: 'productName',
                title: '产品名称',
                width: 150,
              },
              {
                dataIndex: 'regionName',
                title: '目的地',
                width: 150,
              },
              {
                dataIndex: 'companyCodeOfFromName',
                title: '发货仓',
                filterType: true,
                width: 150,
              },
              {
                dataIndex: 'itemName',
                title: '费用名称',
                width: 150,
              },
              // {
              //   dataIndex: 'calcWeight',
              //   title: '计费重量(克)',
              //   width: 150,
              // },

              {
                dataIndex: 'arPrice',
                title: `账单金额(${this.props.currencyName})`,
                width: 150,
                fixed: 'right',
              },
            ]
          : [
              {
                dataIndex: 'customerCode',
                title: '制单账号',
                width: 150,
                // filterType: transType === 'CTC01' ? true : undefined,
                // filterType: true,
              },
              {
                dataIndex: 'calcTime',
                title: '账单日期',
                render: text => {
                  return moment(text).format('YYYY-MM-DD');
                },
                width: 150,
              },
              {
                dataIndex: 'waybillNumber',
                title: '运单号',
                width: 150,
              },
              {
                dataIndex: 'orderCode',
                title: '订单号',
                width: 150,
              },
              {
                dataIndex: 'exchangeNumber',
                title: '转单号',
                filterType: true,
                width: 150,
              },
              {
                dataIndex: 'productName',
                title: '产品名称',
                width: 150,
              },
              {
                dataIndex: 'regionName',
                title: '目的地',
                width: 150,
              },
              {
                dataIndex: 'companyCodeOfFromName',
                title: '发货仓',
                filterType: true,
                width: 150,
              },
              {
                dataIndex: 'itemName',
                title: '费用名称',
                width: 150,
              },
              // {
              //   dataIndex: 'calcWeight',
              //   title: '计费重量(克)',
              //   width: 150,
              // },

              {
                dataIndex: 'arPrice',
                title: `账单金额(${this.props.currencyName})`,
                width: 150,
                fixed: 'right',
              },
            ];
    } else if (transType === 'TC05' || transType === 'BTC05' || transType === 'CTC05') {
      // 应收返点
      columns =
        indicator != 2
          ? [
              {
                dataIndex: 'waybillNumber',
                title: '运单号',
                width: 150,
              },
              {
                dataIndex: 'orderNumber',
                title: '订单号',
                width: 150,
              },
              {
                dataIndex: 'exchangeNumber',
                title: '转单号',
                width: 150,
              },
              {
                dataIndex: 'yanwenNumber',
                title: '参考号',
                width: 150,
              },
              {
                dataIndex: 'calcTime',
                title: '账单日期',
                render: text => {
                  return moment(text).format('YYYY-MM-DD');
                },
                width: 150,
              },
              {
                dataIndex: 'regionName',
                title: '目的地',
                width: 150,
              },
              {
                dataIndex: 'calcWeight',
                title: '计费重量(克)',
                width: 150,
              },
              {
                dataIndex: 'productName',
                title: '产品名称',
                width: 150,
              },
              {
                dataIndex: 'areaName',
                title: '所属区域',
                width: 150,
              },
              {
                dataIndex: 'discountPrice',
                title: `资费(${this.props.currencyName})`,
                width: 130,
              },
              {
                dataIndex: 'proPrice',
                title: `处理费(${this.props.currencyName})`,
                width: 130,
              },
              {
                dataIndex: 'moneyTrunk',
                title: `干线费(${this.props.currencyName})`,
                width: 130,
              },
              {
                dataIndex: 'acctachPrice',
                title: `附加费(${this.props.currencyName})`,
                width: 130,
              },
              {
                dataIndex: 'arPrice',
                title: `账单金额(${this.props.currencyName})`,
                width: 130,
                fixed: 'right',
              },
            ]
          : [
              {
                dataIndex: 'customerCode',
                title: '制单账号',
                width: 150,
              },
              {
                dataIndex: 'timeOfCalc',
                title: '计费时间',
                render: text => {
                  return moment(text).format('YYYY-MM-DD');
                },
                width: 150,
              },
              {
                dataIndex: 'waybillNumber',
                title: '运单号',
                width: 150,
              },
              {
                dataIndex: 'exchangeNumber',
                title: '转单号',
                width: 150,
              },
              // {
              //   dataIndex: 'calcTime',
              //   title: '账单日期',
              //   render: text => {
              //     return moment(text).format('YYYY-MM-DD');
              //   },
              // },
              {
                dataIndex: 'productName',
                title: '产品名称',
                width: 150,
              },
              {
                dataIndex: 'regionName',
                title: '国家',
                width: 150,
              },
              {
                dataIndex: 'postCode',
                title: '邮编',
                width: 150,
              },
              {
                dataIndex: 'piece',
                title: '件数',
                width: 150,
              },
              {
                dataIndex: 'weight',
                title: '实重(g)',
                width: 150,
              },
              {
                dataIndex: 'dimWeight',
                title:
                  billWeightunit === ''
                    ? '体积重'
                    : billWeightunit === 'kg'
                    ? '体积重(kg)'
                    : '体积重(g)',
                width: 150,
              },
              {
                dataIndex: 'calcWeight',
                title: '计费重(g)',
                width: 150,
              },
              // {
              //   dataIndex: 'unitPrice',
              //   title: '单价',
              // },
              {
                dataIndex: 'itemName',
                title: '费用名称',
                width: 150,
              },
              {
                dataIndex: 'companyCodeOfFromName',
                title: '发货仓',
                filterType: true,
                width: 150,
              },

              {
                dataIndex: 'arPrice',
                title: `折前金额(${this.props.currencyName})`,
                width: 150,
              },
              {
                dataIndex: 'discountPrice',
                title: `金额(${this.props.currencyName})`,
                width: 150,
                fixed: 'right',
              },
            ];
    } else if (transType === 'TC08' || transType === 'BTC08' || transType === 'CTC08') {
      // 应收国内快递退件

      columns =
        indicator != 2
          ? [
              // {
              //   dataIndex: 'rowNum',
              //   title: '序号',
              //   width: 150,
              // },
              {
                dataIndex: 'supplierName',
                title: '国内快递公司',
                width: 150,
                render: (text, record) => (
                  <List
                    rowKey={(record, index) => index}
                    dataSource={text}
                    pagination={false}
                    renderItem={item => (
                      <List.Item>
                        <span style={{ textAlign: 'center', flex: 1 }}>{item}</span>
                      </List.Item>
                    )}
                  />
                ),
              },
              {
                dataIndex: 'domesticNo',
                title: '国内快递单号',
                width: 150,
              },

              {
                dataIndex: 'accountCode',
                title: '制单账号',
                width: 150,
                render: (text, record) => (
                  <List
                    rowKey={(record, index) => index}
                    dataSource={text}
                    pagination={false}
                    renderItem={item => (
                      <List.Item>
                        <span style={{ textAlign: 'center', flex: 1 }}>{item}</span>
                      </List.Item>
                    )}
                  />
                ),
              },
              {
                dataIndex: 'waybillNumber',
                title: '运单号',
                width: 150,
                render: (text, record) => (
                  <List
                    rowKey={(record, index) => index}
                    dataSource={text}
                    pagination={false}
                    renderItem={item => (
                      <List.Item>
                        <span style={{ textAlign: 'center', flex: 1 }}>{item}</span>
                      </List.Item>
                    )}
                  />
                ),
              },
              {
                dataIndex: 'timeOfExpress',
                title: '账单日期',
                render: text => {
                  return moment(text).format('YYYY-MM-DD');
                },
                width: 150,
              },
              {
                dataIndex: 'returnTime',
                title: '退件时间',
                render: (text, record) => (
                  <List
                    rowKey={(record, index) => index}
                    dataSource={text}
                    pagination={false}
                    renderItem={item => (
                      <List.Item>
                        <span style={{ textAlign: 'center', flex: 1 }}>
                          {moment(item).format('YYYY-MM-DD')}
                        </span>
                      </List.Item>
                    )}
                  />
                ),
                width: 150,
              },
              {
                dataIndex: 'expressProductNames',
                title: '产品名称',
                width: 150,
                render: (text, record) => (
                  <List
                    rowKey={(record, index) => index}
                    dataSource={text}
                    pagination={false}
                    renderItem={item => (
                      <List.Item>
                        <span style={{ textAlign: 'center', flex: 1 }}>{item}</span>
                      </List.Item>
                    )}
                  />
                ),
              },
              {
                dataIndex: 'regionDesc',
                title: '目的地',
                width: 150,
                render: (text, record) => (
                  <List
                    rowKey={(record, index) => index}
                    dataSource={text}
                    pagination={false}
                    renderItem={item => (
                      <List.Item>
                        <span style={{ textAlign: 'center', flex: 1 }}>{item}</span>
                      </List.Item>
                    )}
                  />
                ),
              },
              {
                dataIndex: 'companyCodeOfFromName',
                title: '发货仓',
                filterType: true,
                width: 150,
                render: (text, record) => (
                  <List
                    rowKey={(record, index) => index}
                    dataSource={text}
                    pagination={false}
                    renderItem={item => (
                      <List.Item>
                        <span style={{ textAlign: 'center', flex: 1 }}>{item}</span>
                      </List.Item>
                    )}
                  />
                ),
              },
              {
                dataIndex: 'reasonDesc',
                title: '退件原因',
                width: 150,
                render: (text, record) => (
                  <List
                    rowKey={(record, index) => index}
                    dataSource={text}
                    pagination={false}
                    renderItem={item => (
                      <List.Item>
                        <span style={{ textAlign: 'center', flex: 1 }}>{item}</span>
                      </List.Item>
                    )}
                  />
                ),
              },
              {
                dataIndex: 'MoneyToDif',
                title: `退件金额(${this.props.currencyName})`,
                width: 150,
                fixed: 'right',
              },
              // {
              //   dataIndex: 'TimeOfExpress1',
              //   title: '国内快递单日期',
              //   render: text => {
              //     return moment(text).format('YYYY-MM-DD');
              //   },
              //   width: 150,
              // },

              // {
              //   dataIndex: 'regionName',
              //   title: '国家',
              //   width: 150,
              // },
              // {
              //   dataIndex: 'areaName',
              //   title: '所属区域',
              //   width: 150,
              // },
            ]
          : [
              {
                dataIndex: 'customerCode',
                title: '制单账号',
                width: 150,
              },
              {
                dataIndex: 'timeOfCalc',
                title: '计费时间',
                render: text => {
                  return moment(text).format('YYYY-MM-DD');
                },
                width: 150,
              },
              {
                dataIndex: 'waybillNumber',
                title: '运单号',
                width: 150,
              },
              {
                dataIndex: 'exchangeNumber',
                title: '转单号',
                width: 150,
              },
              // {
              //   dataIndex: 'calcTime',
              //   title: '账单日期',
              //   render: text => {
              //     return moment(text).format('YYYY-MM-DD');
              //   },
              // },
              {
                dataIndex: 'productName',
                title: '产品名称',
                width: 150,
              },
              {
                dataIndex: 'regionName',
                title: '国家',
                width: 150,
              },
              {
                dataIndex: 'postCode',
                title: '邮编',
                width: 150,
              },
              {
                dataIndex: 'piece',
                title: '件数',
                width: 150,
              },
              {
                dataIndex: 'weight',
                title: '实重(g)',
                width: 150,
              },
              {
                dataIndex: 'dimWeight',
                title:
                  billWeightunit === ''
                    ? '体积重'
                    : billWeightunit === 'kg'
                    ? '体积重(kg)'
                    : '体积重(g)',
                width: 150,
              },
              {
                dataIndex: 'calcWeight',
                title: '计费重(g)',
                width: 150,
              },
              // {
              //   dataIndex: 'unitPrice',
              //   title: '单价',
              // },
              {
                dataIndex: 'itemName',
                title: '费用名称',
                width: 150,
              },

              {
                dataIndex: 'companyCodeOfFromName',
                title: '发货仓',
                filterType: true,
                width: 150,
              },

              {
                dataIndex: 'arPrice',
                title: `折前金额(${this.props.currencyName})`,
                width: 150,
              },
              {
                dataIndex: 'discountPrice',
                title: `金额(${this.props.currencyName})`,
                width: 150,
                fixed: 'right',
              },
            ];
    } else if (transType === 'TC15' || transType === 'BTC15' || transType === 'CTC15') {
      // 应收优惠
      columns =
        indicator != 2
          ? [
              {
                dataIndex: 'rowNum',
                title: '序号',
                width: 150,
              },
              {
                dataIndex: 'waybillNumber',
                title: '运单号',
                width: 150,
              },
              {
                dataIndex: 'orderNumber',
                title: '订单号',
                width: 150,
              },
              {
                dataIndex: 'exchangeNumber',
                title: '转单号',
                width: 150,
              },
              {
                dataIndex: 'yanwenNumber',
                title: '参考号',
                width: 150,
              },
              {
                dataIndex: 'calcTime',
                title: '账单日期',
                render: text => {
                  return moment(text).format('YYYY-MM-DD');
                },
                width: 150,
              },
              {
                dataIndex: 'productName',
                title: '产品名称',
                width: 150,
              },
              {
                dataIndex: 'areaName',
                title: '所属区域',
                width: 150,
              },
              {
                dataIndex: 'regionName',
                title: '目的地',
                width: 150,
              },
              {
                dataIndex: 'calcWeight',
                title: '计费重量(克)',
                width: 150,
              },
              {
                dataIndex: 'arPrice',
                title: `优惠返利(${this.props.currencyName})`,
                width: 150,
                fixed: 'right',
              },
            ]
          : [
              {
                dataIndex: 'customerCode',
                title: '制单账号',
                width: 150,
              },
              {
                dataIndex: 'timeOfCalc',
                title: '计费时间',
                render: text => {
                  return moment(text).format('YYYY-MM-DD');
                },
                width: 150,
              },
              {
                dataIndex: 'waybillNumber',
                title: '运单号',
                width: 150,
              },
              {
                dataIndex: 'exchangeNumber',
                title: '转单号',
                width: 150,
              },
              // {
              //   dataIndex: 'calcTime',
              //   title: '账单日期',
              //   render: text => {
              //     return moment(text).format('YYYY-MM-DD');
              //   },
              // },
              {
                dataIndex: 'productName',
                title: '产品名称',
                width: 150,
              },
              {
                dataIndex: 'regionName',
                title: '国家',
                width: 150,
              },
              {
                dataIndex: 'postCode',
                title: '邮编',
                width: 150,
              },
              {
                dataIndex: 'piece',
                title: '件数',
                width: 150,
              },
              {
                dataIndex: 'weight',
                title: '实重(g)',
                width: 150,
              },
              {
                dataIndex: 'dimWeight',
                title:
                  billWeightunit === ''
                    ? '体积重'
                    : billWeightunit === 'kg'
                    ? '体积重(kg)'
                    : '体积重(g)',
                width: 150,
              },
              {
                dataIndex: 'calcWeight',
                title: '计费重(g)',
                width: 150,
              },
              // {
              //   dataIndex: 'unitPrice',
              //   title: '单价',
              // },
              {
                dataIndex: 'itemName',
                title: '费用名称',
                width: 150,
              },

              {
                dataIndex: 'companyCodeOfFromName',
                title: '发货仓',
                filterType: true,
                width: 150,
              },

              {
                dataIndex: 'arPrice',
                title: `折前金额(${this.props.currencyName})`,
                width: 150,
              },
              {
                dataIndex: 'discountPrice',
                title: `金额(${this.props.currencyName})`,
                width: 150,
                fixed: 'right',
              },
            ];
    } else if (transType === 'BTC35' || transType === 'CTC35') {
      // 应收税费
      columns =
        indicator != 2
          ? [
              {
                dataIndex: 'customerCode',
                title: '制单账号',
                width: 150,
              },
              {
                dataIndex: 'calcTime',
                title: '账单日期',
                render: text => {
                  return moment(text).format('YYYY-MM-DD');
                },
                width: 150,
              },
              {
                dataIndex: 'waybillNumber',
                title: '运单号',
                width: 150,
              },
              {
                dataIndex: 'orderNumber',
                title: '订单号',
                width: 150,
              },
              {
                dataIndex: 'exchangeNumber',
                title: '转单号',
                width: 150,
              },
              {
                dataIndex: 'productName',
                title: '产品名称',
                width: 150,
              },
              {
                dataIndex: 'regionName',
                title: '目的地',
                width: 150,
              },
              {
                dataIndex: 'companyCodeOfFromName',
                title: '发货仓',
                filterType: true,
                width: 150,
              },
              {
                dataIndex: 'arPrice',
                title: `税费(${this.props.currencyName})`,
                width: 150,
                fixed: 'right',
              },
            ]
          : [
              {
                dataIndex: 'customerCode',
                title: '制单账号',
                width: 150,
              },
              {
                dataIndex: 'timeOfCalc',
                title: '计费时间',
                render: text => {
                  return moment(text).format('YYYY-MM-DD');
                },
                width: 150,
              },
              {
                dataIndex: 'waybillNumber',
                title: '运单号',
                width: 150,
              },
              {
                dataIndex: 'exchangeNumber',
                title: '转单号',
                width: 150,
              },
              // {
              //   dataIndex: 'calcTime',
              //   title: '账单日期',
              //   render: text => {
              //     return moment(text).format('YYYY-MM-DD');
              //   },
              // },
              {
                dataIndex: 'productName',
                title: '产品名称',
                width: 150,
              },
              {
                dataIndex: 'regionName',
                title: '国家',
                width: 150,
              },
              {
                dataIndex: 'postCode',
                title: '邮编',
                width: 150,
              },
              {
                dataIndex: 'piece',
                title: '件数',
                width: 150,
              },
              {
                dataIndex: 'weight',
                title: '实重(g)',
                width: 150,
              },
              {
                dataIndex: 'dimWeight',
                title:
                  billWeightunit === ''
                    ? '体积重'
                    : billWeightunit === 'kg'
                    ? '体积重(kg)'
                    : '体积重(g)',
                width: 150,
              },
              {
                dataIndex: 'calcWeight',
                title: '计费重(g)',
                width: 150,
              },
              // {
              //   dataIndex: 'unitPrice',
              //   title: '单价',
              // },
              {
                dataIndex: 'itemName',
                title: '费用名称',
                width: 150,
              },
              {
                dataIndex: 'companyCodeOfFromName',
                title: '发货仓',
                filterType: true,
                width: 150,
              },

              {
                dataIndex: 'arPrice',
                title: `折前金额(${this.props.currencyName})`,
                width: 150,
              },
              {
                dataIndex: 'discountPrice',
                title: `金额(${this.props.currencyName})`,
                width: 150,
                fixed: 'right',
              },
            ];
    } else if (transType === 'BTC39') {
      // 境外重派
      columns = [
        {
          dataIndex: 'customerCode',
          title: '制单账号',
          width: 150,
        },
        {
          dataIndex: 'timeOfExpress',
          title: '账单日期',
          render: text => {
            return moment(text).format('YYYY-MM-DD');
          },
          width: 150,
        },
        // {
        //   dataIndex: 'waybillNumber',
        //   title: '工单号',
        //   width: 150,
        // },
        {
          dataIndex: 'waybillNumber',
          title: '运单号',
          width: 150,
        },
        {
          dataIndex: 'orderNumber',
          title: '订单号',
          width: 150,
        },
        {
          dataIndex: 'exchangeNumber',
          title: '新转单号',
          width: 150,
        },
        {
          dataIndex: 'productName',
          title: '产品名称',
          width: 150,
        },
        {
          dataIndex: 'regionName',
          title: '目的地',
          width: 150,
        },
        {
          dataIndex: 'calcWeight',
          title: '计费重量(g)',
          width: 150,
        },
        {
          dataIndex: 'arPrice',
          title: `重派金额(${this.props.currencyName})`,
          width: 150,
          fixed: 'right',
        },
      ];
    } else if (transType === 'BTC38' || transType === 'TC38') {
      // 首公里揽收
      columns = [
        {
          dataIndex: 'CustomerCode',
          title: '制单账号',
          width: 150,
        },
        {
          dataIndex: 'TimeOfCalc',
          title: '计费时间',
          width: 150,
          render: text => {
            return moment(text).format('YYYY-MM-DD');
          },
        },
        {
          dataIndex: 'timeOfExpress',
          title: '账单日期',
          render: text => {
            return moment(text).format('YYYY-MM-DD');
          },
          width: 150,
        },
        {
          dataIndex: 'waybillNumber',
          title: '计费单号',
          width: 150,
        },
        {
          dataIndex: 'PackageNum',
          title: '包数',
          width: 150,
        },
        {
          dataIndex: 'ProductName',
          title: '产品名称',
          width: 150,
        },
        {
          dataIndex: 'CalcWeight',
          title: '重量(g)',
          width: 150,
        },
        {
          dataIndex: 'CompanyCodeOfFromName',
          title: '发货仓',
          width: 150,
        },
        {
          dataIndex: 'ArPrice',
          title: `账单金额(${this.props.currencyName})`,
          width: 150,
          fixed: 'right',
        },
      ];
    } else {
      columns = [];
    }

    return columns;
  };

  handleClick = () => {
    const { r } = this.props;
    transType = r.transType;
    mainNo = r.mainNo;
    if (r.startFDate !== null && r.endTDate) {
      startDate = r.startFDate;
      endDate = r.endTDate;
    }
    this.setState({
      visible: true,
      billInfo: {
        ...r,
      },
    });
    this.handleQuery();
  };

  componentDidMount() {
    // this.handleToIndex();
  }

  componentWillUnmount() {
    startDate = moment()
      .startOf('month')
      .format('YYYY-MM-DD');
    endDate = moment().format('YYYY-MM-DD');
  }

  // handleToIndex = () => {
  //   const { dispatch, r } = this.props;
  //   transType = r.transType;
  //   mainNo = r.mainNo;
  // };

  // 时间选择事件
  onChange = (date, dateString) => {
    startDate = dateString[0];
    endDate = dateString[1];
  };
  handleQuery = () => {
    const { dispatch } = this.props;
    const { waybillNumber, currentPage, flag, pageSize } = this.state;
    const param = {
      currentPage: currentPage,
      pageSize: pageSize,
      startCalcTime: flag ? '' : startDate,
      endCalcTime: flag ? '' : endDate,
      transType: transType,
      mainNo: mainNo,
      waybillNumber: waybillNumber,
    };
    dispatch({
      type: 'bill/detail',
      payload: param,
      callback: result => {
        if (result.success) {
          this.setState({
            billWeightunit: result.data?.rows?.[0]?.billWeightunit ?? 0,
            data: result.data?.rows ?? [],
            total: result.data.totalrows,
            totalAmount: result.data.totalAmount,
            sumRow: result.data.sumrows,
          });
        } else {
          this.setState({
            data: [],
          });
          message.error(result.message);
        }
      },
    });
  };

  handleWaybillNumber = e => {
    this.setState({
      waybillNumber: e.target.value,
    });
  };
  buttonQuery = () => {
    this.state.currentPage = 1;
    this.state.flag = false;
    this.handleQuery();
  };
  exportBill = () => {
    const param = {
      transType: transType,
      mainNo: mainNo,
    };
    const { dispatch } = this.props;
    let exportData = {
      dispatch: dispatch,
      params: param,
      type: DownloadTaskType.BILL_DETAIL,
      pageSize: this.state.total,
      callback: this.currentDownload,
    };
    console.log(exportData);
    downloadExport(exportData);
  };

  currentDownload = () => {
    const { waybillNumber, currentPage, flag, pageSize } = this.state;
    let url = '';
    if (flag) {
      url =
        portalUrl +
        '/bill/detail/export?transType=' +
        transType +
        '&mainNo=' +
        mainNo +
        '&currentPage=' +
        currentPage +
        '&pageSize=' +
        pageSize +
        '&waybillNumber=' +
        waybillNumber;
    } else {
      url =
        portalUrl +
        '/bill/detail/export?transType=' +
        transType +
        '&mainNo=' +
        mainNo +
        '&currentPage=' +
        currentPage +
        '&pageSize=' +
        pageSize +
        '&startCalcTime=' +
        startDate +
        '&endCalcTime=' +
        endDate +
        '&waybillNumber=' +
        waybillNumber;
    }
    window.open(url);
  };

  render() {
    const { data, totalAmount, total, billInfo, sumRow, indicator, visible, pageSize } = this.state;
    const { pageLoading } = this.props;
    if (billInfo.transType == 'CTC35') {
      billInfo.transTypeName = '应收税费';
    }
    const paginationProps = {
      onChange: (page, pageSize) => {
        this.state.currentPage = page;
        this.setState(
          {
            currentPage: page,
            pageSize,
          },
          this.handleQuery
        );
      },
      current: this.state.currentPage,
      pageSize: pageSize,
      pageSizeOptions: ['10', '20', '50', '100', '500'],
      total: total,
    };
    // .filter(item => item.filterType === undefined)
    this.columns = this.getTableColumns(this.state.transType, this.state.indicator);

    return (
      <span>
        {React.cloneElement(this.props.children, { onClick: this.handleClick })}
        <Modal
          visible={visible}
          style={{
            top: 0,
          }}
          width="100%"
          height="100%"
          footer={false}
          onCancel={() => {
            this.setState({
              visible: false,
            });
          }}
        >
          <PageContainerComponent
            header={{
              title: null,
              breadcrumb: {},
              breadcrumbRender: props => (
                <PageHeaderBreadcrumb {...props} col>
                  <h2 style={{ margin: '16px 0', fontSize: '20px' }}>{'账单明细查询'}</h2>
                </PageHeaderBreadcrumb>
              ),
            }}
          >
            <div>
              {/* 筛选条件 */}
              <Card>
                <Row gutter={8}>
                  <Col xs={24} sm={24} md={12} lg={5} style={{ padding: '10px 0px' }}>
                    结账日期：{billInfo.startFDate} 到 {billInfo.endTDate}
                  </Col>
                  <Col xs={12} sm={12} md={12} lg={4} style={{ padding: '10px 0px' }}>
                    业务类型：{indicator == 2 ? 'FBA业务' : '直发业务'}
                  </Col>
                  <Col xs={12} sm={12} md={12} lg={5} style={{ padding: '10px 0px' }}>
                    结账类型：{billInfo.transTypeName}
                  </Col>
                  <Col xs={12} sm={12} md={8} lg={4} style={{ padding: '10px 0px' }}>
                    账单金额：<span style={{ color: 'red' }}>{billInfo.billAmount}</span>（
                    {this.props.currencyName}）
                  </Col>
                  <Col
                    xs={12}
                    sm={12}
                    md={8}
                    lg={4}
                    style={{ display: indicator == 2 ? 'none' : '', padding: '10px 0px' }}
                  >
                    总计票数：{sumRow == null ? total : sumRow}
                  </Col>
                  <Col xs={12} sm={12} md={8} lg={4} style={{ padding: '10px 0px' }}>
                    调整标记：{billInfo.isChange === '0' ? '否' : '是'}
                  </Col>
                </Row>
                <Divider />
                <Row style={{ display: indicator == 2 ? 'none' : '' }}>
                  <Col span={10}>
                    <StandardFormRow
                      title="日期"
                      block
                      style={{ marginTop: 0, marginBottom: 0, border: 'none' }}
                    >
                      <RangePicker
                        onChange={this.onChange}
                        defaultValue={[
                          moment(startDate, 'YYYY-MM-DD'),
                          moment(endDate, 'YYYY-MM-DD'),
                        ]}
                      />
                    </StandardFormRow>
                  </Col>
                  <Col span={10}>
                    <StandardFormRow
                      title="运单号"
                      block
                      style={{ marginRight: 40, marginTop: 0, marginBottom: 0, border: 'none' }}
                    >
                      <Input placeholder="请输入运单号" onChange={this.handleWaybillNumber} />
                    </StandardFormRow>
                  </Col>
                  <Col span={4}>
                    <Button type="primary" onClick={this.buttonQuery}>
                      查询
                    </Button>
                    <Button type="primary" onClick={this.exportBill} style={{ marginLeft: 10 }}>
                      下载
                    </Button>
                  </Col>
                </Row>
              </Card>
              <Card style={{ padding: '0px' }} bordered={false} className="listCard">
                {/* <Table
            dataSource={data}
            columns={this.columns.filter(item => item.filterType === undefined)}
            scroll={{ x: 2200, y: 280 }}
            pagination={paginationProps}
          /> */}
                <ProTable
                  loading={pageLoading}
                  dataSource={data}
                  columns={this.columns.filter(item => item.filterType === undefined)}
                  pagination={paginationProps}
                  search={false}
                  scroll={{ x: 2200, y: 280 }}
                  options={{
                    reload: false,
                  }}
                  columnsState={{
                    persistenceKey: 'billDetailList',
                    persistenceType: 'localStorage',
                  }}
                />
                <div
                  style={{
                    textAlign: 'right',
                    fontSize: 20,
                    paddingRight: 40,
                    display: indicator == 2 ? 'none' : '',
                  }}
                >
                  筛选合计：&nbsp;&nbsp;&nbsp;&nbsp;
                  <span style={{ fontSize: 20 }}>{total}票</span>
                  &nbsp;&nbsp;&nbsp;&nbsp;
                  <span style={{ color: 'red', fontSize: 20 }}>
                    {totalAmount === 0 ? '' : totalAmount}
                  </span>
                  {this.props.currencyName}
                </div>
              </Card>
            </div>
          </PageContainerComponent>
        </Modal>
      </span>
    );
  }
}

export default BillDetail;
