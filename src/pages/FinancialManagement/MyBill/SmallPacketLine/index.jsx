/**
 * 我的账单
 */
import React, { Component } from 'react';
import { ExclamationCircleFilled } from '@ant-design/icons';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import ProTable from '@ant-design/pro-table';
import {
  Card,
  Row,
  Col,
  Button,
  Tooltip,
  message,
  Modal,
  Spin,
  Descriptions,
  Tabs,
  Divider,
  Typography,
} from 'antd';
import { connect } from 'dva';
import moment from 'moment';
import { downloadFile } from '@/utils/download';
import SearchTabs from './components/searchTabs';
import PageHeaderBreadcrumb from '@/components/PageHeaderBreadcrumb';
import PageContainerComponent from '@/components/PageContainer';
import noBill from '@/assets/noBill.png';
import infoicon from '@/assets/infoicon.png';
import account_icon1 from '@/assets/account_icon1.png';
import account_icon2 from '@/assets/account_icon2.png';
import account_icon3 from '@/assets/account_icon3.png';
import account_icon4 from '@/assets/account_icon4.png';
import NotOutBill from './components/notOutBill';
import BillDetail from './components/billDetail';
import MerchantStatusModal from '@/pages/HomePage/components/MerchantStatusModal';

import WithholdingBillList from './components/withholdingBillList';
import { DownloadTaskType, foreignKeyEnum } from '@/utils/commonConstant';
import { downloadExport, handleLinkParam } from '@/utils/utils';
import { formatMessage } from 'umi-plugin-react/locale';

const supportTransTypes = [
  'TC01',
  'BTC01',
  'CTC01',
  'TC02',
  'BTC02',
  'CTC02',
  'TC03',
  'BTC03',
  'CTC03',
  'TC04',
  'BTC04',
  'CTC04',
  'TC05',
  'BTC05',
  'CTC05',
  'TC08',
  'BTC08',
  'CTC08',
  'TC15',
  'BTC15',
  'CTC15',
  'BTC35',
  'CTC35',
  'BTC39',
  'TC38',
  'BTC38',
];

const { TabPane } = Tabs;

let pageSize = 10;

@connect(({ homePage, bill, user, loading, merchantRegisterCompnent }) => ({
  bill,
  homePage,
  merchantRegisterCompnent,
  user,
  merchantInfoLoading: loading.effects['merchantRegisterCompnent/getMerchantInfo'],
  authorizedLoading: loading.effects['user/qryMerchantState'],
  getBalanceLoading: loading.effects['bill/getBalance'],
  getUnTransferBillsLoading: loading.effects['bill/getUnTransferBills'],
  selectFreezeReasonsLoading: loading.effects['homePage/selectFreezeReasons'],
}))
@Form.create()
class Bill extends Component {
  constructor(props) {
    super(props);

    this.notOutBillRef = React.createRef();
    this.withHoldingBillRef = React.createRef();
    this.state = {
      key: '1',
      merchantInfoObj: null,
      merchantCode: '',
      data: [],
      allTotalAmount: '', //运单总计元
      totalSize: '', //总运单数量
      sumBillAmount: '',
      sumBalingAmount: '', // 未清
      sumBalAmount: '', // 入账
      total: '',
      unTransferCurrent: 1,
      isShowFrozenBalance: '',
      visible: false,
      bankAccountName: '',
      bankAddr: '',
      accountBank: '',
      accountAlipay: '',
      alipayAccountName: '',
      isMadelLoading: true,
      pageSize: 10,
      selectTabDetails: 0, // tabs的选择,
      tabDetailsData: [
        { title: formatMessage({ id: '运单明细' }), key: 0 },
        { title: formatMessage({ id: '账期清单' }), key: 1 },
        { title: formatMessage({ id: '类型清单' }), key: 2 },
        { title: formatMessage({ id: '电子账单' }), key: 3 },
      ],
      loading: false,
      current: 1,
      costTypeList: [
        { value: 'allType', label: formatMessage({ id: '全部' }) },
        { value: 'billType', label: formatMessage({ id: '账单' }) },
        { value: 'receType', label: formatMessage({ id: '收款' }) },
      ],
      exportLoading: false,
      frozenStatus: '',
      merchantStatusOpen: false,
      isFrozenMoney: false, // 是否是金额冻结
      account: [],
      selectedRowKeys: [],
      selectedRows: [],
      currencyName: '',
    };
  }

  // ----余额信息-----
  componentDidMount() {
    const { dispatch, location } = this.props;
    Promise.all([this.getMerchantAccount()]).then(() => {
      if (location?.query?.type == 0) {
        // 未出账单
        this.notOutBillRef.current?.click();
      } else if (location?.query?.type == 1) {
        // 制单预扣款
        this.withHoldingBillRef.current?.click();
      } else if (location?.query?.type == 2) {
        // 账期清单选择未清
        this.setState(
          {
            selectTabDetails: 1,
          },
          () => {
            this.props.form.setFieldsValue({
              payType: '2',
              dateTime: undefined,
            });
            setTimeout(() => {
              this.getList();
            }, 300);
          }
        );
      } else {
        if (foreignKeyEnum.billDownload == location?.query?.foreignKey) {
          this.selectTabs(3);
          handleLinkParam(this.props, [foreignKeyEnum.foreignKey]);
        }
      }
    });
    this.getMerchantState();
    this.getFreezeReasons();
    // 获取金额信息（账户金额、未出账单金额、可用余额、是否预扣款客户）
    dispatch({
      type: 'bill/getBillBalance',
      payload: { businessType: 0 },
      callback: result => {
        if (result.success) {
          this.setState({
            merchantInfoObj: result.data,
            currencyName: result.data.currencyName,
          });
        }
      },
    });
  }

  getMerchantAccount = () => {
    return new Promise((resolve, reject) => {
      const { dispatch } = this.props;
      dispatch({
        type: 'Order/getShippingAccount',
        payload: { scene: 2 },
        callback: response => {
          if (response.success) {
            this.setState(
              {
                account: response.data,
              },
              () => {
                this.props.form.setFieldsValue({
                  customerCode: 'all',
                });
                resolve(response.data);
              }
            );
          } else {
            reject();
          }
        },
      });
    });
  };

  // 获取冻结项中是否包含金额冻结
  getFreezeReasons = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'homePage/selectFreezeReasons',
      callback: response => {
        if (response.success) {
          if (
            response.data &&
            response.data.reasons &&
            response.data.reasons.findIndex(item => item.type === '1') != -1
          ) {
            this.setState({ isFrozenMoney: true });
          }
        }
      },
    });
  };

  // 获取业务账号状态
  getMerchantState = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'smallBag/getPacketBaseInfo',
      callback: response => {
        if (response.success) {
          this.setState({
            frozenStatus: response.data?.frozenStatus ?? '',
          });
        }
      },
    });
  };

  // 初始化余额信息
  info = (IconStr, borderColor, bcgColor, title, value, bordered, color, isIcon, weekPay) => {
    const { merchantInfoObj, isFrozenMoney } = this.state;
    return (
      <div>
        <Row>
          <Col span={12} style={{ textAlign: 'right', paddingRight: '20px' }}>
            <div
              style={{
                fontSize: '30px',
                borderWidth: '10px',
                borderStyle: 'solid',
                borderRadius: '100px',
                width: '80px',
                height: '80px',
                fontWeight: 900,
                display: 'inline-block',
                textAlign: 'center',
                backgroundColor: bcgColor,
                borderColor,
              }}
            >
              <img src={IconStr} alt={IconStr} />
            </div>
          </Col>
          <Col span={12} style={{ textAlign: 'left' }}>
            <div style={{ paddingTop: '12px' }}>
              <span>
                {title}&nbsp;
                <Tooltip
                  placement="top"
                  title={
                    title === formatMessage({ id: '账户余额' })
                      ? `${formatMessage({ id: '代表已扣除已出账单部分的余款' })}，${formatMessage({
                          id: '正数代表有余款',
                        })}，${formatMessage({ id: '负数代表已发生欠款' })}`
                      : title === formatMessage({ id: '未出账单金额' })
                      ? `${formatMessage({ id: '代表当周所发货物' })}，${formatMessage({
                          id: '处理后产生的金额',
                        })}；`
                      : title === formatMessage({ id: '制单预扣金额' })
                      ? `${formatMessage({ id: '代表在制单系统成功下单时预扣的金额' })}`
                      : title === formatMessage({ id: '可用余额' }) && weekPay
                      ? `${formatMessage({ id: '代表账户的当前可用余款' })}，${formatMessage({
                          id: '已经扣除未出账单金额和制单预扣金额',
                        })}，${formatMessage({ id: '正数代表有余款' })}，${formatMessage({
                          id: '负数代表已经发生欠款',
                        })}`
                      : title === formatMessage({ id: '可用余额' })
                      ? `${formatMessage({ id: '代表账户的当前可用余款' })}，${formatMessage({
                          id: '已经扣除未出账单金额',
                        })}，${formatMessage({ id: '正数代表有余款' })}，${formatMessage({
                          id: '负数代表已经发生欠款',
                        })}`
                      : null
                  }
                >
                  <ExclamationCircleFilled
                    style={{ color: '#c0ccda', fontSize: 12, display: isIcon }}
                  />
                </Tooltip>
              </span>
              <p style={{ fontSize: 20, marginBottom: 15 }}>
                {title === formatMessage({ id: '未出账单金额' }) ? (
                  merchantInfoObj?.payCycle !== null && merchantInfoObj?.payCycle === 9 ? (
                    value
                  ) : (
                    <NotOutBill currencyName={this.state.currencyName}>
                      <Button ref={this.notOutBillRef} type="link" style={{ fontSize: 20 }}>
                        {value}
                      </Button>
                    </NotOutBill>
                  )
                ) : title === formatMessage({ id: '制单预扣金额' }) ? (
                  <WithholdingBillList currencyName={this.state.currencyName}>
                    <Button type="link" ref={this.withHoldingBillRef} style={{ fontSize: 20 }}>
                      {value}
                    </Button>
                  </WithholdingBillList>
                ) : (
                  value
                )}
                &nbsp;
                <Tooltip title="系统数据异常，请联系销售或者客服，或者点右下角的在线咨询！">
                  <img
                    src={infoicon}
                    style={{
                      width: '18px',
                      display: title === '账户余额' && value === 'NULL' ? '' : 'none',
                    }}
                  />
                </Tooltip>
                {isFrozenMoney && title === '可用余额' && (
                  <p>
                    <a
                      style={{ fontSize: '14px' }}
                      onClick={() => {
                        this.setState({
                          merchantStatusOpen: true,
                        });
                      }}
                    >
                      申请解冻
                    </a>
                  </p>
                )}
              </p>
            </div>
          </Col>
        </Row>
        {bordered && <em />}
      </div>
    );
  };

  //-----------------------------------------------------------------

  // 运单、账期、类型清单tab选择tabs
  selectTabs = e => {
    this.setState(
      {
        data: [],
        selectTabDetails: +e,
        current: 1,
        pageSize: 10,
      },
      () => {
        this.SearchTabs.changeMoreType(false);
        this.resetFormData();
        this.getList();
      }
    );
  };

  // 运单明细、账期清单、类型清单查询事件
  getList = () => {
    const { selectTabDetails } = this.state;
    this.setState({
      loading: true,
      data: [],
    });
    if (selectTabDetails === 0) {
      // 运单明细
      this.getWayBillList();
    } else if (selectTabDetails === 1) {
      // 账期清单
      this.getUnTransferBills();
    } else if (selectTabDetails === 2) {
      // 类型清单
      this.getTypeBillList();
    } else {
      // 电子账单
      this.getElectronicBillList();
    }
  };
  // 电子账单
  getElectronicBillList() {
    const { dispatch, form } = this.props;
    const { current, pageSize } = this.state;
    form.validateFields((err, values) => {
      if (!err) {
        const startTime = values.dateTime[0].format('YYYY-MM-DD');
        const endTime = values.dateTime[1].format('YYYY-MM-DD');
        const params = {
          currentPage: current,
          pageSize,
          startTime,
          endTime,
          businessType: 0,
        };
        dispatch({
          type: 'bill/getEBillList',
          payload: params,
          callback: result => {
            if (result.success) {
              this.setState({
                data: result.data?.rows,
                total: result.data?.total ?? 0,
                loading: false,
              });
            } else {
              this.setState({
                data: [],
                total: 0,
                loading: false,
              });
            }
          },
        });
      }
    });
  }

  onSelectChange = (selectedRowKeys, selectedRows, info) => {
    this.setState({
      selectedRowKeys: selectedRowKeys,
      selectedRows: selectedRows,
    });
  };

  exportEBillFile = () => {
    const { selectedRows } = this.state;
    if (selectedRows.length == 0) {
      message.error('请至少选择一条数据');
    } else if (selectedRows.length == 1) {
      downloadFile({
        url: `/csc/file/download?path=${selectedRows[0].fileUrl}`,
      });
    } else {
      const urls = selectedRows.map(row => row.fileUrl);
      const { dispatch } = this.props;
      let exportData = {
        dispatch: dispatch,
        params: urls,
        type: DownloadTaskType.E_BILL,
        callback: this.currentDownload,
      };
      console.log(exportData);
      downloadExport(exportData);
    }
  };

  // 重置搜索表单
  resetFormData = (startTime, endTime) => {
    const { merchantInfoObj, selectTabDetails } = this.state;
    let startTimeOneMouth =
      merchantInfoObj?.payCycle == 5
        ? moment()
            .month(moment().month() - 1)
            .startOf('month')
            // .subtract(1, 'month')
            .format('YYYY-MM-DD')
        : moment()
            .subtract(moment().isoWeekday(), 'days')
            .subtract(1, 'month')
            .format('YYYY-MM-DD');
    let endTimeOneMonth =
      merchantInfoObj?.payCycle == 5
        ? moment()
            .month(moment().month() - 1)
            .endOf('month')
            .format('YYYY-MM-DD')
        : moment()
            .subtract(moment().isoWeekday(), 'days')
            .format('YYYY-MM-DD');

    let startOneYear = moment()
      .subtract(moment().isoWeekday(), 'days')
      .subtract(358, 'days')
      .format('YYYY-MM-DD');
    let endOneYear = moment()
      .subtract(moment().isoWeekday(), 'days')
      .format('YYYY-MM-DD');

    let startNowTime = moment()
      .subtract(6, 'months')
      .format('YYYY-MM-DD');
    let endSixMonthsTime = moment().format('YYYY-MM-DD');
    this.props.form.setFieldsValue({
      payType: '3',
      epCodes: undefined,
      costType: undefined,
      dateTime:
        selectTabDetails === 1
          ? [moment(startOneYear, 'YYYY-MM-DD'), moment(endOneYear, 'YYYY-MM-DD')]
          : selectTabDetails === 3
          ? [moment(startNowTime, 'YYYY-MM-DD'), moment(endSixMonthsTime, 'YYYY-MM-DD')]
          : [moment(startTimeOneMouth, 'YYYY-MM-DD'), moment(endTimeOneMonth, 'YYYY-MM-DD')],
      dateTimeBill: [
        moment(startTimeOneMouth, 'YYYY-MM-DD'),
        moment(endTimeOneMonth, 'YYYY-MM-DD'),
      ],
      transType: '',
      customerCode: 'all',
    });
  };

  //---------------------------------运单明细-----------------------------------
  // 运单明细---运单号textarea校验
  validateToNextPassword = value => {
    var counts = value.split('\n').length;
    if (counts > 500) {
      message.warning('最多输入500单');
      return false;
    } else {
      return true;
    }
  };

  // 合并单元格，处理waybillNumber
  changeData = (data, field) => {
    let count = 0; //重复项的第一项
    let indexCount = 1; //下一项
    while (indexCount < data.length) {
      let item = data.slice(count, count + 1)[0]; //获取没有比较的第一个对象
      if (!item.rowSpan) {
        item.rowSpan = 1; //初始化为1
      }
      if (item[field] === data[indexCount][field]) {
        //第一个对象与后面的对象相比，有相同项就累加，并且后面相同项设置为0
        item.rowSpan++;
        data[indexCount].rowSpan = 0;
      } else {
        count = indexCount;
      }
      indexCount++;
    }
  };

  // 运单明细查询
  getWayBillList = () => {
    const { current, pageSize } = this.state;
    this.props.form.validateFields((err, values) => {
      if (!err) {
        let result;
        if (values?.epCodes) {
          result = this.validateToNextPassword(values.epCodes);
        }
        if (!result && result !== undefined) {
          this.resetFormData();
          this.setState({
            data: [],
            total: 0,
            loading: false,
          });
          return;
        }
        let epCodes =
          values?.epCodes != '' && values?.epCodes != undefined ? values?.epCodes : undefined;
        // 去掉.join()
        let waybillNumbers = epCodes?.split(/[\r\n]/g)?.map(item => item?.replace(/\s+/g, ''));
        const dateOfBillBegin = values.dateTimeBill?.[0]?.format('YYYY-MM-DD');
        const dateOfBillEnd = values.dateTimeBill?.[1]?.format('YYYY-MM-DD');
        let diffDays = values.dateTimeBill[1].diff(values.dateTimeBill[0], 'days', true);
        if (diffDays > 365) {
          message.warning('查询时间间隔不能超过一年');
          this.setState({
            data: [],
            total: 0,
            loading: false,
          });
          return;
        }
        const { dispatch } = this.props;
        dispatch({
          type: 'bill/getNumberList',
          payload: {
            waybillNumbers,
            dateOfBillBegin,
            dateOfBillEnd,
            page: current,
            size: pageSize,
            transType: values.transType,
            customerCode:
              typeof values?.customerCode === 'string' && values?.customerCode !== 'all'
                ? values?.customerCode
                : values?.customerCode?.includes('all')
                ? undefined
                : values?.customerCode?.join(','),
          },
          callback: result => {
            if (result.success) {
              let data = result.data?.records ?? [];
              this.changeData(data, 'waybillNumber');
              this.setState({
                data,
                allTotalAmount: result.data?.arPriceSum ?? '',
                totalSize: result.data?.totalRecord ?? '',
                total: +result.data?.totalRecord ?? 0,
                loading: false,
              });
            } else {
              this.setState({
                data: [],
                allTotalAmount: '',
                totalSize: '',
                total: 0,
                loading: false,
              });
            }
          },
        });
      }
    });
  };

  // 运单明细导出
  exportFile = async () => {
    const { form } = this.props;
    const { data } = this.state;
    let { epCodes, dateTimeBill, transType, merchantCode } = form.getFieldsValue();
    let waybillNumbers = epCodes?.split(/[\r\n]/g);
    const dateOfBillBegin = dateTimeBill[0].format('YYYY-MM-DD');
    const dateOfBillEnd = dateTimeBill[1].format('YYYY-MM-DD');
    let diffDays = dateTimeBill[1].diff(dateTimeBill[0], 'days', true);
    if (diffDays > 365) return message.warning('查询时间间隔不能超过一年');
    if (data.length > 0) {
      this.setState({
        exportLoading: true,
      });
      const data = await downloadFile({
        url: `/csc/bill/waybillExport`,
        param: {
          waybillNumbers,
          dateOfBillBegin,
          dateOfBillEnd,
          transType: transType,
        },
        method: 'POST',
      });
      if (data === 1) {
        this.setState({
          exportLoading: false,
        });
      }
    } else {
      message.error('请导出有数据的运单号');
    }
  };

  //---------------------------------账期清单-----------------------------------
  // 账期清单查询
  getUnTransferBills = () => {
    const { dispatch, form } = this.props;
    form.validateFields((err, values) => {
      if (!err) {
        const startRefDate = values?.dateTime?.[0]?.format('YYYY-MM-DD');
        const endRefDate = values?.dateTime?.[1]?.format('YYYY-MM-DD');
        // let diffDays = values.dateTime[1].diff(values.dateTime[0], 'days', true);
        // if (diffDays > 90) {
        //   message.warning('查询时间间隔不能超过3个月');
        //   this.setState({
        //     data: [],
        //     loading: false,
        //   });
        //   return;
        // }

        const param = {
          startRefDate,
          endRefDate,
          chkStatus: values.payType,
        };
        dispatch({
          type: 'bill/getAccountPeriodList',
          payload: param,
          callback: result => {
            if (result.success) {
              this.setState({
                data: result.data?.list ?? [],
                sumBillAmount: result.data?.sumBillAmount ?? '',
                sumBalingAmount: result.data?.sumBalingAmount ?? '',
                sumBalAmount: result.data?.sumBalAmount ?? '',
                loading: false,
              });
            } else {
              this.setState({
                data: [],
                sumBillAmount: '',
                sumBalingAmount: '',
                sumBalAmount: '',
                loading: false,
              });
              message.error(result.message);
            }
          },
        });
      }
    });
  };

  //---------------------------------类型清单-----------------------------------
  // 类型清单查询
  getTypeBillList = () => {
    const { dispatch, form } = this.props;
    const { current, pageSize } = this.state;
    form.validateFields((err, values) => {
      if (!err) {
        const startRefDate = values.dateTime[0].format('YYYY-MM-DD');
        const endRefDate = values.dateTime[1].format('YYYY-MM-DD');
        const params = {
          currentPage: current,
          pageSize,
          startRefDate,
          endRefDate,
          billType:
            values.costType && values.costType.length !== 0
              ? values.costType.toString()
              : undefined,
        };
        // JIRA-55698 客户中心-类型清单调整
        if (params.billType && params.billType.indexOf('TC19') != -1) {
          params.billType = params.billType + ',TC21';
        }
        let diffDays = values.dateTime[1].diff(values.dateTime[0], 'days', true);
        if (diffDays > 180 && values.costType !== undefined) {
          message.warning('查询时间间隔不能超过6个月');
          this.setState({
            data: [],
            total: 0,
            loading: false,
          });
          return;
        }
        dispatch({
          type: values.costType === undefined ? 'bill/typeList' : 'bill/getHistoryListByType',
          payload: params,
          callback: result => {
            if (result.success) {
              let array = [];
              this.setState({
                loading: false,
              });
              if (values.costType === undefined) {
                array =
                  result.data?.rows?.map(item => {
                    item.billAmount2 = item.billAmount;
                    item.balAmount2 = item.balAmount;
                    return item;
                  }) ?? [];
              } else {
                array =
                  result.data?.rows?.map(item => {
                    item.billAmount2 = item.billAmount;
                    item.balAmount2 = item.balAmount;
                    return item;
                  }) ?? [];
              }
              this.setState({
                data: array,
                sumBillAmount: result.data?.sumBillAmount ?? '',
                sumBalAmount:
                  values.costType === undefined
                    ? result.data?.sumReceAmout
                    : result.data?.sumPaidAmount,
                total: result.data?.total ?? 0,
                loading: false,
              });
            } else {
              this.setState({
                data: [],
                sumBillAmount: '',
                sumBalAmount: '',
                loading: false,
              });
              message.error(result.message);
            }
          },
        });
      }
    });
  };
  //类型清单精准查询折叠展开
  changeSelectKey = type => {
    this.props.form.setFieldsValue({
      costType: type
        ? ['TC01', 'TC02', 'TC03', 'TC04', 'TC08', 'TC35', 'TC39', 'TC38', 'TC19']
        : undefined,
    });
  };

  // 账期、类型清单跳转详情
  getDetail = record => {
    const { form } = this.props;
    // 账期明细
    let startTime = record.billPeriod?.split('_')?.[0];
    let endTime = record.billPeriod?.split('_')?.[1];
    form.setFieldsValue({
      dateTime: [moment(startTime, 'YYYY-MM-DD'), moment(endTime, 'YYYY-MM-DD')],
    });
    this.setState(
      {
        selectTabDetails: 2,
      },
      this.getList
    );
  };

  render() {
    const {
      merchantInfoObj,
      data,
      sumBillAmount,
      sumBalingAmount,
      sumBalAmount,
      total,
      merchantCode,
      visible,
      bankAccountName,
      bankAddr,
      accountBank,
      accountAlipay,
      alipayAccountName,
      isMadelLoading,
      isShowFrozenBalance,
      pageSize,
      selectTabDetails,
      tabDetailsData,
      loading,
      costTypeList,
      allTotalAmount,
      totalSize,
      exportLoading,
      frozenStatus,
      merchantStatusOpen,
      account,
    } = this.state;
    const { authorizedLoading, getBalanceLoading, getUnTransferBillsLoading } = this.props;
    // 表头
    const columns = [
      {
        title: formatMessage({ id: '运单号' }),
        dataIndex: 'waybillNumber',
        key: 'waybillNumber',
        filterType: selectTabDetails === 0,
        fixed: 'left',
        width: 200,
        render: (text, row) => {
          return {
            children: text,
            props: {
              rowSpan: row.rowSpan,
            },
          };
        },
      },
      {
        title: formatMessage({ id: '制单账号' }),
        dataIndex: 'customerCode',
        key: 'customerCode',
        filterType: selectTabDetails === 0,
        width: 110,
        render: (text, row) => {
          return {
            children: text,
            props: {
              rowSpan: row.rowSpan,
            },
          };
        },
      },

      {
        title: formatMessage({ id: '订单号' }),
        dataIndex: 'orderNumber',
        key: 'orderNumber',
        filterType: selectTabDetails === 0,
        width: 180,
        render: (text, row) => {
          return {
            children: text,
            props: {
              rowSpan: row.rowSpan,
            },
          };
        },
      },
      {
        title: formatMessage({ id: '转单号' }),
        dataIndex: 'exchangeNumber',
        key: 'exchangeNumber',
        filterType: selectTabDetails === 0,
        width: 200,
      },
      {
        title: formatMessage({ id: '流水号' }),
        dataIndex: 'yanwenOrderNumber',
        key: 'yanwenOrderNumber',
        filterType: selectTabDetails === 0,
        width: 200,
      },
      {
        title: formatMessage({ id: '产品名称' }),
        dataIndex: 'productName',
        key: 'productName',
        filterType: selectTabDetails === 0,
        width: 200,
        render: (text, row) => {
          return {
            children: text,
            props: {
              rowSpan: row.rowSpan,
            },
          };
        },
      },
      {
        title: formatMessage({ id: '目的地' }),
        dataIndex: 'regionName',
        key: 'regionName',
        filterType: selectTabDetails === 0,
        width: 80,
        render: (text, row) => {
          return {
            children: text,
            props: {
              rowSpan: row.rowSpan,
            },
          };
        },
      },
      {
        title: formatMessage({ id: '账单总金额' }),
        dataIndex: 'money',
        key: 'money',
        filterType: selectTabDetails === 0,
        width: 200,
        render: (text, row) => {
          return {
            children: text,
            props: {
              rowSpan: row.rowSpan,
            },
          };
        },
      },

      {
        title: `${formatMessage({ id: '实际重量' })}(G)`,
        dataIndex: 'weight',
        key: 'weight',
        filterType: selectTabDetails === 0,
        width: 200,
      },
      {
        title: `${formatMessage({ id: '计费重量' })}(G)`,
        dataIndex: 'calcWeight',
        key: 'calcWeight',
        filterType: selectTabDetails === 0,
        width: 200,
      },
      {
        title: `${formatMessage({ id: '长' })}(CM)`,
        dataIndex: 'expressLength',
        key: 'expressLength',
        filterType: selectTabDetails === 0,
        width: 200,
      },
      {
        title: `${formatMessage({ id: '宽' })}(CM)`,
        dataIndex: 'expressWidth',
        key: 'expressWidth',
        filterType: selectTabDetails === 0,
        width: 200,
      },
      {
        title: `${formatMessage({ id: '高' })}(CM)`,
        dataIndex: 'expressHeight',
        key: 'expressHeight',
        filterType: selectTabDetails === 0,
        width: 200,
      },
      {
        title: formatMessage({ id: '计费时间' }),
        dataIndex: 'timeOfCalc',
        key: 'timeOfCalc',
        filterType: selectTabDetails === 0,
        width: 200,
      },
      {
        title: formatMessage({ id: '账单日期' }),
        dataIndex: 'dateOfBill',
        key: 'dateOfBill',
        filterType: selectTabDetails === 0,
        width: 200,
        render: text => moment(text).format('YYYY-MM-DD'),
      },
      {
        title: `${formatMessage({ id: '账单金额' })}(${this.state.currencyName})`,
        dataIndex: 'arPrice',
        key: 'arPrice',
        filterType: selectTabDetails === 0,
        width: 200,
      },
      {
        title: formatMessage({ id: '费用类型' }),
        dataIndex: 'transTypeName',
        key: 'transTypeName',
        filterType: selectTabDetails === 0,
        width: 200,
      },
      {
        title: formatMessage({ id: '账单周期' }),
        dataIndex: 'billPeriod',
        filterType: selectTabDetails === 1,
        width: 100,
        render: text => text?.replace('_', '~') ?? null,
      },
      {
        title: formatMessage({ id: '到期日' }),
        dataIndex: 'dueDate',
        filterType: selectTabDetails === 1,
        width: 100,
      },
      {
        title: `${formatMessage({ id: '账单金额' })}(${this.state.currencyName})`,
        dataIndex: 'billAmount',
        filterType: selectTabDetails === 1,
        width: 100,
      },
      {
        title: `${formatMessage({ id: '入账金额' })}(${this.state.currencyName})`,
        dataIndex: 'balAmount',
        filterType: selectTabDetails === 1,
        width: 50,
      },

      {
        title: `${formatMessage({ id: '未清金额' })}(${this.state.currencyName})`,
        dataIndex: 'balingAmount',
        filterType: selectTabDetails === 1,
        width: 50,
      },
      {
        title: formatMessage({ id: '账期状态' }),
        dataIndex: 'chkStatus',
        filterType: selectTabDetails === 1,
        width: 50,
        render: text => (text == 1 ? formatMessage({ id: '已清' }) : formatMessage({ id: '未清' })),
      },
      {
        title: formatMessage({ id: '账单周期' }),
        dataIndex: 'startFDate',
        filterType: selectTabDetails === 2,
        width: 100,
        render: (text, record) => `${text}~${record.endTDate}`,
      },
      {
        title: formatMessage({ id: '业务类型' }),
        dataIndex: 'indicator',
        filterType: selectTabDetails === 2,
        width: 60,
        render: text => {
          return text == 2 ? formatMessage({ id: 'FBA业务' }) : formatMessage({ id: '直发业务' });
        },
      },
      {
        title: formatMessage({ id: '费用类型' }),
        dataIndex: 'transTypeName',
        width: 60,
        filterType: selectTabDetails === 2,
        render: (text, record) => {
          if (record.transType == 'TC19' || record.transType == 'TC21') {
            return formatMessage({ id: '应收调整' });
          } else {
            return text;
          }
        },
      },
      {
        title: formatMessage({ id: '调整标记' }),
        dataIndex: 'isChange',
        width: 20,
        filterType: selectTabDetails === 2,
        render: text => {
          return text == 0 ? formatMessage({ id: '否' }) : formatMessage({ id: '是' });
        },
      },
      {
        title: `${formatMessage({ id: '账单金额' })}(${this.state.currencyName})`,
        dataIndex: 'billAmount2',
        width: 50,
        filterType: selectTabDetails === 2,
      },
      {
        title: `${formatMessage({ id: '入账金额' })}(${this.state.currencyName})`,
        dataIndex: 'balAmount2',
        width: 50,
        filterType: selectTabDetails === 2,
      },
      {
        title: formatMessage({ id: '账单周期' }),
        dataIndex: 'billPeriodStr',
        filterType: selectTabDetails === 3,
        width: 100,
      },
      {
        title: `${formatMessage({ id: '费用总金额' })}(${this.state.currencyName})`,
        dataIndex: 'billAmount',
        filterType: selectTabDetails === 3,
        width: 100,
      },
      {
        title: formatMessage({ id: '操作' }),
        dataIndex: 'operation',
        width: 100,
        filterType: selectTabDetails !== 0,
        render: (text, record) => {
          if (selectTabDetails === 2) {
            if (supportTransTypes.indexOf(record.transType) < 0) {
              // return <a style={{ color: 'black' }}>{text}</a>;
              return null;
            } else {
              return (
                <BillDetail
                  value={formatMessage({ id: '详情' })}
                  r={record}
                  currencyName={this.state.currencyName}
                >
                  <a>{formatMessage({ id: '详情' })}</a>
                </BillDetail>
              );
            }
          } else if (selectTabDetails === 3) {
            return (
              <a
                onClick={() => {
                  downloadFile({
                    url: `/csc/file/download?path=${record?.fileUrl}`,
                  });
                }}
              >
                {formatMessage({ id: '下载' })}
              </a>
            );
          } else {
            return (
              <div>
                <a
                  onClick={() => {
                    this.getDetail(record);
                  }}
                >
                  {formatMessage({ id: '详情' })}
                </a>
              </div>
            );
          }
        },
      },
    ];
    // 账期清单、类型清单pagination
    const paginationProps = {
      onChange: (page, pageSize) => {
        this.state.current = page;
        this.state.pageSize = pageSize;
        this.setState(
          {
            current: page,
            pageSize,
          },
          this.getList
        );
      },
      pageSize: pageSize,
      showSizeChanger: true,
      pageSizeOptions: ['10', '20', '50', '100', '500'],
      current: this.state.current,
      total: total,
      showTotal: () => {
        return `共- ${total} -条记录`;
      },
    };

    const billValue = param => {
      let str = '';
      if (merchantInfoObj === null) {
        return str;
      }
      let value = merchantInfoObj.changedBalance;
      if (param === 'unSettledBalance') {
        value = merchantInfoObj.unSettledBalance;
      }
      if (param === 'settledBalance') {
        value = merchantInfoObj.settledBalance;
      }

      if (param === 'frozenBalance') {
        value = merchantInfoObj.frozenBalance;
      }

      if (value === undefined || value === null || value === '') {
        return str;
      }
      if (value === 0) {
        return str;
      }
      str = value;
      return str;
    };
    const noFrozenBalance = {
      sm: {
        span: 5,
      },
      xs: {
        span: 24,
      },
    };
    const haveFrozenBalanceLi = {
      sm: {
        span: 4,
      },
      xs: {
        span: 24,
      },
    };
    const haveFrozenBalance = {
      sm: {
        span: 6,
      },
      xs: {
        span: 24,
      },
    };

    const merchantStatus = ejfStatus => {
      let str = ''; // 0
      if (ejfStatus === 1) {
        str = formatMessage({ id: '活动' });
      } else if (ejfStatus === 2) {
        str = formatMessage({ id: '冻结' });
      } else if (ejfStatus === 3) {
        str = formatMessage({ id: '其他' });
      } else if (ejfStatus === 0) {
        str = formatMessage({ id: '不活动' });
      }
      return str;
    };

    const showFrozenBalance =
      merchantInfoObj !== null && merchantInfoObj.frozenCustomer
        ? haveFrozenBalance
        : noFrozenBalance;
    const stateSpan =
      merchantInfoObj !== null && merchantInfoObj.frozenCustomer
        ? haveFrozenBalanceLi
        : noFrozenBalance;
    return (
      <div>
        <Card bordered={merchantInfoObj !== null} loading={getBalanceLoading}>
          {merchantInfoObj === undefined ? (
            <div
              style={{
                width: '100%',
                textAlign: 'center',
              }}
            >
              <img src={noBill} style={{ height: '60%' }} />
              <div style={{ fontSize: '18px', marginTop: '5px' }}>
                {formatMessage({ id: '账单服务暂时不可用' })}
              </div>
            </div>
          ) : (
            <Row type="flex" justify="space-around" align="middle">
              <Col {...showFrozenBalance}>
                {this.info(
                  account_icon1,
                  '#e4f1d7',
                  '#79b837',
                  formatMessage({ id: '账户余额' }),
                  billValue('changedBalance'),
                  true,
                  '#ffa837',
                  ''
                )}
              </Col>
              <Col {...showFrozenBalance}>
                {this.info(
                  account_icon2,
                  '#fff5d5',
                  '#ffcc2e',
                  formatMessage({ id: '未出账单金额' }),
                  billValue('unSettledBalance'),
                  true,
                  '#3782c4',
                  ''
                )}
              </Col>
              {merchantInfoObj !== null && merchantInfoObj.frozenCustomer && (
                <Col {...showFrozenBalance}>
                  {this.info(
                    account_icon4,
                    '#ffcac6',
                    '#ff5043',
                    formatMessage({ id: '制单预扣金额' }),
                    billValue('frozenBalance'),
                    true,
                    '#8b8b8b',
                    ''
                  )}
                </Col>
              )}
              <Col {...showFrozenBalance}>
                {this.info(
                  account_icon3,
                  '#dbecff',
                  '#4da1ff',
                  formatMessage({ id: '可用余额' }),
                  billValue('settledBalance'),
                  merchantInfoObj === null || !merchantInfoObj.frozenCustomer,
                  '#073473',
                  '',
                  merchantInfoObj?.frozenCustomer
                )}
              </Col>

              <Col {...stateSpan} style={{ textAlign: 'center' }}>
                {formatMessage({ id: '状态' })}：{merchantStatus(frozenStatus)}
              </Col>
            </Row>
          )}
        </Card>
        <Card style={{ marginTop: '10px' }} loading={getBalanceLoading}>
          <div style={{ display: 'flex', marginBottom: '-25px', marginLeft: '5px' }}>
            {tabDetailsData.map((element, index) => (
              <div
                onClick={() => this.selectTabs(element.key)}
                style={{
                  width: 'auto',
                  marginRight: '10px',
                  padding: '10px',
                  zIndex: '99',
                  borderBottomWidth: '2px',
                  borderBottomStyle: 'solid',
                  borderBottomColor: selectTabDetails === element.key ? '#52c41a' : 'transparent',
                }}
              >
                <span
                  style={{
                    color: selectTabDetails === element.key ? '#52c41a' : '',
                    cursor: 'pointer',
                  }}
                >
                  {element.title}
                </span>
              </div>
            ))}
          </div>
          <Divider style={{ height: '1px' }} />
          <SearchTabs
            payCycle={merchantInfoObj?.payCycle}
            onRef={node => (this.SearchTabs = node)}
            selectKey={selectTabDetails}
            resetFormData={this.resetFormData}
            costTypeList={costTypeList}
            changeSelectKey={this.changeSelectKey}
            account={account}
            payTypeList={[
              { value: '3', label: formatMessage({ id: '全部' }) },
              { value: '1', label: formatMessage({ id: '已清' }) },
              { value: '2', label: formatMessage({ id: '未清' }) },
            ]}
            getList={this.getList}
            {...this.props}
          />
        </Card>
        <Card bodyStyle={{ padding: '5px' }} style={{ marginTop: '10px' }}>
          <ProTable
            headerTitle={
              selectTabDetails === 0 ? (
                <Typography.Text type="danger">
                  {formatMessage({ id: '本页面只支持查询已出账单的运单明细' })}，
                  {formatMessage({ id: '当周的运单请到' })}【
                  <NotOutBill currencyName={this.state.currencyName}>
                    <a>{formatMessage({ id: '未出账单金额' })}</a>
                  </NotOutBill>
                  】{formatMessage({ id: '页面查询' })}
                </Typography.Text>
              ) : null
            }
            rowSelection={
              selectTabDetails === 3
                ? {
                    type: 'checkbox',
                    selectedRowKeys: this.state.selectedRowKeys,
                    onChange: this.onSelectChange,
                  }
                : undefined
            }
            rowKey={(render, index) => index}
            columns={columns.filter(item => item.filterType === true)}
            dataSource={data}
            pagination={selectTabDetails === 1 ? false : paginationProps}
            search={false}
            options={{
              reload: false,
            }}
            scroll={{ x: 'max-content' }}
            loading={loading}
            columnsState={{
              persistenceKey:
                selectTabDetails === 0
                  ? 'wayBillList'
                  : selectTabDetails === 1
                  ? 'billList'
                  : 'typeBillList',
              persistenceType: 'localStorage',
            }}
            toolBarRender={() => [
              selectTabDetails === 0 ? (
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'end',
                    paddingRight: '20px',
                  }}
                >
                  <Button type="primary" onClick={() => this.exportFile()} loading={exportLoading}>
                    {formatMessage({ id: '导出' })}
                  </Button>
                </div>
              ) : selectTabDetails === 3 ? (
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'end',
                    paddingRight: '20px',
                  }}
                >
                  <Button
                    type="primary"
                    onClick={() => this.exportEBillFile()}
                    loading={exportLoading}
                  >
                    {formatMessage({ id: '批量下载' })}
                  </Button>
                </div>
              ) : (
                <div />
              ),
            ]}
          />
          <div
            style={{
              textAlign: 'right',
              fontSize: 16,
              // paddingTop: 20,
              paddingRight: 40,
              display: selectTabDetails === 1 || selectTabDetails === 2 ? '' : 'none',
            }}
          >
            <span>
              {formatMessage({ id: '账单总金额' })}：
              <span style={{ color: 'red' }}>{sumBillAmount ?? ''}</span>
              {this.state.currencyName}
            </span>
            &nbsp;&nbsp;&nbsp;&nbsp;{' '}
            <span style={{ display: selectTabDetails === 1 ? '' : 'none' }}>
              {formatMessage({ id: '入账总金额' })}：
              <span style={{ color: 'red' }}>{sumBalAmount ?? ''}</span>
              {this.state.currencyName}
            </span>
            &nbsp;&nbsp;&nbsp;&nbsp;
            <span style={{ display: selectTabDetails === 1 ? '' : 'none' }}>
              {formatMessage({ id: '未清总金额' })}：
              <span style={{ color: 'red' }}>{sumBalingAmount ?? ''}</span>
              {this.state.currencyName}
            </span>
          </div>
          <div
            style={{
              textAlign: 'right',
              fontSize: 16,
              // paddingTop: 20,
              paddingRight: 40,
              display: selectTabDetails === 0 ? '' : 'none',
            }}
          >
            <span>
              {formatMessage({ id: '合计' })}：
              <span style={{ color: 'red', display: 'none' }}>
                {totalSize ?? '0'}
                {formatMessage({ id: '票' })}
              </span>
            </span>
            &nbsp;&nbsp;&nbsp;&nbsp;{' '}
            <span>
              <span style={{ color: 'red' }}>{allTotalAmount ?? ''}</span>
              {this.state.currencyName}
            </span>
          </div>
        </Card>
        {/* {formatMessage({id:'用于弹窗使用'})} */}
        <Col {...showFrozenBalance} style={{ display: 'none' }}>
          {this.info(
            account_icon4,
            '#ffcac6',
            '#ff5043',
            formatMessage({ id: '制单预扣金额' }),
            billValue('frozenBalance'),
            true,
            '#8b8b8b',
            ''
          )}
        </Col>
        {/* {formatMessage({id:'用于弹窗使用'})} */}
        <MerchantStatusModal
          {...this.props}
          visible={merchantStatusOpen}
          onCancel={() => {
            this.setState({
              merchantStatusOpen: false,
            });
          }}
        />
      </div>
    );
  }
}

export default Bill;
