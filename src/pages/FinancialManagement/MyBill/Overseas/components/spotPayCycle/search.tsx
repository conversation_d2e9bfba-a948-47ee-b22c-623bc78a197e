import './index.less';
import {
  ProCard,
  ProForm,
  ProFormDateRangePicker,
  ProFormRadio,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Button, Col, Row, Space } from 'antd';
import React from 'react';
import { BILL_SERVICE, Search } from '../../../typings';
import { formatMessage } from 'umi-plugin-react/locale';

const SearchComponent = ({
  formRef,
  accountData,
  onSearch,
  buttonLoading,
  dateTime,
  setBillTypeKey,
  billTypeKey,
}: Search.IProps<BILL_SERVICE.getOverseaRecordRequest>) => {
  const billTypeOptions = [
    { value: '0', label: '账单详情' },
    { value: '1', label: '对账单' },
  ];

  const expenseTypeOptions = [
    { value: '', label: '全部' },
    { value: 'TC01', label: '消费' },
    { value: 'TC02', label: '退费' },
    { value: 'TC03', label: '赔偿' },
    { value: 'TC04', label: '二次费用' },
    { value: 'TC20', label: '付款' },
    { value: 'TC41', label: '清关处理费' },
    { value: 'TC39', label: '重派处理费' },
  ];

  const handleReset = () => {
    formRef.current?.resetFields();
    formRef.current?.setFieldsValue({
      accountCode: accountData?.[0]?.accountCode,
      dateTime,
      selectType: '0',
    });
  };

  const handleSearch = async () => {
    try {
      const values = await formRef.current?.validateFields();
      onSearch(values);
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <ProCard ghost>
      <ProCard bordered>
        <ProForm
          formRef={formRef}
          labelAlign="left"
          layout="horizontal"
          labelCol={{ flex: '70px' }}
          submitter={{
            render: () => {
              return [];
            },
          }}
        >
          <Row gutter={20} className="pt-2">
            <Col span={24}>
              <div className="ant-radio-button-part-no-border">
                <ProFormRadio.Group
                  name="accountCode"
                  label={formatMessage({ id: '业务账号' })}
                  radioType="button"
                  options={accountData}
                  fieldProps={{
                    buttonStyle: 'solid',
                  }}
                />
              </div>
            </Col>
            <Col span={24}>
              <div className="ant-radio-button-part-no-border">
                <ProFormRadio.Group
                  name="selectType"
                  label={formatMessage({ id: '账单类型' })}
                  radioType="button"
                  options={billTypeOptions}
                  fieldProps={{
                    buttonStyle: 'solid',
                    onChange(e) {
                      setBillTypeKey(e.target.value);
                    },
                  }}
                />
              </div>
            </Col>
            {billTypeKey === '0' && (
              <Col span={24}>
                <div className="ant-radio-button-part-no-border">
                  <ProFormRadio.Group
                    name="expenseType"
                    label={'消费类型'}
                    radioType="button"
                    options={expenseTypeOptions}
                    fieldProps={{
                      buttonStyle: 'solid',
                      onChange(e) {
                        handleSearch();
                      },
                    }}
                  />
                </div>
              </Col>
            )}
            {billTypeKey === '0' && (
              <Col span={6}>
                <ProFormTextArea
                  label={formatMessage({ id: '单号查询' })}
                  name={'transNo'}
                  placeholder={`${formatMessage({ id: '请输入订单' })}/${formatMessage({
                    id: '运单号',
                  })}，${formatMessage({ id: '最多输入' })}500${formatMessage({
                    id: '个订单',
                  })}/${formatMessage({ id: '运单号' })}/${formatMessage({
                    id: '交易号',
                  })}，${formatMessage({ id: '多单号请以逗号' })}、${formatMessage({
                    id: '空格或回车隔开',
                  })}`}
                ></ProFormTextArea>
              </Col>
            )}
            <Col span={6}>
              <ProFormDateRangePicker
                fieldProps={{
                  style: { width: '100%' },
                }}
                name={'dateTime'}
                label={formatMessage({ id: '交易时间' })}
                placeholder={[
                  formatMessage({ id: '请选择开始时间' }),
                  formatMessage({ id: '请选择结束时间' }),
                ]}
              />
            </Col>
            <Col span={6}>
              <Space className="pl-2">
                <Button type="primary" onClick={handleSearch} loading={buttonLoading}>
                  {formatMessage({ id: '查询' })}
                </Button>
                <Button onClick={handleReset}>{formatMessage({ id: '重置' })}</Button>
              </Space>
            </Col>
          </Row>
        </ProForm>
      </ProCard>
    </ProCard>
  );
};

export default SearchComponent;
