// import BillService from '@/services/bill';
import { ProFormInstance } from '@ant-design/pro-components';
// import { useModel } from '@umijs/max';
import { useMount, useUpdateEffect } from 'ahooks';
import { useRef, useState } from 'react';
import SearchComponent from './search';
import TablesComponents from './tables';
import moment from 'moment';
import React from 'react';
import { BILL_SERVICE } from '../../../typings';
import { message } from 'antd';
// const { getOverseaRecord } = BillService.Controller;

type TAccountDataInfo = USER_API.getOverseaAccountsData & {
  label?: string;
  value?: string;
};

type TAccountData = Array<TAccountDataInfo>;

const startTimeDate = moment()
  .subtract(6, 'months')
  .format('YYYY-MM-DD');
const endTimeDate = moment().format('YYYY-MM-DD');

const Index = (props: any) => {
  const { dispatch } = props;
  const formRef = useRef<ProFormInstance<BILL_SERVICE.getOverseaRecordRequest>>();
  const [pageSize, setPageSize] = useState(10);
  const [current, setCurrent] = useState(1);
  const [total, setTotal] = useState(0);
  const [dataList, setDataList] = useState<BILL_SERVICE.getOverseaRecordResponse['rows']>([]);
  const [tabActiveKey, setTabActiveKey] = useState('1');
  const [accountData, setAccountData] = useState<TAccountData>([]);
  const [billTypeKey, setBillTypeKey] = useState('0');
  useUpdateEffect(() => {
    getList();
  }, [pageSize, current, tabActiveKey, billTypeKey]);

  useMount(() => {
    getInit();
  });

  const getInitFunc = async () =>
    new Promise((resolve, reject) => {
      dispatch({
        type: 'overseas/getOverseaAccounts',
        payload: '0',
        callback: response => {
          if (response.success) {
            const data = response.data?.map(item => ({
              ...item,
              label: `${item?.accountCode}-${item?.currencyCode}`,
              value: item?.accountCode,
            }));
            setAccountData(data || []);
            resolve(data || []);
          } else {
            reject([]);
          }
        },
      });
    });

  const getInit = async () => {
    Promise.all([getInitFunc()]).then(res => {
      formRef.current?.setFieldsValue({
        accountCode: res[0]?.[0]?.accountCode,
        dateTime: [startTimeDate, endTimeDate],
        selectType: '0',
        expenseType: '',
      });
      getList();
    });
  };

  const getList = async () => {
    try {
      const values = await formRef.current?.validateFields();
      const params: Partial<typeof values> = {
        ...values,
        pageSize,
        currentPage: current,
        transStartDate:
          typeof values?.dateTime?.[0] === 'string'
            ? values?.dateTime?.[0]
            : values?.dateTime?.[0]?.format('YYYY-MM-DD') ?? undefined,
        transEndDate:
          typeof values?.dateTime?.[1] === 'string'
            ? values?.dateTime?.[1]
            : values?.dateTime?.[1]?.format('YYYY-MM-DD') ?? undefined,
      };
      if (values.transNo != undefined && !/^\s*$/.test(values.transNo)) {
        let textWaybill1 = textWaybill(values.transNo);
        if (!textWaybill1) {
          return;
        }
        // @ts-ignore
        params.numbers = textWaybill1.join(',');
        delete params.transNo;
      }
      dispatch({
        type: 'bill/getOverseaRecord',
        payload: params,
        callback: response => {
          if (response.success) {
            setDataList(response.data?.rows ?? []);
            setTotal(response.data?.total ?? 0);
          }
        },
      });
    } catch (error) {
      console.error(error);
    }
  };

  const textWaybill = value => {
    if (value === undefined || /^\s*$/.test(value)) {
      message.error(
        '请输入要搜索的运单编号，最多输入500个运单号，多单号请以逗号、空格或回车隔开',
        2
      );
      return false;
    }
    value = value.replace(/(^\s*)|(\s*$)/g, '');
    value = value.replace(/[\s+|\t+|,]/g, ',');
    // const reg = /[`~!@#$%^&*()_\-+=<>?:"{}|.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'。、]/im;
    // const lowercase = new RegExp('[a-z]+', 'g');
    const chineseReg = /^[^\u4E00-\u9FA5]+$/g;
    if (!chineseReg.test(value)) {
      message.error('运单号不能输入汉字');
      return false;
    }
    // if (lowercase.test(value)) {
    //   message.error('运单号不能输入小写字母');
    //   return;
    // }
    // if (reg.test(value)) {
    //   message.error('运单号不能输入特殊字符');
    //   return;
    // }
    const waybillarray = value.split(/,|\s|\n/);
    let tempArray = [];
    for (let i = 0; i < waybillarray.length; i++) {
      if (waybillarray[i].length !== 0) {
        tempArray.push(waybillarray[i]);
      }
    }
    if (tempArray.length > 500) {
      message.error('订单/运单号搜索最多输入500个订单/运单号，多单号请以逗号、空格或回车隔开');
      return false;
    }
    // if (this.state.account.length === 0) {
    //   message.error('暂无制单账号，请稍后重试');
    //   return;
    // }
    return tempArray;
  };

  const resetPageSize = () => {
    setCurrent(1);
    setPageSize(10);
  };

  const pagination = {
    pageSize,
    current,
    total,
    showSizeChanger: true,
    pageSizeOptions: ['5', '10', '20', '50', '100'],
    onChange: (page: number, pageSize: number) => {
      setCurrent(page);
      setPageSize(pageSize);
    },
    showTotal: () => {
      return `共- ${total} -条记录`;
    },
  };

  const handleSearch = (values: BILL_SERVICE.getOverseaRecordRequest) => {
    getList();
  };

  return (
    <div>
      <SearchComponent
        formRef={formRef}
        {...props}
        accountData={accountData}
        dateTime={[startTimeDate, endTimeDate]}
        onSearch={handleSearch}
        billTypeKey={billTypeKey}
        setBillTypeKey={setBillTypeKey}
      />
      <TablesComponents
        {...props}
        billTypeKey={billTypeKey}
        tabActiveKey={tabActiveKey}
        setTabActiveKey={setTabActiveKey}
        pagination={pagination}
        data={dataList}
        resetPageSize={resetPageSize}
      />
    </div>
  );
};

export default Index;
