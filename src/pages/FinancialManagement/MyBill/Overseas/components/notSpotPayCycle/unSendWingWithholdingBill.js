/**
 * 制单预扣金额--非海外派
 */
import React, { Component, Fragment } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {
  Row,
  Col,
  Button,
  Breadcrumb,
  Table,
  DatePicker,
  Input,
  Message,
  Tabs,
  message,
  Modal,
  Space,
} from 'antd';
import { connect } from 'dva';
import { portalUrl } from '../../../../../../../config/defaultSettings';

const { TextArea } = Input;

let waybillNumberArr = [];
let startCalcTime = '';
let endCalcTime = '';

@Form.create()
@connect(({ bill, loading }) => ({
  bill,
  authorizedLoading: loading.effects['user/getwithholdingbilldetail'],
}))
class UnSendWingWithholdingBill extends Component {
  constructor(props) {
    super(props);
    this.state = {
      total: 0,
      pagecurrent: 1,
      pageSize: 10,
      totalPrice: 0,
      totalTicket: 0,
      dataList: [],
      btnLoading: false,
      tableLoading: false,
      eaportLoading: false,
      isCancelWayBill: false,
      confirmLoading: false,
      cancelOrderReason: '',
      expressCode: undefined,
      selectedRows: [],
    };
  }

  // 取消订单原因变化
  changeCancelOrderReason = e => {
    this.setState({
      cancelOrderReason: e.target.value,
    });
  };

  // 取消制单
  cancelMakeOrder = () => {
    const { expressCode, cancelOrderReason } = this.state;
    this.setState({ confirmLoading: true });
    let param = { waybillKeyList: expressCode, note: cancelOrderReason };
    const { dispatch } = this.props;
    dispatch({
      type: 'overseas/cancelOverseaOrderWithDebitPage',
      payload: param,
      callback: result => {
        if (result.success) {
          message.success(result.message);
          // this.state.currentPage = 1;
          this.getDataList();
        }
        this.setState({
          confirmLoading: false,
          isCancelWayBill: false,
          cancelOrderReason: '',
        });
      },
    });
  };

  // 点击取消
  cancelOrder = params => {
    let waybillNumberList;
    if (params?.waybillNumber) {
      waybillNumberList = [{ userId: params?.customerCode, waybillNumber: params?.waybillNumber }];
    } else {
      const { selectedRows } = this.state;
      if (selectedRows.length == 0) {
        message.error('请至少选择一条数据');
        return;
      }
      waybillNumberList = selectedRows.map(item => ({
        userId: item?.customerCode,
        waybillNumber: item?.waybillNumber,
      }));
    }
    this.setState({
      isCancelWayBill: true,
      expressCode: waybillNumberList,
    });
  };

  columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      render: (text, record, index) =>
        `${index + 1 + this.state.pageSize * (this.state.pagecurrent - 1)}`,
    },
    {
      title: '制单账号',
      dataIndex: 'customerCode',
      key: 'customerCode',
    },
    {
      title: '运单号',
      dataIndex: 'waybillNumber',
      key: 'waybillNumber',
    },
    {
      title: '订单号',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
    },
    {
      title: '扣费日期',
      dataIndex: 'timeOfExpress',
      key: 'timeOfExpress',
      render: text => {
        if (text) {
          var timestamp = new Date(+text);
          return timestamp.toLocaleDateString().replace(/\//g, '-');
        } else {
          return '';
        }
      },
    },
    {
      title: '目的地',
      dataIndex: 'countryName',
      key: 'countryName',
    },
    {
      title: '邮编分区',
      dataIndex: 'zone',
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      key: 'productName',
    },
    {
      title: '计费重量(克)',
      dataIndex: 'calcWeight',
      key: 'calcWeight',
    },
    {
      title: '扣费金额',
      dataIndex: 'arPrice',
      key: 'arPrice',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      render: (text, record) => {
        return (
          <Button type="link" onClick={() => this.cancelOrder(record)}>
            取消
          </Button>
        );
      },
    },
  ];

  componentDidMount() {
    waybillNumberArr = [];
    startCalcTime = '';
    endCalcTime = '';
    this.getDataList();
  }

  saveTimer = cityInfo => {
    if (cityInfo.dates && cityInfo.dates[0]) {
      startCalcTime = cityInfo.dates[0].format('YYYY-MM-DD');
      endCalcTime = cityInfo.dates[1].format('YYYY-MM-DD');
    } else {
      startCalcTime = '';
      endCalcTime = '';
    }
  };
  roolwaybillNumber = cityInfo => {
    const valuerlurs = new RegExp('^[0-9a-zA-Z ,]+$');
    if (!valuerlurs.test(cityInfo.waybillNumbers)) {
      Message.error('运单号不能出现数字及字母外的字符');
      this.setState({
        btnLoading: false,
      });
      return;
    }
    waybillNumberArr = cityInfo.waybillNumbers.split(',');
    if (waybillNumberArr.length > 20) {
      Message.error('最多输入20单');
      this.setState({
        btnLoading: false,
      });
      return;
    }
    const isLast = cityInfo.waybillNumbers.charAt(cityInfo.waybillNumbers.length - 1);
    if (isLast == ',') {
      Message.error('填写运单号请不要以逗号结尾');
      this.setState({
        btnLoading: false,
      });
      return;
    }
    return true;
  };

  handelClick = () => {
    this.setState({
      btnLoading: true,
    });
    let cityInfo = this.props.form.getFieldsValue();
    this.saveTimer(cityInfo);

    if (cityInfo.waybillNumbers != undefined && cityInfo.waybillNumbers != '') {
      if (!this.roolwaybillNumber(cityInfo)) {
        return;
      }
    } else {
      waybillNumberArr = [];
    }
    this.getDataList();
  };
  getDataList = () => {
    this.setState({
      tableLoading: true,
    });
    const { dispatch } = this.props;
    dispatch({
      type: 'bill/withoutList',
      payload: {
        pageSize: this.state.pageSize,
        currentPage: this.state.pagecurrent,
        startCalcTime: startCalcTime,
        endCalcTime: endCalcTime,
        waybillNumbers: waybillNumberArr,
        merchantCode: this.props.merchantCode,
        businessType: 2,
      },
      callback: res => {
        this.setState({
          btnLoading: false,
          tableLoading: false,
        });
        if (res.success) {
          if (res.code != 200) {
            Message.error(res.message);
            this.setState({
              dataList: [],
              total: 0,
              totalPrice: 0,
              totalTicket: 0,
            });
            return;
          }
          this.setState({
            dataList: res.data.returnlist,
            total: res.data.totalrows,
            totalPrice: res.data.totalAmount,
            totalTicket: res.data.totalrows,
          });
        } else {
          Message.error(res.message);
          this.setState({
            dataList: [],
            total: 0,
            totalPrice: 0,
            totalTicket: 0,
          });
        }
      },
    });
  };
  changePage = current => {
    this.setState({
      pagecurrent: current,
    });
    setTimeout(() => {
      this.getDataList();
    });
  };
  changePageSize = (pageSize, current) => {
    this.setState({
      pageSize: pageSize,
      pagecurrent: current,
    });
    setTimeout(() => {
      this.getDataList();
    });
  };
  exportExcel = () => {
    this.setState({
      eaportLoading: true,
    });
    let cityInfo = this.props.form.getFieldsValue();
    this.saveTimer(cityInfo);
    if (cityInfo.waybillNumbers != undefined && cityInfo.waybillNumbers != '') {
      if (!this.roolwaybillNumber(cityInfo)) {
        this.setState({
          eaportLoading: false,
        });
        return;
      }
    } else {
      cityInfo.waybillNumbers = '';
    }

    let url = `${portalUrl}/bill/without/export?wayBillList=${cityInfo.waybillNumbers}&startTime=${startCalcTime}&endTime=${endCalcTime}&businessType=2&merchantCode=${this.props.merchantCode}`;
    window.open(url);
    setTimeout(() => {
      this.setState({
        eaportLoading: false,
      });
    }, 3000);
  };

  render() {
    const {
      total,
      pagecurrent,
      pageSize,
      totalPrice,
      totalTicket,
      dataList,
      btnLoading,
      tableLoading,
      eaportLoading,
      isCancelWayBill,
      confirmLoading,
      cancelOrderReason,
    } = this.state;
    const { getFieldDecorator } = this.props.form;
    const { MonthPicker, RangePicker } = DatePicker;
    const formItemLayout = {
      labelCol: { span: 6 },
      wrapperCol: { span: 14 },
    };
    const paginationProps = {
      // https://blog.csdn.net/luzhaopan/article/details/84996507(分页详细)
      total: total,
      pageSize: pageSize,
      showTotal: () => `共 ${total} 条`,
      style: { textAlign: 'center' },
      current: pagecurrent,
      showSizeChanger: true,
      pageSizeOptions: [10, 20, 50, 100, 500],
      showQuickJumper: true,
      onChange: (current, pageSize) => this.changePageSize(pageSize, current),
    };
    return (
      <div>
        {/*------------------------------------*/}
        <div className="search_b_w" style={{ background: '#fff', padding: '10px 26px' }}>
          <Form layout="inline">
            <Form.Item label="日期:">
              {getFieldDecorator('dates')(<RangePicker format="YYYY-MM-DD" />)}
            </Form.Item>
            <Form.Item label="运单:">
              {getFieldDecorator('waybillNumbers', { rules: [] })(
                <Input
                  style={{ width: '460px' }}
                  allowClear
                  placeholder="最多输入20个单号，输入多个单号请用英文,隔开。请以运单号结尾"
                />
              )}
            </Form.Item>
            <Form.Item>
              <Space>
                <Button type="primary" loading={btnLoading} onClick={this.handelClick}>
                  查询
                </Button>
                <Button type="primary" loading={eaportLoading} onClick={this.exportExcel}>
                  下载
                </Button>
              </Space>
            </Form.Item>
            {this.props.merchantCode && this.props.merchantCode?.startsWith('8') && (
              <Button
                danger
                onClick={this.cancelOrder}
                style={{ marginTop: '7px', float: 'right' }}
              >
                批量取消
              </Button>
            )}
          </Form>
        </div>
        <div className="table_f">
          <Table
            loading={tableLoading}
            bordered
            rowKey={record => record.waybillNumber}
            columns={this.columns}
            dataSource={dataList}
            rowSelection={{
              onChange: (selectedRowKeys, selectedRows) => {
                this.setState({
                  selectedRows,
                });
              },
            }}
            rowClassName={(record, index) => {
              let className = 'light-row';
              if (index % 2 === 1) className = 'dark-row';
              return className;
            }}
            footer={() => (
              <div style={{ overflow: 'hidden' }}>
                <div style={{ float: 'right' }}>
                  <span style={{ fontSize: '15px', fontWeight: '600' }}>合计:</span>
                  <span style={{ fontSize: '15px', margin: '0 10px' }}>{totalTicket} 票</span>
                  <span style={{ fontSize: '15px' }}>{totalPrice} 元</span>
                </div>
              </div>
            )}
            pagination={paginationProps}
          />
        </div>
        {/* 取消运单弹框 */}
        <Modal
          title="取消订单"
          visible={isCancelWayBill}
          onCancel={() => {
            this.setState({ isCancelWayBill: false, cancelOrderReason: '' });
          }}
          footer={[
            <Button
              key="submit"
              type="primary"
              loading={confirmLoading}
              onClick={() => this.cancelMakeOrder()}
            >
              确定
            </Button>,
            <Button
              key="back"
              onClick={() => {
                this.setState({ isCancelWayBill: false, cancelOrderReason: '' });
              }}
            >
              取消
            </Button>,
          ]}
        >
          <p>请输入取消订单原因（最多支持100个字符）</p>
          <TextArea
            rows={4}
            value={cancelOrderReason}
            onChange={this.changeCancelOrderReason}
            placeholder="请输入取消运单原因(最多支持100个字符)"
            maxLength={100}
          />
        </Modal>
      </div>
    );
  }
}

export default UnSendWingWithholdingBill;
