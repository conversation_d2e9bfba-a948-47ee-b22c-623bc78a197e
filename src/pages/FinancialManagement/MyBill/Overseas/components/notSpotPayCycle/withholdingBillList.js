/**
 * 制单预扣金额
 */
import React, { Component } from 'react';
import { Modal, Empty } from 'antd';
import { StopOutlined } from '@ant-design/icons';
import UnSendWingWithholdingBill from './unSendWingWithholdingBill';
import SendWingWithholdingBill from './sendWingWithholdingBill';
import { connect } from 'dva';
import { isAuth } from '@/utils/utils';
import PageContainerComponent from '@/components/PageContainer';
import PageHeaderBreadcrumb from '@/components/PageHeaderBreadcrumb';

@connect(({ bill }) => ({}))
class withholdingBillList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      sendWingFlag: false,
      visible: false,
    };
  }

  showModelHandler = () => {
    this.setState({
      visible: true,
    });
  };

  componentDidMount() {}

  render() {
    let temp = <UnSendWingWithholdingBill />;
    if (this.state.sendWingFlag == undefined) {
      temp = <div></div>;
    } else if (this.state.sendWingFlag) {
      temp = <SendWingWithholdingBill />;
    } else {
      temp = <UnSendWingWithholdingBill merchantCode={this.props.merchantCode} />;
    }

    return (
      <span>
        <span onClick={this.showModelHandler}>{this.props.children}</span>
        <Modal
          visible={this.state.visible}
          style={{
            top: 0,
          }}
          width="100%"
          height="100%"
          footer={false}
          onCancel={() => {
            this.setState({
              visible: false,
            });
          }}
        >
          <PageContainerComponent
            header={{
              title: null,
              breadcrumb: {},
              breadcrumbRender: props => (
                <PageHeaderBreadcrumb {...props} col>
                  <h2 style={{ margin: '16px 0', fontSize: '20px' }}>{'制单预扣金额清单'}</h2>
                </PageHeaderBreadcrumb>
              ),
            }}
          >
            <div style={{ display: this.state.sendWingFlag == undefined ? 'none' : '' }}>
              {temp}
            </div>
          </PageContainerComponent>
        </Modal>
      </span>
    );
  }
}

export default withholdingBillList;
