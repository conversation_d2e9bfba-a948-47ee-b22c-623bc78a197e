import React, { Component } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Button, Col, Descriptions, Input, Popover, Radio, Row, Select, Space, Table } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import { connect } from 'dva';
import { Decimal } from 'decimal.js';
import { formatMessage } from 'umi-plugin-react/locale';

@connect(({ freight, loading }) => ({
  freight,
  chargePriceOverseasLoading: loading.effects['freight/chargePriceOverseas'],
  getOverseaAccountsLoading: loading.effects['overseas/getOverseaAccounts'],
}))
@Form.create()
class FreightTrialOverseas extends Component {
  constructor(props) {
    super(props);
    this.state = {
      defaultValue: [],
      dataList: [],
      expandedRowKeys: [],
      isYWE: false,
      accountCodes: [],
      handoverCodes: [],
      sortField: null, // 当前排序字段
      sortOrder: null, // 当前排序顺序
      originalDataList: [], // 原始未排序的表格数据
    };
  }

  componentDidMount() {
    const { dispatch } = this.props;
    dispatch({
      type: 'overseas/getOverseaAccounts',
      payload: '0',
      callback: response => {
        if (response.success) {
          const data = response.data?.map(item => ({
            ...item,
            label: `${item?.accountCode}-${item?.currencyCode}`,
            value: item?.accountCode,
          }));
          this.setState({
            accountCodes: data || [],
          });
        }
      },
    });
    // 海外交货地
    dispatch({
      type: 'Order/getHandoverCodes',
      callback: result => {
        if (result.success) {
          this.setState({
            handoverCodes: result.data
              .filter(x => x.isEnable)
              .map(item => {
                item.label = `${item.countryName}/${item.overseasDeliveryLocation}`;
                item.value = item.overseasDeliveryLocation;
                return item;
              }),
          });
        }
      },
    });
  }

  getDataList = parameterList => {
    const { dispatch } = this.props;
    dispatch({
      type: 'freight/chargePriceOverseas',
      payload: parameterList,
      callback: res => {
        if (res.success) {
          const tableDataList = res.data.result.items;
          const dataArr = [];
          tableDataList.map(item => {
            let moneynum = 0;
            let moneyOfOriginalnum = 0;
            item.expenseItems.map(m => {
              moneynum = new Decimal(moneynum).add(new Decimal(m.sheetCurrencyMoney));
              moneyOfOriginalnum = new Decimal(moneyOfOriginalnum).add(
                new Decimal(m.sheetCurrencyMoneyOfOriginal)
              );
            });
            // 添加排序辅助字段
            const referAgingSortValue = (() => {
              if (item?.referAging === undefined || item?.referAging === null)
                return Number.MAX_SAFE_INTEGER; // 空值使用最大值

              const match = item.referAging.match(/(\d+)(?:-(\d+))?/);
              if (!match) return Number.MAX_SAFE_INTEGER;

              const min = parseInt(match[1], 10) || 0;
              const max = match[2] ? parseInt(match[2], 10) : min;
              return min * 1000 + max;
            })();

            let tabObj = {
              productname: item.productInfo.productName, //{formatMessage({id: '产品名称'})}
              totalMoney: item.totalMoney, //{formatMessage({id: '金额'})}
              calcCurrency: item.calcCurrency, //{formatMessage({id: '金额'})}
              trackingInfo: item.trackingInfo, //{formatMessage({id: '追踪等级'})}
              sheetCurrencyBasicFreight: item.sheetCurrencyBasicFreight, //{formatMessage({id: '基础资费'})}
              sheetCurrencyTrunkFreight: item.sheetCurrencyTrunkFreight, //{formatMessage({id: '干线费用'})}
              sheetCurrencyFuelCost: item.sheetCurrencyFuelCost, //{formatMessage({id: '燃油费'})}
              sheetCurrencyAdditionalCharge: item.sheetCurrencyAdditionalCharge, //{formatMessage({id: '附加费'})}
              referAging: item.referAging,
              referAgingSortValue: referAgingSortValue, // 新增的排序值字段
              productType: item.productInfo.ywproductGenealogy, //{formatMessage({id: '产品类型'})}
              hasTax: item.productInfo.hasTax, //{formatMessage({id: '是否包税'})}
              itemAttribute: item.productInfo.itemAttribute, //{formatMessage({id: '货品属性'})}
              isThrowweight: item.isThrowweight, //{formatMessage({id: '是否计泡'})}
              productShowOrder: item.productInfo.productShowOrder, //{formatMessage({id: '顺序'})}
              throwWeightFactor: item.throwWeightFactor, //{formatMessage({id: '计泡系数'})}
              calculateWeightFactor: item?.calculateWeightFactor, //{formatMessage({id: '计泡系数'})}
              calcWeight: item.calcWeight, //{formatMessage({id: '重量'})}
              moneyOfOriginal: moneyOfOriginalnum.toNumber().toFixed(2), // {formatMessage({id: '资费'})}（{formatMessage({id: '元'})}）
              money: moneynum.toNumber(), //{formatMessage({id: '折后价'})}（{formatMessage({id: '元'})}）
              discountRate: item.discountRate, // {formatMessage({id: '折扣值'})}
              productFuelRate: item.productFuelRate, //{formatMessage({id: '燃油费率'})}
              productcode: item.productInfo.productNumber, //id
              remark: item.remark,
              topcRemarksData: item.topcRemarks,
              recommendProduct: item?.recommendProduct,
            };
            dataArr.push(tabObj);
          });

          // 保存原始数据的副本
          this.setState({
            dataList: dataArr,
            originalDataList: [...dataArr], // 保存副本
          });
        } else {
          this.setState({
            dataList: [],
            originalDataList: [], // 清空原始数据
          });
        }
      },
    });
  };
  handleSubmit = e => {
    e?.preventDefault();
    this.props.form.validateFields((err, values) => {
      if (!err) {
        console.log(values);
        const parameterList = {
          customerCode: values.customerCode,
          cityId: values.cityId,
          weight: values.weight,
          high: values.high,
          length: values.length,
          width: values.width,
        };
        // this.setState({
        //   dataOBJ: parameterList,
        // });
        this.getDataList(parameterList);
      }
    });
  };
  resetFields = () => {
    this.props.form.resetFields();
    this.setState({ defaultValue: [] });
  };

  expand = record => {
    const ti = (
      <div style={{ marginLeft: '10px', fontSize: '14px' }}>
        {formatMessage({ id: '产品说明' })}：
      </div>
    );

    let obj = record.remark;
    Reflect.ownKeys(obj).forEach(function(key) {
      if (obj[key] == null || obj[key] == '' || obj[key] == undefined) {
      } else {
        obj[key] = obj[key]
          .toString()
          .replace(/\n/g, '<br/>')
          .replace(/\（/g, '(')
          .replace(/\）/g, ')');
      }
    });
    return (
      <div className="b_c_y">
        <Descriptions column={1} bordered title={ti}>
          <Descriptions.Item label={`1.${formatMessage({ id: '参考时效' })}`}>
            <div dangerouslySetInnerHTML={{ __html: record.remark.referAging }}></div>
          </Descriptions.Item>
          <Descriptions.Item label={`2.${formatMessage({ id: '价格构成' })}`}>
            <div dangerouslySetInnerHTML={{ __html: record.remark.priceStructure }}></div>
          </Descriptions.Item>
          <Descriptions.Item label={`3.${formatMessage({ id: '计费方式' })}`}>
            <div dangerouslySetInnerHTML={{ __html: record.remark.billingWay }}></div>
          </Descriptions.Item>
          <Descriptions.Item label={`4.${formatMessage({ id: '走货属性' })}`}>
            <div dangerouslySetInnerHTML={{ __html: record.remark.goodsAttribute }}></div>
          </Descriptions.Item>
          <Descriptions.Item label={`5.${formatMessage({ id: '申报价值' })}`}>
            <div dangerouslySetInnerHTML={{ __html: record.remark.declaredValue }}></div>
          </Descriptions.Item>
          <Descriptions.Item label={`6.${formatMessage({ id: '重量要求' })}`}>
            <div dangerouslySetInnerHTML={{ __html: obj.weightRequirement }} />
          </Descriptions.Item>
          <Descriptions.Item label={`7.${formatMessage({ id: '包装尺寸' })}`}>
            <div
              style={{ color: record.remark.productCode == '801' ? 'red' : '' }}
              dangerouslySetInnerHTML={{ __html: record.remark.packingSize }}
            ></div>
          </Descriptions.Item>
          <Descriptions.Item label={`8.${formatMessage({ id: '派送地址要求' })}`}>
            <div dangerouslySetInnerHTML={{ __html: record.remark.deliveryRequirements }}></div>
          </Descriptions.Item>
          <Descriptions.Item label={`9.${formatMessage({ id: '退件重派' })}`}>
            <div dangerouslySetInnerHTML={{ __html: record.remark.toResend }}></div>
          </Descriptions.Item>
          <Descriptions.Item label={`10.${formatMessage({ id: '保险服务' })}`}>
            <div dangerouslySetInnerHTML={{ __html: record.remark.insurance }}></div>
          </Descriptions.Item>
          <Descriptions.Item label={`11.${formatMessage({ id: '赔偿标准' })}`}>
            <div dangerouslySetInnerHTML={{ __html: record.remark.compensation }}></div>
          </Descriptions.Item>
          <Descriptions.Item label={`12.${formatMessage({ id: '查询网址' })}`}>
            <div dangerouslySetInnerHTML={{ __html: record.remark.queryUrl }}></div>
          </Descriptions.Item>
          <Descriptions.Item label={`13.${formatMessage({ id: '其他要求' })}`}>
            {record.remark.otherRequirements == null ? (
              formatMessage({ id: '暂无' })
            ) : (
              <div dangerouslySetInnerHTML={{ __html: record.remark.otherRequirements }}></div>
            )}
          </Descriptions.Item>
        </Descriptions>
        {record.topcRemarksData.length > 0 ? (
          <Table
            size="small"
            className="datles_table"
            bordered
            rowKey={(_, index) => index}
            style={{ background: '#fff' }}
            columns={this.datilsCol}
            dataSource={record.topcRemarksData}
            pagination={false}
          ></Table>
        ) : null}
      </div>
    );
  };

  datilsCol = [
    {
      title: '国家',
      dataIndex: 'countryName',
      key: 'countryName',
      width: '80px',
    },
    {
      title: '参考时效 (自然日)',
      dataIndex: 'referAging',
      key: 'referAging',
      width: '160px',
    },
    {
      title: '特殊要求',
      dataIndex: 'specialRequirement',
      key: 'specialRequirement',
    },
    {
      title: '申报价值',
      dataIndex: 'declaredValue',
      key: 'declaredValue',
      width: '360px',
    },
    {
      title: '税费征收说明',
      dataIndex: 'rateDesc',
      key: 'rateDesc',
      width: '160px',
    },
    {
      title: '重量要求',
      dataIndex: 'weightRequirement',
      key: 'weightRequirement',
      width: '100px',
    },
    {
      title: '包装尺寸',
      dataIndex: 'packingSize',
      key: 'packingSize',
      width: '170px',
    },
    {
      title: '派送地址要求',
      dataIndex: 'deliveryRequirement',
      key: 'deliveryRequirement',
      width: '160px',
    },
    {
      title: '退件重派',
      dataIndex: 'toResend',
      key: 'toResend',
      width: '160px',
    },
  ];

  ichange = e => {
    e.target.value = e.target.value.replace(/\D/g, '');
  };

  // 业务账号变更
  accountChange = value => {
    this.setState({
      isYWE: value.target.value.startsWith('8'),
    });
  };

  // 修改 handleTableChange 方法
  handleTableChange = (pagination, filters, sorter) => {
    const { originalDataList } = this.state;

    // 保存排序状态
    this.setState({
      sortField: sorter.field || null,
      sortOrder: sorter.order || null,
    });

    // 如果没有排序或取消了排序
    if (!sorter.field || !sorter.order) {
      // 恢复到原始数据顺序
      this.setState({ dataList: [...originalDataList] });
      return;
    }

    // 有排序时，以原始数据为基础进行排序
    let sortedData = [...originalDataList];

    if (sorter.field === 'referAging') {
      sortedData.sort((a, b) => {
        // 解析函数
        const parseTime = timeStr => {
          if (!timeStr) return { valid: false, value: Number.MAX_SAFE_INTEGER };

          const match = timeStr.match(/(\d+)(?:-(\d+))?/);
          if (!match) return { valid: false, value: Number.MAX_SAFE_INTEGER };

          const min = parseInt(match[1], 10) || 0;
          const max = match[2] ? parseInt(match[2], 10) : min;
          return { valid: true, value: min * 1000 + max };
        };

        const aTime = parseTime(a?.referAging);
        const bTime = parseTime(b?.referAging);

        // 空值始终排在最后
        if (!aTime.valid && !bTime.valid) return 0;
        if (!aTime.valid) return 1;
        if (!bTime.valid) return -1;

        // 根据排序方向
        return sorter.order === 'ascend' ? aTime.value - bTime.value : bTime.value - aTime.value;
      });
    } else {
      // 其他字段使用默认排序逻辑
      sortedData.sort((a, b) => {
        const aValue = a[sorter.field];
        const bValue = b[sorter.field];

        // 处理空值
        if (aValue === undefined && bValue === undefined) return 0;
        if (aValue === undefined) return 1;
        if (bValue === undefined) return -1;

        // 升序或降序
        return sorter.order === 'ascend' ? (aValue < bValue ? -1 : 1) : aValue > bValue ? -1 : 1;
      });
    }

    // 更新排序后的数据
    this.setState({ dataList: sortedData });
  };

  // -------------------。
  render() {
    const { accountCodes, handoverCodes, dataList, expandedRowKeys, isYWE } = this.state;
    const { form, chargePriceOverseasLoading, getOverseaAccountsLoading } = this.props;
    const { getFieldDecorator } = form;
    const columns = [
      {
        title: (
          <span>
            产品名称<span style={{ fontSize: '12px', color: '#bfbfbf' }}>（点击查看详情）</span>
          </span>
        ),
        dataIndex: 'productname',
        key: 'productname',
        sorter: (a, b) => {
          var sort = a.productShowOrder - b.productShowOrder;
          if (sort == 0) {
            return a.totalMoney - b.totalMoney;
          }
          return sort;
        },
        render: (text, record, index) => {
          return (
            <div>
              {record.remark ? (
                <>
                  {record?.recommendProduct === 1 ? (
                    <span
                      style={{
                        color: '#ffffff',
                        background: 'rgb(247,54,54)',
                        fontSize: '8px',
                        padding: '2px 10px',
                        borderRadius: '20px',
                        marginRight: '10px',
                      }}
                    >
                      推荐
                    </span>
                  ) : null}

                  <a
                    onClick={() => {
                      const { expandedRowKeys } = this.state;
                      const indexRow = expandedRowKeys.indexOf(index);
                      let keys = [...expandedRowKeys];
                      if (indexRow > -1) keys = keys.filter(key => key !== index);
                      else keys.push(index);
                      this.setState({
                        expandedRowKeys: keys,
                      });
                    }}
                  >
                    {text}
                  </a>
                </>
              ) : (
                <span>{text}</span>
              )}
            </div>
          );
        },
      },
      {
        title: '跟踪类型',
        dataIndex: 'trackingInfo',
        key: 'trackingInfo',
        sorter: (a, b) => a?.trackingInfo?.localeCompare(b?.trackingInfo),
      },
      {
        title: '参考时效(天)',
        dataIndex: 'referAging',
        width: '160px',
        key: 'referAging',
        sorter: (a, b) => a.referAgingSortValue - b.referAgingSortValue,
      },
      {
        title: '产品类型',
        dataIndex: 'productType',
        key: 'productType',
        sorter: (a, b) => a.productType - b.productType,
      },
      {
        title: '货品属性',
        dataIndex: 'itemAttribute',
        key: 'itemAttribute',
      },
      {
        title: '计泡系数',
        dataIndex: 'throwWeightFactor',
        key: 'throwWeightFactor',
        render: (text, record) => {
          return record.isThrowweight ? text : '';
        },
      },
      {
        title: '计费重量（克）',
        dataIndex: 'calcWeight',
        key: 'calcWeight',
        render: text => {
          const {
            form: { getFieldsValue },
          } = this.props;
          const { weight } = getFieldsValue();
          return <span style={{ color: weight != text ? 'red' : '' }}>{text}</span>;
        },
      },
      {
        title: '币种',
        dataIndex: 'calcCurrency',
        key: 'calcCurrency',
      },
      {
        title: '资费',
        dataIndex: 'money',
        key: 'money',
        sorter: (a, b) => a.money - b.money,
        render: (text, record) => {
          if (text) {
            let finalText;
            if (text.toString().indexOf('.') === -1) {
              finalText = text;
            } else {
              finalText = Math.round((text + Number.EPSILON) * 100) / 100;
            }
            return (
              <Popover
                content={
                  <>
                    {record.sheetCurrencyBasicFreight != 0 && (
                      <div>
                        基础费:{' '}
                        <span style={{ color: '#73d13d' }}>{record.sheetCurrencyBasicFreight}</span>
                      </div>
                    )}
                    {record.sheetCurrencyTrunkFreight != 0 && (
                      <div>
                        干线费:{' '}
                        <span style={{ color: '#73d13d' }}>{record.sheetCurrencyTrunkFreight}</span>
                      </div>
                    )}
                    {record.sheetCurrencyFuelCost != 0 && (
                      <div>
                        燃油费:{' '}
                        <span style={{ color: '#73d13d' }}>{record.sheetCurrencyFuelCost}</span>
                      </div>
                    )}
                    {record.sheetCurrencyAdditionalCharge != 0 && (
                      <div>
                        附加费:{' '}
                        <span style={{ color: '#73d13d' }}>
                          {record.sheetCurrencyAdditionalCharge}
                        </span>{' '}
                      </div>
                    )}
                  </>
                }
                trigger="hover"
              >
                <Space size={5}>
                  <span>{finalText}</span>
                  {record.sheetCurrencyBasicFreight != 0 &&
                    (record.sheetCurrencyTrunkFreight != 0 ||
                      record.sheetCurrencyAdditionalCharge != 0 ||
                      record.sheetCurrencyFuelCost != 0) && (
                      <InfoCircleOutlined style={{ color: '#df8909', fontSize: '12px' }} />
                    )}
                </Space>
              </Popover>
            );
          }
        },
      },
    ];
    return (
      <>
        <div style={{ background: '#fff', paddingTop: '10px' }}>
          <Form labelCol={{ flex: '90px' }} className="mb-3" onSubmit={this.handleSubmit}>
            <Row>
              <Col span={20}>
                <Form.Item
                  className="ant-radio-button-part-no-border"
                  label={formatMessage({ id: '业务账号' })}
                  style={{ marginBottom: 12 }}
                >
                  {getFieldDecorator('customerCode', {
                    // initialValue: defaultValue,
                    rules: [{ required: true, message: formatMessage({ id: '请选择业务账号' }) }],
                  })(
                    <Radio.Group
                      options={accountCodes}
                      onChange={this.accountChange}
                      optionType="button"
                      buttonStyle="solid"
                    />
                  )}
                </Form.Item>
              </Col>
              <Col span={4} style={{ textAlign: 'center' }}>
                <Form.Item style={{ marginBottom: 12 }}>
                  <Button
                    type="primary"
                    loading={chargePriceOverseasLoading || getOverseaAccountsLoading}
                    htmlType="submit"
                  >
                    {formatMessage({ id: '查询' })}
                  </Button>
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col span={10}>
                <Form.Item label={formatMessage({ id: '实际重量' })} style={{ marginBottom: 12 }}>
                  {getFieldDecorator('weight', {
                    rules: [{ required: true, message: formatMessage({ id: '请填写重量' }) }],
                  })(
                    <Input
                      onChange={e => this.ichange(e)}
                      maxLength={10}
                      suffix={formatMessage({ id: '克' })}
                    />
                  )}
                </Form.Item>
              </Col>
              <Col span={10}>
                <Form.Item
                  label={`${formatMessage({ id: '尺寸' })}(cm)`}
                  style={{ marginBottom: 12 }}
                >
                  <span style={{ marginRight: '5px' }}>{formatMessage({ id: '长' })}</span>
                  {getFieldDecorator('length', { initialValue: '' })(
                    <Input
                      style={{ width: '100px' }}
                      onChange={e => this.ichange(e)}
                      maxLength={3}
                    />
                  )}
                  <span style={{ margin: '0 5px' }}>{formatMessage({ id: '宽' })}</span>
                  {getFieldDecorator('width', { initialValue: '' })(
                    <Input
                      style={{ width: '100px' }}
                      onChange={e => this.ichange(e)}
                      maxLength={3}
                    />
                  )}
                  <span style={{ margin: '0 5px' }}>{formatMessage({ id: '高' })}</span>
                  {getFieldDecorator('high', { initialValue: '' })(
                    <Input
                      style={{ width: '100px' }}
                      onChange={e => this.ichange(e)}
                      maxLength={3}
                    />
                  )}
                </Form.Item>
              </Col>
              <Col span={4} style={{ textAlign: 'center' }}>
                <Form.Item style={{ marginBottom: 12 }}>
                  <Button type="primary" onClick={this.resetFields}>
                    {formatMessage({ id: '重置' })}
                  </Button>
                </Form.Item>
              </Col>
            </Row>
            {isYWE && (
              <Row>
                <Col span={10}>
                  <Form.Item
                    label={formatMessage({ id: '海外交货地' })}
                    style={{ marginBottom: 12 }}
                  >
                    {getFieldDecorator('cityId', {
                      rules: [
                        { required: true, message: formatMessage({ id: '请选择海外交货地' }) },
                      ],
                    })(<Select allowClear options={handoverCodes} />)}
                  </Form.Item>
                </Col>
              </Row>
            )}
          </Form>
        </div>
        {/*-------------------- // expandIconAsCell: false, //{formatMessage({id: '隐藏'})}+
              // expandIconColumnIndex: -1, //+-------------------------------*/}
        <div style={{ marginTop: '16px', padding: '10px', background: '#fff' }}>
          <Table
            columns={columns
              .filter(item => !item?.hideItem)
              .map(col => ({
                ...col,
                // 保留排序UI但不实际排序
                sorter: col.key === 'referAging' ? true : col.sorter,
              }))}
            bordered
            className="t_s"
            size="small"
            style={{ minHeight: '362px' }}
            dataSource={dataList}
            rowKey={(record, index) => index}
            loading={chargePriceOverseasLoading}
            expandable={{
              onExpand: () => {},
              expandedRowKeys: expandedRowKeys,
              expandIconColumnIndex: -1,
              expandedRowRender: (record, index, indent, expanded) =>
                this.expand(record, index, indent, expanded),
            }}
            title={() => {
              return (
                <div>
                  {formatMessage({ id: '温馨提示' })}：
                  {formatMessage({
                    id:
                      '试算金额根据您选择账号的结算币种展示，此处所查结果仅做参考（点标识可查看费用组成项），最终结算金额以实际账单为准。',
                  })}
                </div>
              );
            }}
            pagination={false}
            onChange={this.handleTableChange}
          ></Table>
        </div>
      </>
    );
  }
}

export default FreightTrialOverseas;
