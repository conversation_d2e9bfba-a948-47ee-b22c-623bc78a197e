import React, { Component } from 'react';
import { router } from 'umi';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {
  AutoComplete,
  Button,
  Col,
  Descriptions,
  Input,
  Row,
  Select,
  Table,
  Tooltip,
  Popover,
  Space,
} from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import { connect } from 'dva';
import { Decimal } from 'decimal.js';
import loadPinYinInit from '@/utils/ChineseHelper';
import { formatMessage } from 'umi-plugin-react/locale';

// {formatMessage({id: '运价试算'})}-{formatMessage({id: '新添加'})}
const { Option } = Select;

@connect(({ freight, loading }) => ({
  freight,
  pageInitLoading:
    loading.effects['freight/getCountries'] || loading.effects['freight/getWarehouses'],
  getchargePriceListLoading: loading.effects['freight/chargePrice'],
}))
@Form.create()
class freightTrial extends Component {
  constructor(props) {
    super(props);
    this.state = {
      residences: [], //{formatMessage({id: '出发地'})}
      defaultValue: [], //{formatMessage({id: '默认值'})}

      dataSourceC: [], //{formatMessage({id: '目的国'})}json{formatMessage({id: '数据'})} {formatMessage({id: '显示'})}
      addressArr: [], //{formatMessage({id: '数据数组'})}
      addressList: [], //{formatMessage({id: '目的国'})}json{formatMessage({id: '数据'})}

      dataList: [], //table
      expandedRowKeys: [], //{formatMessage({id: '展开列'})}
      destinationCn: '',
      // total: 0,//
      // pageSize: 10,//
      // pagecurrent: 1,//

      loadingT: false, //
      searchLoading: false, //
      areaName: '',
      cityName: '',
      // currentAuthorityValue: null, // {formatMessage({id: '商户状态'})}
      dataOBJ: null, // {formatMessage({id: '查询条件'})}
      isShowAddTip: 'none', //{formatMessage({id: '是否展示目的地提示'})}
      showAddTip: '', //{formatMessage({id: '展示目的地提示内容'})}
      sortField: null, // 当前排序字段
      sortOrder: null, // 当前排序顺序
      originalDataList: [], // 原始未排序的表格数据
    };
  }

  componentDidMount() {
    const { dispatch, location, form } = this.props;
    const state = location?.state;
    dispatch({
      type: 'freight/getWarehouses',
      callback: res => {
        const residencesData = res.data.warehouses;
        // areaCode, warehouseCode
        let areaInfo = res.data.warehouses.find(
          item => item.warehouseCode === res.data?.pickWarehouseId
        );
        if (!areaInfo) {
          areaInfo = res.data?.warehouses?.[0];
        }
        residencesData.map(item => {
          item.value = item.warehouseCode;
          item.name = item.name.replace('燕文', '');
          item.label = `${item.name.replace('燕文', '')}/${loadPinYinInit.ConvertPinyin(
            item.name.replace('燕文', '')
          )}`;
        });

        this.setState({
          cityName: areaInfo.name,
        });
        // const newResidences = this.listuniq(residencesData);
        const defaultValue = areaInfo !== undefined ? areaInfo.warehouseCode : [];
        this.setState({
          residences: residencesData,
          defaultValue,
        });
      },
    });
    dispatch({
      type: 'freight/getCountries',
      callback: res => {
        if (res.success) {
          if (res.data.length > 0) {
            const arr = [];
            res.data.map(atem => {
              let b = `${atem.chinesename}/${atem.english2bit}/${atem.englishname}/${atem.chinesepinyin}`;
              arr.push(b);
            });
            this.setState(
              {
                addressList: res.data,
                addressArr: arr,
              },
              () => {
                if (state) {
                  form.setFieldsValue({
                    destination: this.state.addressList?.find(item => item?.id == state?.countryId)
                      ?.chinesename,
                  });
                }
              }
            );
          }
        }
      },
    });
    if (state) {
      setTimeout(() => {
        form.setFieldsValue({
          ...state,
          residence: state?.companyCode,
        });
        setTimeout(() => {
          this.handleSubmit();
          location.state = null;
        }, 500);
      }, 500);
    }
  }

  //json{formatMessage({id: '去重函数'})} {formatMessage({id: '改变数据格式'})}
  listuniq = residencesData => {
    let dataArray = [];
    for (let i = 0; i < residencesData.length; i++) {
      const tempObj = residencesData[i];
      const tempIndex = dataArray.findIndex(x => x.value === tempObj.areaCode);
      if (tempIndex === -1) {
        dataArray.push({
          value: tempObj.areaCode,
          label: tempObj.area,
          children: [
            {
              value: tempObj.warehouseCode,
              label: tempObj.name,
            },
          ],
        });
      } else {
        dataArray[tempIndex].children.push({
          value: tempObj.warehouseCode,
          label: tempObj.name,
        });
      }
    }
    return dataArray;
  };
  // --------- {formatMessage({id: '目的国数据筛选'})} ----
  onSearch = value => {
    let addressfllterArr = [];

    //1.{formatMessage({id: '首先按照中文'})}、{formatMessage({id: '二字码'})}、{formatMessage({id: '英文'})}、{formatMessage({id: '拼音'})}----{formatMessage({id: '来精确匹配'})}
    //2.{formatMessage({id: '匹配上放入数组'})}、{formatMessage({id: '匹配不上则按照中文'})}、{formatMessage({id: '二字码'})}、{formatMessage({id: '英文'})}、{formatMessage({id: '拼音来进行模糊匹配'})}（{formatMessage({id: '每次放入之前'})}，{formatMessage({id: '都判断当前数组中是否存在此条记录信息'})}）
    //3.{formatMessage({id: '每次文本变化后都要按照此规则进行匹配'})}

    if (value) {
      for (let i = 0; i < this.state.addressArr.length; i++) {
        let guojia = this.state.addressArr[i].split('/');
        for (let j = 0; j < guojia.length; j++) {
          if (guojia[j] === value) {
            addressfllterArr.push(this.state.addressArr[i]);
          }
        }
      }

      for (let i = 0; i < this.state.addressArr.length; i++) {
        let guojia = this.state.addressArr[i].split('/');
        if (
          guojia[0].indexOf(value) != -1 &&
          !addressfllterArr.includes(this.state.addressArr[i])
        ) {
          addressfllterArr.push(this.state.addressArr[i]);
        }
      }

      for (let i = 0; i < this.state.addressArr.length; i++) {
        let guojia = this.state.addressArr[i].split('/');
        if (
          guojia[1].indexOf(value) != -1 &&
          !addressfllterArr.includes(this.state.addressArr[i])
        ) {
          addressfllterArr.push(this.state.addressArr[i]);
        }
      }

      for (let i = 0; i < this.state.addressArr.length; i++) {
        let guojia = this.state.addressArr[i].split('/');
        if (
          guojia[2].indexOf(value) != -1 &&
          !addressfllterArr.includes(this.state.addressArr[i])
        ) {
          addressfllterArr.push(this.state.addressArr[i]);
        }
      }

      for (let i = 0; i < this.state.addressArr.length; i++) {
        let guojia = this.state.addressArr[i].split('/');
        if (
          guojia[3].indexOf(value) != -1 &&
          !addressfllterArr.includes(this.state.addressArr[i])
        ) {
          addressfllterArr.push(this.state.addressArr[i]);
        }
      }

      this.setState({
        dataSourceC: addressfllterArr,
      });
    } else {
      this.setState({
        dataSourceC: [],
      });
    }
  };
  onBlur = value => {
    if (this.state.dataSourceC.length <= 0) {
      this.props.form.setFieldsValue({
        destination: '',
      });

      this.setState({
        isShowAddTip: 'none',
        showAddTip: '',
      });
    }
  };

  onSelect = value => {
    if (value) {
      let countryCN = value.split('/')[0];
      this.props.form.setFieldsValue({
        destination: countryCN,
      });
      this.setState({
        destinationCn: countryCN,
      });
    }
  };

  //{formatMessage({id: '日本'})}、{formatMessage({id: '澳大利亚提示'})}
  onChange = value => {
    let chinese = value.split('/')[0];
    if (chinese === '日本') {
      this.setState({
        isShowAddTip: '',
        showAddTip: '燕文专线发往日本冲绳县地区有偏远附加费，请输入邮编以便试算出更准确的费用',
      });
    } else if (chinese === '澳大利亚') {
      this.setState({
        isShowAddTip: '',
        showAddTip: '燕文专线发往澳大利亚按邮编分区计费，需输入邮编进行试算',
      });
    } else {
      this.setState({
        isShowAddTip: 'none',
        showAddTip: '',
      });
    }
  };

  // ---------------------------------------
  // {formatMessage({id: '获取表格数据'})}
  getDataList = parameterList => {
    const { dispatch } = this.props;
    this.setState({
      loadingT: true,
      searchLoading: true,
    });
    dispatch({
      type: 'freight/chargePrice',
      payload: parameterList,
      callback: res => {
        if (res.success) {
          const tableDataList = res.data.result.items;
          const dataArr = [];
          tableDataList.map(item => {
            let moneynum = 0;
            let moneyOfOriginalnum = 0;
            item.expenseItems.map(m => {
              moneynum = new Decimal(moneynum).add(new Decimal(m.money));
              moneyOfOriginalnum = new Decimal(moneyOfOriginalnum).add(
                new Decimal(m.moneyOfOriginal)
              );
            });
            // 添加排序辅助字段
            const referAgingSortValue = (() => {
              if (item?.referAging === undefined || item?.referAging === null)
                return Number.MAX_SAFE_INTEGER; // 空值使用最大值

              const match = item.referAging.match(/(\d+)(?:-(\d+))?/);
              if (!match) return Number.MAX_SAFE_INTEGER;

              const min = parseInt(match[1], 10) || 0;
              const max = match[2] ? parseInt(match[2], 10) : min;
              return min * 1000 + max;
            })();

            let tabObj = {
              productname: item.productInfo.productName, //{formatMessage({id: '产品名称'})}
              rmbTotalMoney: item.rmbTotalMoney, //{formatMessage({id: '金额'})}
              trackingInfo: item.trackingInfo, //{formatMessage({id: '追踪等级'})}
              exceedLengthMoney: item.exceedLengthMoney, //{formatMessage({id: '超长费用'})}
              basicFreight: item.basicFreight, //{formatMessage({id: '基础资费'})}
              trunkFreight: item.trunkFreight, //{formatMessage({id: '干线费用'})}
              fuelCost: item.fuelCost, //{formatMessage({id: '燃油费'})}
              additionalCharge: item.additionalCharge, //{formatMessage({id: '附加费'})}
              referAging: item.referAging,
              referAgingSortValue: referAgingSortValue, // 新增的排序值字段
              productType: item.productInfo.ywproductGenealogy, //{formatMessage({id: '产品类型'})}
              hasTax: item.productInfo.hasTax, //{formatMessage({id: '是否包税'})}
              itemAttribute: item.productInfo.itemAttribute, //{formatMessage({id: '货品属性'})}
              isThrowweight: item.isThrowweight, //{formatMessage({id: '是否计泡'})}
              productShowOrder: item.productInfo.productShowOrder, //{formatMessage({id: '顺序'})}
              throwWeightFactor: item.throwWeightFactor, //{formatMessage({id: '计泡系数'})}
              calculateWeightFactor: item?.calculateWeightFactor, //{formatMessage({id: '计泡系数'})}
              calcWeight: item.calcWeight, //{formatMessage({id: '重量'})}
              moneyOfOriginal: moneyOfOriginalnum.toNumber().toFixed(2), // {formatMessage({id: '资费'})}（{formatMessage({id: '元'})}）
              money: moneynum.toNumber(), //{formatMessage({id: '折后价'})}（{formatMessage({id: '元'})}）
              discountRate: item.discountRate, // {formatMessage({id: '折扣值'})}
              productFuelRate: item.productFuelRate, //{formatMessage({id: '燃油费率'})}
              productcode: item.productInfo.productNumber, //id
              remark: item.remark,
              topcRemarksData: item.topcRemarks,
              recommendProduct: item?.recommendProduct,
            };
            dataArr.push(tabObj);
          });

          // 保存原始数据的副本
          this.setState({
            dataList: dataArr,
            originalDataList: [...dataArr], // 保存副本
            loadingT: false,
            searchLoading: false,
          });
        } else {
          this.setState({
            dataList: [],
            originalDataList: [], // 清空原始数据
            loadingT: false,
            searchLoading: false,
          });
        }
      },
    });
  };
  CascaderonChange = (value, item) => {
    if (item.length >= 2) {
      this.setState({
        areaName: item[0].label,
        cityName: item[1].label,
      });
    }
    if (item.length == 1) {
      this.setState({
        areaName: item[0].label,
        cityName: '',
      });
    }
  };
  // {formatMessage({id: '查询'})}
  handleSubmit = e => {
    e?.preventDefault();
    const { dataOBJ, residences } = this.state;
    this.props.form.validateFields((err, values) => {
      if (!err) {
        let productAttributes;
        let productTypes;
        if (values.productAttributes != undefined) {
          productAttributes = values.productAttributes.toString();
        } else {
          productAttributes = '';
        }
        if (values.productTypes != undefined) {
          productTypes = values.productTypes.toString();
        } else {
          productTypes = '';
        }
        let cityInfo = values;
        let countryIdStr;
        this.state.addressList.map(item => {
          if (item.chinesename === cityInfo.destination) {
            countryIdStr = item.id;
          }
        });

        const parameterList = {
          // areaCode: cityInfo.residence[0],
          // areaName: this.state.areaName,
          cityId: cityInfo.residence,
          cityName: residences?.find(item => item.value === cityInfo.residence)?.name,
          countryId: countryIdStr,
          destination: cityInfo.destination,
          hasTax: cityInfo.hasTax,
          productAttributes: productAttributes,
          productTypes: productTypes,
          weight: cityInfo.weight,
          high: cityInfo.high,
          length: cityInfo.length,
          width: cityInfo.width,
          postCode: cityInfo.postCode,
        };
        this.setState({
          dataOBJ: parameterList,
        });
        this.getDataList(parameterList);
        // // if((cityInfo.length == "" && cityInfo.high == "" && cityInfo.weight == "") || (cityInfo.length && cityInfo.high && cityInfo.weight)) {
        // //     // alert("222")

        // // } else {
        // //     message.error("{formatMessage({id: '长宽高请同时存在'})}")
        // // }
      }
    });
  };
  // {formatMessage({id: '重置'})}
  resetFields = () => {
    this.props.form.resetFields();
    this.setState({ defaultValue: [] });
  };

  // -------- {formatMessage({id: '表格张开渲染'})}-------
  expand = (record, index, indent, expanded) => {
    const ti = (
      <div style={{ marginLeft: '10px', fontSize: '14px' }}>
        {formatMessage({ id: '产品说明' })}：
      </div>
    );

    let obj = record.remark;
    Reflect.ownKeys(obj).forEach(function(key) {
      if (obj[key] == null || obj[key] == '' || obj[key] == undefined) {
      } else {
        obj[key] = obj[key]
          .toString()
          .replace(/\n/g, '<br/>')
          .replace(/\（/g, '(')
          .replace(/\）/g, ')');
      }
    });
    return (
      <div className="b_c_y">
        <Descriptions column={1} bordered title={ti}>
          <Descriptions.Item label={`1.${formatMessage({ id: '参考时效' })}`}>
            <div dangerouslySetInnerHTML={{ __html: record.remark.referAging }}></div>
          </Descriptions.Item>
          <Descriptions.Item label={`2.${formatMessage({ id: '价格构成' })}`}>
            <div dangerouslySetInnerHTML={{ __html: record.remark.priceStructure }}></div>
          </Descriptions.Item>
          <Descriptions.Item label={`3.${formatMessage({ id: '计费方式' })}`}>
            <div dangerouslySetInnerHTML={{ __html: record.remark.billingWay }}></div>
          </Descriptions.Item>
          <Descriptions.Item label={`4.${formatMessage({ id: '走货属性' })}`}>
            <div dangerouslySetInnerHTML={{ __html: record.remark.goodsAttribute }}></div>
          </Descriptions.Item>
          <Descriptions.Item label={`5.${formatMessage({ id: '申报价值' })}`}>
            <div dangerouslySetInnerHTML={{ __html: record.remark.declaredValue }}></div>
          </Descriptions.Item>
          <Descriptions.Item label={`6.${formatMessage({ id: '重量要求' })}`}>
            <div dangerouslySetInnerHTML={{ __html: obj.weightRequirement }} />
          </Descriptions.Item>
          <Descriptions.Item label={`7.${formatMessage({ id: '包装尺寸' })}`}>
            <div
              style={{ color: record.remark.productCode == '801' ? 'red' : '' }}
              dangerouslySetInnerHTML={{ __html: record.remark.packingSize }}
            ></div>
          </Descriptions.Item>
          <Descriptions.Item label={`8.${formatMessage({ id: '派送地址要求' })}`}>
            <div dangerouslySetInnerHTML={{ __html: record.remark.deliveryRequirements }}></div>
          </Descriptions.Item>
          <Descriptions.Item label={`9.${formatMessage({ id: '退件重派' })}`}>
            <div dangerouslySetInnerHTML={{ __html: record.remark.toResend }}></div>
          </Descriptions.Item>
          <Descriptions.Item label={`10.${formatMessage({ id: '保险服务' })}`}>
            <div dangerouslySetInnerHTML={{ __html: record.remark.insurance }}></div>
          </Descriptions.Item>
          <Descriptions.Item label={`11.${formatMessage({ id: '赔偿标准' })}`}>
            <div dangerouslySetInnerHTML={{ __html: record.remark.compensation }}></div>
          </Descriptions.Item>
          <Descriptions.Item label={`12.${formatMessage({ id: '查询网址' })}`}>
            <div dangerouslySetInnerHTML={{ __html: record.remark.queryUrl }}></div>
          </Descriptions.Item>
          <Descriptions.Item label={`13.${formatMessage({ id: '其他要求' })}`}>
            {record.remark.otherRequirements == null ? (
              formatMessage({ id: '暂无' })
            ) : (
              <div dangerouslySetInnerHTML={{ __html: record.remark.otherRequirements }}></div>
            )}
          </Descriptions.Item>
        </Descriptions>
        {record.topcRemarksData.length > 0 ? (
          <Table
            size="small"
            className="datles_table"
            bordered
            rowKey={(_, index) => index}
            style={{ background: '#fff' }}
            columns={this.datilsCol}
            dataSource={record.topcRemarksData}
            pagination={false}
          ></Table>
        ) : null}
      </div>
    );
  };

  getDestinationCn = () => {
    console.log(this.state);
    return '';
  };

  datilsCol = [
    {
      title: '国家',
      dataIndex: 'countryName',
      key: 'countryName',
      width: '80px',
    },
    {
      title: '参考时效 (自然日)',
      dataIndex: 'referAging',
      key: 'referAging',
      width: '160px',
    },
    {
      title: '特殊要求',
      dataIndex: 'specialRequirement',
      key: 'specialRequirement',
    },
    {
      title: '申报价值',
      dataIndex: 'declaredValue',
      key: 'declaredValue',
      width: '360px',
    },
    {
      title: '税费征收说明',
      dataIndex: 'rateDesc',
      key: 'rateDesc',
      width: '160px',
    },
    {
      title: '重量要求',
      dataIndex: 'weightRequirement',
      key: 'weightRequirement',
      width: '100px',
    },
    {
      title: '包装尺寸',
      dataIndex: 'packingSize',
      key: 'packingSize',
      width: '170px',
    },
    {
      title: '派送地址要求',
      dataIndex: 'deliveryRequirement',
      key: 'deliveryRequirement',
      width: '160px',
    },
    {
      title: '退件重派',
      dataIndex: 'toResend',
      key: 'toResend',
      width: '160px',
    },
  ];

  // {formatMessage({id: '分页'})} - -----------------------
  // changePageSize = (pageSize, current) => {
  //     this.setState({
  //         pageSize
  //     })
  // }
  // changePage = () => {}
  ichange = e => {
    // {formatMessage({id: '限制只能输入数字'})}
    e.target.value = e.target.value.replace(/\D/g, '');
  };

  // {formatMessage({id: '点击创建订单'})}
  clickCreatOrder = record => {
    const { dataOBJ } = this.state;
    let searchConditions = {};
    searchConditions.productcode = record.productcode;
    searchConditions.cityName = dataOBJ.cityName ? dataOBJ.cityName : '北京';
    searchConditions.cityId = dataOBJ.cityId ? dataOBJ.cityId : '01';
    searchConditions.countryId = dataOBJ.countryId;
    searchConditions.weight = dataOBJ.weight;
    searchConditions.heigh = dataOBJ.high;
    searchConditions.length = dataOBJ.length;
    searchConditions.width = dataOBJ.width;
    searchConditions.postCode = dataOBJ.postCode;
    router.push(
      `/smallBag/orderManagement/creatOrder?isFreightTrial=${JSON.stringify(searchConditions)}`
    );
  };

  // 修改 handleTableChange 方法
  handleTableChange = (pagination, filters, sorter) => {
    const { originalDataList } = this.state;

    // 保存排序状态
    this.setState({
      sortField: sorter.field || null,
      sortOrder: sorter.order || null,
    });

    // 如果没有排序或取消了排序
    if (!sorter.field || !sorter.order) {
      // 恢复到原始数据顺序
      this.setState({ dataList: [...originalDataList] });
      return;
    }

    // 有排序时，以原始数据为基础进行排序
    let sortedData = [...originalDataList];

    if (sorter.field === 'referAging') {
      sortedData.sort((a, b) => {
        // 解析函数
        const parseTime = timeStr => {
          if (!timeStr) return { valid: false, value: Number.MAX_SAFE_INTEGER };

          const match = timeStr.match(/(\d+)(?:-(\d+))?/);
          if (!match) return { valid: false, value: Number.MAX_SAFE_INTEGER };

          const min = parseInt(match[1], 10) || 0;
          const max = match[2] ? parseInt(match[2], 10) : min;
          return { valid: true, value: min * 1000 + max };
        };

        const aTime = parseTime(a?.referAging);
        const bTime = parseTime(b?.referAging);

        // 空值始终排在最后
        if (!aTime.valid && !bTime.valid) return 0;
        if (!aTime.valid) return 1;
        if (!bTime.valid) return -1;

        // 根据排序方向
        return sorter.order === 'ascend' ? aTime.value - bTime.value : bTime.value - aTime.value;
      });
    } else {
      // 其他字段使用默认排序逻辑
      sortedData.sort((a, b) => {
        const aValue = a[sorter.field];
        const bValue = b[sorter.field];

        // 处理空值
        if (aValue === undefined && bValue === undefined) return 0;
        if (aValue === undefined) return 1;
        if (bValue === undefined) return -1;

        // 升序或降序
        return sorter.order === 'ascend' ? (aValue < bValue ? -1 : 1) : aValue > bValue ? -1 : 1;
      });
    }

    // 更新排序后的数据
    this.setState({ dataList: sortedData });
  };

  // -------------------。
  render() {
    const {
      dataList,
      expandedRowKeys,
      total,
      pageSize,
      pagecurrent,
      loadingT,
      searchLoading,
      residences,
      defaultValue,
      destinationCn,
    } = this.state;
    const { pageInitLoading, form } = this.props;
    const { getFieldDecorator } = form;
    const formItemLayout = {
      labelCol: {
        xs: { span: 4 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 19 },
        sm: { span: 19 },
      },
    };
    const itemCol = {
      span: 10,
    };

    const columns = [
      {
        title: (
          <span>
            产品名称<span style={{ fontSize: '12px', color: '#bfbfbf' }}>（点击查看详情）</span>
          </span>
        ),
        dataIndex: 'productname',
        key: 'productname',
        sorter: (a, b) => {
          var sort = a.productShowOrder - b.productShowOrder;
          if (sort == 0) {
            return a.rmbTotalMoney - b.rmbTotalMoney;
          }
          return sort;
        },
        render: (text, record, index) => {
          return (
            <div>
              {record.remark ? (
                <>
                  {record?.recommendProduct === 1 ? (
                    <span
                      style={{
                        color: '#ffffff',
                        background: 'rgb(247,54,54)',
                        fontSize: '8px',
                        padding: '2px 10px',
                        borderRadius: '20px',
                        marginRight: '10px',
                      }}
                    >
                      推荐
                    </span>
                  ) : null}

                  <a
                    onClick={() => {
                      const { expandedRowKeys } = this.state;
                      const indexRow = expandedRowKeys.indexOf(index);
                      let keys = [...expandedRowKeys];
                      if (indexRow > -1) keys = keys.filter(key => key !== index);
                      else keys.push(index);
                      this.setState({
                        expandedRowKeys: keys,
                      });
                    }}
                  >
                    {text}
                  </a>
                </>
              ) : (
                <span>{text}</span>
              )}
            </div>
          );
        },
      },
      {
        title: '是否包税',
        dataIndex: 'hasTax',
        key: 'hasTax',
        hideItem: destinationCn !== '美国',
        render: (text, record) => {
          return record.hasTax ? '是' : '否';
        },
      },
      {
        title: '跟踪类型',
        dataIndex: 'trackingInfo',
        key: 'trackingInfo',
        sorter: (a, b) => a?.trackingInfo?.localeCompare(b?.trackingInfo),
      },
      {
        title: '参考时效(天)',
        dataIndex: 'referAging',
        key: 'referAging',
        sorter: (a, b) => a.referAgingSortValue - b.referAgingSortValue,
      },
      {
        title: '产品类型',
        dataIndex: 'productType',
        key: 'productType',
        sorter: (a, b) => a.productType - b.productType,
      },
      {
        title: '货品属性',
        dataIndex: 'itemAttribute',
        key: 'itemAttribute',
      },
      {
        title: '计泡系数',
        dataIndex: 'throwWeightFactor',
        key: 'throwWeightFactor',
        render: (text, record) => {
          return record.isThrowweight ? text : '';
        },
      },
      {
        title: '计费重量（克）',
        dataIndex: 'calcWeight',
        key: 'calcWeight',
        render: text => {
          const {
            form: { getFieldsValue },
          } = this.props;
          const { weight } = getFieldsValue();
          return <span style={{ color: weight != text ? 'red' : '' }}>{text}</span>;
        },
      },
      {
        title: '燃油费率',
        dataIndex: 'productFuelRate',
        key: 'productFuelRate',
      },
      {
        title: '资费（元）',
        dataIndex: 'money',
        key: 'money',
        sorter: (a, b) => a.money - b.money,
        render: (text, record) => {
          if (text) {
            let finalText;
            if (text.toString().indexOf('.') === -1) {
              finalText = text;
            } else {
              finalText = Math.round((text + Number.EPSILON) * 100) / 100;
            }
            return (
              <Popover
                content={
                  <>
                    {record.basicFreight != 0 && (
                      <div>
                        基础费: <span style={{ color: '#73d13d' }}>{record.basicFreight}</span> 元
                      </div>
                    )}
                    {record.trunkFreight != 0 && (
                      <div>
                        干线费: <span style={{ color: '#73d13d' }}>{record.trunkFreight}</span> 元
                      </div>
                    )}
                    {record.fuelCost != 0 && (
                      <div>
                        燃油费: <span style={{ color: '#73d13d' }}>{record.fuelCost}</span> 元
                      </div>
                    )}
                    {record.additionalCharge != 0 && (
                      <div>
                        附加费: <span style={{ color: '#73d13d' }}>{record.additionalCharge}</span>{' '}
                        元
                      </div>
                    )}
                  </>
                }
                trigger="hover"
              >
                <Space size={5}>
                  <span>{finalText}</span>
                  {record.basicFreight != 0 &&
                    (record.trunkFreight != 0 ||
                      record.additionalCharge != 0 ||
                      record.fuelCost != 0) && (
                      <InfoCircleOutlined style={{ color: '#df8909', fontSize: '12px' }} />
                    )}
                </Space>
              </Popover>
            );
          }
        },
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        render: (text, record) => {
          return (
            <Button
              type="link"
              onClick={() => {
                this.clickCreatOrder(record);
              }}
            >
              创建订单
            </Button>
          );
        },
      },
    ];
    // const paginationProps = {
    //     // https://blog.csdn.net/luzhaopan/article/details/84996507({formatMessage({id: '分页详细'})})
    //     total: total,
    //     pageSize: pageSize,
    //     showTotal: () => `{formatMessage({id: '共'})} ${total} {formatMessage({id: '条'})}`,
    //     style: {textAlign: "center", display: "block", width: "100%"},

    //     current: pagecurrent,
    //     showSizeChanger: true,
    //     showQuickJumper: true,
    //     onShowSizeChange: (current, pageSize) => this.changePageSize(pageSize, current),
    //     onChange: (current) => this.changePage(current),
    // };
    return (
      <>
        {/*------------------------------------------------*/}
        <div style={{ background: '#fff', paddingTop: '10px' }}>
          <Form {...formItemLayout} className="mb-3" onSubmit={this.handleSubmit}>
            <Row>
              <Col {...itemCol}>
                <Form.Item label={formatMessage({ id: '出发地' })} style={{ marginBottom: 12 }}>
                  {getFieldDecorator('residence', {
                    initialValue: defaultValue,
                    rules: [{ required: true, message: formatMessage({ id: '请选择出发地' }) }],
                  })(
                    // <Cascader
                    //   className="cascader_w"
                    //   onChange={this.CascaderonChange}
                    //   options={residences}
                    //   // onPopupVisibleChange={val => {
                    //   //   if (residences.length > 0) {
                    //   //     this.setState({ defaultValue: [residences[0].value] });
                    //   //   }
                    //   // }}
                    // />
                    <Select
                      showSearch
                      allowClear
                      options={residences}
                      optionFilterProp="children"
                      filterOption={(input, option) => (option?.label ?? '').includes(input)}
                    />
                  )}
                </Form.Item>
                <Form.Item label={formatMessage({ id: '货品属性' })} style={{ marginBottom: 12 }}>
                  {getFieldDecorator('productAttributes', {
                    initialValue: [],
                  })(
                    <Select showArrow allowClear placeholder={formatMessage({ id: '请选择' })}>
                      <Option value="1">{formatMessage({ id: '普货' })}</Option>
                      <Option value="2">{formatMessage({ id: '特货' })}</Option>
                      <Option value="3">{formatMessage({ id: '敏感货' })}</Option>
                      {/* <Option value="4">{formatMessage({ id: '特品' })}</Option> */}
                    </Select>
                  )}
                </Form.Item>
                <Form.Item label={formatMessage({ id: '实际重量' })} style={{ marginBottom: 12 }}>
                  {getFieldDecorator('weight', {
                    rules: [{ required: true, message: formatMessage({ id: '请填写重量' }) }],
                  })(
                    <Input
                      onChange={e => this.ichange(e)}
                      maxLength={10}
                      suffix={formatMessage({ id: '克' })}
                    />
                  )}
                </Form.Item>
                <Form.Item label={formatMessage({ id: '邮编' })} style={{ marginBottom: 12 }}>
                  {getFieldDecorator(
                    'postCode',
                    {}
                  )(<Input placeholder={formatMessage({ id: '请输入' })} />)}
                </Form.Item>
              </Col>
              <Col {...itemCol}>
                <Form.Item label={formatMessage({ id: '目的地' })} style={{ marginBottom: 12 }}>
                  {getFieldDecorator('destination', {
                    rules: [{ required: true, message: formatMessage({ id: '请填写目的地' }) }],
                  })(
                    <AutoComplete
                      dataSource={this.state.dataSourceC}
                      onSearch={this.onSearch}
                      onSelect={this.onSelect}
                      onBlur={this.onBlur}
                      onChange={this.onChange}
                    ></AutoComplete>
                  )}
                  <span style={{ color: 'red', display: this.state.isShowAddTip }}>
                    {this.state.showAddTip}
                  </span>
                </Form.Item>
                <Form.Item label={formatMessage({ id: '产品类型' })} style={{ marginBottom: 12 }}>
                  {getFieldDecorator('productTypes', {
                    initialValue: [],
                  })(
                    <Select
                      mode="multiple"
                      showArrow
                      allowClear
                      placeholder={formatMessage({ id: '请选择' })}
                    >
                      <Option value="3">{formatMessage({ id: '燕文专线' })}</Option>
                      <Option value="2">{formatMessage({ id: '燕文挂号' })}</Option>
                      <Option value="1">{formatMessage({ id: '燕文经济' })}</Option>
                      <Option value="26">{formatMessage({ id: '中邮挂号' })}</Option>
                      <Option value="19">{formatMessage({ id: '外邮产品' })}</Option>
                      <Option value="17">{formatMessage({ id: '商业快递' })}</Option>
                    </Select>
                  )}
                </Form.Item>

                <Form.Item
                  label={`${formatMessage({ id: '尺寸' })}(cm)`}
                  style={{ marginBottom: 12 }}
                >
                  <span style={{ marginRight: '5px' }}>{formatMessage({ id: '长' })}</span>
                  {getFieldDecorator('length', { initialValue: '' })(
                    <Input
                      style={{ width: '100px' }}
                      onChange={e => this.ichange(e)}
                      maxLength={3}
                    />
                  )}
                  <span style={{ margin: '0 5px' }}>{formatMessage({ id: '宽' })}</span>
                  {getFieldDecorator('width', { initialValue: '' })(
                    <Input
                      style={{ width: '100px' }}
                      onChange={e => this.ichange(e)}
                      maxLength={3}
                    />
                  )}
                  <span style={{ margin: '0 5px' }}>{formatMessage({ id: '高' })}</span>
                  {getFieldDecorator('high', { initialValue: '' })(
                    <Input
                      style={{ width: '100px' }}
                      onChange={e => this.ichange(e)}
                      maxLength={3}
                    />
                  )}
                </Form.Item>
                {destinationCn === '美国' && (
                  <Form.Item label={formatMessage({ id: '是否包税' })} style={{ marginBottom: 12 }}>
                    {getFieldDecorator(
                      'hasTax',
                      {}
                    )(
                      <Select showArrow allowClear placeholder={formatMessage({ id: '请选择' })}>
                        <Option value={1}>{formatMessage({ id: '是' })}</Option>
                        <Option value={0}>{formatMessage({ id: '否' })}</Option>
                      </Select>
                    )}
                  </Form.Item>
                )}
              </Col>
              <Col span={4} style={{ textAlign: 'center' }}>
                <Form.Item style={{ marginBottom: 12 }}>
                  <Button type="primary" loading={searchLoading} htmlType="submit">
                    {formatMessage({ id: '查询' })}
                  </Button>
                </Form.Item>
                <Form.Item style={{ marginBottom: 12 }}>
                  <Button type="primary" onClick={this.resetFields}>
                    {formatMessage({ id: '重置' })}
                  </Button>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>
        {/*-------------------- // expandIconAsCell: false, //{formatMessage({id: '隐藏'})}+
              // expandIconColumnIndex: -1, //+-------------------------------*/}
        <div style={{ marginTop: '16px', padding: '10px', background: '#fff' }}>
          <Table
            columns={columns
              .filter(item => !item?.hideItem)
              .map(col => ({
                ...col,
                // 保留排序UI但不实际排序
                sorter: col.key === 'referAging' ? true : col.sorter,
              }))}
            bordered
            className="t_s"
            size="small"
            style={{ minHeight: '362px' }}
            dataSource={dataList}
            rowKey={(record, index) => index}
            loading={loadingT}
            expandable={{
              onExpand: () => {},
              expandedRowKeys: expandedRowKeys, //{formatMessage({id: '控制展开列'})}
              // expandIconAsCell: false,
              expandIconColumnIndex: -1,
              expandedRowRender: (record, index, indent, expanded) =>
                this.expand(record, index, indent, expanded), //{formatMessage({id: '自定义展开内容'})}
            }}
            title={() => {
              return (
                <div>
                  {formatMessage({ id: '温馨提示' })}：
                  {formatMessage({ id: '此处所查结果仅做参考' })}（点标识可查看费用组成项），
                  {formatMessage({ id: '且所有产品不包含税费' })}，
                  <span style={{ color: 'rgb(247,54,54)' }}>
                    {formatMessage({ id: '最终运费以实际发生费用为准' })}
                  </span>
                  ，{formatMessage({ id: '为保障试算结果更准确' })}，
                  {formatMessage({ id: '请结合如下信息进行试算' })}。
                  <br />①{formatMessage({ id: '专线' })}/
                  {formatMessage({ id: '快递类产品建议输入邮编查询' })}。 ②
                  {formatMessage({ id: '若包裹体积超大请输入尺寸查询' })}，
                  <span style={{ color: 'rgb(247,54,54)' }}>
                    {formatMessage({ id: '计费重量标红则表示已计泡' })}
                  </span>
                  。 ③{formatMessage({ id: '仅商业快递产品可通达' })}FBA(
                  {formatMessage({ id: '海外仓' })}){formatMessage({ id: '仓库地址' })}。
                </div>
              );
            }}
            pagination={false}
            onChange={this.handleTableChange}
          ></Table>
        </div>
      </>
    );
  }
}

export default freightTrial;
