import React from 'react';
import PageContainerComponent from '@/components/PageContainer';
import PageHeaderBreadcrumb from '@/components/PageHeaderBreadcrumb';
import { message, Tabs } from 'antd';
import FreightTrial from './components/FreightTrial';
import FreightTrialFBA from './components/FreightTrialFBA';
import FreightTrialBus from './components/FreightTrialBus';
import FreightTrialOverseas from './components/FreightTrialOverseas';
import { isAuth } from '@/utils/utils';
import { formatMessage } from 'umi-plugin-react/locale';

class Index extends React.Component {
  constructor(props) {
    super(props);
    if (isAuth('smallPackage')) {
      this.state = {
        activeKey: '1',
      };
      return;
    }
    if (isAuth('fbaOrder')) {
      this.state = {
        activeKey: '2',
      };
      return;
    }
    if (isAuth('overseas')) {
      this.state = {
        activeKey: '4',
      };
      return;
    }
    message.error('请至少开通小包、FBA、海外派业务之一再进行试算');
    this.state = {
      activeKey: '0',
    };
  }

  componentDidMount() {
    this.initialFunc();
  }

  initialFunc = () => {
    const { location } = this.props;
    const state = location?.state;
    if (!state?.type) return;
    this.setState({
      activeKey: state?.type,
    });
  };

  render() {
    const { activeKey } = this.state;

    return (
      <PageContainerComponent
        header={{
          title: null,
          breadcrumb: {},
          breadcrumbRender: props => (
            <PageHeaderBreadcrumb {...props} col>
              <Tabs
                activeKey={activeKey}
                onChange={key =>
                  this.setState({
                    activeKey: key,
                  })
                }
                type="line"
              >
                {isAuth('smallPackage') && (
                  <Tabs.TabPane tab={formatMessage({ id: `小包运价试算` })} key={'1'} />
                )}
                {isAuth('fbaOrder') && (
                  <Tabs.TabPane tab={`${formatMessage({ id: 'FBA运价试算' })}`} key={'2'} />
                )}
                {isAuth('smallPackage') && (
                  <Tabs.TabPane tab={formatMessage({ id: `商业快递运价试算` })} key={'3'} />
                )}
                {isAuth('overseas') && (
                  <Tabs.TabPane tab={formatMessage({ id: `海外派运价试算` })} key={'4'} />
                )}
              </Tabs>
            </PageHeaderBreadcrumb>
          ),
        }}
      >
        {activeKey === '1' && <FreightTrial {...this.props} />}
        {activeKey === '2' && <FreightTrialFBA {...this.props} />}
        {activeKey === '3' && <FreightTrialBus {...this.props} />}
        {activeKey === '4' && <FreightTrialOverseas {...this.props} />}
      </PageContainerComponent>
    );
  }
}

export default Index;
