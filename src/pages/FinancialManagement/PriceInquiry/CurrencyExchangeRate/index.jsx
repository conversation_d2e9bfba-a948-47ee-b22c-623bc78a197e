import PageHeaderBreadcrumb from '@/components/PageHeaderBreadcrumb';
import PageContainerComponent from '@/components/PageContainer';
import React, { useEffect, useRef, useState } from 'react';
import {
  ProCard,
  ProForm,
  ProFormDatePicker,
  ProFormDateRangePicker,
  ProFormSelect,
  ProTable,
} from '@ant-design/pro-components';
import { onFormErrorMessage } from '@/utils/utils';
import { Button, Col, message, Row, Space, Typography } from 'antd';
import { formatMessage } from 'umi-plugin-react/locale';
import { renderSelectOptionsMulti } from '@/utils/commonConstant';
import { connect } from 'dva';
import moment from 'moment';
const CurrencyExchangeRate = props => {
  const formRef = useRef();
  const { dispatch, queryLoading } = props;
  const [currencyList, setCurrencyList] = useState([]);
  const [dataSource, setDataSource] = useState([]);

  useEffect(() => {
    getCurrency();
  }, []);

  function handleSubmit(values) {
    try {
      let params = {
        ...values,
      };
      params.dateTime = values.dateTime + ' 12:00:00';
      dispatch({
        type: 'common/getCurrencyRateList',
        payload: params,
        callback: response => {
          if (response.success) {
            setDataSource(response.data);
          }
        },
      });
    } catch (error) {
      console.log(error);
    }
  }

  const getCurrency = () => {
    dispatch({
      type: 'Order/getCurrency',
      callback: response => {
        if (response.success) {
          setCurrencyList(
            renderSelectOptionsMulti(response.data, { label: ['name', 'code'], value: 'code' })
          );
        }
      },
    });
  };

  const handleReset = () => {
    // @ts-ignore
    formRef.current?.resetFields();
  };

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      valueType: 'index',
      width: 80,
    },
    {
      title: '源币种',
      dataIndex: 'orgCurrencyName',
    },
    {
      title: '目标币种',
      dataIndex: 'targetCurrencyName',
    },
    {
      title: '现汇卖出价',
      dataIndex: 'sellingRate',
    },
    {
      title: '生效时间',
      dataIndex: 'beginTime',
    },
  ];

  return (
    <>
      <PageContainerComponent
        header={{
          title: null,
          breadcrumb: {},
          breadcrumbRender: props => <PageHeaderBreadcrumb {...props} col></PageHeaderBreadcrumb>,
        }}
      >
        <ProCard>
          <ProForm
            formRef={formRef}
            labelAlign="left"
            layout="horizontal"
            onFinish={handleSubmit}
            submitter={{
              render: () => {
                return [];
              },
            }}
          >
            <Row gutter={15}>
              <Col>
                <ProFormSelect
                  name="orgCurrency"
                  label={`源币种`}
                  fieldProps={{
                    style: { width: '260px' },
                  }}
                  options={currencyList}
                  placeholder={'请选择源币种'}
                  rules={[{ required: true, message: `请选择源币种` }]}
                />
              </Col>
              <Col>
                <ProFormSelect
                  name="targetCurrency"
                  label={`目标币种`}
                  placeholder={'请选择目标币种'}
                  fieldProps={{
                    style: { width: '260px' },
                  }}
                  options={currencyList}
                  rules={[{ required: true, message: `请选择目标币种` }]}
                />
              </Col>
              <Col>
                <ProFormDatePicker
                  name="dateTime"
                  label={`生效时间`}
                  fieldProps={{
                    style: { width: '260px' },
                  }}
                  placeholder={[`生效时间`]}
                  rules={[
                    {
                      required: true,
                      message: '请选择生效时间',
                    },
                  ]}
                />
              </Col>
              <Col>
                <Space>
                  <Button
                    style={{ marginRight: '18px' }}
                    type="primary"
                    htmlType="submit"
                    loading={queryLoading}
                  >
                    {formatMessage({ id: '查询' })}
                  </Button>
                  <Button onClick={handleReset}>{formatMessage({ id: '重置' })}</Button>
                </Space>
              </Col>
            </Row>
          </ProForm>
        </ProCard>
        <ProCard
          style={{ marginTop: 16 }}
          title={
            <Typography.Text type="danger" style={{ fontSize: '14px' }}>
              温馨提示：数据来源于中国银行，汇率查询数据仅供参考！
            </Typography.Text>
          }
        >
          <ProTable
            toolBarRender={false}
            search={false}
            columns={columns}
            dataSource={dataSource}
            rowSelection={false}
            bordered={true}
            size={'large'}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              pageSizeOptions: ['10', '20', '50', '100'],
              showTotal: total => `共 ${total} 条`,
            }}
          />
        </ProCard>
      </PageContainerComponent>
    </>
  );
};
export default connect(({ loading }) => ({
  queryLoading: loading.effects['common/getCurrencyRateList'], // 获取客户信息
}))(CurrencyExchangeRate);
