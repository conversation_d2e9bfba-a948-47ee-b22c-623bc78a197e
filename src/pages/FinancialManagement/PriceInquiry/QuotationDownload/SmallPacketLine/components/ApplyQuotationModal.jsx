import React, { useRef } from 'react';
import { Button, message, Modal, Row } from 'antd';
import {
  ProForm,
  ProFormDatePicker,
  ProFormGroup,
  ProFormRadio,
  ProFormSelect,
} from '@ant-design/pro-components';
import { renderSelectOptions } from '@/utils/commonConstant';

const ApplyQuotationModal = ({
  dispatch,
  isOpen,
  onCancel,
  platforms,
  warehouses,
  onSearch,
  applyLoading,
}) => {
  const formRef = useRef();
  const handleSubmit = values => {
    dispatch({
      type: 'freight/applyQuotation',
      payload: values,
      callback: result => {
        if (result.success) {
          message.info('操作成功');
          onSearch();
          onCancel();
        }
      },
    });
  };

  return (
    <Modal open={isOpen} onCancel={onCancel} title={'申请报价单'} footer={''}>
      <ProForm
        layout="horizontal"
        labelAlign="left"
        labelCol={{ flex: '75px' }}
        formRef={formRef}
        onFinish={handleSubmit}
        submitter={{
          render: (_, dom) => null,
        }}
      >
        <ProFormGroup>
          <ProFormSelect
            width="sm"
            label="交货仓"
            name="companyFrom"
            showSearch
            placeholder="请选择交货仓"
            rules={[{ required: true, message: '请选择交货仓' }]}
            options={warehouses}
          />
          <ProFormRadio.Group
            name="platformType"
            label="平台类型"
            radioType="button"
            rules={[{ required: true, message: '请选择平台' }]}
            options={platforms}
            fieldProps={{
              buttonStyle: 'solid',
            }}
          />
          <ProFormDatePicker
            fieldProps={{
              style: { width: '100%' },
            }}
            rules={[{ required: true, message: '请选择生效时间' }]}
            name="effectTime"
            label="生效时间"
            placeholder={'请选择生效时间'}
          />
        </ProFormGroup>
        <Row justify="end">
          <Button type="primary" htmlType="submit" loading={applyLoading}>
            {'提交'}
          </Button>
        </Row>
      </ProForm>
    </Modal>
  );
};

export default ApplyQuotationModal;
