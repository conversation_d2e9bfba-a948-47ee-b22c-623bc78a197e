import React, { Component } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Table, Card, DatePicker, Button, message, Badge, Row, Col, Alert, Tooltip } from 'antd';
import styles from './components/index.less';
import StandardFormRow from '@/components/StandardFormRow';
import TagSelect from '@/components/TagSelect';
import { connect } from 'dva';
import moment from 'moment';
import AuthNode from '@/components/AuthNode';
import ApplyQuotationModal from '@/pages/FinancialManagement/PriceInquiry/QuotationDownload/SmallPacketLine/components/ApplyQuotationModal';
import { renderSelectOptions } from '@/utils/commonConstant';
import { formatMessage } from 'umi-plugin-react/locale';

const FormItem = Form.Item;

@connect(({ freight, loading }) => ({
  freight,
  applyLoading: loading.effects['freight/applyQuotation'],
}))
@Form.create()
class SamllQuotationDownload extends Component {
  constructor(props) {
    super(props);
    this.state = {
      account: [],
      shipperInitOpt: [],
      typeArray: [],
      typeValueArray: [],
      pageSize: 10,
      current: 1,
      freightData: [],
      total: 0,
      loadingBtn: false,
      showModal: false,
    };
  }

  // {formatMessage({id: '表头'})}
  columns = [
    {
      title: formatMessage({ id: '序号' }),
      dataIndex: 'number',
      key: 'number',
      render: (text, row, index) => {
        return index + 1;
      },
    },
    {
      title: formatMessage({ id: '揽收仓' }),
      dataIndex: 'warehouseName',
      key: 'warehouseName',
    },
    {
      title: formatMessage({ id: '报价单类型' }),
      dataIndex: 'platformTypeName',
      key: 'platformTypeName',
    },
    {
      title: formatMessage({ id: '生效时间' }),
      dataIndex: 'effectTime',
      key: 'effectTime',
    },
    {
      title: formatMessage({ id: '创建时间' }),
      dataIndex: 'createTime',
      key: 'createTime',
    },
    {
      title: formatMessage({ id: '文件状态' }),
      dataIndex: 'fileState',
      key: 'fileState',
      render: (text, record) => {
        if (text == 0) {
          return formatMessage({ id: '报价单生成中' });
        } else if (text == 1) {
          return formatMessage({ id: '报价单已生成' });
        } else if (text == 2) {
          return (
            <Tooltip
              title={`${formatMessage({ id: '生成报价单失败' })}，${formatMessage({
                id: '请联系客服',
              })}/${formatMessage({ id: '销售' })}`}
              color={'#FF7272'}
            >
              <span>{formatMessage({ id: '报价单生成失败' })}</span>
            </Tooltip>
          );
        }
      },
    },
    {
      title: formatMessage({ id: '操作' }),
      dataIndex: 'fileUrl',
      key: 'fileUrl',
      render: (text, record) => {
        const fileState = record.fileState;
        if (fileState == 1 && text) {
          return (
            <div>
              <Button
                type="primary"
                ghost
                onClick={() => {
                  const { dispatch } = this.props;
                  dispatch({
                    type: 'user/addUserActionLog',
                    payload: { id: 32, type: 1 },
                  });
                  window.open(text);
                }}
              >
                {formatMessage({ id: '下载' })}
              </Button>
            </div>
          );
        } else {
          return '';
        }
      },
    },
  ];

  componentDidMount() {
    const { dispatch, form } = this.props;
    dispatch({
      type: 'freight/quotationInfo',
      callback: result => {
        if (result.success) {
          const warehouseData = result.data.warehouseOptions;
          const platData = result.data.platformList;
          let temp = [];
          warehouseData.forEach(value => {
            if (value.checked) {
              temp.push(value.code);
            }
          });
          form.setFieldsValue({
            startTime: moment().subtract(1, 'months'),
            endTime: moment(),
          });
          this.setState({
            account: warehouseData,
            shipperInitOpt: temp,
            typeArray: platData,
            typeValueArray: [platData.find(item => item.name === '线下').code],
            loadingBtn: false,
          });
        }
      },
    });
  }

  downloadCountIncrease = code => {
    const { dispatch } = this.props;
    dispatch({
      type: 'freight/downloadCount',
      payload: {
        quotationId: code,
      },
    });
  };

  handleQuery = () => {
    this.props.form.validateFieldsAndScroll((err, values) => {
      if (values.shippers.length === 0) {
        message.error(formatMessage({ id: '请选择揽收仓' }));
        return;
      }
      if (values.platform.length === 0) {
        message.error(formatMessage({ id: '请选择报价单类型' }));
        return;
      }
      if (values.startTime === undefined) {
        message.error(formatMessage({ id: '请选择生成开始日期' }));
        return;
      }
      if (values.endTime === undefined) {
        message.error(formatMessage({ id: '请选择生成结束日期' }));
        return;
      }
      let startDate = moment(values.startTime).format('YYYY-MM-DD');
      let endDate = moment(values.endTime).format('YYYY-MM-DD');
      const { pageSize, current } = this.state;
      const param = {
        pageNo: current,
        pageSize: pageSize,
        companyFroms: values.shippers,
        platformTypes: values.platform,
        startDate: startDate,
        endDate: endDate,
      };
      this.setState({
        loadingBtn: true,
      });
      this.queryFreightByParam(param);
    });
  };

  queryFreightByParam = param => {
    const { dispatch } = this.props;
    dispatch({
      type: 'freight/quotationDownloadList',
      payload: param,
      callback: result => {
        if (result.success) {
          this.setState({
            freightData: result.data.list,
            total: result.data.total,
            loadingBtn: false,
          });
        } else {
          this.setState({
            loadingBtn: false,
          });
        }
      },
    });
  };

  openModal = () => {
    this.setState({ showModal: true });
  };

  closeModal = () => {
    this.setState({ showModal: false });
  };

  render() {
    const { pageSize, total, loadingBtn } = this.state;
    const paginationProps = {
      onChange: (page, pageSize) => {
        this.setState(
          {
            current: page,
            pageSize: pageSize,
          },
          this.handleQuery
        );
      },
      pageSize: pageSize,
      total: total,
      pageSizeOptions: ['10', '20', '50', '100', '500'],
      showTotal: () => {
        return `${formatMessage({ id: '共' })}- ${total} -${formatMessage({ id: '条记录' })}`;
      },
    };
    const { account, shipperInitOpt, typeArray, typeValueArray } = this.state;
    const { form, authorizedLoading } = this.props;
    const { getFieldDecorator } = form;
    const actionsTextMap = {
      expandText: formatMessage({ id: '展开' }),
      collapseText: formatMessage({ id: '收起' }),
      selectAllText: formatMessage({ id: '全部' }),
    };
    let tempAccountOpen = account.length > 13 ? true : false;

    return (
      <div>
        <Card bordered={false}>
          <Form layout="inline">
            <StandardFormRow
              title={formatMessage({ id: '揽收仓' })}
              block
              style={{ paddingBottom: 11 }}
            >
              <FormItem>
                {getFieldDecorator('shippers', { initialValue: shipperInitOpt })(
                  <TagSelect
                    actionsText={actionsTextMap}
                    className="open"
                    expandable={tempAccountOpen}
                  >
                    {account &&
                      account.map((element, index) => (
                        <TagSelect.Option key={index} value={element.code}>
                          {element.name}
                        </TagSelect.Option>
                      ))}
                  </TagSelect>
                )}
              </FormItem>
            </StandardFormRow>
            <StandardFormRow
              title={formatMessage({ id: '报价单类型' })}
              block
              style={{ paddingBottom: 11 }}
            >
              <FormItem>
                {getFieldDecorator('platform', { initialValue: typeValueArray })(
                  <TagSelect actionsText={actionsTextMap} className="open" expandable={false}>
                    {typeArray &&
                      typeArray.map((element, index) => (
                        <TagSelect.Option key={index} value={element.code}>
                          {element.name}
                        </TagSelect.Option>
                      ))}
                  </TagSelect>
                )}
              </FormItem>
            </StandardFormRow>
            <StandardFormRow title={formatMessage({ id: '生成日期' })} grid last>
              <Row gutter={8}>
                <Col span={20}>
                  <FormItem
                    style={{
                      padding: '0px',
                      marginLeft: '-8px',
                      whiteSpace: 'nowrap',
                      float: 'left',
                    }}
                  >
                    {getFieldDecorator('startTime', {
                      rules: [{ required: false, message: formatMessage({ id: '请选择日期' }) }],
                    })(
                      <DatePicker
                        onChange={this.startTimeOnChange}
                        placeholder={formatMessage({ id: '开始日期' })}
                        allowClear={false}
                      />
                    )}
                    <span>&nbsp;&nbsp;~&nbsp;&nbsp;</span>
                    {getFieldDecorator('endTime', {
                      rules: [{ required: false, message: formatMessage({ id: '请选择日期' }) }],
                    })(
                      <DatePicker
                        onChange={this.endTimeOnChange}
                        placeholder={formatMessage({ id: '结束日期' })}
                        allowClear={false}
                      />
                    )}
                  </FormItem>
                </Col>
                <Col span={4}>
                  <Button type="primary" onClick={this.handleQuery} loading={loadingBtn}>
                    {formatMessage({ id: '查询' })}
                  </Button>
                </Col>
              </Row>
            </StandardFormRow>
          </Form>
        </Card>
        <Card
          title={
            <div style={{ width: '65rem' }}>
              <Alert
                message={`感谢您使用燕文报价单。若查询无合适报价单，请申请报价单，选择交货仓、平台类型，并设置稍晚的生效时间以获取最新报价。如有疑问，请联系销售或客服。`}
                type="success"
              />
            </div>
          }
          bordered={false}
          extra={
            <Button type="primary" className="w-32" onClick={this.openModal}>
              {formatMessage({ id: '申请报价单' })}
            </Button>
          }
        >
          <Table
            rowKey={record => record.code}
            bordered={false}
            dataSource={this.state.freightData}
            columns={this.columns}
            pagination={paginationProps}
          />
        </Card>
        <ApplyQuotationModal
          {...this.props}
          platforms={renderSelectOptions(typeArray, { label: 'name', value: 'code' })}
          warehouses={renderSelectOptions(account, { label: 'name', value: 'code' })}
          isOpen={this.state.showModal}
          onCancel={this.closeModal}
          onSearch={this.handleQuery}
        />
      </div>
    );
  }
}

export default SamllQuotationDownload;
