import React, { useState } from 'react';
import PageHeaderBreadcrumb from '@/components/PageHeaderBreadcrumb';
import PageContainerComponent from '@/components/PageContainer';
import { Tabs, message } from 'antd';
import SamllQuotationDownload from './SmallPacketLine/index';
import FBAQuotationDownload from '@/pages/FBA/PriceSheet/index';
import { isAuth } from '@/utils/utils';
import { useMount } from 'ahooks';
import { formatMessage } from 'umi-plugin-react/locale';

const { TabPane } = Tabs;

const QuotationDownload = props => {
  useMount(() => {
    if (isAuth('smallPackage')) {
      setSelectTabKey(0);
    } else if (isAuth('fbaOrder')) {
      setSelectTabKey(1);
    } else {
      setSelectTabKey(99);
      message.error('请开通小包业务或FBA专线业务后下载报价单');
    }
  });

  const [selectTabKey, setSelectTabKey] = useState('0');

  // 切换我的账单页面tab
  const changeTab = key => {
    setSelectTabKey(key);
  };

  return (
    <div>
      <PageContainerComponent
        header={{
          title: null,
          breadcrumb: {},
          breadcrumbRender: props => (
            <PageHeaderBreadcrumb {...props} col>
              <Tabs
                defaultActiveKey="1"
                activeKey={selectTabKey}
                onChange={changeTab}
                style={{
                  marginBottom: '-25px',
                  borderBottom: '0 solid transparent',
                }}
              >
                {isAuth('smallPackage') && (
                  <TabPane tab={formatMessage({ id: '小包专线' })} key="0"></TabPane>
                )}
                {isAuth('fbaOrder') && (
                  <TabPane tab={formatMessage({ id: 'FBA专线' })} key="1"></TabPane>
                )}
                {/*<TabPane tab="海外仓" key="3"></TabPane>*/}
                {/*<TabPane tab="中国仓" key="2"></TabPane>*/}
                {/*<TabPane tab="海外源" key="4"></TabPane>*/}
              </Tabs>
            </PageHeaderBreadcrumb>
          ),
        }}
      >
        {selectTabKey !== undefined && selectTabKey == '0' && <SamllQuotationDownload />}
        {selectTabKey !== undefined && selectTabKey == '1' && <FBAQuotationDownload />}
      </PageContainerComponent>
    </div>
  );
};

export default QuotationDownload;
