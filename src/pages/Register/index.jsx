import React, { useState, useRef } from 'react';
import { useMount, useUnmount, useUpdateEffect } from 'ahooks';
import {
  Button,
  Col,
  Layout,
  Row,
  Space,
  Checkbox,
  Popover,
  Progress,
  Image,
  Spin,
  message,
} from 'antd';
import {
  ProForm,
  ProFormCheckbox,
  ProFormGroup,
  ProFormSelect,
  ProFormText,
  ProFormCaptcha,
} from '@ant-design/pro-components';
import RegisterServiceAgreement from './components/RegisterServiceAgreement';
import RegisterPersonAgreement from './components/RegisterPersonAgreement';
import ImageCodeView from '@/pages/User/components/imageCode';
import { router } from 'umi';
import { connect } from 'dva';
import Logo from '@/assets/logo.png';
import styles from './Register.less';
import { formatMessage, setLocale } from 'umi-plugin-react/locale';
import { getDeviceId, validatorEmail } from '@/utils/utils';
import { useCountDown } from 'ahooks';
import { mobileValidateRegex } from '@/utils/commonConstant';
import useAbsolutePosition from '@/hooks/useAbsolutePosition';
import useMobileDetect from '@/hooks/useMobileDetect';

const passwordStatusMap = {
  ok: (
    <div className={styles.success}>
      {formatMessage({ id: '强度' })}：{formatMessage({ id: '强' })}
    </div>
  ),
  pass: (
    <div className={styles.warning}>
      {formatMessage({ id: '强度' })}：{formatMessage({ id: '中' })}
    </div>
  ),
  poor: (
    <div className={styles.error}>
      {formatMessage({ id: '强度' })}：{formatMessage({ id: '弱' })}
    </div>
  ),
};

const passwordProgressMap = {
  ok: 'success',
  pass: 'normal',
  poor: 'exception',
};

/**
 * 说明：注册
 * 创建人：Dgh
 * 创建时间：2023-02-20 16:29:20
 */
const Index = props => {
  const { dispatch, imageLoading, location, submitLoading } = props;
  // 使用
  const [btnRef, position] = useAbsolutePosition();
  const isMobile = useMobileDetect();
  const formRef = useRef();
  const imageCodeRef = useRef();

  const [serviceDisplay, setServiceDisplay] = useState('none'); // 燕文物流服务协议
  const [registerDisplay, setRegisterDisplay] = useState('block'); // 注册
  const [personDisplay, setPersonDisplay] = useState('none'); // 个人隐私协议
  const [captchaUrl, setCaptchaUrl] = useState(''); // 图形验证码
  const [open, setOpen] = useState(false); // 密码强度
  const [passwordInput, setPasswordInput] = useState(); // 密码 用于提示密码强度
  const [target, setTarget] = useState();
  const [saleEdit, setSaleEdit] = useState(true);
  const [province, setProvince] = useState([]); // 省
  const [city, setCity] = useState([]); // 市
  const [targetDate, setTargetDate] = useState(); // 倒计时
  const [averageDailyRequired, setAverageDailyRequired] = useState(true); // 日均需求是否必填
  const [check, setCheck] = useState(false);
  const [ticketQuantityList, setTicketQuantityList] = useState([]);
  const [hideLogin, setHideLogin] = useState(false);
  const [platCode, setPlatCode] = useState();
  const [showCaptcha, setShowCaptcha] = useState(false); // 是否展示二维码
  const [url, setUrl] = useState(''); // 二维码地址
  const [fragmentUrl, setFragmentUrl] = useState(''); // 滑块地址
  const [y, setY] = useState(0); // 滑块y轴坐标

  const rootElement = document.getElementById('root');
  const [countdown] = useCountDown({
    targetDate,
  });

  useMount(() => {
    // setLocale('en-US', false);
    init();
  });

  useUpdateEffect(() => {
    if (isMobile) {
      if (rootElement) {
        // 通过切换 CSS 类控制样式
        // rootElement.style.minWidth = 'unset !important';
        rootElement.classList.add('no-min-width');
        rootElement.style.setProperty('min-width', 'unset', 'important');
      }
    }
  }, [isMobile]);

  useUnmount(() => {
    if (isMobile) {
      if (rootElement) {
        // rootElement.style.minWidth = '1500px';
        rootElement.classList.remove('no-min-width');
        rootElement.style.removeProperty('min-width');
      }
    }
  });

  const init = () => {
    getImgUrl();
    getTargetNumber();
    getProvince();
    getAverageDailyRequired();
  };

  // 滑块刷新
  const onReload = () => {
    dispatch({
      type: 'login/getSliderCaptcha',
      callback: response => {
        if (response.success) {
          const url = `data:image/jpeg;base64,${response.data.shadeImage}`.replace(
            /\s/g,
            encodeURIComponent(' ')
          );
          const fragmentUrl = `data:image/jpeg;base64,${response.data.cutoutImage}`.replace(
            /\s/g,
            encodeURIComponent(' ')
          );
          setUrl(url);
          setFragmentUrl(fragmentUrl);
          setY(response.data.pointY);
          setShowCaptcha(true);
          imageCodeRef.current.setStatus();
        }
      },
    });
  };

  // 滑块校验
  const onMatch = (currentX, fun) => {
    sendVerifyPhone(currentX, fun);
  };

  const getProvince = () => {
    dispatch({
      type: 'Register/getProvince',
      callback: response => {
        if (response.success) {
          setProvince(response.data);
        }
      },
    });
  };

  const getCity = provinceName => {
    dispatch({
      type: 'Register/getCity',
      payload: { provinceCode: provinceName },
      callback: response => {
        if (response.success) {
          setCity(response.data);
        }
      },
    });
  };

  // 获取日均票件量
  const getAverageDailyRequired = () => {
    dispatch({
      type: 'Register/shipmentOfDay',
      callback: response => {
        if (response.success) {
          const data = response.data.map(item => ({ value: item.code, label: item.name }));
          // setAverageDailyRequired(response.data);
          setTicketQuantityList(data);
        }
      },
    });
  };

  // 获取链接中的信息
  const getTargetNumber = () => {
    const target = location.query?.target;
    const platCode = location.query?.platCode;
    setPlatCode(platCode);
    setTarget(target);
    if (target) {
      setHideLogin(true);
    }
  };

  const getImgUrl = () => {
    dispatch({
      type: 'Register/getImgCaptcha',
      callback: response => {
        if (response.success) {
          formRef.current?.setFieldsValue({ captchaCode: undefined });
          const imgUrl = response.data.image.replace('*', 'png');
          setCaptchaUrl(imgUrl);
        }
      },
    });
  };

  const sendVerifyPhone = (pointX, fun) => {
    formRef.current?.validateFields(['phone']).then(values => {
      dispatch({
        type: 'Register/sendVerifyPhone',
        payload: {
          phone: values.phone,
          deviceId: getDeviceId(),
          pointX,
        },
        callback: response => {
          if (response.success) {
            setShowCaptcha(false);
            setTargetDate(Date.now() + 1000 * 60);
          } else {
            if (response.status === 'codeError') {
              if (fun) fun('error', response.message);
            } else {
              setShowCaptcha(false);
              if (fun) fun('success');
            }
          }
        },
      });
    });
  };

  const changeView = type => {
    if (type === 'service') {
      setServiceDisplay('block');
      setRegisterDisplay('none');
      setPersonDisplay('none');
    } else if (type === 'person') {
      setServiceDisplay('none');
      setPersonDisplay('block');
      setRegisterDisplay('none');
    } else {
      setServiceDisplay('none');
      setPersonDisplay('none');
      setRegisterDisplay('block');
    }
  };

  const getPasswordStatus = () => {
    if (!formRef.current) return;
    const value = passwordInput;
    const numberTest = new RegExp('[0-9]').test(value);
    const letterTest = new RegExp('[a-zA-Z]').test(value);
    const symbolTest = new RegExp('[_.!@#?]').test(value);
    if (value && value.length < 9) {
      return 'poor';
    }
    if (numberTest && letterTest && symbolTest) {
      return 'ok';
    }
    if ((numberTest && letterTest) || (letterTest && symbolTest) || (numberTest && symbolTest)) {
      return 'pass';
    }
    return 'poor';
  };

  // 密码强中弱设置
  const renderPasswordProgress = () => {
    if (!formRef.current) return;
    const value = passwordInput;
    const passwordStatus = getPasswordStatus();
    return value && value.length ? (
      <div className={styles[`progress-${passwordStatus}`]}>
        <Progress
          status={passwordProgressMap[passwordStatus]}
          className={styles.progress}
          strokeWidth={6}
          percent={value.length * 10 > 100 ? 100 : value.length * 10}
          showInfo={false}
        />
      </div>
    ) : null;
  };

  // 再次输入密码校验
  const checkConfirm = (rule, value, callback) => {
    if (value && value !== passwordInput) {
      callback(formatMessage({ id: '两次输入的密码不匹配!' }));
    } else {
      callback();
    }
  };

  // 校验销售是否存在
  const sellerPhoneBlur = e => {
    const sellerPhone = e.target.value;
    if (sellerPhone.length === 0) {
      return;
    }

    // 手机号校验
    if (sellerPhone.length === 11) {
      dispatch({
        type: 'Register/mobileVerify',
        payload: {
          mobile: sellerPhone,
        },
        callback: data => {
          if (!data.success) {
            formRef.current?.setFields({
              salesPhone: {
                value: sellerPhone,
                errors: [new Error(data.message)],
              },
            });
          }
        },
      });
    }
  };

  const getSaleList = async keyword => {
    let pattern = /^(?:(\d{11})|([\u4e00-\u9fa5]{2,4}))$/;
    if (!keyword) return [];
    if (!pattern.test(keyword)) return [];
    const data = new Promise((resolve, reject) => {
      dispatch({
        type: 'Register/mobileVerify',
        payload: {
          mobile: keyword,
        },
        callback: response => {
          if (response.success) {
            const data = [];
            response?.data?.forEach(item => {
              data.push({
                label: item.empname + '-' + item.mbphone,
                value: item.mbphone,
              });
            });
            resolve(data);
          } else {
            resolve([]);
          }
        },
      });
    });
    return data;
  };

  const verifyPhone = () => {
    formRef.current?.validateFields(['phone']).then(values => {
      onReload();
    });
  };

  // 点击下一步校验销售手机号
  const verifySellMobile = () =>
    new Promise((resolve, reject) => {
      if (sellMobile) {
        dispatch({
          type: 'Register/getConsumerPhone',
          payload: {
            id: sellMobile,
          },
          callback: data => {
            if (!data.success) {
              if (data.status == 500) {
              }
              resolve({ status: false, code: data.status });
            } else {
              resolve({ status: true });
            }
          },
        });
      } else {
        resolve({ status: true });
      }
    });

  const handleSubmit = async values => {
    const params = {
      ...values,
      saleExist: values?.saleExist?.join() === ['0'].join(),
      salesPhone: values?.salesPhone,
      target: target,
      platCode: platCode,
    };
    delete params.agreement;
    dispatch({
      type: 'Register/submit',
      payload: params,
      callback: response => {
        if (response.success) {
          message.success('注册成功', 1).then(() => {
            // 跳转到实名认证页面
            router.push(`/homePageList`);
          });
        } else {
          if (response?.code == '9301') {
            setTarget(undefined);
            return;
          }
          getImgUrl();
          if (response.message.indexOf('验证码') !== -1) {
            if (response.message.indexOf('图片验证码') == -1) {
              formRef.current?.setFields({
                smsVerifyCode: {
                  errors: [new Error('短信验证码错误')],
                },
              });
            }
          }
        }
      },
    });
  };

  return (
    <>
      <RegisterPersonAgreement
        personDisplay={personDisplay}
        agreeService={() => changeView('register')}
      />
      <RegisterServiceAgreement
        serviceDisplay={serviceDisplay}
        agreeService={() => changeView('register')}
      />

      <div style={{ display: registerDisplay }}>
        <p className="text-lg text-center">{formatMessage({ id: '注册' })}</p>
        <Row justify="center">
          <Col xxl={6} xl={9}>
            <ProForm
              formRef={formRef}
              layout="horizontal"
              labelAlign="right"
              labelCol={{ flex: '95px' }}
              onFinish={handleSubmit}
              submitter={{
                render: (_, dom) => null,
              }}
            >
              <ProFormSelect
                label={formatMessage({ id: '合作业务' })}
                name="businessTypeCode"
                valueEnum={{
                  0: formatMessage({ id: '小包专线' }),
                  1: formatMessage({ id: 'FBA专线' }),
                  4: '海外派',
                }}
                rules={[
                  {
                    required: true,
                    message: formatMessage({ id: '请选择合作业务' }),
                  },
                ]}
                fieldProps={{
                  mode: 'multiple',
                  // onChange: value => {
                  //   setAverageDailyRequired(value.includes('0') || value.includes('3'));
                  // },
                }}
              />
              {averageDailyRequired && (
                <ProFormSelect
                  label={formatMessage({ id: '日均票件量' })}
                  name="shipmentOfDayCode"
                  rules={[
                    {
                      required: averageDailyRequired,
                      message: formatMessage({ id: '请选择日均票件量' }),
                    },
                  ]}
                  fieldProps={{
                    options: ticketQuantityList,
                  }}
                />
              )}

              <ProFormText
                name="email"
                label={formatMessage({ id: '邮箱' })}
                placeholder={formatMessage({ id: '请输入邮箱' })}
                rules={[
                  // {
                  //   required: true,
                  //   message: '请输入邮箱',
                  // },
                  {
                    validator: (_, value) => validatorEmail(value, formRef, 'email'),
                  },
                ]}
                fieldProps={{ autoComplete: 'new-password' }}
              />
              <ProFormText
                name="loginName"
                label={formatMessage({ id: '用户名' })}
                placeholder={formatMessage({ id: '请输入用户名' })}
                rules={[
                  {
                    required: true,
                    message: formatMessage({ id: '请输入用户名' }),
                  },
                  {
                    pattern: /^(?![0-9]+$)(?![a-zA-Z]+$)(?![_]+$)[0-9A-Za-z_]{6,20}$/,
                    message: formatMessage({ id: '用户名为6~20位字母、数字或下划线其中两项组合' }),
                  },
                ]}
                fieldProps={{ autoComplete: 'off' }}
              />
              <ProFormText
                name="phone"
                label={formatMessage({ id: '手机号' })}
                placeholder={formatMessage({ id: '请输入手机号' })}
                rules={[
                  {
                    required: true,
                    message: formatMessage({ id: '请输入手机号' }),
                  },
                  {
                    pattern: mobileValidateRegex,
                    message: formatMessage({ id: '手机号格式错误' }),
                  },
                ]}
              />
              <ProFormText
                name="smsVerifyCode"
                label={formatMessage({ id: '验证码' })}
                placeholder={formatMessage({ id: '请输入验证码' })}
                rules={[
                  {
                    required: true,
                    message: formatMessage({ id: '请输入验证码' }),
                  },
                ]}
                addonAfter={
                  <Button disabled={countdown !== 0} onClick={verifyPhone} ref={btnRef}>
                    {countdown === 0
                      ? formatMessage({ id: '获取验证码' })
                      : `${Math.round(countdown / 1000)}s`}
                  </Button>
                }
              />
              <Popover
                getPopupContainer={node => node.parentNode}
                content={
                  <div style={{ padding: '4px 0' }}>
                    {passwordStatusMap[getPasswordStatus()]}
                    {renderPasswordProgress()}
                    <div style={{ marginTop: 10 }}>
                      {formatMessage({
                        id: '请输入由',
                      })}
                      9~20{formatMessage({ id: '位数字' })}、
                      {formatMessage({ id: '字母或符号组成的密码' })}，
                      {formatMessage({ id: '符号可以有' })}_.!@#?
                    </div>
                  </div>
                }
                overlayStyle={{ width: 240 }}
                placement="right"
                open={open}
              >
                <ProFormText.Password
                  name="password"
                  label={formatMessage({ id: '输入密码' })}
                  placeholder={formatMessage({ id: '请输入密码' })}
                  fieldProps={{
                    onChange: e => {
                      setPasswordInput(e.target.value);
                      setOpen(e.target.value.length > 0);
                    },
                    onBlur: () => setOpen(false),
                    autoComplete: 'new-password',
                  }}
                  rules={[
                    {
                      required: true,
                      message: formatMessage({ id: '请输入密码' }),
                    },
                    {
                      pattern: /^(?![0-9]+$)(?![a-zA-Z]+$)(?![_.!@#?]+$)[0-9A-Za-z_.!@#?]{9,20}$/,
                      message: `${formatMessage({ id: '密码由' })}9~20${formatMessage({
                        id: '位数字',
                      })}、${formatMessage({ id: '字母或符号组成' })}，${formatMessage({
                        id: '符号可以有',
                      })}_.!@#?`,
                    },
                  ]}
                />
              </Popover>
              <ProFormText.Password
                name="passwordConfirm"
                label={formatMessage({ id: '确认密码' })}
                placeholder={formatMessage({ id: '请再次输入密码' })}
                rules={[
                  {
                    required: true,
                    message: formatMessage({ id: '请确认密码' }),
                  },
                  {
                    validator: checkConfirm,
                  },
                ]}
              />
              <ProFormGroup>
                <ProFormText
                  name="captchaCode"
                  label={formatMessage({ id: '图形验证码' })}
                  placeholder={formatMessage({ id: '请输入右侧图形验证码' })}
                  rules={[
                    {
                      required: true,
                      message: formatMessage({ id: '请输入右侧图形验证码' }),
                    },
                  ]}
                />
                {imageLoading ? (
                  <div className="flex justify-center items-center">
                    <Spin />
                  </div>
                ) : (
                  <img
                    width={70}
                    height={30}
                    src={captchaUrl === '' ? Logo : captchaUrl}
                    onClick={getImgUrl}
                  />
                )}
              </ProFormGroup>
              {!target && (
                <ProFormGroup>
                  <ProFormSelect
                    showSearch
                    debounceTime={500}
                    name="salesPhone"
                    label={formatMessage({ id: '推荐销售' })}
                    placeholder={formatMessage({ id: '请输入销售姓名或手机号' })}
                    width={230}
                    rules={[
                      {
                        required: saleEdit,
                        message: formatMessage({ id: '请输入正确手机号或者销售姓名' }),
                      },
                    ]}
                    fieldProps={{
                      disabled: !saleEdit,
                      filterOption: () => {
                        return true;
                      },
                    }}
                    request={({ keyWords }) => getSaleList(keyWords)}
                  />
                  <ProFormCheckbox.Group
                    name="saleExist"
                    layout="horizontal"
                    fieldProps={{
                      options: [{ label: formatMessage({ id: '无推荐销售' }), value: '0' }],
                      onChange: e => {
                        setSaleEdit(!e[0]);
                        formRef.current?.resetFields(['salesPhone']);
                      },
                    }}
                  />
                </ProFormGroup>
              )}
              {!saleEdit && (
                <Row justify="space-between">
                  <ProFormSelect
                    style={{
                      width: 150,
                    }}
                    label={formatMessage({ id: '所在城市' })}
                    name="province"
                    rules={[
                      {
                        required: !saleEdit,
                        message: formatMessage({ id: '请选择所在省' }),
                      },
                    ]}
                    fieldProps={{
                      options: province,
                      fieldNames: {
                        label: 'cityName',
                        value: 'cityCode',
                      },
                      onChange: value => {
                        formRef.current?.resetFields(['cityCode']);
                        getCity(value);
                      },
                    }}
                  />
                  <ProFormSelect
                    style={{
                      width: 190,
                    }}
                    name="cityCode"
                    rules={[
                      {
                        required: !saleEdit,
                        message: formatMessage({ id: '请选择所在市' }),
                      },
                    ]}
                    fieldProps={{
                      options: city,
                      fieldNames: {
                        label: 'cityName',
                        value: 'cityCode',
                      },
                    }}
                  />
                </Row>
              )}

              <Row justify="center">
                <Space direction="vertical" align="center">
                  <Button
                    style={{ display: hideLogin ? 'none' : '' }}
                    type="link"
                    onClick={() => router.push(`/user/login`)}
                  >
                    {formatMessage({ id: '已有账号' })}？{formatMessage({ id: '立即登录' })}
                  </Button>
                  {/* <ProFormCheckbox.Group
                    name="agreement"
                    rules={check ? [] : [{ required: true, message: '请阅读并同意协议' }]}
                  >
                    我已阅读并同意 <a onClick={() => changeView('service')}>《燕文物流服务协议》</a>{' '}
                    <a onClick={() => changeView('person')}>《个人隐私协议》</a>
                  </ProFormCheckbox.Group> */}
                  <ProForm.Item
                    required
                    name="agreement"
                    rules={
                      check
                        ? []
                        : [
                            {
                              required: true,
                              message: formatMessage({ id: '请阅读并同意协议' }),
                            },
                          ]
                    }
                  >
                    <Checkbox.Group>
                      <Checkbox>
                        {formatMessage({ id: '我已阅读并同意' })}{' '}
                        <a onClick={() => changeView('service')}>
                          《{formatMessage({ id: '燕文物流服务协议' })}》
                        </a>{' '}
                        <a onClick={() => changeView('person')}>
                          《{formatMessage({ id: '个人隐私协议' })}》
                        </a>
                      </Checkbox>
                    </Checkbox.Group>
                  </ProForm.Item>
                  <Button type="primary" htmlType="submit" className="w-96" loading={submitLoading}>
                    {formatMessage({ id: '注册' })}
                  </Button>
                </Space>
              </Row>
            </ProForm>
          </Col>
        </Row>
        {showCaptcha && (
          <div
            style={{
              position: 'absolute',
              top: `${position.y - 250}px`,
              left: `${position.x - 200}px`,
              // width: '95%',
              // height: '400px',
              zIndex: '2',
            }}
          >
            <div
              style={{
                width: '300px',
                margin: '0px auto',
                backgroundColor: '#fff',
                boxShadow: '0 2px 10px 0 #333',
              }}
            >
              <ImageCodeView
                ref={imageCodeRef}
                imageUrl={url}
                fragmentUrl={fragmentUrl}
                offsetY={y}
                onCloseCode={func => {
                  setShowCaptcha(false);
                  if (func) func();
                }}
                onReload={onReload}
                onMatch={onMatch}
                imageWidth={300}
                imageHeight={171}
                fragmentSize={55}
              />
            </div>
          </div>
        )}
      </div>
    </>
  );
};
export default connect(({ register, loading }) => ({
  imageLoading: loading.effects['Register/getImgCaptcha'],
  submitLoading: loading.effects['Register/submit'],
}))(Index);
