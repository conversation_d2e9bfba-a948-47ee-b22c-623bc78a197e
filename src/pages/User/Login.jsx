import React, { useState, useRef, useEffect } from 'react';
import { connect } from 'dva';
import router from 'umi/router';
import Link from 'umi/link';
import ImageCodeView from './components/imageCode';
import ModalLogin from './components/ModalLogin';
import {
  AlipayCircleOutlined,
  LockOutlined,
  MobileOutlined,
  TaobaoCircleOutlined,
  UserOutlined,
  WeiboCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import {
  LoginForm,
  ProFormCaptcha,
  ProFormCheckbox,
  ProFormText,
  ProFormGroup,
} from '@ant-design/pro-components';
import styles from './Login.less';
import QRCode from 'qrcode.react';
import { Button, message, Space, Tabs, Row, Image, Modal } from 'antd';
import { CheckCircleOutlined } from '@ant-design/icons';
import { getDeviceId, getQueryPath, handleLinkParam } from '@/utils/utils';
import { useCountDown, useMount, useUnmount } from 'ahooks';
import PageLoading from '@/components/PageLoading';
import {
  errorImageUrl,
  foreignKeyEnum,
  mobileValidateRegex,
  pageRouters,
} from '@/utils/commonConstant';
import useCompleteData from '@/hooks/useCompleteData';
import useContractSign from '@/hooks/useContractSign';
import { overseaUrl } from '../../../config/defaultSettings';
import { formatMessage } from 'umi-plugin-react/locale';
import { byWebLanguageChange } from '@/utils/utils';
import { initializeUdeskChat } from '@/utils/robot';
import { logSave, LogType } from '@/utils/logSave';

const qrcodeSuccess = {
  backgroundColor: 'rgba(255, 255, 255, 0.98)',
  width: '180px',
  height: '180px',
  paddingTop: '70px',
  position: 'absolute',
  top: '1px',
  left: '110px',
  color: '#52c41a',
  fontSize: '16px',
};

/**
 * 说明：登录
 * 创建人：Dgh
 * 创建时间：2023-02-20 14:58:17
 */
const Login = props => {
  const { dispatch, getScanCodeLoading, location } = props;
  const imageCodeRef = useRef();
  const loginFormRef = useRef();
  const modalRef = useRef();
  const intervalTaskRef = useRef(); // 定时器使用
  const [loginType, setLoginType] = useState('account');

  const [showCaptcha, setShowCaptcha] = useState(false); // 是否展示二维码
  const [url, setUrl] = useState(''); // 二维码地址
  const [fragmentUrl, setFragmentUrl] = useState(''); // 滑块地址
  const [y, setY] = useState(0); // 滑块y轴坐标
  const [scanStep, setScanStep] = useState(0);
  const [content, setContent] = useState(formatMessage({ id: '请使用微信扫描二维码登录' }));
  const [targetDate, setTargetDate] = useState(); // 倒计时
  const [scanCodeUrl, setScanCodeUrl] = useState(''); // 扫码登录地址
  const [scanKey, setScanKey] = useState(''); // 获取扫码结果需要的key
  const [autoLogin, setAutoLogin] = useState(false); // 自动登录
  const [robotLoginPath, setRobotLoginPath] = useState(undefined);
  const [countdown] = useCountDown({
    targetDate,
  });

  useMount(() => {
    initializeUdeskChat('56688', {});
    checkLogin();
    checkLoginParams();
    byWebLanguageChange();
    checkRobotLogin();
  });

  const checkRobotLogin = () => {
    // 创建一个 URLSearchParams 对象来处理查询参数
    let params = new URLSearchParams(props.location.search);
    let toRedirectPath = params.get('toRedirectPath');
    let foreignKey = params.get('foreignKey');
    if (toRedirectPath != null && foreignKey != null) {
      if (foreignKey == foreignKeyEnum.billDownload) {
        toRedirectPath = toRedirectPath + `?foreignKey=${foreignKey}`;
      }
      setRobotLoginPath(toRedirectPath);
      handleLinkParam(props, ['toRedirectPath', foreignKeyEnum.foreignKey]);
    }
  };

  const checkLoginParams = () => {
    const { query } = location;
    if (query?.key) {
      setAutoLogin(true);
      handleLogin(query);
    } else {
      setAutoLogin(false);
    }
  };

  useUnmount(() => {
    disconnect();
  });

  useEffect(() => {
    if (scanKey !== '') {
      connect();
    }
  }, [scanKey]);

  const disconnect = () => {
    if (intervalTaskRef.current != null) {
      clearInterval(intervalTaskRef.current);
    }
  };
  // 定时调用获取扫码结果
  const connect = () => {
    intervalTaskRef.current = setInterval(() => {
      dispatch({
        type: 'login/getScanResult',
        payload: {
          scanKey,
        },
        callback: response => {
          if (response.success) {
            if (response.data === -1) {
              //是已经过期
              getScanCode();
            } else if (response.data === 0) {
              // 未扫码
            } else if (response.data === 1) {
              // 已经绑定
              setScanStep(1);
              disconnect();
              const params = {
                tokenId: scanKey,
                dsource: 'wechat',
                loginType: '0',
              };
              handleLogin(params);
            } else if (response.data === 2) {
              // 未绑定
              modalRef.current?.openModal(scanKey);
              disconnect();
            }
          }
        },
      });
    }, 2000);
  };

  const checkLogin = () => {
    dispatch({
      type: 'user/queryCurrent',
      callback: response => {
        if (response.success) {
          router.push('/homePageList');
        }
      },
    });
  };

  // 滑块刷新
  const onReload = () => {
    dispatch({
      type: 'login/getSliderCaptcha',
      callback: response => {
        if (response.success) {
          const url = `data:image/jpeg;base64,${response.data.shadeImage}`.replace(
            /\s/g,
            encodeURIComponent(' ')
          );
          const fragmentUrl = `data:image/jpeg;base64,${response.data.cutoutImage}`.replace(
            /\s/g,
            encodeURIComponent(' ')
          );
          setUrl(url);
          setFragmentUrl(fragmentUrl);
          setY(response.data.pointY);
          setShowCaptcha(true);
          imageCodeRef.current.setStatus();
        }
      },
    });
  };

  // 滑块校验
  const onMatch = (currentX, fun) => {
    if (loginType === 'account') {
      const dataInfo = loginFormRef.current?.getFieldsValue?.();
      const values = {
        ...dataInfo,
        dsource: 'pc',
        loginType: '1',
        pointX: currentX.toString(),
        deviceNo: getDeviceId(),
      };
      handleSubmit(values, fun);
    }
    if (loginType === 'mobile') {
      sendPhoneCode(currentX, fun);
    }
  };
  // 短信登录发送验证码
  const sendPhoneCode = (currentX, fun) => {
    let phone = loginFormRef.current?.getFieldValue('phone');
    let param = { phone, pointX: currentX.toString() };
    dispatch({
      type: 'login/sendVerifyPhone',
      payload: param,
      callback: response => {
        if (response.success) {
          setShowCaptcha(false);
          setTargetDate(Date.now() + 1000 * 60);
        }
      },
    });
  };

  const onGetCaptcha = () => {
    loginFormRef.current?.validateFields(['phone']).then(values => {
      onReload();
    });
  };

  const genUuid = () => {};

  const handleSubmit = (values, fun) => {
    // ? values : loginFormRef.current?.getFieldsValue?.();
    let params;
    if (loginType === 'account') {
      if (values.hasOwnProperty('pointX')) {
        params = {
          ...values,
        };
      } else {
        onReload();
        return;
      }
    }
    if (loginType === 'mobile') {
      params = {
        ...values,
        dsource: 'pc',
        loginType: '2',
        deviceNo: getDeviceId(),
      };
    }
    handleLogin(params, fun);
  };

  const handleLoginLog = param => {
    try {
      if (param.loginType === '0') {
        logSave(LogType.login5);
      }
      if (param.loginType === '1') {
        logSave(LogType.login4);
      }
      if (param.loginType === '2') {
        logSave(LogType.login6);
      }
    } catch (e) {}
  };

  const handleLogin = (params, fun) => {
    handleLoginLog(params);
    dispatch({
      type: 'login/login',
      payload: {
        ...params,
      },
      callback: response => {
        if (!response.success) {
          // 不符合规则密码重置密码
          if (response.code === 'mp') {
            router.push(
              `/forget/modificationPassword?loginName=${
                loginType === 'mobile' ? params.phone : params.loginName
              }`
            );
            // router.push(`/forget/password`);
          } else if (response.code === '5001') {
            if (fun) fun('error', response.message);
            else message.error(response.message);
          } else {
            setShowCaptcha(false);
            if (fun) fun('error', response.message);
            else message.error(response.message);
          }
          setAutoLogin(false);
        } else {
          if (response.data?.pageState == 26) {
            setShowCaptcha(false);
            return Modal.confirm({
              title: '重要提示',
              content: '该账号未实名认证，为避免影响限制下单，请在2024年6月20日前完成实名认证！',
              icon: <ExclamationCircleOutlined />,
              okText: '去认证',
              cancelText: '暂不认证',
              onOk: async () => {
                await useCompleteData(dispatch);
              },
              onCancel: () => {
                handleLoginSuccess(response);
              },
            });
          } else if (response.data?.pageState == 27) {
            setShowCaptcha(false);
            return Modal.confirm({
              title: formatMessage({ id: '重要提示' }),
              content: `${formatMessage({
                id: '合同内容已更新，为避免影响您的账户正常使用，请前往阅读并签署最新合同',
              })}。`,
              icon: <ExclamationCircleOutlined />,
              okText: formatMessage({ id: '去签署' }),
              cancelText: formatMessage({ id: '暂不签署' }),
              onOk: async () => {
                await useContractSign(dispatch);
              },
              onCancel: () => {
                handleLoginSuccess(response);
              },
            });
          } else {
            handleLoginSuccess(response);
          }
        }
      },
    });
  };

  const logout = () => {
    dispatch({
      type: 'login/logout',
      callback: result => {},
    });
  };

  const getScanCode = () => {
    disconnect();
    setScanStep(0);
    dispatch({
      type: 'login/getScanCode',
      callback: response => {
        if (response.success) {
          setScanKey(response.data.scanKey);
          setScanCodeUrl(response.data.qrcode);
        } else {
          setScanCodeUrl(errorImageUrl);
          setScanKey('');
        }
      },
    });
  };

  const handleChangeTabs = activeKey => {
    setLoginType(activeKey);
    disconnect();
    setScanStep(0);
    if (activeKey === 'qrcode') {
      // 请求获取二维码
      getScanCode();
    }
  };

  const handleModalCancel = () => {
    disconnect();
    getScanCode();
  };

  const handleLoginSuccess = result => {
    const { query } = location;
    let pageState = result.data.pageState;
    const str = pageRouters[pageState] ?? '/homePageList';
    localStorage.removeItem('permissions');
    localStorage.removeItem('menuList');
    localStorage.removeItem('openMessageModal');
    const urlParams = new URL(window.location.href); // url
    if (robotLoginPath !== undefined) {
      let queryPath = getQueryPath(robotLoginPath, query);
      window.location.href = urlParams.origin + queryPath;
      return;
    }
    const redirect = urlParams.origin + str;
    // router.push(str);
    window.location.href = redirect;
  };

  return (
    <>
      {autoLogin ? (
        <PageLoading paddingTop={200} />
      ) : (
        <LoginForm
          formRef={loginFormRef}
          title={null}
          submitter={{
            render: (props, doms) => {
              return [];
            },
          }}
          actions={
            <div
              style={{
                marginTop: '24px',
                lineHeight: '22px',
                textAlign: 'center',
              }}
            >
              {/* <Link className={styles.register} to="/forgetpassword/register"> */}
              <Link
                style={{
                  color: '#d53030',
                  fontSize: '16px',
                  fontWeight: 'bold',
                }}
                to="/register"
                onClick={() => logSave(LogType.login2)}
              >
                {formatMessage({ id: '没有账号' })}？{formatMessage({ id: '立即注册' })}
              </Link>
              <div style={{ height: 15 }}></div>
              {
                <a
                  href="https://portal-test.yanwentech.com/guideline/index.html"
                  target="_blank"
                  onClick={() => logSave(LogType.login3)}
                >
                  {formatMessage({ id: '注册演示' })}
                </a>
              }
            </div>
          }
          onFinish={handleSubmit}
          contentStyle={{
            width: '385px',
          }}
        >
          <Tabs centered activeKey={loginType} onChange={handleChangeTabs}>
            <Tabs.TabPane key={'account'} tab={formatMessage({ id: '账号密码登录' })} />
            <Tabs.TabPane key={'qrcode'} tab={formatMessage({ id: '扫码登录' })} />
            <Tabs.TabPane key={'mobile'} tab={''} />
          </Tabs>
          {loginType === 'account' && (
            <>
              <ProFormText
                name="loginName"
                fieldProps={{
                  size: 'large',
                  prefix: (
                    <UserOutlined className={'prefixIcon'} style={{ color: 'rgba(0,0,0,.25)' }} />
                  ),
                  style: {},
                }}
                placeholder={formatMessage({ id: '请输入用户名/手机号/邮箱' })}
                rules={[
                  {
                    required: true,
                    message: formatMessage({ id: '请输入用户名/手机号/邮箱' }),
                  },
                ]}
              />
              <ProFormText.Password
                name="password"
                fieldProps={{
                  size: 'large',
                  prefix: (
                    <LockOutlined className={'prefixIcon'} style={{ color: 'rgba(0,0,0,.25)' }} />
                  ),
                  autoComplete: 'new-password',
                  style: {},
                }}
                placeholder={formatMessage({ id: '请输入密码' })}
                rules={[
                  {
                    required: true,
                    message: formatMessage({ id: '请输入密码！' }),
                  },
                ]}
              />
            </>
          )}
          {loginType === 'mobile' && (
            <>
              <ProFormText
                fieldProps={{
                  size: 'large',
                  prefix: (
                    <MobileOutlined className={'prefixIcon'} style={{ color: 'rgba(0,0,0,.25)' }} />
                  ),
                  style: {},
                }}
                name="phone"
                placeholder={formatMessage({ id: '手机号' })}
                rules={[
                  {
                    required: true,
                    message: formatMessage({ id: '请输入手机号！' }),
                  },
                  {
                    pattern: mobileValidateRegex,
                    message: formatMessage({ id: '手机号格式错误！' }),
                  },
                ]}
              />
              <ProFormGroup>
                <ProFormText
                  name="smsCheckCode"
                  placeholder={formatMessage({ id: '请输入验证码' })}
                  rules={[
                    {
                      required: true,
                      message: formatMessage({ id: '请输入验证码' }),
                    },
                  ]}
                  fieldProps={{
                    size: 'large',
                    prefix: (
                      <LockOutlined className={'prefixIcon'} style={{ color: 'rgba(0,0,0,.25)' }} />
                    ),
                    style: {},
                  }}
                />
                <Button disabled={countdown !== 0} onClick={onGetCaptcha} size="large">
                  {' '}
                  {countdown === 0
                    ? formatMessage({ id: '获取验证码' })
                    : `${Math.round(countdown / 1000)}s`}
                </Button>
              </ProFormGroup>
              {/* <ProFormCaptcha
              fieldProps={{
                size: 'large',
                prefix: <LockOutlined className={'prefixIcon'} />,
              }}
              captchaProps={{
                size: 'large',
              }}
              placeholder={'请输入验证码'}
              captchaTextRender={(timing, count) => {
                if (timing) {
                  return `${count} ${'获取验证码'}`;
                }
                return '获取验证码';
              }}
              phoneName="phone"
              name="smsCheckCode"
              rules={[
                {
                  required: true,
                  message: '请输入验证码！',
                },
              ]}
              onGetCaptcha={sendPhoneCode}
            /> */}
            </>
          )}
          {loginType === 'qrcode' && (
            <div align="center" style={{ position: 'relative' }}>
              {/* <QRCode
              value={"https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=gQFu8DwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAydEdRQTFLeEpkeWoxNEFrX05BY20AAgSkGTZkAwSAOgkA"}
              renderAs="canvas"
              bgColor="white"
              fgColor="black"
              size="180"
              // level="M"
              includeMargin="false"
              style={{ border: '#eeeeee solid 1px', display: 'block' }}
            /> */}
              {getScanCodeLoading ? (
                <PageLoading paddingTop={0} />
              ) : (
                <Image
                  preview={false}
                  onClick={() => {
                    getScanCode();
                  }}
                  src={scanCodeUrl}
                  width={180}
                  height={180}
                  style={{ border: '#eeeeee solid 1px', display: 'block' }}
                />
              )}

              {scanStep === 1 ? (
                <div style={qrcodeSuccess}>
                  <CheckCircleOutlined /> {formatMessage({ id: '扫码成功' })}
                </div>
              ) : scanStep === 2 ? (
                <div style={qrcodeSuccess}>
                  <span style={{ color: '#666666' }}>{formatMessage({ id: '二维码已失效' })}</span>{' '}
                  <br /> <a onClick={() => getScanCode()}>{formatMessage({ id: '请点击刷新' })}</a>
                </div>
              ) : null}
              <div
                style={{
                  marginTop: '3px',
                  fontSize: '14px',
                  textAlign: 'center',
                }}
              >
                {content}
              </div>
            </div>
          )}
          {loginType === 'qrcode' ? null : (
            <div
              style={{
                marginBlockEnd: 24,
              }}
            >
              <Link to="/forget/password" onClick={() => logSave(LogType.login1)}>
                {formatMessage({ id: '忘记密码' })}
              </Link>
              <a
                style={{
                  float: 'right',
                  color: '#52c41a',
                }}
                onClick={() => setLoginType('mobile')}
              >
                {formatMessage({ id: '短信验证码登录' })}
              </a>
            </div>
          )}
          {loginType === 'qrcode' ? null : (
            <Button
              size="large"
              type="primary"
              htmlType="submit"
              style={{
                width: '100%',
              }}
            >
              {formatMessage({ id: '登录' })}
            </Button>
          )}
        </LoginForm>
      )}

      {showCaptcha && (
        <div
          style={{
            position: 'absolute',
            top: '9rem',
            left: '3rem',
            width: '95%',
            height: '400px',
            zIndex: '2',
          }}
        >
          <div
            style={{
              width: '300px',
              margin: '0px auto',
              backgroundColor: '#fff',
              boxShadow: '0 2px 10px 0 #333',
            }}
          >
            <ImageCodeView
              ref={imageCodeRef}
              imageUrl={url}
              fragmentUrl={fragmentUrl}
              offsetY={y}
              onCloseCode={func => {
                setShowCaptcha(false);
                if (func) func();
              }}
              onReload={onReload}
              onMatch={onMatch}
              imageWidth={300}
              imageHeight={171}
              fragmentSize={55}
            />
          </div>
        </div>
      )}
      <ModalLogin modalRef={modalRef} onCancel={handleModalCancel} {...props} />
    </>
  );
};
export default connect(({ login, loading, user }) => ({
  login,
  getUserInfoLoading: loading.effects['user/getUserInfo'],
  submitting: loading.effects['login/login'],
  merchantStateLoading: loading.effects['user/qryMerchantState'],
  getScanCodeLoading: loading.effects['login/getScanCode'],
}))(Login);
