import React, { Component, Fragment } from 'react';
import { LoadingOutlined, ReloadOutlined } from '@ant-design/icons';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Input, Button, Modal, Pagination, Radio, message, Spin, Space } from 'antd';
import ProTableList from '@/components/ProTable';
import Details from './Details';
import router from 'umi/router';
import { connect } from 'dva';
import { formatMessage } from 'umi-plugin-react/locale';
import { logSave, LogType } from '@/utils/logSave';

let dataCopy; //{formatMessage({id: '全局变量'})}

@Form.create()
@connect(({ anomalyOrder, homePage, user, loading }) => ({
  anomalyOrder,
  homePage,
  getBalanceLoading: loading.effects['anomalyOrder/getExpressByNumbers'],
  TmsStatusLoading: loading.effects['anomalyOrder/getTmsStatusByNumbers'],
  gcancelLoading: loading.effects['anomalyOrder/cancelInterceptNormal'],
  peratorLoading: loading.effects['anomalyOrder/interceptLog'],
  createBillLoading: loading.effects['anomalyOrder/createIntercept'],
}))
class mainTable extends Component {
  constructor(props) {
    super(props);
    this.state = {
      checkedNum: '0', //{formatMessage({id: '选择的条数'})}
      dataSource: [], //{formatMessage({id: '列表数据'})}
      total: 0, //{formatMessage({id: '总数据条数'})}
      modelTitleList: [], //{formatMessage({id: '选择的运单号数列'})}
      visible: false, //{formatMessage({id: '弹窗显示'})}
      visibleCancle: false, //{formatMessage({id: '取消截留按钮'})}
      loadingT: false, //table
      isBtnShow: true, //{formatMessage({id: '按钮在选择后点击'})}
      isBtnLoding: false, //{formatMessage({id: '搜索等待动画'})}
      TmsStatusList: [],
      isShowSpin: false, //{formatMessage({id: '表头刷新等待动画'})}
      MFvisable: false, //{formatMessage({id: '备注选择可见'})}

      selectedchebox: [],
      receiveStatus: '',
    };
  }

  modalRef = React.createRef();

  componentDidMount() {
    if (this.props.anomalyOrder.identifier === 'other') {
      this.props.form.setFieldsValue({
        epCodes: undefined,
      });
    }
    if (
      this.props.anomalyOrder.identifier === 'detail' &&
      this.props.anomalyOrder.historyPayload?.epCodes
    ) {
      this.state.loadingT = true;
      this.shipmentDataList(this.props.anomalyOrder.historyPayload.epCodes);
      const cowListData = this.props.anomalyOrder.historyPayload.epCodes
        .toString()
        .replace(/[,，]/g, '\r\n');
      this.props.form.setFieldsValue({
        epCodes: cowListData,
      });
    }

    if (this.props.location?.state?.name != undefined) {
      this.modalRef.current?.showModal({
        name: this.props.location.state.name,
        wayBillNumber: this.props.location.state.wayBillNumber,
      });
    }
  }

  componentWillUnmount() {
    const { dispatch } = this.props;
    dispatch({
      type: 'anomalyOrder/saveindentifier',
      payload: {
        identifier: 'other',
      },
    });
  }

  // list{formatMessage({id: '数据'})}
  shipmentDataList = epCodesList => {
    const { dispatch } = this.props;
    dispatch({
      type: 'anomalyOrder/getExpressByNumbers',
      payload: {
        epCodes: epCodesList,
      },
      callback: result => {
        this.state.loadingT = false;
        if (result.success) {
          (dataCopy = result.data),
            this.setState({
              dataSource: result.data,
              total: result.data.total,
              isBtnLoding: false,
            });
          if (result.data?.notYourEpCodes?.length > 0) {
            Modal.confirm({
              title: (
                <div>
                  {formatMessage({ id: '其中' })}
                  {result.data.notYourEpCodes.length <= 5 ? (
                    <span>
                      {result.data.notYourEpCodes.map((item, index) => {
                        return <span key={index}>{item} , </span>;
                      })}
                    </span>
                  ) : (
                    <span>
                      {result.data.notYourEpCodes.map((item, index) => {
                        if (index < 5) {
                          return <span key={index}>{item} , </span>;
                        }
                      })}
                      ...
                    </span>
                  )}
                  {formatMessage({ id: '不是您的单号' })}
                </div>
              ),
              okText: formatMessage({ id: '确认' }),
              cancelText: formatMessage({ id: '取消' }),
            });
          }
        } else {
          this.setState({
            dataSource: [],
            total: 0,
            isBtnLoding: false,
          });
          message.error(result.message);
        }
      },
    });
  };
  antIcon = (<LoadingOutlined style={{ fontSize: 16 }} spin />);
  tableColumns = [
    {
      title: formatMessage({ id: '序号' }),
      width: 80,
      render: (text, record, index) => `${index + 1}`,
    },
    {
      title: formatMessage({ id: '运单信息' }),
      align: 'left',
      width: '410px',
      render: (text, record) => {
        return (
          <div>
            <p style={{ color: '#52c41a' }}>
              {record.orderTime ? (
                <span>
                  {formatMessage({ id: '制单时间' })}：{record.orderTime}
                </span>
              ) : null}
              {record.epCode ? (
                <span style={{ marginLeft: '20px' }}>
                  {formatMessage({ id: '运单号' })}：{record.epCode}
                </span>
              ) : null}
            </p>
            {record.orderNo ? (
              <p>
                {formatMessage({ id: '订单号' })}:{record.orderNo}
              </p>
            ) : null}
          </div>
        );
      },
    },
    {
      title: formatMessage({ id: '制单账号' }),
      width: '300',
      dataIndex: 'merchantShipper',
      key: 'merchantShipper',
    },
    {
      title: formatMessage({ id: '产品名称' }),
      width: '300',
      dataIndex: 'channel',
      key: 'channel',
    },
    {
      title: `${formatMessage({ id: '目的国' })}/${formatMessage({ id: '地区' })}`,
      dataIndex: 'destination',
      key: 'destination',
      render: (text, record) => {
        return (
          <span>
            {record.destination}({record.destinationCode})
          </span>
        );
      },
    },
    // {
    //   title: formatMessage({id: '运单状态'}),
    //   dataIndex: 'ejfStatus',
    //   key: 'ejfStatus',
    //   render: text => <div>{text == 0 ? formatMessage({id: '已取消'})}' : formatMessage({id: '已制单'})}'}</di,
    // },
    // {
    //   title: formatMessage({id: '是否录入'}),
    //   dataIndex: 'entering',
    //   key: 'entering',
    //   render: text => <div>{text == true ? formatMessage({id: '是'})}' : formatMessage({id: '否'})}'}</di,
    // },
    {
      title: () => {
        return (
          <span>
            <span>{formatMessage({ id: '截留状态' })}</span>
            {this.state.isShowSpin ? (
              <Spin indicator={this.antIcon} style={{ marginLeft: '20px' }} />
            ) : (
              <ReloadOutlined onClick={() => this.isSuccess()} className="table_reload" />
            )}
          </span>
        );
      },
      dataIndex: 'apply',
      key: 'apply',
      render: (text, record) => (
        <div>
          {text === true ? (
            <div>{formatMessage({ id: '已截留' })}</div>
          ) : (
            <div>{formatMessage({ id: '未截留' })}</div>
          )}
        </div>
      ),
    },
    {
      title: formatMessage({ id: '截留记录' }),
      key: 'operation',
      render: (text, record) => {
        return (
          <a onClick={() => this.handleClick(text, record)}>{formatMessage({ id: '详情' })}</a>
        );
      },
    },
  ];

  // {formatMessage({id: '详情跳转'})}
  handleClick = (val, rec) => {
    this.modalRef.current?.showModal({
      name: formatMessage({ id: '运单截留记录' }),
      wayBillNumber: rec.epCode,
    });
  };
  // {formatMessage({id: '表头刷新'})}
  isSuccess = () => {
    this.setState({
      isShowSpin: true,
    });
    if (this.state.dataSource.length > 0) {
      this.shipmentTmsStatus();
    } else {
      setTimeout(() => {
        this.setState({
          isShowSpin: false,
        });
      }, 1000);
    }
  };
  shipmentTmsStatus = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'anomalyOrder/getTmsStatusByNumbers',
      payload: {
        epCodes: this.state.TmsStatusList,
      },
      callback: result => {
        this.setState({
          loadingT: false,
          isShowSpin: false,
        });
        if (result.success) {
          dataCopy.map((item, index) => {
            if (item.epCode == result.data.interceptStatuses[index].epCode) {
              item.apply = result.data.interceptStatuses[index].status;
              item.applyRemark = result.data.interceptStatuses[index].remark;
            }
          });
          this.setState({
            dataSource: dataCopy,
          });
        } else {
        }
      },
    });
  };
  // {formatMessage({id: '创建截留运单'})} {formatMessage({id: '弹出框'})}------
  handleCancel = e => {
    this.setState({
      visible: false,
    });
  };
  handleOk = e => {
    e.preventDefault();
    let keyform = ['optionWay', 'convertNumber', 'remark', 'epCodes'];
    this.props.form.validateFields(keyform, (err, values) => {
      if (!err) {
        let detailList = [];
        let epCodelist = [];
        epCodelist = values.epCodes.split(/[(\r\n)\r\n]+/);
        if (values.optionWay === 3) {
          if (this.state.modelTitleList.length > 1) {
            message.error(formatMessage({ id: '请选择一个运单号进行截留' }));
            return;
          } else {
            if (this.state.modelTitleList.find(item => item.epCode === values.convertNumber)) {
              message.error(formatMessage({ id: '新单号不能和老单号一致' }));
              return;
            } else {
              this.state.modelTitleList.map(item => {
                let obj = {
                  optionWay: values.optionWay,
                  remark: values.remark,
                  accountCode: item.merchantShipper,
                  wayBillNo: item.epCode,
                  convertNumber: values.convertNumber ? values.convertNumber : '',
                };
                detailList.push(obj);
              });
            }
          }
        } else {
          this.state.modelTitleList.map(item => {
            let obj = {
              optionWay: values.optionWay,
              remark: values.remark,
              accountCode: item.merchantShipper,
              wayBillNo: item.epCode,
              convertNumber: values.convertNumber ? values.convertNumber : '',
            };
            detailList.push(obj);
          });
        }
        this.createBill(detailList, epCodelist);
      }
    });
  };
  // {formatMessage({id: '弹窗单选'})}
  onChangeM = val => {
    if (val.target.value == 3) {
      this.setState({
        MFvisable: true,
      });
    } else {
      this.setState({
        MFvisable: false,
      });
    }
  };
  // {formatMessage({id: '创建接口'})} {formatMessage({id: '申请截留'})}
  createBill = (detailList, epCodelist) => {
    const { dispatch } = this.props;
    dispatch({
      type: 'anomalyOrder/createIntercept',
      payload: {
        sourceType: 0,
        detail: detailList,
      },
      callback: result => {
        if (result.success) {
          message.warning(result.message);
          this.setState({
            visible: false,
          });
          this.shipmentDataList(epCodelist);
        } else {
          message.error(result.message);
        }
      },
    });
  };
  // ------------{formatMessage({id: '取消接口系列'})}-----------------------------------
  handleCnacleClick = () => {
    logSave(LogType.interceptApply4);
    this.setState({
      visibleCancle: true,
    });
  };
  handleOkCancle = e => {
    this.setState({
      visibleCancle: false,
    });
    let epCodeArr = [];
    let epCodeType = [];
    let epCodeUserId = [];
    this.state.modelTitleList.map(item => {
      epCodeArr.push(item.epCode);
      epCodeType.push(item.orderType);
      epCodeUserId.push(item.merchantShipper);
    });
    this.cancelWithWayBill(epCodeArr, epCodeType, epCodeUserId);
  };
  handleCancelC = e => {
    this.setState({
      visibleCancle: false,
    });
  };
  // {formatMessage({id: '取消接口'})}
  cancelWithWayBill = (epCodeList, epCodeType, epCodeUserId) => {
    const { dispatch, form } = this.props;
    let { epCodes } = form.getFieldsValue();
    let arr = [];
    dispatch({
      type: 'anomalyOrder/cancelInterceptNormal',
      payload: {
        wayBillNo: epCodeList,
        wayBillType: epCodeType,
        wayBillUserId: epCodeUserId,
        sourceType: 0,
      },
      callback: result => {
        if (result.success) {
          message.success(result.message);
          arr = epCodes.split(/[(\r\n)\r\n]+/);
          this.shipmentDataList(arr);
        } else {
          message.error(result.message);
        }
      },
    });
  };
  // ---------------------------------------------
  // {formatMessage({id: '查询'})}
  handleSubmit = e => {
    e.preventDefault();
    logSave(LogType.interceptApply1);
    let searchKeyForm = ['epCodes'];
    this.props.form.validateFieldsAndScroll(searchKeyForm, (err, values) => {
      if (!err) {
        let ArrList = values?.epCodes || '';
        const ISerr = ArrList.replace(/[\r\n]/g, '');
        const epCodesList = ArrList.split(/[(\r\n)\r\n]+/);
        this.setState({
          isBtnLoding: true,
          isBtnShow: true,
          TmsStatusList: epCodesList, //{formatMessage({id: '保存一下数据'})}，{formatMessage({id: '刷新请求使用'})}
        });
        const valuerlurs = new RegExp('^[0-9a-zA-Z ]+$');
        const errIndexList = [];
        const errItemList = [];
        if (valuerlurs.test(ISerr)) {
          this.setState({
            selectedchebox: [],
            loadingT: true,
          });
          this.shipmentDataList(epCodesList);
        } else {
          epCodesList.map((item, index) => {
            if (!valuerlurs.test(item)) {
              errItemList.push(item);
              errIndexList.push(index);
            }
          });
          Modal.confirm({
            title: (
              <div>
                {formatMessage({ id: '在第' })}
                {errIndexList.map(item => {
                  return <span>{item + 1},</span>;
                })}
                {formatMessage({ id: '行中' })}，{formatMessage({ id: '单号有误' })}，
                {formatMessage({ id: '请重新确认' })}
              </div>
            ),
            okText: formatMessage({ id: '确认' }),
            cancelText: formatMessage({ id: '取消' }),
          });
        }
      }
    });
  };
  // {formatMessage({id: '表单验证'})}
  validateToNextPassword = (rule, value, callback) => {
    if (value) {
      const str = value.replace(/[\r\n]/g, '');
      var counts = value.split('\n').length - 1;
      let valrlurs = new RegExp('^[0-9a-zA-Z ]+$');
      if (counts >= 50) {
        callback(`${formatMessage({ id: '最多输入' })}50${formatMessage({ id: '单' })}`);
      } else if (value && !valrlurs.test(str)) {
        callback(`${formatMessage({ id: '不能出现数字及字母外的字符' })}`);
      } else {
        callback();
      }
    } else {
      callback('');
    }
  };

  render() {
    //state
    const {
      dataSource,
      total,
      isBtnShow,
      loadingT,
      isBtnLoding,
      visibleCancle,
      isShowSpin,
      checkedNum,
      modelTitleList,
      MFvisable,
      visible,
    } = this.state;
    const { createBillLoading } = this.props;
    // input
    const { TextArea } = Input;
    // form{formatMessage({id: '绑定'})}
    const { getFieldDecorator, getFieldsError, getFieldError, isFieldTouched } = this.props.form;
    // table{formatMessage({id: '多选'})}
    const rowSelection = {
      selectedRowKeys: this.state.selectedchebox,
      // getCheckboxProps: record => ( {
      //   disabled: record.entering === true || record.ejfStatus == 0,
      // } ),
      onChange: (selectedRowKeys, selectedRows) => {
        this.state.selectedchebox = selectedRowKeys;
        this.setState({
          selectedRowKeys: this.state.selectedchebox,
        });
        if (selectedRows.length < 1) {
          this.setState({
            isBtnShow: true,
            checkedNum: selectedRows.length,
            modelTitleList: selectedRows,
            TmsStatusList: selectedRows,
          });
        } else {
          this.setState({
            isBtnShow: false,
            checkedNum: selectedRows.length,
            modelTitleList: selectedRows,
          });
        }
      },
    };
    // {formatMessage({id: '弹窗'})}
    const radioStyle = {
      display: 'block',
      height: '30px',
      fontSize: '16px',
      lineHeight: '30px',
    };
    const Pstyle = {
      style: {
        marginLeft: '60px',
      },
    };
    const formItemLayout = {
      labelCol: { span: 6 },
      wrapperCol: { span: 14 },
    };
    return (
      <div>
        <div className="p-5 bg-white flex justify-start ">
          {/* <div className="text-base mr-11 font-semibold">{formatMessage({id: '运单号'})}</div> */}
          <div>
            <Form layout="inline">
              <Form.Item label={formatMessage({ id: '运单号' })}>
                {getFieldDecorator('epCodes', {
                  rules: [
                    { required: true, message: formatMessage({ id: '搜索运单号不能为空' }) },
                    {
                      validator: this.validateToNextPassword,
                    },
                  ],
                })(
                  <TextArea
                    allowClear
                    placeholder={`${formatMessage({ id: '最多' })}50${formatMessage({
                      id: '单',
                    })},${formatMessage({ id: '每单一行' })}`}
                    style={{ width: '260px', height: '120px' }}
                  />
                )}
              </Form.Item>
            </Form>
          </div>
          <Space direction="vertical" size="large" align="start" className="pt-6">
            <Button type="primary" loading={isBtnLoding} onClick={this.handleSubmit}>
              {formatMessage({ id: '查询' })}
            </Button>
            <Button
              onClick={() => {
                logSave(LogType.interceptApply2);
                this.props.form.resetFields();
                this.setState({
                  dataSource: [],
                  checkedNum: 0,
                  total: 0,
                  isBtnLoding: false,
                  loadingT: false,
                  selectedchebox: [],
                  isBtnShow: true,
                });
              }}
            >
              {formatMessage({ id: '重置' })}
            </Button>
          </Space>
        </div>
        <div style={{ background: '#fff', padding: '10px', marginTop: '16px' }}>
          <ProTableList
            style={{ minHeight: '500px' }}
            rowSelection={rowSelection}
            columns={this.tableColumns}
            dataSource={dataSource}
            rowKey={record => record.epCode}
            loading={loadingT}
            bordered
            scroll={{ x: 1500, y: 500 }}
            title={() => {
              return (
                <div className="overflow-hidden">
                  <span className="text-base font-semibold mr-5">
                    {formatMessage({ id: '查询列表' })}
                  </span>
                  <span>
                    {formatMessage({ id: '当前共' })} {total} {formatMessage({ id: '条订单' })}，
                    {formatMessage({ id: '已选择' })} {checkedNum} {formatMessage({ id: '条' })}
                  </span>
                  <Button
                    disabled={isBtnShow}
                    type="primary"
                    className="py-1.5 px-5 mr-2 float-right"
                    onClick={() => {
                      logSave(LogType.interceptApply3);
                      this.setState({
                        visible: true,
                      });
                    }}
                  >
                    {formatMessage({ id: '申请截留' })}
                  </Button>
                  <Button
                    disabled={isBtnShow}
                    type="primary"
                    className="py-1.5 px-5 mr-2 float-right"
                    onClick={() => this.handleCnacleClick()}
                  >
                    {formatMessage({ id: '取消截留' })}
                  </Button>
                </div>
              );
            }}
            pagination={false}
            search={false}
            toolBarRender={false}
          ></ProTableList>
        </div>
        {/*{formatMessage({id: '弹窗'})}1*/}
        <Modal
          title={formatMessage({ id: '确认对以下单号取消截留' })}
          visible={visibleCancle}
          closable={false}
          onOk={this.handleOkCancle}
          onCancel={this.handleCancelC}
          footer={[
            <Button type="primary" onClick={this.handleOkCancle}>
              {formatMessage({ id: '确定' })}
            </Button>,
            <Button onClick={this.handleCancelC}>{formatMessage({ id: '取消' })}</Button>,
          ]}
        >
          <div>
            {modelTitleList.length > 0 ? (
              <div style={{ display: 'flex', maxHeight: '300px', overflow: 'auto' }}>
                <div style={{ marginLeft: '40px' }}>
                  {modelTitleList.map((item, index) => {
                    if (index % 2 == 0) {
                      return <p key={index}>{item.epCode}</p>;
                    }
                  })}
                </div>
                <div style={{ marginLeft: '100px' }}>
                  {modelTitleList.map((item, index) => {
                    if (index % 2 != 0) {
                      return <p key={index}>{item.epCode}</p>;
                    }
                  })}
                </div>
              </div>
            ) : (
              <div>{formatMessage({ id: '请您选择取消截留单号' })}</div>
            )}
          </div>
        </Modal>
        {/*{formatMessage({id: '弹窗'})}2*/}
        <Modal
          title={`${formatMessage({ id: '选中' })} ${this.state.checkedNum} ${formatMessage({
            id: '票运件',
          })}`}
          visible={visible}
          width={600}
          confirmLoading={createBillLoading}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          afterClose={e => {
            this.props.form.resetFields(['optionWay', 'remark']);
            this.setState({
              MFvisable: false,
            });
          }}
          footer={[
            <Button type="primary" onClick={this.handleOk} loading={createBillLoading}>
              {formatMessage({ id: '确定' })}
            </Button>,
            <Button onClick={this.handleCancel}>{formatMessage({ id: '取消' })}</Button>,
          ]}
        >
          <Form {...formItemLayout}>
            <Form.Item label={formatMessage({ id: '处理方式' })}>
              {getFieldDecorator('optionWay', {
                rules: [
                  {
                    required: true,
                    message: formatMessage({ id: '请选择处理方式' }),
                  },
                ],
              })(
                <Radio.Group onChange={val => this.onChangeM(val)}>
                  {/* <Radio style={radioStyle} value={1}>
                    {formatMessage({id: '截留后退回'})}
                  </Radio>
                  <Radio style={radioStyle} value={3}>
                    {formatMessage({id: '截留后换单'})}
                  </Radio>
                  <Radio style={radioStyle} value={4}>
                    {formatMessage({id: '截留放置待发'})}
                  </Radio> */}
                  <Radio style={radioStyle} value={104}>
                    {formatMessage({ id: '退回' })}-{formatMessage({ id: '司机' })}
                  </Radio>
                  <Radio style={radioStyle} value={106}>
                    {formatMessage({ id: '退回' })}-{formatMessage({ id: '自取' })}
                  </Radio>
                  <Radio style={radioStyle} value={1051}>
                    {formatMessage({ id: '退回' })}-{formatMessage({ id: '快递寄付' })}
                  </Radio>
                  <Radio style={radioStyle} value={1052}>
                    {formatMessage({ id: '退回' })}-{formatMessage({ id: '快递到付' })}
                  </Radio>
                  <Radio style={radioStyle} value={3}>
                    {formatMessage({ id: '截留后换单' })}
                  </Radio>
                  <Radio style={radioStyle} value={4}>
                    {formatMessage({ id: '截留放置待发' })}
                  </Radio>
                </Radio.Group>
              )}
            </Form.Item>
            {MFvisable ? (
              <Form.Item label={formatMessage({ id: '新单号' })}>
                {getFieldDecorator('convertNumber', {
                  rules: [
                    { required: true, message: formatMessage({ id: '请填写新的单号' }) },
                    {
                      pattern: '^[0-9a-zA-Z ]+$',
                      message: formatMessage({ id: '不能出现数字及字母外的字符' }),
                    },
                  ],
                })(<Input />)}
              </Form.Item>
            ) : null}
            <Form.Item label={formatMessage({ id: '备注' })}>
              {getFieldDecorator('remark')(<TextArea rows={4} />)}
            </Form.Item>
          </Form>
        </Modal>
        <Details ref={this.modalRef} {...this.props} />
      </div>
    );
  }
}

export default mainTable;
