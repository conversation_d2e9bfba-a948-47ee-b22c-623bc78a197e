import React, { Component, Fragment } from 'react';
import router from 'umi/router';
import { connect } from 'dva';
import { Redirect } from 'umi';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {
  Input,
  Breadcrumb,
  Tabs,
  Card,
  Table,
  Button,
  DatePicker,
  message,
  Select,
  Row,
  Col,
  Upload,
  Space,
  Radio,
  Alert,
  Typography,
  Badge,
  Tooltip,
  TreeSelect,
} from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import moment from 'moment';
import ExportJsonExcel from 'js-export-excel';
import StandardFormRow from '@/components/StandardFormRow';
import TagSelect from '@/components/TagSelect';
import SingleTagSelect from '@/components/SingleTagSelect';
import { portalUrl } from '../../../../../../config/defaultSettings';
import { downloadFile } from '@/utils/problemDownload';
import styles from './index.less';
import { formatMessage } from 'umi-plugin-react/locale';
import { mergeRowsByField } from '@/utils/utils';
import { downloadFile as productDownload } from '@/utils/download';

import { wareTypeList } from '@/utils/commonConstant';
import ProTableList from '@/components/ProTable';
import OldAbnormalDetail from './components/OldAbnormalDetail';
import AbnormalDetail from './components/AbnormalDetail';
import AbnormalDispose from './components/AbnormalDispose';
import AbnormalBatch from './components/AbnormalBatch';
import AbnormalBatchOver from './components/AbnormalBatchOver';
import AbnormalWareDetail from './components/AbnormalWareDetail';
import AbnormalOverDetail from './components/AbnormalOverDetail';
import AbnormalExpressDetail from './components/AbnormalExpressDetail';
import AbnormalProductDispose from './components/AbnormalProductDispose';
import AbnormalProductDetail from './components/AbnormalProductDetail';
import NotOutBill from '@/pages/FinancialManagement/MyBill/Overseas/components/notSpotPayCycle/notOutBill';
import { logSave, LogType } from '@/utils/logSave';
import loadPinYinInit from '@/utils/ChineseHelper';

const { TabPane } = Tabs;
const { TextArea } = Input;
const FormItem = Form.Item;
const { RangePicker } = DatePicker;

let waybillSearchValue = undefined;
let selectKey = '0';
// {formatMessage({id: '时间'})}
const startT = moment()
  .subtract(6, 'day')
  .format('YYYY-MM-DD');
const endT = moment().format('YYYY-MM-DD');
let timeChange = false;

@connect(({ anomalyOrder, loading }) => ({
  anomalyOrder,
  loading: loading.effects['anomalyOrder/problemPiecesList'],
  returnLoading: loading.effects['anomalyOrder/returnOraginList'],
  returnExportLoading: loading.effects['anomalyOrder/returnReceiptExcelDownload'],
  outsideAndWareExportLoading:
    loading.effects['anomalyOrder/outsideTheWarehousePortionDownload'] ||
    loading.effects['anomalyOrder/outsideTheWarehouseDownload'],
  outsideSubmitLoading:
    loading.effects['anomalyOrder/outsideTheWarehouseCustomerHandle'] ||
    loading.effects['anomalyOrder/outsideTheWarehouseCustomerBatchHandle'],
  wareLoading: loading.effects['anomalyOrder/outsideTheWarehouseSelectPage'],
  oldtableloding: loading.effects['anomalyOrder/problemPiecesOldList'],
  pageInitLoading:
    loading.effects['anomalyOrder/makeAccountLoad'] ||
    loading.effects['anomalyOrder/problemPiecesAllAbnormalCause'] ||
    loading.effects['anomalyOrder/problemPiecesGetHandleMode'] ||
    loading.effects['anomalyOrder/outsideTheWarehouseGetAbnormalType'],
  abnormalDisposeSubmitLoading:
    loading.effects['anomalyOrder/problemPieceschooseHandleType'] ||
    loading.effects['anomalyOrder/orderInformation'],
  cardLoading: loading.effects['anomalyOrder/problemPiecesGetHandleMode'],
  abnormalBatchOverSubmitLoading: loading.effects['anomalyOrder/outsideTheWarehouseCustomerHandle'],
  uploadLoading: loading.effects['anomalyOrder/problemParcelUploading'],
  computationalLoading: loading.effects['anomalyOrder/computational'],
  expressLoading: loading.effects['anomalyOrder/commercialExpressList'],
  expressDetailLoading: loading.effects['anomalyOrder/commercialExpressDetail'],
  expressDisposeLoading: loading.effects['anomalyOrder/commercialExpressDispose'],
  productLoading: loading.effects['anomalyOrder/productNameChangeQueryList'],
  productBatchLoading: loading.effects['anomalyOrder/productNameChangeBatchHandle'],
  productDetailLoading: loading.effects['anomalyOrder/productNameChangeDetail'],
}))
@Form.create()
class ProblemPieces extends Component {
  // eslint-disable-next-line react/sort-comp
  constructor(props) {
    super(props);
    this.state = {
      isSignFor: '1', // {formatMessage({id: '退件签收的'})}tab
      selectWare: '1', // {formatMessage({id: '仓外'})} 0 {formatMessage({id: '全部'})}  1 {formatMessage({id: '待处理'})}  2{formatMessage({id: '已处理'})}
      selectOverseas: '1',
      activeKey: '0',
      activeExpressKey: '0',
      activeProductKey: '1', // 品名异常状态key
      completeCount: 0,
      untreatedCount: 0,
      account: [],
      shipperInitOpt: [],
      // selectedRowKeys: [],
      isExpanded: false,
      startDate: startT,
      endDate: endT,
      // {formatMessage({id: '搜索数据'})}
      selectValue: '1',
      completeCountNum: '',
      untreatedCountNum: '',
      processingList: [],
      anomalouscauseList: [],
      workOrderAndywCustomerCode: [],
      isClickSelect: false, // {formatMessage({id: '初始化查询按钮'})}
      selectedRowspra: [],
      btndisable: false,
      selectedRowKeys: [],
      validateStatus: 'success',
      validateTypeStatus: 'success',
      currentAuthorityValue: null,
      normalSelectAbnormal: [0],
      fileList: [],
      abnormalList: [
        {
          id: 0,
          name: formatMessage({ id: '仓内异常件' }),
        },
        {
          id: 5,
          name: formatMessage({ id: '更改品名' }),
        },
        {
          id: 4,
          name: formatMessage({ id: '商快费用确认' }),
        },
        {
          id: 2,
          name: formatMessage({ id: '仓外异常件' }),
        },
        {
          id: 3,
          name: formatMessage({ id: '海外重派' }),
        },
        {
          id: 1,
          name: formatMessage({ id: '退件签收' }),
        },
      ],
      pageIndex: 1,
      pageSize: 10,
      commercialExpressDataList: [], // {formatMessage({id: '商快费用确认数据'})}
      commercialExpressTotalNum: 0, // {formatMessage({id: '商快费用确认数据总数'})}
      productNameDataList: [], // {formatMessage({id: '品名异常'})}
      productNameTotalNum: 0, // {formatMessage({id: '品名异常总数'})}
      productPendingCount: 0, // 待处理
      productEndCount: 0, // 处理完成
      productTotalCount: 0, // 全部
      returnDataList: [], // {formatMessage({id: '退件签收数据'})}
      returnTotalNum: 0, // {formatMessage({id: '退件签收数据总数'})}
      warePageIndex: 1,
      wareDataList: [], // {formatMessage({id: '仓外异常件数据'})}
      wareTotalNum: 0, // {formatMessage({id: '仓外异常件总数'})}
      overPageIndex: 1,
      overDataList: [], // {formatMessage({id: '海外重派数据'})}
      overTotalNum: 0, // {formatMessage({id: '海外重派总数'})}
      selectedRowWare: [],
      selectedRowOver: [], // {formatMessage({id: '海外重派选择多条'})}
      abnormalTypeList: [],
      exportLoading: false, // {formatMessage({id: '仓内异常件已处理导出按钮'})}loading
      isFreeStatus: true, // {formatMessage({id: '商户是否冻结'})}
      channels: [],
      countryList: [],
      abnormalTree: [],
    };
  }

  oldDetailModal = React.createRef(); // {formatMessage({id: '历史仓内异常件详情'})}
  problemDetailModal = React.createRef(); // {formatMessage({id: '仓内异常件详情'})}
  problemDisposeModal = React.createRef(); // {formatMessage({id: '仓内异常件处理页面'})}
  outsideBatchModal = React.createRef(); // {formatMessage({id: '仓外异常件批量处理'})}
  outsideDetailModal = React.createRef(); // {formatMessage({id: '仓外异常件详情'})}
  wareBatchModal = React.createRef(); // {formatMessage({id: '海外重派处理'})}
  wareDetailModal = React.createRef(); // {formatMessage({id: '海外重派详情'})}
  expressDetailModal = React.createRef(); // {formatMessage({id: '商业快递详情'})}
  productDisposeModal = React.createRef(); // 品名异常处理
  productDetailModal = React.createRef(); // 品名工单详情
  // {formatMessage({id: '表头'})}
  oldColumns = [
    {
      title: formatMessage({ id: '制单账号' }),
      dataIndex: 'customerCode',
      width: 100,
    },
    {
      title: formatMessage({ id: '运单号' }),
      dataIndex: 'waybillNumber',
      width: 150,
      render: (text, record) => {
        return (
          <a
            onClick={() => {
              this.oldDetailModal.current.openModal(record);
              // let obj = JSON.stringify(record);
              // router.push(`/express/anomalyOrder/OldAbnormalDetail/${obj}`);
            }}
          >
            {text}
          </a>
        );
      },
    },
    {
      title: formatMessage({ id: '订单号' }),
      dataIndex: 'orderCode',
      width: 160,
    },
    {
      title: formatMessage({ id: '产品名称' }),
      dataIndex: 'productName',
      width: 160,
    },
    {
      title: formatMessage({ id: '目的国' }),
      dataIndex: 'regionName',
      width: 140,
    },
    {
      title: formatMessage({ id: '异常原因' }),
      dataIndex: 'typeName',
      width: 250,
    },
    {
      title: formatMessage({ id: '退回类型' }),
      dataIndex: 'returnTypeName',
      width: 100,
    },
    {
      title: formatMessage({ id: '处理方式' }),
      dataIndex: 'customerHandleMode',
      width: 100,
      render: text => <div>{this.getCustomerHandleModeString(text)}</div>,
    },
    {
      title: formatMessage({ id: '状态' }),
      dataIndex: 'status',
      width: 100,
      render: text => {
        return text === '1' ? formatMessage({ id: '待处理' }) : formatMessage({ id: '已处理' });
      },
    },
    {
      title: formatMessage({ id: '创建时间' }),
      dataIndex: 'createTime',
      width: 150,
    },
    {
      title: formatMessage({ id: '处理时间' }),
      dataIndex: 'operatorTime',
      width: 150,
    },
  ];
  // {formatMessage({id: '详情'})}
  detail = record => {
    this.problemDetailModal.current.openModal(record);
    // router.push(`/express/anomalyOrder/abnormalDetail/${obj}`);
  };

  batchProduct = record => {
    logSave(LogType.editProductName5);
    this.productDisposeModal.current.openModal(record);
  };

  productDetail = record => {
    this.productDetailModal.current.openModal(record);
  };

  // {formatMessage({id: '处理'})}
  disposeAction = record => {
    if (record.exceptionTypeName == '录入冻结') {
      message.error(
        `${formatMessage({ id: '请安排付清运费后处理异常件' })}，${formatMessage({
          id: '即日起三个月内未处理',
        })}，${formatMessage({ id: '货物将默认弃件处理' })}`,
        1
      );
    } else {
      const { workOrderAndywCustomerCode } = this.state;
      const para = {
        ...record,
        recommendSolution: record.recommendSolution,
        workOrderNo: record.workOrderNo,
        waybillNumber: record.waybillNumber,
        ywCustomerCode: record.ywCustomerCode,
        exceptionTypeName: record.exceptionTypeName,
        exceptionTypeId: record.exceptionTypeId,
        expressLength: record.expressLength,
        expressWidth: record.expressWidth,
        expressHigh: record.expressHigh,
        weight: record.weight,
        status: record.status,
      };
      this.problemDisposeModal.current.openModal(para);
      // router.push(`/express/anomalyOrder/abnormalDispose/${para}/${record.status}`);
    }
  };
  // {formatMessage({id: '多个处理'})}
  manyprocessing = () => {
    const { workOrderAndywCustomerCode, activeKey, selectedRowspra } = this.state;
    new Promise(function(resolve, reject) {
      selectedRowspra.map(item => {
        if (item.exceptionTypeName == '录入冻结') {
          return reject('1'); // {formatMessage({id: '数据处理出错'})};
        }
        if (item.status == '1') {
          return reject('2'); // {formatMessage({id: '数据处理出错'})};
        }
      });
      // {formatMessage({id: '一段耗时的异步操作'})}
      resolve('成功'); // {formatMessage({id: '数据处理完成'})}
    }).then(
      res => {
        const para = { workOrderAndywCustomerCode, status: activeKey };
        this.problemDisposeModal.current.openModal(para);
        // router.push(`/express/anomalyOrder/abnormalDispose/${para}/${activeKey}`);
      }, // {formatMessage({id: '成功'})}
      err => {
        if (err == '1') {
          message.error(
            `${formatMessage({ id: '异常原因为录入冻结时' })}，${formatMessage({
              id: '不支持批量处理',
            })}。${formatMessage({ id: '请您选择其他的数据进行操作' })}！`
          );
        } else {
          message.error(
            `${formatMessage({ id: '请选择未处理的数据进行处理' })}，${formatMessage({
              id: '已处理的不可再次处理',
            })}！`
          );
        }
      } // {formatMessage({id: '失败'})}
    );
  };

  waybillSearchChange = e => {
    this.setState({
      isClickSelect: true,
    });
    waybillSearchValue = e.target.value;
  };

  workOrderChange = e => {
    this.setState({
      isClickSelect: true,
    });
  };

  // {formatMessage({id: '校验工单号'})}
  textWorkOrder = value => {
    // || value === ''
    if (value == '') return undefined;
    if (value === undefined) {
      message.error(formatMessage({ id: '请输入要搜索的工单编号' }), 2);
      return;
    }
    value = value.replace(/(^\s*)|(\s*$)/g, '');
    value = value.replace(/[\s+|\t+|,]/g, '，');
    const reg = /[`~!@#$%^&*()_\-+=<>?:"{}|.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'。、]/im;
    const lowercase = new RegExp('[a-z]+', 'g');
    const chineseReg = /^[^\u4E00-\u9FA5]+$/g;
    if (!chineseReg.test(value)) {
      message.error(formatMessage({ id: '工单号不能输入汉字' }));
      return;
    }
    if (lowercase.test(value)) {
      message.error(formatMessage({ id: '工单号不能输入小写字母' }));
      return;
    }
    if (reg.test(value)) {
      message.error(formatMessage({ id: '工单号不能输入特殊字符' }));
      return;
    }
    if (this.state.account.length === 0) {
      message.error(`${formatMessage({ id: '暂工单号' })}，${formatMessage({ id: '请稍后重试' })}`);
      return;
    }
    const workOrderNoArr = value.split('，');
    let workOrderNoArray = [];
    for (let i = 0; i < workOrderNoArr.length; i++) {
      if (workOrderNoArr[i].length !== 0) {
        workOrderNoArray.push(workOrderNoArr[i]);
      }
    }
    if (workOrderNoArray.length > 50) {
      message.error(
        `${formatMessage({ id: '运单号搜索最多输入' })}50${formatMessage({
          id: '个运单号',
        })}，${formatMessage({ id: '多单号请以逗号' })}、${formatMessage({ id: '空格或回车隔开' })}`
      );
      return;
    }
    return workOrderNoArray;
  };

  // {formatMessage({id: '校验运单号'})}
  textWaybill = (value, number = 500) => {
    // || value === ''
    if (value == '') return undefined;
    if (value === undefined) {
      message.error(
        `${formatMessage({ id: '请输入要搜索的运单编号' })}，${formatMessage({
          id: '最多输入',
        })}50${formatMessage({ id: '个运单号' })}，${formatMessage({
          id: '多单号请以逗号',
        })}、${formatMessage({ id: '空格或回车隔开' })}`,
        2
      );
      return false;
    }
    value = value.replace(/(^\s*)|(\s*$)/g, '');
    value = value.replace(/[\s+|\t+|,]/g, '，');
    const reg = /[`~!@#$%^&*()_\-+=<>?:"{}|.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'。、]/im;
    const lowercase = new RegExp('[a-z]+', 'g');
    const chineseReg = /^[^\u4E00-\u9FA5]+$/g;
    if (!chineseReg.test(value)) {
      message.error(formatMessage({ id: '运单号不能输入汉字' }));
      return false;
    }
    if (lowercase.test(value)) {
      message.error(formatMessage({ id: '运单号不能输入小写字母' }));
      return false;
    }
    if (reg.test(value)) {
      message.error(formatMessage({ id: '运单号不能输入特殊字符' }));
      return false;
    }
    const waybillarray = value.split('，');
    let tempArray = [];
    for (let i = 0; i < waybillarray.length; i++) {
      if (waybillarray[i].length !== 0) {
        tempArray.push(waybillarray[i]);
      }
    }
    if (tempArray.length > number) {
      message.error(
        `${formatMessage({ id: '运单号搜索最多输入' })}${number}${formatMessage({
          id: '个运单号',
        })}，${formatMessage({ id: '多单号请以逗号' })}、${formatMessage({ id: '空格或回车隔开' })}`
      );
      return false;
    }
    if (this.state.account.length === 0) {
      message.error(
        `${formatMessage({ id: '暂无制单账号' })}，${formatMessage({ id: '请稍后重试' })}`
      );
      return false;
    }

    return tempArray;
  };

  // {formatMessage({id: '分布加载'})}
  componentDidMount() {
    this.init();
    if (this.props?.onRef !== undefined) {
      this.props.onRef(this);
    }
    // this.validateMerchantStatus(); isFreeStatus
  }
  init = () => {
    this.openDispose();
    this.loadShipper();
    this.getSelect();
    this.getFreeStatus();
    this.getChannels();
    this.getCountryList();
    this.getAbnormalTypeList();
  };

  getChannels = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'Order/getProductName',
      payload: {},
      callback: result => {
        if (result.success) {
          this.setState({
            channels: result.data,
          });
        }
      },
    });
  };

  getCountryList = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'Order/getCountriesName',
      callback: result => {
        if (result.success) {
          result.data.forEach(item => {
            item.countryNamePinYin = loadPinYinInit.ConvertPinyin(item.nameCh);
          });
          this.setState({
            countryList: result.data,
          });
        }
      },
    });
  };

  getAbnormalTypeList = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'common/getAbnormalTypeList',
      callback: result => {
        if (result.success) {
          this.setState({
            abnormalTree: result.data,
          });
        }
      },
    });
  };

  openDispose = () => {
    const { location } = this.props;
    const showDispose = this.props.location?.query?.showDispose;
    if (showDispose) {
      const params = JSON.parse(sessionStorage.getItem('createOrderData'));
      this.problemDisposeModal.current?.openModal(params);
    }
  };

  getFreeStatus = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'anomalyOrder/getFreezeStatus',
      callback: response => {
        if (response.success) {
          this.setState({
            isFreeStatus: response.data == 2,
          });
        } else {
          this.setState({
            isFreeStatus: true,
          });
        }
      },
    });
  };

  // {formatMessage({id: '获取下拉数据'})}
  getSelect = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'anomalyOrder/problemPiecesAllAbnormalCause',
      callback: response => {
        if (response.success) {
          this.setState({
            anomalouscauseList: response.data.data,
          });
        }
      },
    });
    dispatch({
      type: 'anomalyOrder/problemPiecesGetHandleMode',
      callback: data => {
        this.setState({
          processingList: data,
        });
      },
    });
    dispatch({
      type: 'anomalyOrder/outsideTheWarehouseGetAbnormalType',
      callback: response => {
        if (response.success) {
          this.setState({
            abnormalTypeList: response.data,
          });
        }
      },
    });
  };
  // {formatMessage({id: '获取制单账号'})}
  loadShipper = () => {
    const { dispatch, abnormal, form } = this.props;
    let type = +this.props.location?.query?.type;
    const query = this.props.location?.query;
    dispatch({
      type: 'anomalyOrder/makeAccountLoad',
      payload: {
        accountType: 0,
        scene: 1,
      },
      callback: response => {
        if (response.success) {
          const shipperss = response.data;
          const zhs = [];
          if (type == 0) {
            form.setFieldsValue({
              WaybillNumbers: query?.waybillNumber,
            });
            this.setState({
              activeKey: query?.waybillNumber ? '2' : '0',
            });
          }
          if (query?.userId) {
            zhs.push(query?.userId);
          } else {
            for (let i = 0; i < shipperss.length; i++) {
              zhs.push(shipperss[i].accountCode);
            }
          }

          this.setState(
            {
              account: shipperss,
              shipperInitOpt: zhs,
              normalSelectAbnormal:
                type === 2 ? [2] : type === 3 ? [3] : type === 4 ? [1] : type === 0 ? [0] : [0],
            },
            () => {
              if (type === 2) {
                // 仓外异常件
                this.getWareDataList();
              } else if (type === 3) {
                // 海外重派
                this.getOverseasList();
              } else if (type === 4) {
                // 退件签收
                this.getnumbers(zhs);
                this.getReturnList();
              } else if (type === 0) {
                // 仓内异常件
                selectKey = '0';
                this.getNumber(zhs);
                this.getdataList(zhs);
                this.getoldList(zhs, 1);
              } else {
                selectKey = '0';
                this.getNumber(zhs);
                this.getdataList(zhs);
                this.getoldList(zhs, 1);
                // 更改品名
                // this.setState(
                //   {
                //     pageSize: 10,
                //   },
                //   this.getProductList
                // );
              }
            }
          );
        }
      },
    });
  };
  getdataList = (zhs, judge, v, pageSize) => {
    const { dispatch, abnormal, form } = this.props;
    const { shipperInitOpt, activeKey, startDate, endDate, validateStatus } = this.state;
    //WaybillNumbers --{formatMessage({id: '运单号'})}   WorkOrderNos -- {formatMessage({id: '工单号'})}
    // createdAt: undefined  -- {formatMessage({id: '日期'})}
    // exceptionType: [0]  -- {formatMessage({id: '异常类型'})}
    // exceptionTypeId: undefined  -- {formatMessage({id: '异常原因'})}
    // shippers: (13)  -- {formatMessage({id: '制单账号'})}
    // solutionTypeId: undefined  -- {formatMessage({id: '处理方式'})}
    let {
      WaybillNumbers,
      WorkOrderNos,
      createdAt,
      exceptionType,
      exceptionTypeId,
      shippers,
      solutionTypeId,
      productCode,
      orderCodes,
      regionId,
    } = form.getFieldsValue();
    let startTimeParam = ''; // {formatMessage({id: '开始时间'})}
    let endTimeParam = ''; // {formatMessage({id: '结束时间'})}
    let wayBillNumber; // {formatMessage({id: '运单号'})}
    let workOrderNumber; // {formatMessage({id: '工单号'})}
    let orderNumber; // {formatMessage({id: '工单号'})}
    if (createdAt && createdAt !== undefined && createdAt !== '') {
      if (createdAt.length > 0) {
        startTimeParam = createdAt[0].format('YYYY-MM-DD');
        endTimeParam = createdAt[1].format('YYYY-MM-DD');
      }
    }
    (wayBillNumber = WaybillNumbers !== undefined ? this.textWaybill(WaybillNumbers) : undefined),
      (orderNumber = orderCodes !== undefined ? this.textWaybill(orderCodes) : undefined),
      (workOrderNumber = WorkOrderNos !== undefined ? this.textWorkOrder(WorkOrderNos) : undefined);
    if (wayBillNumber === false) {
      return;
    }
    let realpara = {};
    let para = {
      shippingAccounts: zhs == undefined ? shipperInitOpt : zhs,
      sourceIds: [0, 4, 5, 6, 8, 9, 10, 12, 18, 20, 25],
      isExport: false,
      status: judge == 'tabs' ? v : activeKey,
      pageIndex: judge == 'pages' ? v : 1,
      workOrderNos: workOrderNumber,
      waybillNumbers: wayBillNumber,
      solutionTypeId: solutionTypeId,
      pageSize: pageSize ?? this.state.pageSize,
      insideOroutside: true,
      exceptionTypeIdList: exceptionTypeId,
      createTimes:
        startTimeParam !== '' ? [startTimeParam + ' 00:00:00', endTimeParam + ' 23:59:59'] : null,
      productCode: productCode === 'all' ? undefined : productCode,
      orderCodes: orderNumber,
      regionId: regionId,
    };

    if (validateStatus == 'success') {
      dispatch({
        type: 'anomalyOrder/problemPiecesList',
        payload: para,
      });
    } else {
      message.error(formatMessage({ id: '请选择制单账号' }));
    }
  };
  // {formatMessage({id: '获取'})} {formatMessage({id: '数量'})}
  getNumber = zhs => {
    const { dispatch, form } = this.props;
    const { shipperInitOpt } = this.state;
    let { createdAt } = form.getFieldsValue();
    let startTimeParam = ''; // {formatMessage({id: '开始时间'})}
    let endTimeParam = ''; // {formatMessage({id: '结束时间'})}
    if (createdAt && createdAt !== undefined && createdAt !== '') {
      if (createdAt.length > 0) {
        startTimeParam = createdAt[0].format('YYYY-MM-DD');
        endTimeParam = createdAt[1].format('YYYY-MM-DD');
      }
    }
    dispatch({
      type: 'anomalyOrder/problemPiecesStatisticsnumber',
      payload: {
        shippingAccounts: zhs == undefined ? shipperInitOpt : zhs,
        createTimes:
          startTimeParam !== '' ? [startTimeParam + ' 00:00:00', endTimeParam + ' 23:59:59'] : null,
      },
      callback: response => {
        if (response.success) {
          this.setState({
            completeCountNum: response.data.completeCount,
            untreatedCountNum: response.data.untreatedCount,
          });
        }
      },
    });
  };
  // {formatMessage({id: '计算创建时间距当前时间是否相差'})}24{formatMessage({id: '小时'})}，{formatMessage({id: '即当前异常单子是否超过'})}24{formatMessage({id: '小时'})}
  isExceed24 = record => {
    const date1 = record.createTime; // {formatMessage({id: '开始时间'})}
    const date2 = new Date(); // {formatMessage({id: '结束时间'})}
    const date3 = date2.getTime() - new Date(date1).getTime(); // {formatMessage({id: '时间差的毫秒数'})}
    // {formatMessage({id: '计算出相差天数'})}
    const days = Math.floor(date3 / (24 * 3600 * 1000));
    if (days >= 1) {
      return true;
    }
    return false;
  };
  // tabs
  handleTabChange = v => {
    const { startDate, endDate, normalSelectAbnormal } = this.state;
    if (normalSelectAbnormal[0] === 0) {
      // {formatMessage({id: '仓内异常件'})}
      this.setState({
        activeKey: v,
        selectedRowKeys: [],
        selectedRowspra: [],
        selectedRowWare: [],
        selectedRowOver: [],
        pageSize: 10,
        pageIndex: 1,
        selectValue: '1',
      });
      selectKey = v;
      this.props.form.setFieldsValue({
        WorkOrderNos: undefined,
        WaybillNumbers: undefined,
      });

      if (v != '3') {
        this.getdataList(undefined, 'tabs', v);
      } else {
        this.getoldList(undefined, 1);
      }
    } else if (normalSelectAbnormal[0] === 1) {
      // {formatMessage({id: '退件签收'})}
      this.setState(
        {
          isSignFor: v,
          selectedRowKeys: [],
          selectedRowspra: [],
          selectedRowWare: [],
          selectedRowOver: [],
          pageIndex: 1,
          pageSize: 10,
        },
        () => {
          this.getReturnList();
        }
      );
    } else if (normalSelectAbnormal[0] === 2) {
      // this.props.form.setFieldsValue({
      //   createWarehouse: [moment(startDate, 'YYYY-MM-DD'), moment(endDate, 'YYYY-MM-DD')],
      // });
      // {formatMessage({id: '仓外异常件'})}
      this.setState(
        {
          selectedRowKeys: [],
          selectedRowWare: [],
          selectWare: v,
          warePageIndex: 1,
          pageSize: 10,
        },
        () => {
          this.getWareDataList();
        }
      );
    } else if (normalSelectAbnormal[0] === 3) {
      // {formatMessage({id: '海外重派'})}
      this.setState(
        {
          selectedRowKeys: [],
          selectedRowOver: [],
          selectOverseas: v,
          overPageInde: 1,
          pageSize: 10,
        },
        this.getOverseasList
      );
    } else if (normalSelectAbnormal[0] === 4) {
      this.setState(
        {
          selectedRowKeys: [],
          activeExpressKey: v,
          pageIndex: 1,
          pageSize: 10,
        },
        this.getCommercialExpressList
      );
    } else if (normalSelectAbnormal[0] === 5) {
      this.setState(
        {
          selectedRowKeys: [],
          activeProductKey: v,
          pageIndex: 1,
          pageSize: 10,
        },
        this.getProductList
      );
    }
  };

  getProductList = () => {
    const { dispatch, form } = this.props;
    const { pageIndex, pageSize, activeProductKey } = this.state;
    const values = form.getFieldsValue();
    let { shippers, dateTime, waybillNumber, arrivedState } = values;

    if ((waybillNumber === undefined || waybillNumber === '') && !dateTime) {
      return message.error(formatMessage({ id: '请选择创建时间' }), 2);
    }
    if (
      waybillNumber !== undefined &&
      waybillNumber !== '' &&
      !this.textWaybill(waybillNumber, 50)
    ) {
      return;
    }
    this.setState({
      productNameDataList: [],
    });
    let params = {
      pageIndex: pageIndex,
      pageSize: pageSize,
      arrivedState: +arrivedState,
      customerCode: shippers.toString(),
      createTimeStart: dateTime?.[0] ? `${dateTime[0].format('YYYY-MM-DD')} 00:00:00` : undefined,
      createTimeEnd: dateTime?.[1] ? `${dateTime[1].format('YYYY-MM-DD')} 23:59:59` : undefined,
      state: +activeProductKey === 0 ? undefined : +activeProductKey,
      waybillNumber:
        waybillNumber !== undefined && waybillNumber !== ''
          ? this.textWaybill(waybillNumber, 50)?.join(',')
          : undefined,
    };
    dispatch({
      type: 'anomalyOrder/productNameChangeQueryList',
      payload: params,
      callback: response => {
        if (response.success) {
          const data = mergeRowsByField(response.data?.list ?? [], 'waybillNumber');
          this.setState({
            productNameDataList: data, // {formatMessage({id: '品名异常数据'})}
            productNameTotalNum: response.data?.total ?? 0, // {formatMessage({id: '品名异常总数'})}
            productPendingCount: response.data?.pendingCount ?? 0,
            productEndCount: response.data?.endCount ?? 0,
            productTotalCount: response.data?.totalCount ?? 0,
          });
        }
      },
    });
  };

  // {formatMessage({id: '重置按钮'})}
  resetFormData = () => {
    const { form } = this.props;
    const { startDate, endDate, normalSelectAbnormal, isSignFor, selectWare } = this.state;
    if (normalSelectAbnormal[0] === 0) {
      // {formatMessage({id: '仓内异常件'})}
      // this.setState({ selectValue: '1' });
      logSave(LogType.innerAbnormal2);
      if (selectKey === '0' || selectKey === '3') {
        form.setFieldsValue({
          solutionTypeId: undefined,
          exceptionTypeId: undefined,
          createdAt: undefined,
          WorkOrderNos: undefined,
          WaybillNumbers: undefined,
        });
      } else {
        form.setFieldsValue({
          solutionTypeId: undefined,
          exceptionTypeId: undefined,
          WorkOrderNos: undefined,
          WaybillNumbers: undefined,
          createdAt: [moment(startDate, 'YYYY-MM-DD'), moment(endDate, 'YYYY-MM-DD')],
        });
      }
    } else if (normalSelectAbnormal[0] === 5) {
      // {formatMessage({id: '品名异常'})}
      const expressStartTime = moment()
        .subtract(3, 'months')
        .format('YYYY-MM-DD');
      const expressEndTime = moment().format('YYYY-MM-DD');
      form.setFieldsValue({
        dateTime: [moment(expressStartTime, 'YYYY-MM-DD'), moment(expressEndTime, 'YYYY-MM-DD')],
        waybillNumber: undefined,
        arrivedState: undefined,
      });
    } else if (normalSelectAbnormal[0] === 4) {
      // {formatMessage({id: '商快费用确认'})}
      const expressStartTime = moment()
        .subtract(3, 'months')
        .format('YYYY-MM-DD');
      const expressEndTime = moment().format('YYYY-MM-DD');
      form.setFieldsValue({
        dateTime: [moment(expressStartTime, 'YYYY-MM-DD'), moment(expressEndTime, 'YYYY-MM-DD')],
        waybillNumbers: undefined,
      });
    } else if (normalSelectAbnormal[0] === 1) {
      // {formatMessage({id: '退件签收'})}
      if (isSignFor == 1) {
        // this.setState({ selectValue: '1' });
        form.setFieldsValue({
          WaybillNumbers1: undefined,
          packageNumber1: undefined,
          shippersw: [],
        });
      } else {
        form.setFieldsValue({
          WaybillNumbers1: undefined,
          packageNumber1: undefined,
          shippersw: [moment(startDate, 'YYYY-MM-DD'), moment(endDate, 'YYYY-MM-DD')],
        });
      }
    } else if (normalSelectAbnormal[0] === 2) {
      // {formatMessage({id: '仓外异常件'})}
      form.setFieldsValue({
        createWarehouse: [moment(startDate, 'YYYY-MM-DD'), moment(endDate, 'YYYY-MM-DD')],
        exceptionTypeId2: undefined,
        waybillNumbers2: undefined,
      });
    } else {
      // {formatMessage({id: '海外重派'})}
      form.setFieldsValue({
        createOverseas: [],
        exceptionTypeId3: undefined,
        waybillNumbers3: undefined,
      });
    }
  };

  // {formatMessage({id: '新的查询数据方式'})}  {formatMessage({id: '点击查询按钮'})}
  getNewDataList = () => {
    const { activeKey } = this.state;
    let { exceptionType } = this.props.form.getFieldsValue();
    let type = exceptionType[0];
    if (type === 0) {
      if (activeKey == '3') {
        this.getoldList(undefined, 1);
      } else {
        logSave(LogType.innerAbnormal1);
        this.getNumber();
        this.getdataList(undefined, 'pages', 1);
      }
    }
  };

  getoldList = (zhs, v, pageSize) => {
    const { dispatch, abnormal, form } = this.props;
    const { shipperInitOpt } = this.state;
    let {
      WaybillNumbers,
      WorkOrderNos,
      createdAt,
      exceptionType,
      exceptionTypeId,
      shippers,
      solutionTypeId,
    } = form.getFieldsValue();
    let startTimeParam = undefined; // {formatMessage({id: '开始时间'})}
    let endTimeParam = undefined; // {formatMessage({id: '结束时间'})}
    let wayBillNumber; // {formatMessage({id: '运单号'})}
    let workOrderNumber; // {formatMessage({id: '工单号'})}
    if (createdAt !== undefined && createdAt !== '') {
      startTimeParam = createdAt[0].format('YYYY-MM-DD');
      endTimeParam = createdAt[1].format('YYYY-MM-DD');
    }
    wayBillNumber =
      WaybillNumbers !== undefined && WaybillNumbers !== ''
        ? this.textWaybill(WaybillNumbers)
        : undefined;
    workOrderNumber =
      WorkOrderNos !== undefined && WorkOrderNos !== ''
        ? this.textWorkOrder(WorkOrderNos)
        : undefined;
    let oldpara = {
      customerCode: zhs == undefined ? shipperInitOpt : zhs,
      status: '3',
      currentPage: v,
      pageSize: pageSize ?? this.state.pageSize,
      changeFiter: false,
      startTime: startTimeParam,
      endTime: endTimeParam,
      waybillNumbers: wayBillNumber,
      orderId: workOrderNumber,
    };
    dispatch({
      type: 'anomalyOrder/problemPiecesOldList',
      payload: oldpara,
    });
  };

  //base64{formatMessage({id: '解析下载'})} dataurl{formatMessage({id: '是后端返回的'})}base64{formatMessage({id: '字符串'})}，name{formatMessage({id: '是文件名'})}
  dataURLtoDownload = (dataurl, name) => {
    let bstr = atob(dataurl), //{formatMessage({id: '解析'})} base-64 {formatMessage({id: '编码的字符串'})}
      n = bstr.length,
      u8arr = new Uint8Array(n); //{formatMessage({id: '创建初始化为'})}0{formatMessage({id: '的'})}，{formatMessage({id: '包含'})}length{formatMessage({id: '个元素的无符号整型数组'})}
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n); //{formatMessage({id: '返回字符串第一个字符的'})} Unicode {formatMessage({id: '编码'})}
    }
    let blob = new Blob([u8arr]); //{formatMessage({id: '转化成'})}blob
    let url = URL.createObjectURL(blob); //{formatMessage({id: '这个新的'})}URL {formatMessage({id: '对象表示指定的'})} File {formatMessage({id: '对象或'})} Blob {formatMessage({id: '对象'})}
    let a = document.createElement('a'); //{formatMessage({id: '创建一个'})}a{formatMessage({id: '标签'})}
    a.href = url;
    a.download = name;
    a.click();
    URL.revokeObjectURL(a.href); //{formatMessage({id: '释放之前创建的'})}url{formatMessage({id: '对象'})}
  };

  // {formatMessage({id: '导出'})}
  exportData = () => {
    logSave(LogType.innerAbnormal6);
    const { abnormal, dispatch, form } = this.props;
    const { activeKey, pageIndex, pageSize, shipperInitOpt, selectValue } = this.state;
    let { createdAt, exceptionTypeId, WorkOrderNos, WaybillNumbers } = form.getFieldsValue();
    if (!shipperInitOpt.length) {
      message.error(formatMessage({ id: '请选择制单账号' }));
      return;
    }
    let time = '';
    let params = {
      status: activeKey,
      pageIndex: pageIndex,
      pageSize: pageSize,
      shippingAccounts: shipperInitOpt,
      collectionTime: undefined,
      exceptionTypeIdList: exceptionTypeId,
      waybillNumbers:
        WaybillNumbers !== undefined && WaybillNumbers !== ''
          ? this.textWaybill(WaybillNumbers)
          : undefined,
      workOrderNos:
        WorkOrderNos !== undefined && WorkOrderNos !== ''
          ? this.textWorkOrder(WorkOrderNos)
          : undefined,
    };

    if (createdAt != undefined) {
      params.collectionTime = [
        `${createdAt[0].format('YYYY-MM-DD')} 00:00:00`,
        `${createdAt[1].format('YYYY-MM-DD')} 23:59:59`,
      ];
    }

    this.setState({
      exportLoading: true,
    });
    dispatch({
      type: 'anomalyOrder/problemPiecesDownload',
      payload: params,
      callback: response => {
        this.setState({
          exportLoading: false,
        });
        if (response.success) {
          message.success(formatMessage({ id: '导出成功' }));
          this.dataURLtoDownload(response.data.url, response.data.fileName);
        }
      },
    });
  };

  // {formatMessage({id: '导入处理'})}
  importProcessing = file => {
    logSave(LogType.innerAbnormal3);
    const { dispatch } = this.props;
    const { fileList, normalSelectAbnormal, activeKey } = this.state;
    const formData = new FormData();
    fileList.forEach(file => {
      formData.append('file', file);
    });

    dispatch({
      type: 'anomalyOrder/problemParcelUploading',
      payload: formData,
      callback: res => {
        if (res.success) {
          message.success(res.message).then(() => {
            if (normalSelectAbnormal[0] === 0) {
              if (activeKey != '3') {
                this.getNumber();
                this.getdataList(undefined, 'tabs', activeKey);
              } else {
                this.getoldList(undefined, 1);
              }
            } else if (normalSelectAbnormal[0] === 1) {
              this.getReturnList();
            } else {
              this.getWareDataList();
            }
          });
        }
      },
    });
  };

  // {formatMessage({id: '导出'})}
  OldexportData = () => {
    const { selectedRowKeys } = this.state;
    const { anomalyOrder } = this.props;
    let currentPro = anomalyOrder.OldtableList;
    const dataTableOld = [];
    const oldoption = {};
    currentPro.map(value => {
      const obj = {
        [formatMessage({ id: '工单编号' })]: value.orderId,
        [formatMessage({ id: '制单账号' })]: value.customerCode,
        [formatMessage({ id: '运单号' })]: value.waybillNumber,
        [formatMessage({ id: '订单号' })]: value.orderCode,
        [formatMessage({ id: '目的国' })]: value.country,
        [formatMessage({ id: '异常原因' })]: value.typeName,
        [formatMessage({ id: '退回类型' })]: value.returnTypeName,
        [formatMessage({ id: '处理方式' })]: this.getCustomerHandleModeString(
          value.customerHandleMode
        ),
        [formatMessage({ id: '状态' })]:
          value.status === '1' ? formatMessage({ id: '待处理' }) : formatMessage({ id: '已处理' }),
        [formatMessage({ id: '创建时间' })]: value.createTime,
        [formatMessage({ id: '处理时间' })]: value.operatorTime,
      };
      dataTableOld.push(obj);
    });
    oldoption.fileName = formatMessage({ id: '异常工单历史处理表' });
    oldoption.datas = [
      {
        sheetData: dataTableOld,
        sheetName: formatMessage({ id: '异常工单历史处理表' }),
        sheetFilter: [
          formatMessage({ id: '工单编号' }),
          formatMessage({ id: '制单账号' }),
          formatMessage({ id: '运单号' }),
          formatMessage({ id: '订单号' }),
          formatMessage({ id: '目的国' }),
          formatMessage({ id: '异常原因' }),
          formatMessage({ id: '退回类型' }),
          formatMessage({ id: '处理方式' }),
          formatMessage({ id: '状态' }),
          formatMessage({ id: '创建时间' }),
          formatMessage({ id: '处理时间' }),
        ],
        sheetHeader: [
          formatMessage({ id: '工单编号' }),
          formatMessage({ id: '制单账号' }),
          formatMessage({ id: '运单号' }),
          formatMessage({ id: '订单号' }),
          formatMessage({ id: '目的国' }),
          formatMessage({ id: '异常原因' }),
          formatMessage({ id: '退回类型' }),
          formatMessage({ id: '处理方式' }),
          formatMessage({ id: '状态' }),
          formatMessage({ id: '创建时间' }),
          formatMessage({ id: '处理时间' }),
        ],
      },
    ];
    const toExcel = new ExportJsonExcel(oldoption); // new
    toExcel.saveExcel();
  };
  // {formatMessage({id: '根据异常原因'})}code{formatMessage({id: '显示对应异常原因'})}
  getCustomerHandleModeString = customerHandleMode => {
    const codeArray = [
      '100',
      '101',
      '102',
      '103',
      '104',
      '105',
      '106',
      '107',
      '108',
      '109',
      '110',
      '111',
    ];
    const customerHandleModeArray = [
      `${formatMessage({ id: '待' })}crm${formatMessage({ id: '指定' })}`,
      formatMessage({ id: '取消异常' }),
      formatMessage({ id: '退回' }),
      formatMessage({ id: '换单' }),
      formatMessage({ id: '修改资料发出' }),
      formatMessage({ id: '弃件' }),
      formatMessage({ id: '销毁' }),
      formatMessage({ id: '通知客服' }),
      formatMessage({ id: '确认费用发出' }),
      formatMessage({ id: '修改制单账号' }),
      formatMessage({ id: '快递退回' }),
      formatMessage({ id: '自取' }),
    ];
    const index = this.search(codeArray, customerHandleMode);
    const str = customerHandleModeArray[index];
    return str;
  };
  // {formatMessage({id: '数组遍历找下标'})}
  search = (arr, dst) => {
    let resultIndex;
    // eslint-disable-next-line array-callback-return,consistent-return
    arr.map((value, index) => {
      if (value === dst) {
        resultIndex = index;
      }
    });
    return resultIndex;
  };

  // {formatMessage({id: '制单账号'})}
  onChangeTagSelect = v => {
    this.setState({
      shipperInitOpt: v,
    });
    if (v.length >= 1) {
      this.setState({
        validateStatus: 'success',
      });
      // this.getList(v)
    } else {
      this.setState({
        validateStatus: 'error',
      });
    }
  };

  // {formatMessage({id: '异常类型'})}
  onChangeTagTypeSelect = (v, pageSize) => {
    const { shipperInitOpt } = this.state;
    const { onProblemTypeChange, form } = this.props;
    onProblemTypeChange(v);
    this.setState({
      selectValue: '1',
      normalSelectAbnormal: v,
      selectedRowKeys: [],
      selectedRowspra: [],
      selectedRowWare: [],
      selectedRowOver: [],
      isSignFor: '1', // {formatMessage({id: '退件签收的'})}tab
      selectWare: '1',
      selectOverseas: '1',
      activeKey: '0',
      workOrderAndywCustomerCode: [],
      pageSize: pageSize ? pageSize : 10,
    });
    selectKey = '0';
    if (v[0] === 0) {
      // {formatMessage({id: '仓内异常件'})}
      form.setFieldsValue({
        solutionTypeId: undefined,
        exceptionTypeId: undefined,
        WorkOrderNos: undefined,
        WaybillNumbers: undefined,
      });
      if (selectKey != '3') {
        this.getNumber();
        this.getdataList(undefined, 'tabs', selectKey);
      } else {
        this.getoldList(undefined, 1);
      }
      this.getNumber(shipperInitOpt);
    } else if (v[0] === 1) {
      // {formatMessage({id: '退件签收'})}
      let { shippers } = this.props.form.getFieldsValue();
      this.setState(
        {
          pageIndex: 1,
          pageSize: 10,
        },
        () => {
          this.getnumbers(shippers);
          this.getReturnList();
        }
      );
    } else if (v[0] === 2) {
      // {formatMessage({id: '仓外异常件'})}
      this.setState(
        {
          warePageIndex: 1,
          pageSize: 10,
        },
        () => {
          this.getWareDataList();
        }
      );
    } else if (v[0] === 3) {
      // {formatMessage({id: '海外重派'})}
      this.setState(
        {
          overPageIndex: 1,
          pageSize: 10,
        },
        this.getOverseasList
      );
    } else if (v[0] === 4) {
      // {formatMessage({id: '商快费用确认'})}
      this.setState(
        {
          pageIndex: 1,
          pageSize: 10,
        },
        this.getCommercialExpressList
      );
    } else if (v[0] === 5) {
      // {formatMessage({id: '品名异常'})}
      this.setState(
        {
          pageIndex: 1,
          pageSize: 10,
        },
        this.getProductList
      );
    }
  };
  /// {formatMessage({id: '获取退件签收的数量'})}
  getnumbers = val => {
    const { dispatch } = this.props;
    dispatch({
      type: 'anomalyOrder/getreturnnumbers',
      payload: {
        ShippingAccount: val,
        Status: 3,
        IsExport: false,
        PageSize: 10,
        PageIndex: 1,
      },
      callback: res => {
        if (res.success) {
          this.setState({
            completeCount: res.data.completeCount,
            untreatedCount: res.data.untreatedCount,
          });
        }
      },
    });
  };

  // {formatMessage({id: '确认签收'})}
  sureSign = () => {
    logSave(LogType.returnSign3);
    const { dispatch } = this.props;
    const { selectedRowspra } = this.state;
    let checkedList = [];
    selectedRowspra.map(item => {
      let obj = {
        waybillNumber: item.waybillNumber,
        packageNumber: item.packageNumber,
        operator: item.shippingAccount,
        isWeixinClient: false,
        workOrderNo: item.workOrderNo,
      };
      checkedList.push(obj);
    });
    dispatch({
      type: 'anomalyOrder/returnReceiptBatchConfirmation',
      payload: checkedList,
      callback: res => {
        if (res.success) {
          message.success(res.message);
          this.getReturnList();
          this.setState({
            selectedRowKeys: [],
            selectedRowspra: [],
          });
        }
      },
    });
  };

  // {formatMessage({id: '查询商快费用确认'})}
  getCommercialExpressList = () => {
    const { dispatch, form } = this.props;
    const { pageIndex, pageSize, activeExpressKey } = this.state;
    const values = form.getFieldsValue();
    let { shippers, dateTime, waybillNumbers } = values;

    if ((waybillNumbers === undefined || waybillNumbers === '') && !dateTime) {
      return message.error(formatMessage({ id: '请选择创建时间' }), 2);
    }
    let params = {
      currentPage: pageIndex,
      pageSize: pageSize,
      customerCode: shippers.toString(),
      createTimeStart: dateTime?.[0] ? `${dateTime[0].format('YYYY-MM-DD')} 00:00:00` : undefined,
      createTimeEnd: dateTime?.[1] ? `${dateTime[1].format('YYYY-MM-DD')} 23:59:59` : undefined,
      state: +activeExpressKey,
      waybillNumbers:
        waybillNumbers !== undefined && waybillNumbers !== ''
          ? this.textWaybill(waybillNumbers, 50)?.join(',')
          : undefined,
    };
    dispatch({
      type: 'anomalyOrder/commercialExpressList',
      payload: params,
      callback: response => {
        if (response.success) {
          this.setState({
            commercialExpressDataList: response.data?.data, // {formatMessage({id: '商快费用确认数据'})}
            commercialExpressTotalNum: response.data?.totalCount, // {formatMessage({id: '商快费用确认总数'})}
          });
        }
      },
    });
  };

  // {formatMessage({id: '查询退件签收列表'})}
  getReturnList = (pages, pageSize) => {
    const { dispatch, form } = this.props;
    const { isSignFor, validateStatus } = this.state;
    this.setState({
      pageIndex: pages === undefined ? 1 : pages,
    });
    let { shippers, shippersw, WaybillNumbers1, packageNumber1 } = form.getFieldsValue();
    let timeParams = undefined;
    if (shippersw?.length > 0) {
      timeParams = [
        `${shippersw[0].format('YYYY-MM-DD')} 00:00:00`,
        `${shippersw[1].format('YYYY-MM-DD')} 23:59:59`,
      ];
    }
    let params = {
      shippingAccount: shippers,
      isExport: false,
      pageSize: pageSize ?? this.state.pageSize,
      pageIndex: pages === undefined ? 1 : pages,
      collectionTime: shippersw?.length > 0 ? timeParams : null,
      status: isSignFor == 1 ? 3 : isSignFor == 2 ? 4 : 0,
      waybillNumbers:
        WaybillNumbers1 !== undefined ? WaybillNumbers1.replace(/，/g, ',').split(',') : undefined,
      packageNumbers:
        packageNumber1 !== undefined ? packageNumber1.replace(/，/g, ',').split(',') : undefined,
    };

    if (validateStatus == 'success') {
      dispatch({
        type: 'anomalyOrder/returnReceiptList',
        payload: params,
        callback: res => {
          if (res.success) {
            this.setState({
              returnDataList: res.data.data,
              returnTotalNum: res.data.count,
              untreatedCount: res.data.handlingInformation.untreatedCount,
              completeCount: res.data.handlingInformation.completeCount,
            });
          } else {
            this.setState({
              returnDataList: [],
              returnTotalNum: 0,
              untreatedCount: 0,
              completeCount: 0,
            });
          }
        },
      });
    } else {
      message.error(formatMessage({ id: '请选择制单账号' }));
    }
  };

  // {formatMessage({id: '退件签收导出'})}
  exportReturnData = () => {
    logSave(LogType.returnSign4);
    const { isSignFor, validateStatus, pageIndex, pageSize } = this.state;
    const { dispatch, form } = this.props;

    let { shippers, shippersw, WaybillNumbers1, packageNumber1 } = form.getFieldsValue();
    let timeParams = undefined;
    if (shippersw?.length > 0) {
      timeParams = [
        `${shippersw[0].format('YYYY-MM-DD')} 00:00:00`,
        `${shippersw[1].format('YYYY-MM-DD')} 23:59:59`,
      ];
    }
    let params = {
      shippingAccount: shippers,
      isExport: false,
      pageSize: pageSize,
      pageIndex: pageIndex,
      collectionTime: shippersw?.length > 0 ? timeParams : null,
      status: isSignFor == 1 ? 3 : isSignFor == 2 ? 4 : 0,
      waybillNumbers:
        WaybillNumbers1 !== undefined && WaybillNumbers1 !== ''
          ? WaybillNumbers1.replace(/，/g, ',').split(',')
          : undefined,
      packageNumbers:
        packageNumber1 !== undefined && packageNumber1 !== ''
          ? packageNumber1.replace(/，/g, ',').split(',')
          : undefined,
    };
    if (validateStatus == 'success') {
      dispatch({
        type: 'anomalyOrder/returnReceiptExcelDownload',
        payload: params,
        callback: res => {
          if (res.success) {
            message.success(res.message);
            this.dataURLtoDownload(res.data.url, res.data.fileName);
          }
        },
      });
    } else {
      message.error(formatMessage({ id: '请选择制单账号' }));
    }
  };

  // {formatMessage({id: '获取仓外异常件'})}
  getWareDataList = () => {
    const { dispatch, form } = this.props;
    const { selectWare, validateStatus, pageSize, warePageIndex } = this.state;
    let { createWarehouse, exceptionTypeId2, shippers, waybillNumbers2 } = form.getFieldsValue();
    let params = {
      pageIndex: warePageIndex,
      pageSize,
      customerCode: shippers.toString(),
      createTime:
        createWarehouse?.length > 0
          ? `${createWarehouse[0].format('YYYY-MM-DD')} 00:00:00`
          : undefined,
      endTime:
        createWarehouse?.length > 0
          ? `${createWarehouse[1].format('YYYY-MM-DD')} 23:59:59`
          : undefined,
      abnormalTypeName:
        typeof exceptionTypeId2 == 'string' ? exceptionTypeId2 : exceptionTypeId2?.join(','),
      status: selectWare === '0' ? undefined : selectWare === '1' ? 0 : 1, // 0 {formatMessage({id: '全部等于不穿'})}  1 {formatMessage({id: '待处理等于'})} 0  2 {formatMessage({id: '已处理等于'})}1
      overseas: '0',
      export: 0,
      waybillNumbers:
        waybillNumbers2 !== undefined && waybillNumbers2 !== ''
          ? this.textWaybill(waybillNumbers2, 50)?.join(',')
          : undefined,
    };
    if (validateStatus == 'success') {
      dispatch({
        type: 'anomalyOrder/outsideTheWarehouseSelectPage',
        payload: params,
        callback: result => {
          if (result.success) {
            this.setState({
              wareDataList: result.data.outsideWarehouseList,
              completeCount: +result.data.endCountInteger,
              untreatedCount: +result.data.pendingCountInteger,
              wareTotalNum:
                selectWare === '0'
                  ? +result.data.totalCountInteger
                  : selectWare === '1'
                  ? +result.data.pendingCountInteger
                  : +result.data.endCountInteger,
            });
          } else {
            this.setState({
              wareDataList: [],
              completeCount: 0,
              untreatedCount: 0,
              wareTotalNum: 0,
            });
          }
        },
      });
    } else {
      message.error(formatMessage({ id: '请选择制单账号' }));
    }
  };

  // {formatMessage({id: '仓外异常件批量处理'})}
  batchWare = () => {
    const { selectedRowWare } = this.state;

    let wareData = selectedRowWare.filter(item => item.abnormalType !== '客户IOSS税号错误');

    if (wareData.length > 0) {
      message.error(
        `${formatMessage({ id: '只支持异常原因为' })} '${formatMessage({
          id: '客户',
        })}IOSS${formatMessage({ id: '税号错误' })}' ${formatMessage({ id: '的异常件进行处理' })} `
      );
      return;
    } else {
      let wareSelectData = selectedRowWare.filter(item => item.status == 0);
      if (wareSelectData.length > 0) {
        this.outsideBatchModal.current.openModal(wareSelectData);
        // sessionStorage.setItem('wareBatch', JSON.stringify(wareSelectData));
        // router.push(`/express/anomalyOrder/abnormalBatch?data=${'wareBatch'}`);
      } else {
        message.error(formatMessage({ id: '请选择未处理的异常件' }));
        return;
      }
    }
  };

  // {formatMessage({id: '仓外异常件批量确认'})}
  confirmWare = () => {
    const { selectedRowWare } = this.state;
    const { dispatch } = this.props;
    let wareData = selectedRowWare.filter(item => item.abnormalType !== '燕文已代缴税');
    if (wareData.length > 0) {
      message.error(
        `${formatMessage({ id: '只支持异常原因为' })} '${formatMessage({
          id: '燕文已代缴税',
        })}' ${formatMessage({ id: '的异常件进行确认' })} `
      );
      return;
    } else {
      let wareConfirmData = selectedRowWare.filter(item => item.status == 0);
      if (wareConfirmData.length > 0) {
        let params = wareConfirmData.map(item => {
          item.handleType = 4;
          return item;
        });
        dispatch({
          type: 'anomalyOrder/outsideTheWarehouseCustomerBatchHandle',
          payload: params,
          callback: result => {
            if (result.success) {
              message.success(result.message);
              this.getWareDataList();
            }
          },
        });
      } else {
        message.error(`{formatMessage({id: '请选择未确认的异常件'})}！`);
        return;
      }
    }
  };

  // {formatMessage({id: '仓外异常件单个确认'})}
  confirmSingleWare = record => {
    const { dispatch } = this.props;
    let params = {
      ...record,
      handleType: 4,
    };
    dispatch({
      type: 'anomalyOrder/outsideTheWarehouseCustomerHandle',
      payload: params,
      callback: result => {
        if (result.success) {
          message.success(result.message);
          this.getWareDataList();
        }
      },
    });
  };

  // {formatMessage({id: '仓外异常件单个处理'})}
  batchSingleWare = record => {
    this.outsideBatchModal.current.openModal(record);
    // sessionStorage.setItem('wareBatch', JSON.stringify(record));
    // router.push(`/express/anomalyOrder/abnormalBatch?data=${'wareBatch'}`);
  };

  // {formatMessage({id: '海外重派处理'})}
  batchSingleOver = record => {
    this.wareBatchModal.current.initalFun(record);
    // sessionStorage.setItem('overBatch', JSON.stringify(record));
    // router.push(`/express/anomalyOrder/abnormalBatchOver?data=${'overBatch'}`);
  };

  // {formatMessage({id: '仓外异常件详情'})}
  detailWare = record => {
    this.outsideDetailModal.current?.openModal(record);
    // sessionStorage.setItem('ware', JSON.stringify(record));
    // router.push(`/express/anomalyOrder/abnormalWareDetail?data=${'ware'}`);
  };

  // {formatMessage({id: '海外重派详情'})}
  detailOver = record => {
    this.wareDetailModal.current.initalFun(record);
    // sessionStorage.setItem('over', JSON.stringify(record));
    // router.push(`/express/anomalyOrder/abnormalOverDetail?data=${'over'}`);
  };

  // {formatMessage({id: '获取海外重派数据'})}
  getOverseasList = () => {
    const { dispatch, form } = this.props;
    const { selectOverseas, validateStatus, pageSize, overPageIndex } = this.state;
    let { createOverseas, exceptionTypeId3, shippers, waybillNumbers3 } = form.getFieldsValue();
    let params = {
      pageIndex: overPageIndex,
      pageSize,
      customerCode: shippers.toString(),
      createTime:
        createOverseas?.length > 0
          ? `${createOverseas[0].format('YYYY-MM-DD')} 00:00:00`
          : undefined,
      endTime:
        createOverseas?.length > 0
          ? `${createOverseas[1].format('YYYY-MM-DD')} 23:59:59`
          : undefined,
      abnormalTypeName:
        typeof exceptionTypeId3 == 'string' ? exceptionTypeId3 : exceptionTypeId3?.join(','),
      status: selectOverseas === '0' ? undefined : selectOverseas === '1' ? 0 : 1, // 0 {formatMessage({id: '全部等于不穿'})}  1 {formatMessage({id: '待处理等于'})} 0  2 {formatMessage({id: '已处理等于'})}1
      overseas: '1',
      export: 0,
      waybillNumbers:
        waybillNumbers3 !== undefined && waybillNumbers3 !== ''
          ? this.textWaybill(waybillNumbers3, 50)?.join(',')
          : undefined,
    };
    if (validateStatus == 'success') {
      dispatch({
        type: 'anomalyOrder/outsideTheWarehouseSelectPage',
        payload: params,
        callback: result => {
          if (result.success) {
            this.setState({
              overDataList: result.data.outsideWarehouseList,
              completeCount: +result.data.endCountInteger,
              untreatedCount: +result.data.pendingCountInteger,
              overTotalNum:
                selectOverseas === '0'
                  ? +result.data.totalCountInteger
                  : selectOverseas === '1'
                  ? +result.data.pendingCountInteger
                  : +result.data.endCountInteger,
            });
          } else {
            this.setState({
              overDataList: [],
              completeCount: 0,
              untreatedCount: 0,
              overTotalNum: 0,
            });
          }
        },
      });
    } else {
      message.error(formatMessage({ id: '请选择制单账号' }));
    }
  };
  // {formatMessage({id: '仓外异常件导出'})}
  handleWareExport = () => {
    logSave(LogType.outAbnormal5);
    const { form, dispatch } = this.props;
    const { selectWare, validateStatus, pageSize, warePageIndex, selectedRowWare } = this.state;
    let { createWarehouse, exceptionTypeId2, shippers } = form.getFieldsValue();

    let params =
      selectedRowWare.length > 0
        ? selectedRowWare
        : {
            pageIndex: warePageIndex,
            pageSize,
            customerCode: shippers.toString(),
            createTime:
              createWarehouse?.length > 0
                ? `${createWarehouse[0].format('YYYY-MM-DD')} 00:00:00`
                : undefined,
            endTime:
              createWarehouse?.length > 0
                ? `${createWarehouse[1].format('YYYY-MM-DD')} 23:59:59`
                : undefined,
            abnormalTypeName: exceptionTypeId2,
            status: selectWare === '0' ? undefined : selectWare === '1' ? 0 : 1, // 0 {formatMessage({id: '全部等于不穿'})}  1 {formatMessage({id: '待处理等于'})} 0  2 {formatMessage({id: '已处理等于'})}1
            overseas: '0',
            export: 1,
          };
    dispatch({
      type: `anomalyOrder/${
        selectedRowWare.length > 0
          ? 'outsideTheWarehousePortionDownload'
          : 'outsideTheWarehouseDownload'
      }`,
      payload: params,
      callback: response => {
        if (response.success) {
          message.success(response.message);
          this.dataURLtoDownload(response.data.url, response.data.fileName);
        }
      },
    });
  };

  // {formatMessage({id: '海外重派导出'})}
  handleOverExport = () => {
    const { form, dispatch } = this.props;
    const { selectOverseas, validateStatus, pageSize, overPageIndex, selectedRowOver } = this.state;
    let { createOverseas, exceptionTypeId3, shippers } = form.getFieldsValue();
    let params =
      selectedRowOver.length > 0
        ? selectedRowOver
        : {
            pageIndex: overPageIndex,
            pageSize,
            customerCode: shippers.toString(),
            createTime:
              createOverseas?.length > 0
                ? `${createOverseas[0].format('YYYY-MM-DD')} 00:00:00`
                : undefined,
            endTime:
              createOverseas?.length > 0
                ? `${createOverseas[1].format('YYYY-MM-DD')} 23:59:59`
                : undefined,
            abnormalTypeName: exceptionTypeId3,
            status: selectOverseas === '0' ? undefined : selectOverseas === '1' ? 0 : 1, // 0 {formatMessage({id: '全部等于不穿'})}  1 {formatMessage({id: '待处理等于'})} 0  2 {formatMessage({id: '已处理等于'})}1
            overseas: '1',
            export: 1,
          };
    dispatch({
      type: `anomalyOrder/${
        selectedRowOver.length > 0
          ? 'outsideTheWarehousePortionDownload'
          : 'outsideTheWarehouseDownload'
      }`,
      payload: params,
      callback: response => {
        if (response.success) {
          message.success(response.message);
          this.dataURLtoDownload(response.data.url, response.data.fileName);
        }
      },
    });
  };

  // {formatMessage({id: '判断商户是否是冻结'})}
  handleMerchantFreeStatus = callback => {
    const { dispatch } = this.props;
    return new Promise((resolve, reject) => {
      dispatch({
        type: 'anomalyOrder/getFreezeStatus',
        callback: response => {
          if (response.success) {
            if (response.data != 2) {
              if (callback) callback();
              resolve(true);
            } else {
              reject(false);
              message.error(response.message);
            }
          } else {
            reject(false);
          }
        },
      });
    });
  };

  handleExpressSolve = handleType => {
    const { dispatch } = this.props;
    const { selectedRowKeys } = this.state;
    dispatch({
      type: 'anomalyOrder/commercialExpressDispose',
      payload: {
        waybillNumbers: selectedRowKeys?.join(','),
        handleType,
      },
      callback: response => {
        if (response.success) {
          message.success(response.message);
          this.setState(
            {
              selectedRowKeys: [],
              pageIndex: 1,
              pageSize: 10,
            },
            this.getCommercialExpressList
          );
        }
      },
    });
  };

  handleProductBatch = () => {
    logSave(LogType.editProductName4);
    const { selectedRowKeys, productNameDataList } = this.state;
    const data = productNameDataList?.filter(item => selectedRowKeys?.includes(item.waybillNumber));
    this.productDisposeModal.current.openModal(mergeRowsByField(data, 'waybillNumber'));
  };

  handleProductExport = async () => {
    logSave(LogType.editProductName3);
    try {
      const { form } = this.props;
      const { pageIndex, pageSize, activeProductKey } = this.state;
      this.setState({
        exportLoading: true,
      });
      const values = form.getFieldsValue();
      let { shippers, dateTime, waybillNumber, arrivedState } = values;
      if ((waybillNumber === undefined || waybillNumber === '') && !dateTime) {
        return message.error(formatMessage({ id: '请选择创建时间' }), 2);
      }
      let params = {
        currentPage: pageIndex,
        pageSize: pageSize,
        arrivedState: +arrivedState,
        customerCode: shippers.toString(),
        createTimeStart: dateTime?.[0] ? `${dateTime[0].format('YYYY-MM-DD')} 00:00:00` : undefined,
        createTimeEnd: dateTime?.[1] ? `${dateTime[1].format('YYYY-MM-DD')} 23:59:59` : undefined,
        state: +activeProductKey,
        waybillNumber:
          waybillNumber !== undefined && waybillNumber !== ''
            ? this.textWaybill(waybillNumber, 50)?.join(',')
            : undefined,
      };

      const data = await productDownload({
        url: '/csc/productNameChange/exportData',
        param: params,
        method: 'POST',
      });
      if (data === 1) {
        this.setState({
          exportLoading: false,
        });
      }
    } catch (error) {
      this.setState({
        exportLoading: false,
      });
    }
  };

  getBadgeCount = id => {
    const { problemMap } = this.props;

    switch (id) {
      case 0:
        return problemMap?.problemNumber ?? 0;
      case 5:
        return problemMap?.productNameNumber ?? 0;
      case 1:
        return problemMap?.returnNumber ?? 0;
      case 2:
        return problemMap?.warehouseNumber ?? 0;
      case 3:
        return problemMap?.overseasNumber ?? 0;
      case 4:
        return null; // Indicates the badge should be hidden
      default:
        return 0;
    }
  };

  render() {
    const {
      dataSourceArray,
      completeCountNum,
      untreatedCountNum,
      selectValue,
      activeKey,
      isExpanded,
      shipperInitOpt,
      account,
      processingList,
      anomalouscauseList,
      workOrderAndywCustomerCode,
      startDate,
      endDate,
      currentAuthorityValue,
      selectedRowKeys,
      validateStatus,
      normalSelectAbnormal,
      abnormalList,
      validateTypeStatus,
      isSignFor,
      untreatedCount,
      completeCount,
      selectedRowspra,
      pageIndex,
      returnDataList,
      returnTotalNum,
      selectWare,
      wareDataList,
      wareTotalNum,
      warePageIndex,
      selectedRowWare,
      selectedRowOver,
      pageSize,
      fileList,
      abnormalTypeList,
      overDataList,
      overPageIndex,
      overTotalNum,
      selectOverseas,
      exportLoading,
      isFreeStatus,
      commercialExpressDataList,
      commercialExpressTotalNum,
      activeExpressKey,
      productNameDataList,
      productNameTotalNum,
      activeProductKey,
      productPendingCount,
      productEndCount,
      productTotalCount,
      channels,
      countryList,
      abnormalTree,
    } = this.state;
    const {
      loading,
      form: { getFieldDecorator, setFieldsValue, getFieldsValue },
      anomalyOrder,
      oldtableloding,
      expressLoading,
      returnLoading, // {formatMessage({id: '退件签收'})}
      wareLoading, // {formatMessage({id: '仓外异常件'})}
      pageInitLoading, // {formatMessage({id: '页面初始化时'})}
      returnExportLoading, // {formatMessage({id: '退件签收导出'})}
      outsideAndWareExportLoading, // {formatMessage({id: '仓外和海外导出'})}
      outActiveKey,
      uploadLoading,
      productLoading,
    } = this.props;
    const { tableList, totalNum, pageNum, OldtableList, OldtotalNum, OldpageNum } = anomalyOrder;

    const expressStartTime = moment()
      .subtract(3, 'months')
      .format('YYYY-MM-DD');
    const expressEndTime = moment().format('YYYY-MM-DD');

    const tabLists = [
      {
        key: '0',
        tab: (
          <span>
            {formatMessage({ id: '待处理' })}{' '}
            <span style={{ fontSize: 14 }}>({untreatedCountNum})</span>{' '}
          </span>
        ),
        disabled: loading,
      },

      {
        key: '1',
        tab: (
          <span>
            {formatMessage({ id: '已处理' })}{' '}
            <span style={{ fontSize: 14 }}>({completeCountNum})</span>
          </span>
        ),
        disabled: loading,
      },
      {
        key: '2',
        tab: (
          <span>
            {formatMessage({ id: '全部' })}{' '}
            <span style={{ fontSize: 14 }}>({completeCountNum + untreatedCountNum})</span>
          </span>
        ),
        disabled: loading,
      },
      {
        key: '3',
        tab: (
          <span>
            {formatMessage({ id: '历史处理' })}{' '}
            <span style={{ fontSize: 14 }}>({OldtotalNum})</span>
          </span>
        ),
        disabled: loading,
      },
    ];
    // {formatMessage({id: '仓内异常件'})}
    const columns = [
      {
        title: formatMessage({ id: '运单号' }),
        dataIndex: 'waybillNumber',
        width: 150,
        fixed: 'left',
        render: (text, record) => <a onClick={() => this.detail(record)}>{text}</a>,
      },
      {
        title: formatMessage({ id: '制单账号' }),
        dataIndex: 'ywCustomerCode',
        width: 100,
      },

      {
        title: formatMessage({ id: '订单号' }),
        dataIndex: 'orderCode',
        width: 140,
      },
      {
        title: formatMessage({ id: '平台交易号' }),
        dataIndex: 'transactionNumber',
        width: 140,
        render: (text, record) => <span>{record?.parcel?.transactionNumber}</span>,
      },
      {
        title: formatMessage({ id: '目的国' }),
        dataIndex: 'regionName',
        width: 140,
      },
      {
        title: formatMessage({ id: '产品名称' }),
        dataIndex: 'productName',
        width: 140,
      },
      {
        title: formatMessage({ id: '异常原因' }),
        dataIndex: 'exceptionTypeName',
        width: 250,
      },
      {
        title: formatMessage({ id: '异常具体原因' }),
        dataIndex: 'memo',
        width: 250,
        render: text => {
          let arr = text?.split(':');
          return arr?.length > 1 && text !== null ? arr[arr.length - 1] : text;
        },
      },
      {
        title: formatMessage({ id: '处理方式' }),
        dataIndex: 'solutionTypeName',
        width: 120,
        filterType: activeKey == '0' ? true : undefined,
        render: text => {
          if (text == '客户封存') {
            return <span>弃件</span>;
          } else if (text == '退回客户-快递（到付）') {
            return <span>退回-快递到付</span>;
          } else if (text == '退回客户-快递（寄付）') {
            return <span>退回-快递寄付</span>;
          } else {
            return <span>{text}</span>;
          }
        },
      },
      // {
      //   title: formatMessage({id: '备注'}),
      //   dataIndex: 'remark',
      //   // width: 200,
      // },
      {
        title: formatMessage({ id: '状态' }),
        dataIndex: 'status',
        width: 100,
        render: text => (
          <div>
            {text == '0' ? formatMessage({ id: '待处理' }) : formatMessage({ id: '已处理' })}
          </div>
        ),
      },
      {
        title: formatMessage({ id: '创建时间' }),
        dataIndex: 'createTime',
        width: 150,
      },
      {
        title: formatMessage({ id: '处理时间' }),
        dataIndex: 'handlingTime',
        width: 150,
      },
      {
        title: formatMessage({ id: '操作' }),
        dataIndex: 'wldo',
        fixed: 'right',
        width: 100,
        render: (text, record) => {
          return (
            <span>
              {record.status == '0' ? (
                <Button
                  type="link"
                  onClick={() =>
                    this.handleMerchantFreeStatus(() => {
                      logSave(LogType.innerAbnormal5);
                      this.disposeAction(record);
                    })
                  }
                >
                  {formatMessage({ id: '处理' })}
                </Button>
              ) : (
                <a onClick={() => this.detail(record)}>{formatMessage({ id: '详情' })}</a>
              )}
            </span>
          );
        },
      },
    ];

    // 品名异常
    const productColumns = [
      {
        title: formatMessage({ id: '运单号' }),
        dataIndex: 'waybillNumber',
        key: 'waybillNumber',
        width: 150,
        fixed: 'left',
        onCell: (row, index) => ({
          rowSpan: row.rowSpan,
        }),
      },
      {
        title: formatMessage({ id: '制单账号' }),
        dataIndex: 'customerCode',
        key: 'customerCode',
        width: 100,
        onCell: (row, index) => ({
          rowSpan: row.rowSpan,
        }),
      },

      {
        title: formatMessage({ id: '订单号' }),
        dataIndex: 'userOrderNumber',
        key: 'userOrderNumber',
        width: 150,
        onCell: (row, index) => ({
          rowSpan: row.rowSpan,
        }),
      },
      {
        title: '下单时间',
        dataIndex: 'orderTime',
        width: 200,
        readonly: true,
        onCell: (row, index) => ({
          rowSpan: row.rowSpan,
        }),
      },
      {
        title: 'SKU',
        dataIndex: 'sku',
        key: 'sku',
      },
      {
        title: formatMessage({ id: '中文品名' }),
        dataIndex: 'oldGoodsNameCh',
        key: 'oldGoodsNameCh',
      },
      {
        title: formatMessage({ id: '英文品名' }),
        dataIndex: 'oldGoodsNameEh',
        key: 'oldGoodsNameEh',
      },
      {
        title: formatMessage({ id: '新中文品名' }),
        dataIndex: 'newGoodsNameCh',
        key: 'newGoodsNameCh',
      },
      {
        title: formatMessage({ id: '新英文品名' }),
        dataIndex: 'newGoodsNameEh',
        key: 'newGoodsNameEh',
      },
      {
        title: formatMessage({ id: '是否已到仓' }),
        dataIndex: 'arrivedStateName',
        key: 'arrivedStateName',
      },
      {
        title: formatMessage({ id: '状态' }),
        dataIndex: 'stateName',
        key: 'stateName',
      },
      {
        title: '创建时间',
        width: 160,
        dataIndex: 'createTime',
      },
      {
        title: formatMessage({ id: '操作' }),
        dataIndex: 'option',
        key: 'option',
        render: (text, record) => {
          return (
            <Space>
              {record?.state == '1' && (
                <a onClick={() => this.batchProduct(record)}>{formatMessage({ id: '处理' })}</a>
              )}
              {record?.state == '2' && (
                <a onClick={() => this.productDetail(record)}>{formatMessage({ id: '详情' })}</a>
              )}
            </Space>
          );
        },
      },
    ];

    // {formatMessage({id: '商快费用确认'})}
    const expressColumns = [
      {
        title: formatMessage({ id: '运单号' }),
        dataIndex: 'waybillNumber',
        key: 'waybillNumber',
        fixed: 'left',
        render: (text, record) => {
          return (
            <a
              onClick={() => {
                this.expressDetailModal.current.open(record);
                // let obj = JSON.stringify(record);
                // router.push(`/express/anomalyOrder/OldAbnormalDetail/${obj}`);
              }}
            >
              {text}
            </a>
          );
        },
      },
      {
        title: formatMessage({ id: '制单账号' }),
        dataIndex: 'customerCode',
        key: 'customerCode',
      },

      {
        title: formatMessage({ id: '转单号' }),
        dataIndex: 'exchangeNumber',
        key: 'exchangeNumber',
      },
      {
        title: formatMessage({ id: '目的国' }),
        dataIndex: 'regionCh',
        key: 'regionCh',
      },
      {
        title: formatMessage({ id: '产品名称' }),
        dataIndex: 'productName',
        key: 'productName',
      },
      {
        title: formatMessage({ id: '是否计泡' }),
        dataIndex: 'existThrowWeight',
        key: 'existThrowWeight',
      },
      {
        title: `${formatMessage({ id: '计费重' })}}(g)`,
        dataIndex: 'calcWeightG',
        key: 'calcWeightG',
      },
      {
        title: `${formatMessage({ id: '总费用' })}(${formatMessage({ id: '元' })})`,
        dataIndex: 'rmbTotalMoney',
        key: 'rmbTotalMoney',
      },
      {
        title: formatMessage({ id: '状态' }),
        dataIndex: 'state',
        key: 'state',
      },
      {
        title: formatMessage({ id: '创建时间' }),
        dataIndex: 'createTime',
        key: 'createTime',
      },
    ];
    // {formatMessage({id: '退件签收'})}
    const returnColumns = [
      {
        title: formatMessage({ id: '退件大包号' }),
        dataIndex: 'packageNumber',
        key: 'packageNumber',
      },
      {
        title: formatMessage({ id: '制单账号' }),
        dataIndex: 'shippingAccount',
        key: 'shippingAccount',
      },
      {
        title: formatMessage({ id: '运单号' }),
        dataIndex: 'waybillNumber',
        key: 'waybillNumber',
      },
      {
        title: formatMessage({ id: '制单时间' }),
        dataIndex: 'platformOrderTime',
        key: 'platformOrderTime',
      },
      {
        title: formatMessage({ id: '退件时间' }),
        dataIndex: 'collectionTime',
        key: 'collectionTime',
      },
      {
        title: formatMessage({ id: '客户签收时间' }),
        dataIndex: 'signTime',
        key: 'signTime',
        filterType: isSignFor == '1' ? true : undefined,
      },
      {
        title: formatMessage({ id: '退件原因' }),
        dataIndex: 'exceptionTypeName',
        key: 'exceptionTypeName',
      },
      {
        title: formatMessage({ id: '目的国' }),
        dataIndex: 'regionName',
        key: 'regionName',
      },
      {
        title: formatMessage({ id: '状态' }),
        dataIndex: 'status',
        key: 'status',
        render: text => {
          return (
            <div>
              {text == 3
                ? formatMessage({ id: '司机送达' })
                : text == 4
                ? formatMessage({ id: '客户签收' })
                : text == 7
                ? formatMessage({ id: '微信签收' })
                : null}
            </div>
          );
        },
      },
    ];
    // {formatMessage({id: '仓外异常件'})}
    const columnsWare = [
      {
        title: formatMessage({ id: '运单号' }),
        dataIndex: 'waybillNumber',
        fixed: 'left',
        render: (text, record) => <a onClick={() => this.detailWare(record)}>{text}</a>,
        width: 100,
      },
      {
        title: formatMessage({ id: '订单号' }),
        dataIndex: 'orderCode',
        width: 150,
      },
      {
        title: formatMessage({ id: '平台交易号' }),
        dataIndex: 'transactionNumber',
        width: 140,
      },
      {
        title: formatMessage({ id: '制单账号' }),
        dataIndex: 'customerCode',
        width: 150,
      },
      {
        title: formatMessage({ id: '转单号' }),
        dataIndex: 'exchangeNumber',
        width: 140,
      },
      {
        title: formatMessage({ id: '产品名称' }),
        dataIndex: 'productName',
        width: 250,
      },
      {
        title: formatMessage({ id: '目的地' }),
        dataIndex: 'regionCh',
        width: 250,
      },
      {
        title: formatMessage({ id: '异常原因' }),
        dataIndex: 'abnormalType',
        width: 140,
      },
      {
        title: formatMessage({ id: '异常详情' }),
        dataIndex: 'abnormalityDetails',
        width: 140,
      },
      {
        title: formatMessage({ id: '建议处理方案' }),
        dataIndex: 'suggestHandleType',
        width: 420,
      },
      {
        title: formatMessage({ id: '原申报价值' }),
        dataIndex: 'apDeclaredValue',
        width: 140,
      },
      {
        title: formatMessage({ id: '申报币种' }),
        dataIndex: 'declaredCurrency',
        width: 140,
      },
      {
        title: formatMessage({ id: '申报品名' }),
        dataIndex: 'chineseName',
        width: 140,
      },
      {
        title: formatMessage({ id: '创建时间' }),
        dataIndex: 'createTime',
        width: 140,
      },
      {
        title: formatMessage({ id: '截止处理时间' }),
        dataIndex: 'deadline',
        width: 140,
      },
      {
        title: formatMessage({ id: '处理方案' }),
        dataIndex: 'handleType',
        width: 140,
        filterType: selectWare == '1' ? true : undefined,
      },
      {
        title: formatMessage({ id: '操作' }),
        dataIndex: 'wldo',
        fixed: 'right',
        width: '100px',
        render: (text, record) => {
          return record.status == 0 ? (
            <span>
              {/* {formatMessage({id: '改为'})} === 0 */}
              {this.state.abnormalTypeList.includes(record.abnormalType) && record.status == 0 ? (
                <Button
                  type="link"
                  onClick={() =>
                    this.handleMerchantFreeStatus(() => {
                      logSave(LogType.outAbnormal6);
                      this.batchSingleWare(record);
                    })
                  }
                >
                  {formatMessage({ id: '处理' })}
                </Button>
              ) : record.abnormalType == '燕文已代缴税' && record.status == 0 ? (
                <a
                  onClick={() =>
                    this.handleMerchantFreeStatus(() => {
                      logSave(LogType.outAbnormal7);
                      this.confirmSingleWare(record);
                    })
                  }
                >
                  {formatMessage({ id: '确认' })}
                </a>
              ) : null}
            </span>
          ) : (
            <a onClick={() => this.detailWare(record)}>{formatMessage({ id: '详情' })}</a>
          );
        },
      },
    ];

    // {formatMessage({id: '海外重派'})}
    const columnsOver = [
      {
        title: formatMessage({ id: '制单账号' }),
        dataIndex: 'customerCode',
        width: 150,
      },
      {
        title: formatMessage({ id: '运单号' }),
        dataIndex: 'waybillNumber',
        // render: (text, record) => <a onClick={() => this.detailWare(record)}>{text}</a>,
        width: 100,
      },
      {
        title: formatMessage({ id: '订单号' }),
        dataIndex: 'orderCode',
        width: 100,
      },
      {
        title: formatMessage({ id: '平台交易号' }),
        dataIndex: 'transactionNumber',
        width: 140,
      },
      {
        title: formatMessage({ id: '新转单号' }),
        dataIndex: 'newNumber',
        width: 140,
        filterType: selectOverseas == '1' ? true : undefined,
      },
      {
        title: formatMessage({ id: '目的地' }),
        dataIndex: 'regionCh',
        width: 250,
      },
      {
        title: formatMessage({ id: '产品名称' }),
        dataIndex: 'productName',
        width: 250,
      },

      {
        title: formatMessage({ id: '异常原因' }),
        dataIndex: 'abnormalType',
        width: 140,
      },
      {
        title: formatMessage({ id: '处理提示' }),
        dataIndex: 'suggestHandleType',
        filterType: selectOverseas == '2' ? true : undefined,
        width: 420,
      },
      {
        title: ` ${formatMessage({ id: '预估重派费' })}（${formatMessage({ id: '元' })}）`,
        dataIndex: 'predictCost',
        width: 140,
        render: (text, record) => {
          return record.handleType === '销毁' ? null : text;
        },
      },
      {
        title: formatMessage({ id: '处理方式' }),
        dataIndex: 'handleType',
        width: 140,
        filterType: selectOverseas == '1' ? true : undefined,
      },
      {
        title: formatMessage({ id: '创建时间' }),
        dataIndex: 'createTime',
        width: 140,
      },
      {
        title: formatMessage({ id: '截止处理时间' }),
        dataIndex: 'deadline',
        width: 140,
      },

      {
        title: formatMessage({ id: '操作' }),
        dataIndex: 'wldo',
        fixed: 'right',
        width: '100px',
        render: (text, record) => {
          return record.status == 0 ? (
            <span>
              <Button
                type="link"
                onClick={() =>
                  this.handleMerchantFreeStatus(() => {
                    this.batchSingleOver(record);
                  })
                }
              >
                {formatMessage({ id: '处理' })}
              </Button>
            </span>
          ) : (
            <a onClick={() => this.detailOver(record)}>{formatMessage({ id: '详情' })}</a>
          );
        },
      },
    ];

    // {formatMessage({id: '仓内异常件'})}
    const rowSelection = {
      selectedRowKeys: selectedRowKeys,
      onChange: (selectedRowKeys, selectedRows) => {
        let workOrderAndywCustomerCode = [];
        selectedRows.map(item => {
          workOrderAndywCustomerCode.push({
            exceptionTypeName: item.exceptionTypeName,
            workOrderNo: item.workOrderNo,
            ywCustomerCode: item.ywCustomerCode,
            recommendSolution: item.recommendSolution,
            exceptionTypeId: item.exceptionTypeId,
            waybillNumber: item.waybillNumber,
          });
        });
        this.setState({
          selectedRowKeys,
          workOrderAndywCustomerCode,
          selectedRowspra: selectedRows,
        });
      },
    };
    // {formatMessage({id: '退件签收'})}
    const rowReturnSelection = {
      selectedRowKeys,
      onChange: (selectedRowKeys, selectedRows) => {
        this.setState({ selectedRowKeys, selectedRowspra: selectedRows });
      },
      getCheckboxProps: record => ({
        disabled: isSignFor == 3 ? record.status === 4 || record.status === 7 : false, // Column configuration not to be checked
      }),
    };
    // {formatMessage({id: '仓外异常件'})}
    const rowWareSelection = {
      selectedRowKeys,
      onChange: (selectedRowKeys, selectedRows) => {
        this.setState({ selectedRowKeys, selectedRowWare: selectedRows });
      },
    };

    // {formatMessage({id: '商业快递'})}
    const rowExpressSelection = {
      selectedRowKeys,
      onChange: (selectedRowKeys, selectedRows) => {
        this.setState({ selectedRowKeys });
      },
    };

    // {formatMessage({id: '品名异常'})}
    const rowProductSelection = {
      selectedRowKeys,
      onChange: (selectedRowKeys, selectedRows) => {
        this.setState({ selectedRowKeys });
      },
    };

    // {formatMessage({id: '海外重派'})}
    const rowOverSelection = {
      selectedRowKeys,
      onChange: (selectedRowKeys, selectedRows) => {
        this.setState({ selectedRowKeys, selectedRowOver: selectedRows });
      },
    };

    const actionsTextMap = {
      selectAllText: formatMessage({ id: '全部' }),
    };
    // {formatMessage({id: '仓内异常件'})}
    const paginationProps = {
      pageSize: pageSize,
      showSizeChanger: true,
      pageSizeOptions: ['10', '20', '50', '100', '500'],
      current: activeKey == '3' ? OldpageNum : pageNum,
      total: activeKey == '3' ? parseInt(OldtotalNum) : parseInt(totalNum),
      showTotal: () => {
        return `${formatMessage({ id: '共' })}- ${
          activeKey == '3' ? parseInt(OldtotalNum) : parseInt(totalNum)
        } -${formatMessage({ id: '条记录' })}`;
      },
      onChange: (current, pageSize) => {
        this.setState({
          pageIndex: current,
          pageSize,
          selectedRowKeys: [],
          workOrderAndywCustomerCode: [],
          selectedRowspra: [],
        });
        if (activeKey == '3') {
          this.getoldList(undefined, current, pageSize);
        } else {
          this.getdataList(undefined, 'pages', current, pageSize);
        }
      },
    };
    // {formatMessage({id: '退件签收'})}
    const paginations = {
      pageSize: pageSize,
      showSizeChanger: true,
      pageSizeOptions: ['10', '20', '50', '100', '500'],
      current: pageIndex,
      total: returnTotalNum,
      showTotal: () => {
        return `${formatMessage({ id: '共' })}- ${returnTotalNum} -${formatMessage({
          id: '条记录',
        })}`;
      },
      style: { textAlign: 'center', display: 'block', width: '100%' },
      onChange: (page, pageSize) => {
        this.setState({ pageIndex: page, pageSize, selectedRowKeys: [], selectedRowspra: [] });
        this.getReturnList(page, pageSize);
      },
    };

    // {formatMessage({id: '仓外异常件'})}
    const warePageProps = {
      pageSize: pageSize,
      showSizeChanger: true,
      pageSizeOptions: ['10', '20', '50', '100', '500'],
      current: warePageIndex,
      total: wareTotalNum,
      showTotal: () => {
        return `${formatMessage({ id: '共' })}- ${wareTotalNum} -${formatMessage({
          id: '条记录',
        })}`;
      },
      onChange: (page, pageSize) => {
        this.setState(
          { warePageIndex: page, pageSize, selectedRowKeys: [], selectedRowWare: [] },
          this.getWareDataList
        );
      },
    };

    // {formatMessage({id: '海外重派'})}
    const overPageProps = {
      pageSize: pageSize,
      showSizeChanger: true,
      pageSizeOptions: ['10', '20', '50', '100', '500'],
      current: overPageIndex,
      total: overTotalNum,
      showTotal: () => {
        return `${formatMessage({ id: '共' })}- ${overTotalNum} -${formatMessage({
          id: '条记录',
        })}`;
      },
      onChange: (page, pageSize) => {
        this.setState(
          { overPageIndex: page, pageSize, selectedRowKeys: [], selectedRowOver: [] },
          this.getOverseasList
        );
      },
    };
    // {formatMessage({id: '商快费用'})}
    const commercialExpressProps = {
      pageSize: pageSize,
      showSizeChanger: true,
      pageSizeOptions: ['10', '20', '50', '100', '500'],
      current: pageIndex,
      total: commercialExpressTotalNum,
      showTotal: () => {
        return `${formatMessage({ id: '共' })}- ${commercialExpressTotalNum} -${formatMessage({
          id: '条记录',
        })}`;
      },
      onChange: (page, pageSize) => {
        this.setState({ pageIndex: page, pageSize, selectedRowKeys: [] }, this.getOverseasList);
      },
    };

    // {formatMessage({id: '品名异常'})}
    const productNameProps = {
      pageSize: pageSize,
      showSizeChanger: true,
      pageSizeOptions: ['10', '20', '50', '100', '500'],
      current: pageIndex,
      total: productNameTotalNum,
      showTotal: () => {
        return `${formatMessage({ id: '共' })}- ${productNameTotalNum} -${formatMessage({
          id: '条记录',
        })}`;
      },
      onChange: (page, pageSize) => {
        this.setState({ pageIndex: page, pageSize, selectedRowKeys: [] }, this.getProductList);
      },
    };

    const formItemLayout = {
      labelCol: { xs: { span: 24 }, xl: { span: 8 }, sm: { span: 8 } },
      wrapperCol: { xs: { span: 24 }, xl: { span: 14 }, sm: { span: 16 } },
    };

    const props = {
      onRemove: file => {
        this.setState(state => {
          const index = state.fileList.indexOf(file);
          const newFileList = state.fileList.slice();
          newFileList.splice(index, 1);
          return {
            fileList: newFileList,
          };
        });
      },
      beforeUpload: async file => {
        const type = file.type;
        try {
          await this.handleMerchantFreeStatus(() => {
            this.setState(state => ({
              fileList: [file],
            }));
          });
        } catch (error) {
          return false || Upload.LIST_IGNORE;
        }
      },
      fileList: [],
    };

    const filterTabData = value => {
      let data = [];
      switch (value) {
        case 0:
          return { tabList: tabLists };
        case 4:
          return {
            tabList: [
              { key: '0', tab: formatMessage({ id: '待处理' }) },
              { key: '1', tab: formatMessage({ id: '已认可' }) },
              { key: '2', tab: formatMessage({ id: '已拒绝' }) },
            ],
          };
        case 5:
          return {
            tabList: [
              { key: '1', tab: `${formatMessage({ id: '待处理' })}(${productPendingCount})` },
              { key: '2', tab: `${formatMessage({ id: '已处理' })}(${productEndCount})` },
              { key: '0', tab: `${formatMessage({ id: '全部' })}(${productTotalCount})` },
            ],
          };
        case 1:
          data = [
            {
              key: '3',
              tab: `${formatMessage({ id: '全部' })} (${completeCount + untreatedCount})`,
            },
            { key: '1', tab: `${formatMessage({ id: '待签收' })} (${untreatedCount})` },
            { key: '2', tab: `${formatMessage({ id: '已签收' })} (${completeCount})` },
          ];
          return { tabList: data };
        case 2:
          data = [
            { key: '1', tab: `${formatMessage({ id: '待处理' })} (${untreatedCount})` },
            { key: '2', tab: `${formatMessage({ id: '已处理' })} (${completeCount})` },
            {
              key: '0',
              tab: `${formatMessage({ id: '全部' })} (${completeCount + untreatedCount})`,
            },
          ];
          return { tabList: data };
        case 3:
          data = [
            { key: '1', tab: `${formatMessage({ id: '待处理' })} (${untreatedCount})` },
            { key: '2', tab: `${formatMessage({ id: '已处理' })} (${completeCount})` },
            {
              key: '0',
              tab: `${formatMessage({ id: '全部' })} (${completeCount + untreatedCount})`,
            },
          ];
          return { tabList: data };
        default:
          return { tabList: tabLists };
      }
    };

    const filterTabButton = value => {
      switch (value) {
        case 0:
          return (
            <Space>
              {normalSelectAbnormal[0] === 0 && outActiveKey === '1' && !isFreeStatus && (
                <Upload
                  {...props}
                  accept=".xlsx,.xls"
                  onChange={file => this.importProcessing(file)}
                  customRequest={() => {}}
                >
                  <Button type="primary" ghost loading={uploadLoading}>
                    {formatMessage({ id: '导入处理' })}
                  </Button>
                </Upload>
              )}
              {activeKey == 1 || activeKey == 3 ? null : (
                <Button
                  type="primary"
                  disabled={workOrderAndywCustomerCode.length <= 0}
                  onClick={() =>
                    this.handleMerchantFreeStatus(() => {
                      logSave(LogType.innerAbnormal4);
                      this.manyprocessing();
                    })
                  }
                >
                  {formatMessage({ id: '批量处理' })}
                </Button>
              )}
              <Button
                type="primary"
                loading={exportLoading}
                onClick={activeKey == 3 ? this.OldexportData : this.exportData}
              >
                {formatMessage({ id: '导出' })}
              </Button>
            </Space>
          );
        case 1:
          return (
            <>
              {isSignFor == 2 ? null : (
                <Button
                  type="primary"
                  disabled={selectedRowspra.length <= 0}
                  onClick={this.sureSign}
                >
                  {formatMessage({ id: '确认签收' })}
                </Button>
              )}
              {isSignFor == 3 ? null : (
                <Button
                  type="primary"
                  style={{ marginLeft: '10px' }}
                  loading={returnExportLoading}
                  onClick={this.exportReturnData}
                >
                  {formatMessage({ id: '导出' })}
                </Button>
              )}
            </>
          );
        case 2:
          return (
            <>
              {selectWare == 2 ? null : (
                <Space>
                  <Button
                    type="primary"
                    disabled={selectedRowWare.length <= 0}
                    onClick={() =>
                      this.handleMerchantFreeStatus(() => {
                        logSave(LogType.outAbnormal3);
                        this.batchWare();
                      })
                    }
                  >
                    {formatMessage({ id: '批量处理' })}
                  </Button>
                  <Button
                    type="primary"
                    disabled={selectedRowWare.length <= 0}
                    onClick={() =>
                      this.handleMerchantFreeStatus(() => {
                        logSave(LogType.outAbnormal4);
                        this.confirmWare();
                      })
                    }
                  >
                    {formatMessage({ id: '批量确认' })}
                  </Button>
                </Space>
              )}
              <Button
                type="primary"
                style={{ marginLeft: '10px' }}
                onClick={this.handleWareExport}
                loading={outsideAndWareExportLoading}
              >
                {formatMessage({ id: '导出' })}
              </Button>
            </>
          );
        case 3:
          return (
            <>
              <Button
                type="primary"
                style={{ marginLeft: '10px' }}
                onClick={this.handleOverExport}
                loading={outsideAndWareExportLoading}
              >
                {formatMessage({ id: '导出' })}
              </Button>
            </>
          );

        case 4:
          return (
            activeExpressKey === '0' && (
              <Space>
                <Button
                  type="primary"
                  disabled={selectedRowKeys.length === 0}
                  onClick={() => this.handleExpressSolve('1')}
                >
                  {formatMessage({ id: '认可' })}
                </Button>
                <Button
                  disabled={selectedRowKeys.length === 0}
                  danger
                  onClick={() => this.handleExpressSolve('2')}
                >
                  {formatMessage({ id: '拒绝' })}
                </Button>
              </Space>
            )
          );
        case 5:
          return (
            <Space>
              <Button
                type="primary"
                loading={exportLoading}
                onClick={() => this.handleProductExport()}
              >
                {formatMessage({ id: '导出' })}
              </Button>
              {activeProductKey == 1 && (
                <Button
                  type="primary"
                  disabled={selectedRowKeys.length === 0}
                  onClick={() => this.handleProductBatch()}
                >
                  {formatMessage({ id: '批量处理' })}
                </Button>
              )}
            </Space>
          );
      }
    };

    return (
      <div>
        {/*  */}
        <Fragment>
          <Card
            loading={pageInitLoading}
            bordered={false}
            className="listShipper"
            bodyStyle={{
              padding: '24px 24px 0px 24px',
            }}
          >
            <Form layout="horizontal">
              <>
                <StandardFormRow title={formatMessage({ id: '异常类型' })} block>
                  <FormItem
                    style={{ marginRight: 70, marginBottom: '0px' }}
                    validateStatus={validateTypeStatus}
                    help={
                      validateTypeStatus == 'error' ? (
                        <div style={{ textAlign: 'left' }}>
                          {formatMessage({ id: '请至少选择一个异常类型' })}
                        </div>
                      ) : null
                    }
                  >
                    {getFieldDecorator('exceptionType', { initialValue: normalSelectAbnormal })(
                      <SingleTagSelect
                        hideCheckAll
                        className="open"
                        onChange={value => {
                          this.onChangeTagTypeSelect(value);
                        }}
                        style={{ maxHeight: '100%', lineHeight: '44px' }}
                      >
                        {abnormalList &&
                          abnormalList.map((element, index) => {
                            const badgeCount = this.getBadgeCount(element.id);
                            return (
                              <SingleTagSelect.Option key={index} value={element.id}>
                                {badgeCount !== null ? (
                                  <Badge size="small" count={badgeCount} offset={[14, -7]}>
                                    <span
                                      style={{
                                        fontWeight: '900',
                                        color:
                                          normalSelectAbnormal[0] === element.id ? 'white' : '',
                                      }}
                                    >
                                      {element.name}
                                    </span>
                                  </Badge>
                                ) : (
                                  <span
                                    style={{
                                      fontWeight: '900',
                                    }}
                                  >
                                    {element.name}
                                  </span>
                                )}
                              </SingleTagSelect.Option>
                            );
                          })}
                      </SingleTagSelect>
                    )}
                  </FormItem>
                </StandardFormRow>
                <StandardFormRow title={formatMessage({ id: '制单账号' })} block>
                  {normalSelectAbnormal[0] === 4 && (
                    <FormItem
                      style={{ marginRight: 70, marginBottom: '0px' }}
                      validateStatus={validateStatus}
                      help={
                        validateStatus == 'error' ? (
                          <div style={{ textAlign: 'left' }}>
                            {formatMessage({ id: '请至少选择一个制单账号' })}
                          </div>
                        ) : null
                      }
                    >
                      {getFieldDecorator('shippers', { initialValue: [shipperInitOpt[0]] })(
                        <SingleTagSelect
                          actionsText={actionsTextMap}
                          style={{ maxHeight: '100%' }}
                          onChange={this.onChangeTagSelect}
                        >
                          {account &&
                            account.map((element, index) => (
                              // @ts-ignore
                              <SingleTagSelect.Option key={index} value={element?.accountCode}>
                                {element?.accountCode}
                              </SingleTagSelect.Option>
                            ))}
                        </SingleTagSelect>
                      )}
                    </FormItem>
                  )}
                  {normalSelectAbnormal[0] !== 4 && (
                    <FormItem
                      style={{ marginRight: 70, marginBottom: '0px' }}
                      validateStatus={validateStatus}
                      help={
                        validateStatus == 'error' ? (
                          <div style={{ textAlign: 'left' }}>
                            {formatMessage({ id: '请至少选择一个制单账号' })}
                          </div>
                        ) : null
                      }
                    >
                      {getFieldDecorator('shippers', { initialValue: shipperInitOpt })(
                        <TagSelect
                          actionsText={actionsTextMap}
                          className="open"
                          onChange={this.onChangeTagSelect}
                          style={{ maxHeight: '100%' }}
                        >
                          {account &&
                            account.map((element, index) => (
                              <TagSelect.Option key={index} value={element.accountCode}>
                                {element.accountCode}
                              </TagSelect.Option>
                            ))}
                        </TagSelect>
                      )}
                    </FormItem>
                  )}
                </StandardFormRow>
              </>
              {/* {formatMessage({id: '仓内异常件'})} start normalSelectAbnormal === 0 */}
              {normalSelectAbnormal[0] === 0 && (
                <Row gutter={20}>
                  <Col span={6} xl={6} xxl={6}>
                    <div
                      style={{
                        textAlign: 'center',
                        display: 'flex',
                        marginBottom: '10px',
                      }}
                    >
                      <Select
                        value={selectValue}
                        className="h-10"
                        bordered={false}
                        style={{ flex: '1' }}
                        onChange={v => {
                          this.setState({ selectValue: v });
                        }}
                      >
                        {activeKey != '3' && (
                          <Select.Option value="0">{formatMessage({ id: '工单号' })}</Select.Option>
                        )}
                        {activeKey != '3' && <Select.Option value="2">{'订单号'}</Select.Option>}
                        <Select.Option value="1">{formatMessage({ id: '运单号' })}</Select.Option>
                      </Select>
                      {selectValue === '0' ? (
                        <FormItem>
                          {getFieldDecorator(
                            'WorkOrderNos',
                            {}
                          )(
                            <TextArea
                              style={{
                                width: 300,
                                height: activeKey == '1' || activeKey == '2' ? 170 : 98,
                              }}
                              placeholder={formatMessage({ id: '请输入工单编号' })}
                              rows={4}
                            ></TextArea>
                          )}
                        </FormItem>
                      ) : selectValue === '1' ? (
                        <FormItem>
                          {getFieldDecorator(
                            'WaybillNumbers',
                            {}
                          )(
                            <TextArea
                              style={{
                                width: 300,
                                verticalAlign: 'top',
                                height: activeKey == '1' || activeKey == '2' ? 170 : 98,
                              }}
                              placeholder={`${formatMessage({
                                id: '请输入运单编号',
                              })}，${formatMessage({ id: '最多输入' })}500${formatMessage({
                                id: '个运单号',
                              })}，${formatMessage({ id: '多单号请以逗号' })}、${formatMessage({
                                id: '空格或回车隔开',
                              })}`}
                              rows={4}
                            ></TextArea>
                          )}
                        </FormItem>
                      ) : (
                        <FormItem>
                          {getFieldDecorator(
                            'orderCodes',
                            {}
                          )(
                            <TextArea
                              style={{
                                width: 300,
                                height: activeKey == '1' || activeKey == '2' ? 170 : 98,
                              }}
                              placeholder={formatMessage({ id: '请输入订单编号' })}
                              rows={4}
                            ></TextArea>
                          )}
                        </FormItem>
                      )}
                    </div>
                  </Col>
                  <Col span={6} xl={6} xxl={6}>
                    <FormItem
                      labelCol={{ flex: '70px' }}
                      wrapperCol={{ flex: '1' }}
                      label={formatMessage({ id: '创建时间' })}
                      style={{
                        whiteSpace: 'nowrap',
                      }}
                    >
                      {getFieldDecorator('createdAt', {})(<RangePicker allowClear />)}
                    </FormItem>
                    {activeKey != '3' && (
                      <FormItem
                        labelCol={{ flex: '70px' }}
                        wrapperCol={{ flex: '1' }}
                        label={formatMessage({ id: '产品' })}
                      >
                        {getFieldDecorator(
                          'productCode',
                          {}
                        )(
                          <Select
                            allowClear
                            style={{ width: '290px' }}
                            showSearch
                            placeholder={formatMessage({ id: '请选择产品' })}
                            optionFilterProp="children"
                          >
                            {channels && [
                              <Select.Option key={'all'} value="all">
                                全部
                              </Select.Option>,
                              ...channels
                                ?.filter(v => v.status === '1')
                                ?.map((element, index) => (
                                  <Select.Option key={index} value={element.id}>
                                    {element.nameCh}
                                  </Select.Option>
                                )),
                            ]}
                          </Select>
                        )}
                      </FormItem>
                    )}
                    {(activeKey == '1' || activeKey == '2') && (
                      <FormItem
                        labelCol={{ flex: '70px' }}
                        wrapperCol={{ flex: '1' }}
                        label={formatMessage({ id: '处理方式' })}
                        // style={{ width: '23%' }}
                      >
                        {getFieldDecorator(
                          'solutionTypeId',
                          {}
                        )(
                          <Select allowClear>
                            {processingList.map((item, index) => {
                              return (
                                <Select.Option key={index} value={item.code}>
                                  {item.name}
                                </Select.Option>
                              );
                            })}
                          </Select>
                        )}
                      </FormItem>
                    )}
                  </Col>
                  {activeKey != '3' && (
                    <Col span={7} xl={7} xxl={7}>
                      <FormItem
                        labelCol={{ flex: '70px' }}
                        wrapperCol={{ flex: '1' }}
                        label={formatMessage({ id: '异常原因' })}
                        // style={{ width: '30%' }}
                      >
                        {getFieldDecorator(
                          'exceptionTypeId',
                          {}
                        )(
                          <TreeSelect
                            allowClear
                            treeCheckable
                            className="w-full"
                            treeData={abnormalTree}
                            //   value={record?.subscribeValue ?? []}
                            // style={{ width: '250px' }}
                            placeholder="请选择异常原因"
                            //  showCheckedStrategy={SHOW_PARENT}
                            fieldNames={{
                              label: 'value',
                              value: 'code',
                              children: 'children',
                            }}
                            treeNodeFilterProp="value"
                            treeDefaultExpandedKeys={['6-0', '5-0', '4-0']}
                            //   onChange={value => handleTreeDataChange(value, 'subscribeValue', index)}
                            maxTagCount={2}
                          />
                        )}
                      </FormItem>
                      <FormItem
                        labelCol={{ flex: '70px' }}
                        wrapperCol={{ flex: '1' }}
                        label={formatMessage({ id: '国家' })}
                      >
                        {getFieldDecorator(
                          'regionId',
                          {}
                        )(
                          <Select
                            allowClear
                            showSearch
                            placeholder={formatMessage({ id: '请选择国家' })}
                            optionFilterProp="children"
                          >
                            {countryList &&
                              countryList.map((element, index) => (
                                <Select.Option key={index} value={element.id}>
                                  {element.nameCh +
                                    '/' +
                                    element.countryNamePinYin +
                                    '/' +
                                    element.nameEn +
                                    '/' +
                                    element.code}
                                </Select.Option>
                              ))}
                          </Select>
                        )}
                      </FormItem>
                      {(activeKey == '1' || activeKey == '2') && (
                        <Col span={7} offset={4}>
                          <FormItem>
                            <Space>
                              <Button onClick={this.getNewDataList} type="primary">
                                {formatMessage({ id: '查询' })}
                              </Button>
                              <Button onClick={this.resetFormData}>
                                {formatMessage({ id: '重置' })}
                              </Button>
                            </Space>
                          </FormItem>
                        </Col>
                      )}
                    </Col>
                  )}
                  {activeKey != '1' && activeKey != '2' && (
                    <Col span={2} xl={2} xxl={2}>
                      <Space style={{ marginTop: 4 }}>
                        <Button onClick={this.getNewDataList} type="primary">
                          {formatMessage({ id: '查询' })}
                        </Button>
                        <Button onClick={this.resetFormData}>
                          {formatMessage({ id: '重置' })}
                        </Button>
                      </Space>
                    </Col>
                  )}
                </Row>
              )}

              {/* {formatMessage({id: '仓内异常件'})} end normalSelectAbnormal === 0 */}
              {/* 品名异常 start normalSelectAbnormal === 5 */}
              {normalSelectAbnormal[0] === 5 && (
                <Row>
                  <Col span={6} xl={6} xxl={6}>
                    <FormItem label={formatMessage({ id: '运单号' })}>
                      {getFieldDecorator(
                        'waybillNumber',
                        {}
                      )(
                        <Input.TextArea
                          placeholder={`${formatMessage({
                            id: '请输入运单编号',
                          })}，${formatMessage({ id: '最多输入' })}50${formatMessage({
                            id: '个运单号',
                          })}，${formatMessage({ id: '多单号请以逗号' })}、${formatMessage({
                            id: '空格或回车隔开',
                          })}`}
                          size="large"
                          style={{ width: 300, verticalAlign: 'top', fontSize: '14px' }}
                          rows={4}
                        />
                      )}
                    </FormItem>
                  </Col>
                  <Col span={6} xl={6} xxl={6}>
                    <FormItem label={formatMessage({ id: '创建时间' })}>
                      {getFieldDecorator('dateTime', {
                        initialValue: [
                          moment(expressStartTime, 'YYYY-MM-DD'),
                          moment(expressEndTime, 'YYYY-MM-DD'),
                        ],
                      })(<RangePicker format="YYYY-MM-DD" />)}
                    </FormItem>
                    <FormItem label={formatMessage({ id: '是否到仓' })}>
                      {getFieldDecorator('arrivedState')(
                        <Radio.Group
                          options={[
                            { label: '是', value: '2' },
                            { label: '否', value: '1' },
                          ]}
                        />
                      )}
                    </FormItem>
                  </Col>
                  <Col span={6} xl={6} xxl={6}>
                    <Space style={{ marginTop: 4, marginLeft: 20 }} direction="vertical">
                      <Button
                        onClick={() => {
                          logSave(LogType.editProductName1);
                          this.getProductList();
                        }}
                        type="primary"
                        loading={productLoading}
                      >
                        {formatMessage({ id: '查询' })}
                      </Button>
                      <Button
                        onClick={() => {
                          logSave(LogType.editProductName2);
                          this.resetFormData();
                        }}
                      >
                        {formatMessage({ id: '重置' })}
                      </Button>
                    </Space>
                  </Col>
                </Row>
              )}
              {/* 品名异常 end normalSelectAbnormal === 5 */}
              {/* {formatMessage({id: '商快费用确认'})} start normalSelectAbnormal === 4 */}
              {normalSelectAbnormal[0] === 4 && (
                <Row>
                  {/* {formatMessage({id: '暂时隐藏掉运单号查询'})}，{formatMessage({id: '后续有需求在添加'})} */}
                  {/* <Col span={6} xl={6} xxl={6}>
                    <FormItem label="{formatMessage({id: '运单号'})}">
                      {getFieldDecorator(
                        'waybillNumbers',
                        {}
                      )(
                        <Input.TextArea
                          placeholder="{formatMessage({id: '请填写运单号'})}，{formatMessage({id: '以逗号隔开'})}"
                          size="large"
                          style={{ width: 300, verticalAlign: 'top', fontSize: '14px' }}
                          rows={4}
                        />
                      )}
                    </FormItem>
                  </Col> */}
                  <Col span={6} xl={6} xxl={6}>
                    <FormItem label={formatMessage({ id: '创建时间' })}>
                      {getFieldDecorator('dateTime', {
                        initialValue: [
                          moment(expressStartTime, 'YYYY-MM-DD'),
                          moment(expressEndTime, 'YYYY-MM-DD'),
                        ],
                      })(<RangePicker format="YYYY-MM-DD" />)}
                    </FormItem>
                  </Col>
                  <Col span={6} xl={6} xxl={6}>
                    <Space style={{ marginTop: 4, marginLeft: 20 }}>
                      <Button onClick={() => this.getCommercialExpressList()} type="primary">
                        {formatMessage({ id: '查询' })}
                      </Button>
                      <Button onClick={this.resetFormData} style={{ marginLeft: '10px' }}>
                        {formatMessage({ id: '重置' })}
                      </Button>
                    </Space>
                  </Col>
                </Row>
              )}
              {/* {formatMessage({id: '商快费用确认'})} end normalSelectAbnormal === 4 */}
              {/*  {formatMessage({id: '退件签收'})} start normalSelectAbnormal === 1 */}
              {normalSelectAbnormal[0] === 1 && (
                <Row>
                  <Col span={5}>
                    <FormItem label={'运单号'}>
                      {getFieldDecorator(
                        'WaybillNumbers1',
                        {}
                      )(
                        <Input.TextArea
                          placeholder={`${formatMessage({ id: '请填写运单号' })}，${formatMessage({
                            id: '以逗号隔开',
                          })}`}
                          size="large"
                          style={{ width: '210px', fontSize: '14px' }}
                          rows={4}
                        />
                      )}
                    </FormItem>
                  </Col>
                  <Col span={5}>
                    <FormItem label={'退件大包号'}>
                      {getFieldDecorator(
                        'packageNumber1',
                        {}
                      )(
                        <Input.TextArea
                          placeholder={`${formatMessage({
                            id: '请填写退件大包号',
                          })}，${formatMessage({
                            id: '以逗号隔开',
                          })}`}
                          size="large"
                          style={{ width: '210px', fontSize: '14px' }}
                          rows={4}
                        />
                      )}
                    </FormItem>
                  </Col>
                  <Col span={8}>
                    <StandardFormRow
                      title={formatMessage({ id: '退件时间' })}
                      grid
                      last
                      style={{ paddingLeft: '24px' }}
                    >
                      <FormItem
                        style={{ padding: '0px', marginLeft: '-8px', whiteSpace: 'nowrap' }}
                      >
                        {getFieldDecorator('shippersw', {
                          initialValue:
                            isSignFor == 1
                              ? []
                              : [moment(startDate, 'YYYY-MM-DD'), moment(endDate, 'YYYY-MM-DD')],
                        })(<RangePicker format="YYYY-MM-DD" />)}
                      </FormItem>
                      <Space style={{ marginTop: 4, marginLeft: 20 }}>
                        <Button
                          onClick={() => {
                            logSave(LogType.returnSign1);
                            this.getReturnList();
                          }}
                          type="primary"
                        >
                          {formatMessage({ id: '查询' })}
                        </Button>
                        <Button
                          onClick={() => {
                            logSave(LogType.returnSign2);
                            this.resetFormData();
                          }}
                          style={{ marginLeft: '10px' }}
                        >
                          {formatMessage({ id: '重置' })}
                        </Button>
                      </Space>
                    </StandardFormRow>
                  </Col>
                </Row>
              )}

              {/*  {formatMessage({id: '退件签收'})} end normalSelectAbnormal === 1 */}
              {/*   {formatMessage({id: '仓外异常件'})} start normalSelectAbnormal === 2 */}
              {normalSelectAbnormal[0] === 2 && (
                <Row
                  style={{
                    marginLeft: '24px',
                  }}
                >
                  <Col span={6} xl={6} xxl={6}>
                    <FormItem label={formatMessage({ id: '运单号' })}>
                      {getFieldDecorator(
                        'waybillNumbers2',
                        {}
                      )(
                        <TextArea
                          style={{ width: 300, verticalAlign: 'top' }}
                          placeholder={`${formatMessage({
                            id: '请输入运单编号',
                          })}，${formatMessage({ id: '最多输入' })}50${formatMessage({
                            id: '个运单号',
                          })}，${formatMessage({ id: '多单号请以逗号' })}、${formatMessage({
                            id: '空格或回车隔开',
                          })}`}
                          rows={4}
                        ></TextArea>
                      )}
                    </FormItem>
                  </Col>
                  <Col span={6} xl={6} xxl={6}>
                    <FormItem label={formatMessage({ id: '创建时间' })}>
                      {getFieldDecorator(
                        'createWarehouse',
                        {}
                      )(<RangePicker format="YYYY-MM-DD" />)}
                    </FormItem>
                  </Col>
                  <Col span={6} xl={6} xxl={6}>
                    <FormItem label={formatMessage({ id: '异常原因' })}>
                      {getFieldDecorator(
                        'exceptionTypeId2',
                        {}
                      )(
                        <Select
                          style={{ width: '200px' }}
                          allowClear
                          showSearch
                          mode="multiple"
                          maxTagCount={3}
                          onSelect={value => {
                            const { exceptionTypeId2 } = getFieldsValue(['exceptionTypeId2']);
                            if (value === 'all') {
                              setFieldsValue({
                                exceptionTypeId2: ['all'].concat(
                                  abnormalTypeList.map(item => item)
                                ),
                              });
                            } else if (exceptionTypeId2?.length === abnormalTypeList?.length) {
                              setFieldsValue({
                                exceptionTypeId2: ['all'].concat(exceptionTypeId2),
                              });
                            }
                          }}
                          onDeselect={value => {
                            const { exceptionTypeId2 } = getFieldsValue(['exceptionTypeId2']);
                            if (value === 'all') {
                              setFieldsValue({
                                exceptionTypeId2: [],
                              });
                            } else if (
                              exceptionTypeId2?.includes('all') &&
                              exceptionTypeId2?.filter(item => item !== 'all')?.length <
                                abnormalTypeList?.length
                            ) {
                              setFieldsValue({
                                exceptionTypeId2: exceptionTypeId2?.filter(item => item !== 'all'),
                              });
                            }
                          }}
                        >
                          <Select.Option value={'all'}>全部</Select.Option>
                          {abnormalTypeList.map((item, index) => {
                            return (
                              <Select.Option key={index} value={item}>
                                {item}
                              </Select.Option>
                            );
                          })}
                        </Select>
                      )}
                    </FormItem>
                  </Col>
                  <Col span={6} xl={6} xxl={6}>
                    <div style={{ display: 'inline-block', lineHeight: '40px' }}>
                      <Button
                        onClick={() => {
                          logSave(LogType.outAbnormal1);
                          this.getWareDataList();
                        }}
                        type="primary"
                      >
                        {formatMessage({ id: '查询' })}
                      </Button>
                      <Button
                        onClick={() => {
                          logSave(LogType.outAbnormal2);
                          this.resetFormData();
                        }}
                        style={{ marginLeft: '10px' }}
                      >
                        {formatMessage({ id: '重置' })}
                      </Button>
                    </div>
                  </Col>

                  {/* <Col span={24} style={{ paddingLeft: '24px' }}></Col> */}
                </Row>
              )}

              {/*  {formatMessage({id: '仓外异常件'})} end normalSelectAbnormal === 2 */}
              {/*  {formatMessage({id: '海外重派'})} end normalSelectAbnormal === 3 */}
              {normalSelectAbnormal[0] === 3 && (
                <div
                  style={{
                    marginLeft: '24px',
                  }}
                >
                  <Row>
                    <Col span={6} xl={6} xxl={6}>
                      <FormItem
                        label={formatMessage({ id: '运单号' })}
                        style={{ marginBottom: '0px' }}
                      >
                        {getFieldDecorator(
                          'waybillNumbers3',
                          {}
                        )(
                          <TextArea
                            style={{ width: 300, verticalAlign: 'top' }}
                            placeholder={`${formatMessage({
                              id: '请输入运单编号',
                            })}，${formatMessage({ id: '最多输入' })}50${formatMessage({
                              id: '个运单号',
                            })}，${formatMessage({ id: '多单号请以逗号' })}、${formatMessage({
                              id: '空格或回车隔开',
                            })}`}
                            rows={4}
                          ></TextArea>
                        )}
                      </FormItem>
                    </Col>
                    <Col span={6} xl={6} xxl={6}>
                      <FormItem label={formatMessage({ id: '创建时间' })}>
                        {getFieldDecorator(
                          'createOverseas',
                          {}
                        )(<RangePicker format="YYYY-MM-DD" />)}
                      </FormItem>
                    </Col>
                    <Col span={6} xl={6} xxl={6}>
                      <FormItem label={formatMessage({ id: '异常原因' })}>
                        {getFieldDecorator(
                          'exceptionTypeId3',
                          {}
                        )(
                          <Select
                            style={{ width: '200px' }}
                            allowClear
                            showSearch
                            mode="multiple"
                            maxTagCount={3}
                            onSelect={value => {
                              const { exceptionTypeId3 } = getFieldsValue(['exceptionTypeId3']);
                              if (value === 'all') {
                                setFieldsValue({
                                  exceptionTypeId3: ['all'].concat(
                                    abnormalTypeList.map(item => item)
                                  ),
                                });
                              } else if (exceptionTypeId3?.length === abnormalTypeList?.length) {
                                setFieldsValue({
                                  exceptionTypeId3: ['all'].concat(exceptionTypeId3),
                                });
                              }
                            }}
                            onDeselect={value => {
                              const { exceptionTypeId3 } = getFieldsValue(['exceptionTypeId3']);
                              if (value === 'all') {
                                setFieldsValue({
                                  exceptionTypeId3: [],
                                });
                              } else if (
                                exceptionTypeId3?.includes('all') &&
                                exceptionTypeId3?.filter(item => item !== 'all')?.length <
                                  abnormalTypeList?.length
                              ) {
                                setFieldsValue({
                                  exceptionTypeId3: exceptionTypeId3?.filter(
                                    item => item !== 'all'
                                  ),
                                });
                              }
                            }}
                          >
                            <Select.Option value="all">全部</Select.Option>
                            {abnormalTypeList.map((item, index) => {
                              return (
                                <Select.Option key={index} value={item}>
                                  {item}
                                </Select.Option>
                              );
                            })}
                          </Select>
                        )}
                      </FormItem>
                    </Col>
                    <Col span={6} xl={6} xxl={6}>
                      <div style={{ display: 'inline-block', lineHeight: '40px' }}>
                        <Button
                          onClick={() => {
                            logSave(LogType.overseaReTrack1);
                            this.getOverseasList();
                          }}
                          type="primary"
                        >
                          {formatMessage({ id: '查询' })}
                        </Button>
                        <Button
                          onClick={() => {
                            logSave(LogType.overseaReTrack2);
                            this.resetFormData();
                          }}
                          style={{ marginLeft: '10px' }}
                        >
                          {formatMessage({ id: '重置' })}
                        </Button>
                      </div>
                    </Col>
                  </Row>
                  <div style={{ color: '#ff6161', padding: '10px 0px' }}>
                    {formatMessage({ id: '请及时处理工单' })},{formatMessage({ id: '若在' })}【
                    {formatMessage({ id: '截止处理时间' })}】
                    {formatMessage({ id: '前未提供处理方式' })},
                    {formatMessage({ id: '则默认做销毁处理' })}
                  </div>
                </div>
              )}

              {/*  {formatMessage({id: '海外重派'})} end normalSelectAbnormal === 3 */}
            </Form>
          </Card>
          <Card
            title={
              normalSelectAbnormal[0] === 5 && (
                <Typography.Text type="danger" style={{ fontSize: '14px' }}>
                  温馨提示：此处修改仅变更品名工单信息，未变更原始订单信息，修改后的数据用于海关清关。
                </Typography.Text>
              )
            }
            loading={pageInitLoading}
            style={{ marginTop: 24 }}
            bordered={false}
            className="listCard"
            tabList={filterTabData(normalSelectAbnormal[0])?.tabList}
            activeTabKey={
              normalSelectAbnormal[0] === 0
                ? activeKey
                : normalSelectAbnormal[0] === 1
                ? isSignFor
                : normalSelectAbnormal[0] === 2
                ? selectWare
                : normalSelectAbnormal[0] === 3
                ? selectOverseas
                : normalSelectAbnormal[0] === 4
                ? activeExpressKey
                : normalSelectAbnormal[0] === 5
                ? activeProductKey
                : ''
            }
            tabProps={{
              size: 'small',
              style: {
                marginTop: '10px',
              },
            }}
            onTabChange={this.handleTabChange}
            tabBarExtraContent={filterTabButton(normalSelectAbnormal[0])}
          >
            {/*  {formatMessage({id: '仓内异常件'})}table */}
            {normalSelectAbnormal[0] === 0 && (
              <ProTableList
                size="middle"
                bordered
                dataSource={activeKey == '3' ? OldtableList : tableList}
                columns={
                  activeKey == '3'
                    ? this.oldColumns
                    : columns.filter(item => item.filterType === undefined)
                }
                pagination={paginationProps}
                rowSelection={rowSelection}
                rowClassName={record => {
                  if (this.isExceed24(record) && record.status == '0') return styles.table_td_red;
                }}
                options={{
                  reload: false,
                }}
                columnsState={{
                  persistenceKey:
                    activeKey == '3'
                      ? 'oldAbnormalPartsInTheWarehouse'
                      : 'abnormalPartsInTheWarehouse',
                  persistenceType: 'localStorage',
                }}
                scroll={{ x: 1500 }}
                sticky={{
                  offsetHeader: 56,
                }}
                rowKey={(record, index) => index}
                loading={activeKey == '3' ? oldtableloding : loading}
                search={false}
                toolBarRender={() => [
                  <Tooltip title="您可以拖动字段调整排序，自定义是否展示该字段，点击保存即可完成设置。">
                    <InfoCircleOutlined style={{ color: 'rgba(0,0,0,.45)' }} />
                  </Tooltip>,
                ]}
              />
            )}
            {/*  {formatMessage({id: '仓内异常件'})}table */}
            {/* {formatMessage({id: '品名异常'})} */}
            {normalSelectAbnormal[0] === 5 && (
              <ProTableList
                loading={productLoading}
                size="middle"
                columns={productColumns}
                bordered
                rowSelection={rowProductSelection}
                rowKey={(record, index) => record?.waybillNumber}
                dataSource={productNameDataList}
                pagination={productNameProps}
                search={false}
                toolBarRender={false}
                sticky={{
                  offsetHeader: 56,
                }}
              />
            )}
            {/* {formatMessage({id: '商快费用确认'})} */}
            {normalSelectAbnormal[0] === 4 && (
              <ProTableList
                loading={expressLoading}
                size="middle"
                columns={expressColumns.filter(item => item.filterType == undefined)}
                bordered
                rowSelection={rowExpressSelection}
                rowKey={(record, index) => record?.waybillNumber}
                dataSource={commercialExpressDataList}
                pagination={commercialExpressProps}
                search={false}
                toolBarRender={false}
                sticky={{
                  offsetHeader: 56,
                }}
              />
            )}
            {/* {formatMessage({id: '退件签收'})}table */}
            {normalSelectAbnormal[0] === 1 && (
              <ProTableList
                loading={returnLoading}
                size="middle"
                columns={returnColumns.filter(item => item.filterType == undefined)}
                bordered
                rowSelection={rowReturnSelection}
                rowKey={(record, index) => index}
                dataSource={returnDataList}
                pagination={paginations}
                search={false}
                toolBarRender={false}
                sticky={{
                  offsetHeader: 56,
                }}
              />
            )}

            {/* {formatMessage({id: '退件签收'})}table */}
            {/* {formatMessage({id: '仓外异常件'})}table */}
            {normalSelectAbnormal[0] === 2 && (
              <ProTableList
                loading={wareLoading}
                size="middle"
                columns={columnsWare.filter(item => item.filterType == undefined)}
                bordered
                rowSelection={rowWareSelection}
                rowKey={(record, index) => index}
                dataSource={wareDataList}
                pagination={warePageProps}
                scroll={{ x: 1500 }}
                sticky={{
                  offsetHeader: 56,
                }}
                search={false}
                toolBarRender={false}
              />
            )}

            {/* {formatMessage({id: '仓外异常件'})}table */}
            {/* {formatMessage({id: '海外重派'})}table */}
            {normalSelectAbnormal[0] === 3 && (
              <ProTableList
                loading={wareLoading}
                size="middle"
                columns={columnsOver.filter(item => item.filterType == undefined)}
                bordered
                rowSelection={rowOverSelection}
                rowKey={(record, index) => index}
                dataSource={overDataList}
                pagination={overPageProps}
                scroll={{ x: 1500 }}
                sticky={{
                  offsetHeader: 56,
                }}
                search={false}
                toolBarRender={false}
              />
            )}

            {/* {formatMessage({id: '海外重派'})}table */}
          </Card>
        </Fragment>
        <OldAbnormalDetail
          ref={this.oldDetailModal}
          {...this.props}
          onCancel={() => this.onChangeTagTypeSelect(normalSelectAbnormal, pageSize)}
        />
        <AbnormalDetail
          ref={this.problemDetailModal}
          {...this.props}
          onCancel={() => this.onChangeTagTypeSelect(normalSelectAbnormal, pageSize)}
        />
        <AbnormalDispose
          ref={this.problemDisposeModal}
          onCancel={() => this.onChangeTagTypeSelect(normalSelectAbnormal, pageSize)}
          {...this.props}
        />
        <AbnormalBatch
          ref={this.outsideBatchModal}
          onCancel={() => this.onChangeTagTypeSelect(normalSelectAbnormal, pageSize)}
          {...this.props}
        />
        <AbnormalBatchOver
          modalRef={this.wareBatchModal}
          onCancel={() => this.onChangeTagTypeSelect(normalSelectAbnormal, pageSize)}
        />
        <AbnormalWareDetail ref={this.outsideDetailModal} />
        <AbnormalOverDetail modalRef={this.wareDetailModal} />
        <AbnormalExpressDetail
          {...this.props}
          modalRef={this.expressDetailModal}
          onSubmit={() => this.onChangeTagTypeSelect(normalSelectAbnormal, pageSize)}
          activeExpressKey={activeExpressKey}
        />
        <AbnormalProductDispose
          {...this.props}
          modalRef={this.productDisposeModal}
          onSubmit={() => this.onChangeTagTypeSelect(normalSelectAbnormal, pageSize)}
        />
        <AbnormalProductDetail {...this.props} modalRef={this.productDetailModal} />
      </div>
    );
  }
}

export default ProblemPieces;
