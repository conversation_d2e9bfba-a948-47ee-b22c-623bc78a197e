import React, { Component, forwardRef } from 'react';
import router from 'umi/router';
import { connect } from 'dva';
import { PlusOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {
  Breadcrumb,
  Button,
  Card,
  Radio,
  Upload,
  Modal,
  Input,
  message,
  Affix,
  Table,
  Descriptions,
  Space,
  notification,
  Tooltip,
} from 'antd';
// import styles from '@/pages/Express/anomalyOrder/AbnormalDispose.less';
import { isAuth } from '@/utils/utils';
import {
  editNewsArray,
  recognitionExpense,
  invoiceArr,
  Issueagain,
  provideInformation,
  CancelIntercept,
  Modifycustomernum,
  showWeightAndFreight,
  declareValueList,
  editNewsRecipientArray,
  editNewsDeclarationArray,
} from '@/utils/commonConstant';
import order from '@/models/order';
import PlatformDispose from './PlatformDispose';
import PlatformDisposeEditTable from './PlatformDisposeEditTable';

const RadioButton = Radio.Button;
const RadioGroup = Radio.Group;
const FormItem = Form.Item;
const { TextArea } = Input;
let isImag = false;

const checkRedioArr = [
  { name: '弃件', code: '13' },
  { name: '退回-司机', code: '4' },
  { name: '退回-自取', code: '6' },
  { name: '退回-快递寄付', code: '20' },
  { name: '退回-快递到付', code: '19' },
];

class AbnormalDispose extends Component {
  // eslint-disable-next-line no-useless-constructor
  constructor(props) {
    super(props);

    this.state = {
      checkedValue: [],
      processing: '',
      // 图片上传
      changeOrderFileList: [],
      previewImage: '',
      accessoryVisible: false,
      isbtndis: true,
      dataSource: [],
      remarksplaceholder: false,
      remarksplaBtn: false,
      editremarkvisiable: false,
      recordata: {},
      labelname: '',
      exceptionTypeId: '-1',
      noteState: false,
      parcelList: [],
      changeOrderState: false, // 一键换单提示框
      waybillNumber: '', // 运单号
      customerCode: '', // 制单账号
      initData: {}, // 初始化数据
      changeOrderStatus: 0, // 一键换单数据状态
      newAwbs: '',
      open: false,
      showWeightAndFreightItem: false,
      paramsInfo: {},
      platformData: [], // 平台数据
    };

    this.platformRef = React.createRef();
  }

  openModal = params => {
    this.setState(
      {
        paramsInfo: {
          ...this.state.paramsInfo,
          ...params,
        },
      },
      () => {
        this.gettableList(params);
        this.getDisposeType(params);
      }
    );
    if (sessionStorage.getItem('newAwb')) {
      this.setState({
        newAwbs: sessionStorage.getItem('newAwb'),
        processing: '10',
        labelname: '换运单重发',
        isbtndis: false,
        noteState: true,
      });
    }
  };

  // 根据异常原因类型获取处理方式
  getDisposeType = para => {
    const { dispatch } = this.props;
    // const para = JSON.parse(match.params.orderIdJson);
    this.setState({
      open: true,
      initData: para,
      changeOrderStatus: para.status,
    });
    // const { exceptionTypeId } = this.state;
    let exceptionTypeId = [];
    const priceArr = [
      '超偏远附加费',
      '超长超重费用',
      '磁检费大货',
      '磁检费小货',
      '高关税',
      '偏远附加费',
      '私人地址费',
      '限运目的地附加费',
      '邮编偏远',
      '重新包装费',
      '泡重比过高',
    ];
    let recommendSolution = [];
    let sameState = [];
    let ids = []; // 修改信息ID
    let receiverArr = []; // 收件人信息ID
    let declareArr = []; // 申报信息ID
    let costArr = []; // 确认费用ID
    let newInvoice = []; // 新发票ID
    let issueagainArr = []; // 重出签发ID
    // 申报价值ID
    let declareValueArr = [];
    if (para.workOrderAndywCustomerCode && para.workOrderAndywCustomerCode != undefined) {
      this.setState({ checkedValue: checkRedioArr });
      para.workOrderAndywCustomerCode.map(item => {
        exceptionTypeId.push(item.exceptionTypeId);
        if (recommendSolution.length) {
          let arr = item?.recommendSolution?.split(',') ?? [];
          let b = [];
          for (let i = 0; i < arr.length; i++) {
            for (let j = 0; j < recommendSolution.length; j++) {
              if (recommendSolution[j] == arr[i]) {
                b.push(arr[i]);
              }
            }
          }
          sameState = b;
        }
        if (item?.recommendSolution) {
          recommendSolution = [...item.recommendSolution.split(',')];
        }
      });
      // 筛选相同值
      exceptionTypeId.forEach(item => {
        if (editNewsRecipientArray.includes(item)) {
          receiverArr.push(item);
        } else if (editNewsDeclarationArray.includes(item)) {
          declareArr.push(item);
        } else if (recognitionExpense.includes(item)) {
          costArr.push(item);
        } else if (invoiceArr.includes(item)) {
          newInvoice.push(item);
        } else if (Issueagain.includes(item)) {
          issueagainArr.push(item);
        } else if (declareValueList.includes(item)) {
          declareValueArr.push(item);
        }
      });

      let dataArr = para.workOrderAndywCustomerCode;
      for (var i = 0; i < priceArr.length; i++) {
        for (var j = 0; j < dataArr.length; j++) {
          if (dataArr[j].exceptionTypeName == priceArr[i]) {
            this.setState({ remarksplaceholder: true });
          }
        }
      }
      dispatch({
        type: 'anomalyOrder/problemPiecesGetHandleMode',
        callback: data => {
          let dataArr = data;
          let redioArr = [];
          if (sameState.length) {
            for (let i = 0; i < dataArr.length; i++) {
              for (let j = 0; j < sameState.length; j++) {
                if (dataArr[i].code == sameState[j]) {
                  redioArr.push(dataArr[i]);
                }
              }
            }
          } else {
            for (let i = 0; i < dataArr.length; i++) {
              for (let j = 0; j < recommendSolution.length; j++) {
                if (dataArr[i].code == recommendSolution[j]) {
                  redioArr.push(dataArr[i]);
                }
              }
            }
          }

          let filtdata = [];
          // if (exceptionTypeId.length != ids.length) {
          //   filtdata = redioArr.filter(item => {
          //     return item.code != '12';
          //   });
          // } else {
          filtdata = redioArr;
          // }
          filtdata.map(item => {
            if (item.code == '12') {
              const indexs = editNewsRecipientArray.filter(function(x) {
                if (receiverArr.length == exceptionTypeId.length) {
                  item.name = '收件人信息修改';
                }
              });
              const indexx = editNewsDeclarationArray.filter(function(x) {
                if (declareArr.length == exceptionTypeId.length) {
                  item.name = '申报信息修改';
                }
              });
              const indexe = recognitionExpense.filter(function(x) {
                if (costArr.length == exceptionTypeId.length) {
                  item.name = '确认费用';
                }
              });
              const indexi = invoiceArr.filter(function(x) {
                if (newInvoice.length == exceptionTypeId.length) {
                  item.name = '提供新发票';
                }
              });
              const indexis = Issueagain.filter(function(x) {
                if (issueagainArr.length == exceptionTypeId.length) {
                  if (indexis !== -1) {
                    item.name = '重出签发';
                  }
                }
              });
              const indexiss = declareValueList.filter(function(x) {
                if (declareValueArr.length == exceptionTypeId.length) {
                  if (indexiss !== -1) {
                    item.name = '申报价值确认';
                  }
                }
              });
              let deWeight = [...new Set(exceptionTypeId)];
              if (deWeight.length == 1 && deWeight[0] == '1063') {
                item.name = '修改客户号';
              } else if (deWeight.length == 1 && deWeight[0] == '1126') {
                item.name = '提供资料';
              } else if (deWeight.length == 1 && deWeight[0] == '1100') {
                item.name = '取消截留';
              } else if (deWeight.length == 1 && deWeight[0] == '1184') {
                item.name = '补齐税号';
              } else {
                item.name = item.name;
              }
            }
          });
          for (let i = 0; i < filtdata.length; i++) {
            if (filtdata[i].code == 12 && filtdata[i].name == '原单重发') {
              filtdata.splice(i, 1);
            }
          }
          this.setState({ checkedValue: filtdata });
        },
      });
    } else {
      recommendSolution =
        para.recommendSolution != undefined ? para.recommendSolution.split(',') : [];
      priceArr.map(item => {
        if (item == para.exceptionTypeName) {
          this.setState({ remarksplaceholder: true });
        }
      });
      let exceptionTypeId = para.exceptionTypeId;
      dispatch({
        type: 'anomalyOrder/problemPiecesGetHandleMode',
        callback: data => {
          let dataArr = data;
          let redioArr = [];
          for (let i = 0; i < dataArr.length; i++) {
            for (let j = 0; j < recommendSolution.length; j++) {
              if (dataArr[i].code == recommendSolution[j]) {
                redioArr.push(dataArr[i]);
              }
            }
          }
          let filtdata = redioArr;
          filtdata.map(item => {
            if (item.code == '12') {
              // Check editNewsArray first
              if (editNewsRecipientArray.includes(exceptionTypeId)) {
                item.name = '收件人信息修改';
              } else if (editNewsDeclarationArray.includes(exceptionTypeId)) {
                item.name = '申报信息修改';
              }
              // Then check recognitionExpense
              else if (recognitionExpense.includes(exceptionTypeId)) {
                item.name = '确认费用';
              }
              // Continue with other checks
              else if (invoiceArr.includes(exceptionTypeId)) {
                item.name = '提供新发票';
              } else if (Issueagain.includes(exceptionTypeId)) {
                item.name = '重出签发';
              } else if (declareValueList?.includes(exceptionTypeId)) {
                item.name = '申报价值确认';
              } else {
                // Use the existing ternary operator for the remaining cases
                item.name =
                  exceptionTypeId == '1063'
                    ? '修改客户号'
                    : exceptionTypeId == '1126'
                    ? '提供资料'
                    : exceptionTypeId == '1100'
                    ? '取消截留'
                    : exceptionTypeId == '1184'
                    ? '补齐税号'
                    : item.name;
              }
            }
            return item; // Don't forget to return the item
          });
          this.setState({ checkedValue: filtdata });
        },
      });
    }
  };
  gettableList = para => {
    const { form, dispatch } = this.props;
    // const para = JSON.parse(match.params.orderIdJson);
    const WorkOrderNosArr = [];
    if (para.workOrderAndywCustomerCode && para.workOrderAndywCustomerCode != undefined) {
      para.workOrderAndywCustomerCode.map(item => {
        WorkOrderNosArr.push(item.workOrderNo);
      });
    } else {
      WorkOrderNosArr.push(para.workOrderNo);
    }
    dispatch({
      type: 'anomalyOrder/problemPiecesList',
      payload: {
        workOrderNos: WorkOrderNosArr,
        sourceIds: [0, 4, 5, 6, 8, 9, 10, 12, 18, 25],
        isExport: false,
        status: para.status,
      },
      callback: res => {
        let exceptionTypeIds = [];
        for (let i = 1; i < res.data.length; i++) {
          exceptionTypeIds.push(res.data[i].exceptionTypeId);
        }
        exceptionTypeIds = [...new Set(exceptionTypeIds)];
        if (exceptionTypeIds.length == 1) {
          this.setState({ exceptionTypeId: exceptionTypeIds[0] });
        }
        let filterdata = res.data;
        filterdata.map(item => {
          item.solutionTypeName = '';
          for (const key in item.parcel) {
            if (item.parcel[key] === null) {
              item.parcel[key] = '';
            }
          }
        });
        if (sessionStorage.getItem('newAwb')) {
          if (filterdata.length == 1) {
            filterdata[0].newWaybillNumber = sessionStorage.getItem('newAwb');
          } else {
            let arr = JSON.parse(sessionStorage.getItem('haveNewWaybillNumber'));
            let oldWaybillNumber = sessionStorage.getItem('oldWaybillNumber');
            for (let i = 0; i < filterdata.length; i++) {
              for (let j = 0; j < arr.length; j++) {
                if (
                  filterdata[i].waybillNumber == arr[j].waybillNumber &&
                  oldWaybillNumber == filterdata[i].waybillNumber
                ) {
                  filterdata[i].newWaybillNumber = sessionStorage.getItem('newAwb');
                } else if (filterdata[i].waybillNumber == arr[j].waybillNumber) {
                  filterdata[i].newWaybillNumber = arr[j].newWaybillNumber;
                }
              }
            }
          }
          sessionStorage.removeItem('newAwb');
        }
        this.setState({
          dataSource: filterdata,
          parcelList: JSON.parse(JSON.stringify(filterdata)),
        });
      },
    });
  };

  // 方法3：使用 for 循环（适用于大数据集）
  findMissingWaybillsUsingForLoop(arrayA, arrayB) {
    const waybillMap = {};
    const result = [];

    for (const item of arrayA) {
      waybillMap[item.waybillNumber] = true;
    }

    for (const item of arrayB) {
      if (!waybillMap[item.waybillNumber]) {
        result.push(item);
      }
    }

    return result;
  }
  // 提交
  validate = () => {
    const { form, dispatch } = this.props;
    const {
      changeOrderFileList,
      dataSource,
      processing,
      labelname,
      parcelList,
      initData,
      platformData,
    } = this.state;
    const para = initData;

    if (processing == 12 && (labelname == '修改信息' || labelname == '申报价值确认')) {
      let params = [];
      const missData = this.findMissingWaybillsUsingForLoop(platformData, dataSource);
      if (missData.length > 0) {
        message.error(`运单号${missData.map(item => item.waybillNumber).join(',')},请修改后提交`);
        return;
      }
      params = [...platformData];
      dispatch({
        type: 'anomalyOrder/orderInformation',
        payload: params,
        callback: res => {
          if (res.success) {
            message.success(res.message, 1).then(() => {
              // router.push(`/express/abnormalList`);
              this.subAbnormal();
            });
          }
        },
      });
    } else if (processing == 12 && labelname == '补齐税号') {
      let params = [];
      let isTaxationNumber;
      let isReceiverTaxNumber;
      if (dataSource.length === 1) {
        // 单个处理
        isTaxationNumber = dataSource[0].parcel?.taxationNumber ? true : false;
        isReceiverTaxNumber = dataSource[0].parcel?.receiverTaxNumber ? true : false;
        params.push({
          ...dataSource[0],
          ...dataSource[0].parcel,
        });
      } else {
        // 批量处理
        isTaxationNumber = !dataSource.some(item => !item?.parcel?.taxationNumber);
        isReceiverTaxNumber = !dataSource.some(item => !item?.parcel?.receiverTaxNumber);
        dataSource.forEach(item => {
          params.push({
            ...item,
            ...item?.parcel,
          });
        });
      }
      if (!isTaxationNumber && !isReceiverTaxNumber && dataSource.length === 1)
        return message.error('请填写寄件人税号或收件人税号!');
      dispatch({
        type: 'anomalyOrder/taxationNumber',
        payload: params,
        callback: res => {
          if (res.success) {
            message.success(res.message, 1).then(() => {
              this.subAbnormal();
            });
          } else {
            message.error(`${res.message}: ${res?.data?.join(',')}`, 1);
          }
        },
      });
    } else {
      this.subAbnormal();
    }
  };
  // 封装异常件接口
  subAbnormal = () => {
    const { form, dispatch } = this.props;
    const { initData } = this.state;
    const para = initData;
    const { changeOrderFileList, dataSource, processing, newAwbs, labelname } = this.state;
    let isWorkOrderNo = false;
    form.validateFieldsAndScroll((error, values) => {
      if (!error) {
        let paraArray = [];
        let url =
          values.changeOrderImg &&
          values.changeOrderImg != undefined &&
          changeOrderFileList.length > 0
            ? changeOrderFileList[0].url.split('?')[0]
            : null;
        if (para.workOrderAndywCustomerCode && para.workOrderAndywCustomerCode != undefined) {
          dataSource.map(item => {
            if (
              (item.newWaybillNumber == null || Number(item.newWaybillNumber) == 0) &&
              newAwbs == ''
            ) {
              isWorkOrderNo = true;
            }
            let obj = {
              ...item,
              solutionType: newAwbs ? '10' : values.disposeType,
              // WorkOrderNo: '',
              // solutionType: '',
              isToPay: true,
              returnType: '1',
              label: url,
              isPaySurcharge: false,
              isPortal: true,
            };
            paraArray.push(obj);
          });
        } else {
          if (
            (dataSource[0].newWaybillNumber == null ||
              Number(dataSource[0].newWaybillNumber) == 0) &&
            newAwbs == ''
          ) {
            isWorkOrderNo = true;
          }
          paraArray = [
            {
              ...para,
              solutionTypeName: labelname,
              solutionType: newAwbs ? '10' : values.disposeType,
              isToPay: true,
              returnType: '1',
              remark: dataSource[0].remark,
              label: url,
              isPaySurcharge: false,
              isPortal: true,
              newWaybillNumber: dataSource[0].newWaybillNumber,
              waybillNumber: dataSource[0].waybillNumber,
            },
          ];
        }
        if (processing == 10 && isWorkOrderNo == true) {
          message.error('请填写新运单号');
          return;
        }
        const sameDataWaybillNumber = paraArray.filter(
          item => item.newWaybillNumber === item.waybillNumber
        );
        if (sameDataWaybillNumber.length > 0) return message.error('请填写不同的运单号');
        dispatch({
          type: 'anomalyOrder/problemPieceschooseHandleType',
          payload: paraArray,
          callback: response => {
            sessionStorage.removeItem('oldWaybillNumber');
            sessionStorage.removeItem('haveNewWaybillNumber');
            if (response.success) {
              message.success('处理成功', 2).then(() => {
                // router.push(`/express/abnormalList`);
                this.handleCancel(response.success);
              });
            } else {
              if (processing == 10) {
                message.error(response.message, 1).then(() => {
                  //   router.push(`/express/abnormalList`);
                  this.handleCancel();
                });
              } else if (response.code == '502') {
                message.error(`${response.message}: ${response?.data?.join(',')}`, 1).then(() => {
                  //   router.push(`/express/abnormalList`);
                  this.handleCancel();
                });
              } else {
                message.error('异常工单已处理!', 1).then(() => {
                  //   router.push(`/express/abnormalList`);
                  this.handleCancel();
                });
              }
            }
          },
        });
      }
    });
  };

  handelEjf = () => {
    const { dispatch } = this.props;
    const { initData } = this.state;
    const para = initData;
    dispatch({
      type: 'homePage/enterEjf',
      payload: {
        customerCode: para.ywCustomerCode,
      },
      callback: response => {
        if (response && response.success) {
          if (response.success) {
            window.open(response.data);
          } else {
            Message.error('EJF服务暂时不可用', 2);
          }
        } else {
          Message.error('EJF服务暂时不可用', 2);
        }
      },
    });
  };

  // 图片 --------------------------------------------
  uploadChange = fileListObject => {
    const that = this;
    const { checkV } = this.state;
    // eslint-disable-next-line no-unused-vars
    const fileObj = fileListObject.file;
    if (that.state.uploadIsOK === 'no' && fileObj.status === 'done') {
      fileListObject.fileList[0].status = 'uploading';
    }
    this.setState({
      changeOrderFileList: fileListObject.fileList,
    });
  };

  handleRomove = file => {
    return new Promise(function(resolve, reject) {
      resolve(true);
    });
  };
  checkImageWH = file => {
    const that = this;
    const { dispatch } = that.props;
    const { initData } = that.state;
    const para = initData;
    return new Promise(function(resolve, reject) {
      const filereader = new FileReader();
      filereader.onload = e => {
        const formData = new FormData();
        formData.append('attach', file);
        formData.append('objName', 'tc_express_abnormal');
        formData.append('objId', para.workOrderNo);
        formData.append('fileType', '0');
        formData.append('appId', 'work-order-2.0');
        dispatch({
          type: 'paymentAccount/upload',
          payload: formData,
          callback: response => {
            const newFileList = [
              {
                uid: '-1',
                name: file.name,
                status: 'done',
                url: '',
              },
            ];
            if (response.success) {
              newFileList[0].url = response.data;
              that.state.changeOrderFileList = newFileList;
              that.setState({
                changeOrderFileList: newFileList,
              });
            } else {
              that.setState({
                changeOrderFileList: [],
              });
              message.error('文件上传失败！', 2);
            }
          },
        });
        resolve();
      };
      filereader.readAsDataURL(file);
    });
  };

  handleBeforeUpload = file => {
    const arr = file.type.split('/');
    isImag = arr[0] === 'image';
    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      message.error('请上传小于5M的文件或图片', 2);
      return false;
    }
    if (isLt5M) {
      this.checkImageWH(file);
    }
    // return isLt5M;
  };
  handlePreview = file => {
    this.setState({
      previewImage: file.url || file.thumbUrl,
      accessoryVisible: true,
    });
  };
  accessoryCancel = () => this.setState({ accessoryVisible: false });
  // -----------------------------------------------------
  /**
   * 获取退件地址
   */
  getReturnAddress = () => {
    const { processing, initData, dataSource } = this.state;
    const { dispatch } = this.props;
    const params = initData?.hasOwnProperty('workOrderAndywCustomerCode')
      ? dataSource?.map(item => ({
          ...item,
          companyCode: item?.parcel?.companyCode,
          returnType: processing,
        }))
      : [
          {
            ...initData,
            companyCode: initData?.parcel?.companyCode,
            returnType: processing,
          },
        ];
    switch (processing) {
      case '4':
      case '6':
      case '20':
      case '19':
        dispatch({
          type: 'anomalyOrder/getAddress',
          payload: params,
          callback: response => {
            if (response.success) {
              const data = response.data;
              const temporaryDataSource = dataSource;
              this.setState(prevState => {
                // Destructure initData from prevState if needed
                const { initData } = prevState;
                // Check for the 'workOrderAndywCustomerCode' property
                if (initData?.hasOwnProperty('workOrderAndywCustomerCode')) {
                  const updatedDataSource = temporaryDataSource.map(item => {
                    const existItem = data?.find(val => item?.waybillNumber === val?.waybillNumber);
                    const flag = existItem?.flag;
                    return existItem
                      ? flag
                        ? { ...item, ...existItem }
                        : {
                            ...item,
                            ...existItem,
                            companyCodeName: undefined,
                            contacts: undefined,
                            tel: undefined,
                            contactPost: undefined,
                            address: undefined,
                            collectionId: undefined,
                          }
                      : item;
                  });
                  return { dataSource: updatedDataSource };
                } else {
                  // Assuming that all the properties are dependent on 'data?.[0]?.flag'
                  const flag = data?.[0]?.flag;
                  const commonData = flag
                    ? {
                        ...data?.[0],
                      }
                    : {
                        companyCodeName: undefined,
                        contacts: undefined,
                        tel: undefined,
                        contactPost: undefined,
                        address: undefined,
                        collectionId: undefined,
                        flag,
                      };
                  return {
                    initData: {
                      ...initData,
                      ...commonData,
                    },
                  };
                }
              });
            }
          },
        });
        break;
    }
  };

  handleChange = v => {
    if (v.target.labelname == '原单重发') {
      this.setState({ remarksplaBtn: true });
    } else {
      this.setState({ remarksplaBtn: false });
    }
    const { dataSource } = this.state;
    const newData = dataSource.map(value => ({ ...value, solutionTypeName: v.target.labelname }));
    this.setState(
      {
        processing: v.target.value,
        changeOrderFileList: [],
        isbtndis: false,
        dataSource: newData,
        labelname: v.target.labelname,
      },
      () => {
        this.getReturnAddress();
      }
    );
    if (v.target.labelname == '换运单重发' || v.target.value == 12) {
      this.setState({
        noteState: true,
      });
    } else {
      this.setState({
        noteState: false,
      });
    }
  };
  // 编辑备注
  remarkChange = e => {
    let temp = [...this.state.dataSource];
    const newData = temp;
    newData.map(value => {
      value.remark = e.target.value;
    });
    this.setState({ dataSource: newData });
  };
  remarkChangea = e => {
    const { recordata, dataSource } = this.state;
    let onlychange = dataSource;
    onlychange[recordata].remark = e.target.value;
    this.setState({ dataSource: onlychange });
  };

  handelFocus = (e, index) => {
    //  弹窗 单独修改 备注
    this.props.form.setFieldsValue({ remarksMol: e.target.value });
    this.setState({ editremarkvisiable: true, recordata: index });
  };
  // 表单验证
  validateToNextPassword = (rule, value, callback) => {
    // let valNum = new RegExp('^[0-9]+$');
    let valEn = new RegExp('^[0-9A-Z]+$');
    if (value && !valEn.test(value)) {
      callback('单号由数字与大写字母组成或纯数字');
      return;
    }

    const { form } = this.props;
    const { initData } = this.state;
    const para = initData;
    const orderInfo = para && para != undefined ? para : undefined;
    if (
      orderInfo !== undefined &&
      form.getFieldValue('disposeType') === '10' &&
      value === orderInfo.waybillNumber
    ) {
      callback('新单号不能与当前异常运单一样');
      return;
    }
    callback();
  };
  onChange = (e, record, num, txt) => {
    if (num == 1) {
      record.newWaybillNumber = e.target.value;
    } else if (num == 2) {
      record.remark = e.target.value;
    } else {
      switch (txt) {
        case 'name':
          record.parcel.receiverName = e.target.value;
          break;
        case 'phone':
          record.parcel.receiverPhone = e.target.value;
          break;
        case 'postalCode':
          record.parcel.postcode = e.target.value;
          break;
        case 'state':
          record.parcel.receiverState = e.target.value;
          break;
        case 'city':
          record.parcel.receiverCity = e.target.value;
          break;
        case 'address1':
          record.parcel.receiverAddress1 = e.target.value;
          break;
        case 'address2':
          record.parcel.receiverAddress2 = e.target.value;
          break;
        case 'mail':
          record.parcel.receiverEmail = e.target.value;
          break;
        case 'taxNo':
          record.parcel.dutyParagraph = e.target.value;
          break;
        case 'chineseName':
          record.parcel.cnName = e.target.value;
          break;
        case 'englishName':
          record.parcel.enName = e.target.value;
          break;
        case 'declaredValue':
          record.parcel.declareValue = e.target.value;
          break;
        case 'hsCode':
          record.parcel.hsCode = e.target.value;
          break;
        case 'taxationNumber':
          record.parcel.taxationNumber = e.target.value;
          break;
        case 'receiverTaxNumber':
          record.parcel.receiverTaxNumber = e.target.value;
          break;
      }
    }
  };

  // 展示费用
  computational = () => {
    const { dataSource, initData } = this.state;
    const { dispatch } = this.props;
    const params = initData?.hasOwnProperty('workOrderAndywCustomerCode')
      ? dataSource
          ?.map(item => ({
            ...item,
            postcode: item?.parcel?.postcode,
            companyCode: item?.parcel?.companyCode,
          }))
          ?.filter(item => showWeightAndFreight?.includes(item?.exceptionTypeId))
      : [
          {
            ...initData,
            postcode: initData?.parcel?.postcode,
            companyCode: initData?.parcel?.companyCode,
          },
        ];
    dispatch({
      type: 'anomalyOrder/computational',
      payload: params,
      callback: response => {
        if (response.success) {
          const errorDataMessage = response?.data
            ?.filter(item => !item?.success)
            ?.map(item => item?.uniqueCode);
          const needNewArray = dataSource.filter(item =>
            showWeightAndFreight?.includes(item?.exceptionTypeId)
          );
          if (errorDataMessage.length > 0) {
            notification.error({
              message: '计算提示',
              description: `运单号：${errorDataMessage.join(',')} 计算费用失败，请联系客服或销售！`,
              duration: null,
            });
            if (errorDataMessage.length === needNewArray?.length) return;
          }
          const newData = dataSource?.map(item => {
            const existItem = response.data?.find(val => val?.uniqueCode == item?.waybillNumber);
            if (existItem) {
              return {
                ...item,
                ...existItem,
              };
            }
            return item;
          });
          if (initData?.hasOwnProperty('workOrderAndywCustomerCode')) {
            this.setState({
              dataSource: newData,
              showWeightAndFreightItem: true,
            });
          } else {
            this.setState({
              initData: { ...initData, ...response?.data?.[0] },
              dataSource: newData,
              showWeightAndFreightItem: true,
            });
          }
        }
      },
    });
  };

  // 点击一键换单
  clickChangeOrder = (code, num) => {
    this.setState({
      changeOrderState: true,
      waybillNumber: num,
      customerCode: code,
    });
  };
  // 一键换单确定
  keySingle = () => {
    const { waybillNumber, customerCode, initData, changeOrderStatus } = this.state;
    router.push(
      `/smallBag/orderManagement/creatOrder?userId=${customerCode}&expressCode=${waybillNumber}&para=true&status=${changeOrderStatus}`
    );
    sessionStorage.setItem('INITDATA', JSON.stringify(initData));
    sessionStorage.setItem('haveNewWaybillNumber', JSON.stringify(this.state.dataSource));
    sessionStorage.setItem('oldWaybillNumber', this.state.waybillNumber);
  };

  reset = () => {
    this.setState({
      isbtndis: true,
      newAwbs: '',
      dataSource: [],
      parcelList: [],
      initData: {},
      showWeightAndFreightItem: false,
      processing: '',
      platformData: [],
    });
  };

  handleCancel = value => {
    const { onCancel, form } = this.props;
    this.setState(
      {
        open: false,
      },
      () => {
        form.resetFields();
        this.reset();
      }
    );
    onCancel();
  };

  render() {
    const {
      checkedValue,
      processing,
      previewImage,
      accessoryVisible,
      changeOrderFileList,
      isbtndis,
      dataSource,
      remarksplaceholder,
      remarksplaBtn,
      editremarkvisiable,
      labelname,
      noteState,
      changeOrderState,
      waybillNumber,
      customerCode,
      initData,
      changeOrderStatus,
      newAwbs,
      open,
      showWeightAndFreightItem,
      paramsInfo,
      platformData,
    } = this.state;
    const { form, abnormalDisposeSubmitLoading, cardLoading, computationalLoading } = this.props;
    const para = initData;
    const orderInfo = para && para != undefined ? para : undefined;
    const { getFieldDecorator } = form;
    // 上传按钮
    const uploadButton = (
      <div>
        <PlusOutlined />
        <div className="ant-upload-text">Upload</div>
      </div>
    );
    // 是否展示费用的按钮逻辑
    const showMoneyButton = para.hasOwnProperty('workOrderAndywCustomerCode')
      ? para?.workOrderAndywCustomerCode?.findIndex(item =>
          showWeightAndFreight.includes(item?.exceptionTypeId)
        ) != -1
      : showWeightAndFreight.includes(para?.exceptionTypeId);

    const showLinkDownloadInvoice =
      orderInfo?.exceptionTypeName === '发票缺失' &&
      ['45', '1155', '1291'].indexOf(orderInfo?.productCode) != -1;

    const shouldHideItem = orderInfo => {
      const hasWeightException =
        orderInfo?.exceptionTypeName === '重量超重' ||
        orderInfo?.workOrderAndywCustomerCode?.find(item => item.exceptionTypeName === '重量超重')
          ?.exceptionTypeName === '重量超重';

      const hasDiffException =
        orderInfo?.exceptionTypeName === '实重比对差异过大' ||
        orderInfo?.workOrderAndywCustomerCode?.find(
          item => item.exceptionTypeName === '实重比对差异过大'
        )?.exceptionTypeName === '实重比对差异过大';

      return hasWeightException || hasDiffException ? undefined : true;
    };

    const showMoneyColumnsItem = showWeightAndFreightItem ? undefined : true;

    // 展示更换地址 or 新增地址  true 展示更新 false 新增
    const showUpdateAddressItem = para.hasOwnProperty('workOrderAndywCustomerCode')
      ? dataSource?.findIndex(item => item?.flag) != -1
      : para?.flag;

    // 展示退回地址 undefined 显示 true 隐藏
    const returnAddressHideItem =
      processing == 4 || processing == 6 || processing == 20 || processing == 19 ? undefined : true;

    const columns = [
      {
        title: '制单账号',
        dataIndex: 'ywCustomerCode',
        fixed: 'left',
        width: 100,
      },
      {
        title: '运单号',
        dataIndex: 'waybillNumber',
        width: 150,
      },
      {
        title: '新运单号',
        dataIndex: 'newWorkOrderNo',
        width: 150,
        hideItem: this.state.processing == 10 ? undefined : true,
        render: (text, record) => {
          return (
            <Input placeholder="新运单号" onChange={e => this.onChange(e, record, 1)} allowClear />
          );
        },
      },
      {
        title: '目的国',
        dataIndex: 'regionName',
        width: 140,
      },
      {
        title: '产品名称',
        dataIndex: 'productName',
        width: 200,
      },
      {
        title: '实重(g)',
        dataIndex: 'weight',
        hideItem: shouldHideItem(orderInfo),
        width: 160,
      },
      {
        title: '实重运费（元）',
        dataIndex: 'money',
        hideItem: showMoneyColumnsItem,
        width: 160,
      },
      {
        title: '计费重量（g）',
        dataIndex: 'bubbleCalcWeight',
        hideItem: showMoneyColumnsItem,
        width: 160,
      },
      {
        title: '预估费用（元）',
        dataIndex: 'bubbleMoney',
        hideItem: showMoneyColumnsItem,
        width: 160,
      },
      {
        title: '运费差额',
        dataIndex: 'freightDifference',
        hideItem: showMoneyColumnsItem,
        width: 160,
      },
      {
        title: '退件联系人',
        dataIndex: 'contacts',
        hideItem: returnAddressHideItem,
        width: 160,
      },
      {
        title: '退件联系人手机号',
        dataIndex: 'tel',
        hideItem: returnAddressHideItem,
        width: 160,
      },
      {
        title: '退件联系人岗位',
        dataIndex: 'contactPost',
        hideItem: returnAddressHideItem,
        width: 160,
      },
      {
        title: '退件地址',
        dataIndex: 'address',
        hideItem: returnAddressHideItem,
        width: 160,
      },
      {
        title: '交货仓',
        width: 160,
        render: (text, record, index) => record?.parcel?.companyCodeName,
      },
      {
        title: '备注',
        dataIndex: 'remark',
        width: 140,
        hideItem:
          this.state.processing == 12 && this.state.labelname == '补齐税号' ? true : undefined,
        render: (text, record, index) => {
          return (
            <Input
              defaultValue={record.remark}
              onChange={e => this.onChange(e, record, 2)}
              placeholder="请输入"
              suffix={
                <Tooltip title="信息请在对应框修改，填写在备注栏无法生效。">
                  <InfoCircleOutlined style={{ color: 'rgba(0,0,0,.45)' }} />
                </Tooltip>
              }
            />
          );
        },
      },
      {
        title: '异常原因',
        dataIndex: 'exceptionTypeName',
        width: 160,
      },
      {
        title: '异常具体原因',
        dataIndex: 'memo',
        width: 160,
        render: text => {
          let arr = text?.split(':');
          return arr?.length > 1 && text !== null ? arr[arr.length - 1] : text;
        },
      },
      {
        title: '处理方式',
        dataIndex: 'solutionTypeName',
        width: 140,
        render: text => {
          return <div className="noEditCell">{text}</div>;
        },
      },
      {
        title: '寄件人税号',
        dataIndex: 'taxationNumber',
        width: 140,
        hideItem:
          this.state.processing == 12 && this.state.labelname == '补齐税号' ? undefined : true,
        render: (text, record, index) => {
          return (
            <Input
              defaultValue={record.parcel.taxationNumber}
              onChange={e => this.onChange(e, record, 3, 'taxationNumber')}
            />
          );
        },
      },
      {
        title: '收件人税号',
        // dataIndex: 'ReceiverCity',
        width: 140,
        hideItem:
          this.state.processing == 12 && this.state.labelname == '补齐税号' ? undefined : true,
        render: (text, record, index) => {
          return (
            <Input
              defaultValue={record.parcel.receiverTaxNumber}
              onChange={e => this.onChange(e, record, 3, 'receiverTaxNumber')}
            />
          );
        },
      },

      {
        title: '操作',
        dataIndex: 'operator',
        width: 140,
        fixed: 'right',
        hideItem:
          this.state.processing == 12 &&
          this.state.noteState &&
          (this.state.labelname == '修改信息' || this.state.labelname == '申报价值确认')
            ? undefined
            : true,
        render: (text, record) => {
          return (
            <a
              onClick={() => {
                this.platformRef.current?.open({
                  ...record,
                  ...record?.parcel,
                });
              }}
            >
              修改
            </a>
          );
        },
      },
    ];
    const column = [
      // {
      //   title: '工单编号',
      //   dataIndex: 'workOrderNo',
      //   render: (text, record) => <a onClick={() => this.detail(record)}>{text}</a>,
      //   fixed: 'left',
      //   width: 250,
      // },
      {
        title: '制单账号',
        dataIndex: 'ywCustomerCode',
        width: 100,
        fixed: 'left',
      },
      {
        title: '运单号',
        dataIndex: 'waybillNumber',
        width: 150,
      },
      {
        title: '新运单号',
        dataIndex: 'newWorkOrderNo',
        width: 150,
        render: (text, record) => {
          return (
            <Input
              placeholder="新运单号"
              defaultValue={record.newWaybillNumber}
              onChange={e => this.onChange(e, record, 1)}
            />
          );
        },
      },
      {
        title: '目的国',
        dataIndex: 'regionName',
        width: 140,
      },
      {
        title: '产品名称',
        dataIndex: 'productName',
        width: 200,
      },
      {
        title: '异常原因',
        dataIndex: 'exceptionTypeName',
        width: 160,
      },
      {
        title: '异常具体原因',
        dataIndex: 'memo',
        width: 160,
        render: text => {
          let arr = text?.split(':');
          return arr?.length > 1 && text !== null ? arr[arr.length - 1] : text;
        },
      },
      {
        title: '处理方式',
        dataIndex: 'solutionTypeName',
        width: 140,
        render: text => {
          return <div className="noEditCell">{text}</div>;
        },
      },
      {
        title: '实重运费（元）',
        dataIndex: 'money',
        hideItem: showMoneyColumnsItem,
        width: 160,
      },
      {
        title: '计费重量（g）',
        dataIndex: 'bubbleCalcWeight',
        hideItem: showMoneyColumnsItem,
        width: 160,
      },
      {
        title: '预估费用（元）',
        dataIndex: 'bubbleMoney',
        hideItem: showMoneyColumnsItem,
        width: 160,
      },
      {
        title: '运费差额',
        dataIndex: 'freightDifference',
        hideItem: showMoneyColumnsItem,
        width: 160,
      },
      {
        title: '备注',
        dataIndex: 'remark',
        width: 140,
        render: (text, record, index) => {
          return (
            <Input
              defaultValue={record.remark}
              onChange={e => this.onChange(e, record, 2)}
              placeholder="请输入"
              suffix={
                <Tooltip title="信息请在对应框修改，填写在备注栏无法生效。">
                  <InfoCircleOutlined style={{ color: 'rgba(0,0,0,.45)' }} />
                </Tooltip>
              }
            />
          );
        },
      },
      {
        title: '操作',
        dataIndex: '',
        width: 140,
        fixed: 'right',
        hideItem: isAuth('anomalyOrder:order') ? undefined : true,
        render: (text, record, index) => {
          return record?.plateform == 'E键发' ? (
            <Button
              type="link"
              onClick={() => this.clickChangeOrder(record.ywCustomerCode, record.waybillNumber)}
            >
              一键换单
            </Button>
          ) : (
            <span>--</span>
          );
        },
      },
    ];
    return (
      <Modal
        open={open}
        footer={null}
        title={'异常工单处理'}
        width={'80%'}
        onCancel={() => this.handleCancel()}
        maskClosable={false}
      >
        {/* */}
        <Card
          style={{ marginBottom: 20 }}
          bordered={false}
          className="disposeCardClass"
          loading={cardLoading}
          extra={
            <Space>
              {showMoneyButton && (
                <Button
                  className="float-right"
                  type="primary"
                  onClick={() => this.computational()}
                  loading={computationalLoading}
                >
                  展示费用
                </Button>
              )}
              {showLinkDownloadInvoice && (
                <Button
                  className="float-right"
                  type="primary"
                  onClick={() => {
                    //  生产
                    window.open(
                      `/serviceManagement/helpCenter?type=3&id=7173581784381132800&selectId=361`
                    );
                  }}
                >
                  下载发票
                </Button>
              )}
              {labelname != '收件人信息修改' && labelname != '申报信息修改' && (
                <Button
                  type="primary"
                  style={{ float: 'right', display: checkedValue.length === 0 ? 'none' : '' }}
                  onClick={this.validate}
                  loading={abnormalDisposeSubmitLoading}
                  disabled={isbtndis}
                >
                  提交
                </Button>
              )}
            </Space>
          }
        >
          {checkedValue.length === 0 ? (
            <div style={{ textAlign: 'center', color: '#52c41a', fontSize: 20, fontWeight: 500 }}>
              暂无处理方式，请与燕文客服沟通
            </div>
          ) : (
            <div>
              <h3 style={{ fontWeight: 540 }}>选择处理方式</h3>
              <Form style={{ marginTop: 8 }}>
                <FormItem>
                  {getFieldDecorator('disposeType')(
                    <RadioGroup
                      defaultValue={newAwbs ? '10' : ''}
                      onChange={v => this.handleChange(v)}
                      style={{ marginBottom: 10 }}
                    >
                      {checkedValue.map((value, indx) => (
                        <RadioButton key={indx} value={value.code} labelname={value.name}>
                          {value.name}
                        </RadioButton>
                      ))}
                    </RadioGroup>
                  )}
                </FormItem>
                {/* <div style={{ display: 'flex' }}>
                  <div style={{ width: '600px' }}>
                    <FormItem>
                      {getFieldDecorator('remarks', {
                        initialValue: '',
                        rules: [{ required: false }],
                      })(
                        <TextArea
                          onChange={this.remarkChange}
                          style={{ minHeight: 94, maxHeight: 94 }}
                          rows={4}
                          placeholder={
                            remarksplaceholder && remarksplaBtn
                              ? '确认支付附加费(不可删除)'
                              : '请填写备注'
                          }
                        />
                      )}
                    </FormItem>
                  </div>
                  <div></div>
                </div> */}
                {/* 换单图片 */}
                {processing == '12' && (labelname == '提供新发票' || labelname == '提供资料') ? (
                  <FormItem help="提示信息：请上传小于5M的文件，多个文件上传，请上传压缩包文件">
                    {getFieldDecorator('changeOrderImg', {
                      rules: [{ required: false, message: '请上传付款截图' }],
                    })(
                      <div className="clearfix">
                        <Upload
                          action="/csc/file/upload/attachment"
                          beforeUpload={this.handleBeforeUpload}
                          listType="picture-card"
                          accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,
                            application/vnd.openxmlformats-officedocument.wordprocessingml,.pdf,application/msword,
                            image/jpeg,image/png,image/gif,.zip,.rar" // 限制格式：图片：png.jpg,jpeg.gif   文件:doc,docx,xls,xlsx,pdf
                          fileList={changeOrderFileList}
                          onPreview={this.handlePreview}
                          onChange={this.uploadChange}
                          onRemove={this.handleRomove}
                        >
                          {changeOrderFileList.length >= 1 ? null : uploadButton}
                        </Upload>
                        <Modal
                          visible={accessoryVisible}
                          footer={[
                            <Button type="primary" onClick={this.accessoryCancel}>
                              {' '}
                              取消{' '}
                            </Button>,
                          ]}
                          onCancel={this.accessoryCancel}
                        >
                          {isImag ? (
                            <img alt="example" style={{ width: '100%' }} src={previewImage} />
                          ) : (
                            <div>该文件格式不支持预览</div>
                          )}
                        </Modal>
                      </div>
                    )}
                  </FormItem>
                ) : null}
              </Form>
            </div>
          )}
        </Card>
        {/* orderInfo === undefined ||  */}
        {para.workOrderAndywCustomerCode != undefined ||
        processing == 10 ||
        processing == 12 ? null : (
          <Card style={{ paddingBottom: 0, marginBottom: 60 }} bordered={false}>
            <h3 style={{ fontWeight: 540, marginBottom: '16px' }}>工单信息</h3>
            <Descriptions
              column={3}
              style={{ marginBottom: 10, borderBottom: '1px solid #f0f0f0', paddingBottom: '20px' }}
            >
              <Descriptions.Item label="工单编号">{orderInfo.workOrderNo}</Descriptions.Item>
              <Descriptions.Item label="运单号">{orderInfo.waybillNumber}</Descriptions.Item>
              <Descriptions.Item label="制单账号">{orderInfo.ywCustomerCode}</Descriptions.Item>
              {orderInfo?.parcel?.companyCodeName && (
                <Descriptions.Item label="交货仓">
                  {orderInfo?.parcel?.companyCodeName ?? ''}
                </Descriptions.Item>
              )}
              <Descriptions.Item label="备注">
                <Input
                  style={{ width: 300 }}
                  value={dataSource?.[0]?.remark}
                  onChange={e => this.remarkChange(e)}
                  placeholder="请输入"
                  suffix={
                    <Tooltip title="信息请在对应框修改，填写在备注栏无法生效。">
                      <InfoCircleOutlined style={{ color: 'rgba(0,0,0,.45)' }} />
                    </Tooltip>
                  }
                />
              </Descriptions.Item>
            </Descriptions>
            <h3 style={{ fontWeight: 540, marginBottom: '16px' }}>异常信息</h3>
            <Descriptions>
              <Descriptions.Item label="异常原因">{orderInfo.exceptionTypeName}</Descriptions.Item>
              <Descriptions.Item label="异常具体原因">
                {(() => {
                  let arr = orderInfo.memo?.split(':');
                  return arr?.length > 1 && orderInfo.memo !== null
                    ? arr[arr.length - 1]
                    : orderInfo.memo;
                })()}
              </Descriptions.Item>
              <Descriptions.Item
                style={{
                  display:
                    orderInfo.exceptionTypeId == 1093 || orderInfo.exceptionTypeId == 1094
                      ? ''
                      : 'none',
                }}
                label="长(cm)"
              >
                {orderInfo.expressLength}
              </Descriptions.Item>
              <Descriptions.Item
                style={{
                  display:
                    orderInfo.exceptionTypeId == 1093 || orderInfo.exceptionTypeId == 1094
                      ? ''
                      : 'none',
                }}
                label="宽(cm)"
              >
                {orderInfo.expressWidth}
              </Descriptions.Item>
              <Descriptions.Item
                style={{
                  display:
                    orderInfo.exceptionTypeId == 1093 || orderInfo.exceptionTypeId == 1094
                      ? ''
                      : 'none',
                }}
                label="高(cm)"
              >
                {orderInfo.expressHigh}
              </Descriptions.Item>
              <Descriptions.Item
                style={{
                  display:
                    orderInfo.exceptionTypeId == 1093 ||
                    orderInfo.exceptionTypeId == 1094 ||
                    orderInfo.exceptionTypeId == 1096
                      ? ''
                      : 'none',
                }}
                label="实重(g)"
              >
                {orderInfo.weight}
              </Descriptions.Item>
              {showWeightAndFreightItem && (
                <>
                  <Descriptions.Item label="实重运费（元）">{orderInfo.money}</Descriptions.Item>
                  <Descriptions.Item label="计费重量（g）">
                    {orderInfo.bubbleCalcWeight}
                  </Descriptions.Item>
                  <Descriptions.Item label="预估费用（元）">
                    {orderInfo.bubbleMoney}
                  </Descriptions.Item>
                  <Descriptions.Item label="运费差额">
                    {orderInfo.freightDifference}
                  </Descriptions.Item>
                </>
              )}
            </Descriptions>
            {(processing == 4 || processing == 6 || processing == 20 || processing == 19) && (
              <>
                <Space>
                  <h3 style={{ fontWeight: 540, margin: 0 }}>退回地址</h3>
                  <Button
                    type="primary"
                    ghost
                    onClick={() => {
                      router.push(
                        showUpdateAddressItem
                          ? {
                              pathname: '/smallBag/collectManagement/addressMaintenance',
                              state: {
                                accountCode: orderInfo.ywCustomerCode,
                                warehouse: orderInfo?.parcel?.companyCode,
                              },
                            }
                          : {
                              pathname: '/smallBag/collectManagement/addressMaintenance',
                              state: {
                                accountCode: orderInfo.ywCustomerCode,
                                warehouse: orderInfo?.parcel?.companyCode,
                              },
                            }
                      );
                    }}
                  >
                    {showUpdateAddressItem ? '更换' : '新增'}地址
                  </Button>
                </Space>
                <Descriptions
                  style={{
                    marginBottom: 10,
                    borderBottom: '1px solid #f0f0f0',
                    paddingBottom: '20px',
                  }}
                >
                  <Descriptions.Item label="联系人">{orderInfo?.contacts ?? ''}</Descriptions.Item>
                  <Descriptions.Item label="联系人手机号">{orderInfo?.tel ?? ''}</Descriptions.Item>
                  <Descriptions.Item label="联系人岗位">
                    {orderInfo?.contactPost ?? ''}
                  </Descriptions.Item>
                  <Descriptions.Item label="详细地址">{orderInfo?.address ?? ''}</Descriptions.Item>
                </Descriptions>
              </>
            )}
          </Card>
        )}
        {para.workOrderAndywCustomerCode &&
        para.workOrderAndywCustomerCode != undefined &&
        !noteState &&
        (labelname == '收件人信息修改' || labelname == '申报信息修改') ? (
          <PlatformDisposeEditTable dataSource={dataSource} dispatch={this.props.dispatch} />
        ) : para.workOrderAndywCustomerCode &&
          para.workOrderAndywCustomerCode != undefined &&
          !noteState ? (
          <Card style={{ marginTop: 24 }} bordered={false} className="listCard">
            <Table
              rowKey={(record, index) => index}
              rowClassName={() => 'editable-row'}
              bordered
              dataSource={dataSource}
              columns={columns.filter(item => item.hideItem === undefined)}
              scroll={{ x: 1600 }}
              pagination={false}
            />
          </Card>
        ) : null}
        {/* 换运单重发表格 */}
        {processing == 10 && labelname == '换运单重发' ? (
          <Card style={{ marginTop: 24 }} bordered={false} className="listCard">
            <Table
              rowKey={(record, index) => index}
              rowClassName={() => 'editable-row'}
              bordered
              dataSource={[...dataSource]}
              columns={column.filter(item => item.hideItem === undefined)}
              scroll={{ x: 1600 }}
              pagination={false}
            />
          </Card>
        ) : null}
        {processing == 12 && (labelname == '收件人信息修改' || labelname == '申报信息修改') ? (
          <PlatformDisposeEditTable
            dataSource={dataSource}
            dispatch={this.props.dispatch}
            labelname={labelname}
            loading={abnormalDisposeSubmitLoading}
            subAbnormal={dataSource => {
              this.setState(
                {
                  dataSource,
                },
                () => {
                  this.subAbnormal();
                }
              );
            }}
            handleCancel={this.handleCancel}
            handleTableCancel={(row, data, labelname) => {
              const newDataSource = data.filter(item => item.workOrderNo !== row.workOrderNo);
              const newParamsInfoArray = paramsInfo.workOrderAndywCustomerCode?.filter(
                item => item.workOrderNo !== row.workOrderNo
              );
              this.setState(
                {
                  dataSource: newDataSource,
                  paramsInfo: {
                    ...paramsInfo,
                    workOrderAndywCustomerCode: newParamsInfoArray,
                  },
                },
                () => {
                  this.getDisposeType(paramsInfo);
                  setTimeout(() => {
                    form.setFieldsValue({
                      disposeType: checkedValue?.find(item => item.name === labelname)?.code,
                    });
                  }, 1000);
                }
              );
              message.success('取消成功');
            }}
          />
        ) : processing == 12 ? (
          <Card style={{ marginTop: 24 }} bordered={false} className="listCard">
            <Table
              rowKey={(record, index) => index}
              rowClassName={() => 'editable-row'}
              bordered
              dataSource={[...dataSource]}
              // columns={this.columns.filter(item => item.title != '新运单号')}
              columns={columns.filter(item => item.hideItem === undefined)}
              scroll={{ x: 1600 }}
              pagination={false}
            />
          </Card>
        ) : null}
        <Modal
          visible={editremarkvisiable}
          onCancel={() => this.setState({ editremarkvisiable: false })}
          footer={[
            <Button type="primary" onClick={() => this.setState({ editremarkvisiable: false })}>
              确定
            </Button>,
            <Button onClick={() => this.setState({ editremarkvisiable: false })}>取消</Button>,
          ]}
        >
          <Form>
            <Form.Item label="备注">
              {getFieldDecorator('remarksMol', { initialValue: '', rules: [{ required: false }] })(
                <TextArea
                  onChange={this.remarkChangea}
                  style={{ minHeight: 94, maxHeight: 94 }}
                  rows={4}
                  placeholder={
                    remarksplaceholder && remarksplaBtn ? '确认支付附加费(不可删除)' : '请填写备注'
                  }
                />
              )}
            </Form.Item>
          </Form>
        </Modal>
        {/* 一键换单弹框提示 */}
        <Modal
          title="一键换单"
          visible={changeOrderState}
          onOk={() => this.keySingle()}
          onCancel={() => {
            this.setState({ changeOrderState: false });
          }}
          footer={[
            <Button type="primary" onClick={() => this.keySingle()}>
              确定
            </Button>,
            <Button onClick={() => this.setState({ changeOrderState: false })}>取消</Button>,
          ]}
        >
          <p>
            目前一键换单功能仅支持在【小包专线-订单管理-创建订单】下创建的运单使用此功能，非在此创建的运单暂不支持
          </p>
        </Modal>
        <PlatformDispose
          {...this.props}
          modalRef={this.platformRef}
          onSubmit={values => {
            const data = dataSource.map(item => {
              if (item.waybillNumber === values?.waybillNumber) {
                return { ...item, ...values };
              }
              return item;
            });
            const submitData = platformData;
            if (
              submitData?.findIndex(item => item?.waybillNumber === values?.waybillNumber) === -1
            ) {
              submitData.push({ ...values });
            }
            this.setState({
              dataSource: data,
              platformData: submitData,
            });
          }}
        />
      </Modal>
    );
  }
}
export default AbnormalDispose;
