import React, { Component } from 'react';
import router from 'umi/router';
import { connect } from 'dva';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Card,
  Table,
  Divider,
  Modal,
  Upload,
  Descriptions,
  Row,
  Col,
  Form,
  Input,
  Typography,
  Collapse,
  Timeline,
} from 'antd';
import { ProForm, ProFormText } from '@ant-design/pro-components';
import '@/pages/SmallBag/OrderManagement/CreatOrder/createOrder.less';

import { portalUrl } from '../../../../../../../config/defaultSettings';
// const { Descriptions.Item } = Descriptions;
/*
 *@Descriptions.Item: 问题件详情
 *@MethodAuthor: dangh
 *@Date: 2023-04-12 16:41:37
 */
// @connect(({ anomalyOrder, loading }) => ({
//   anomalyOrder,
//   loading: loading.effects['anomalyOrder/problemPiecesList'],
// }))
class AbnormalDetail extends Component {
  // eslint-disable-next-line no-useless-constructor
  constructor(props) {
    super(props);
    this.state = {
      open: false,
      dataSource: [],
      showListId: [
        1082,
        1091,
        1092,
        1093,
        1094,
        1095,
        1096,
        1256,
        1257,
        1258,
        1259,
        1260,
        1261,
        1297,
        1303,
        1304,
        1305,
        1265,
        1266,
        1274,
        1275,
      ],
      previewImage: '',
      fileList: [
        {
          uid: '-1',
          name: 'image.png',
          status: 'done',
          url: '',
        },
      ],
      fileVisible: false,
      solutionTypeId: '',
      type: '1',
      abnormalLog: [],
    };
    this.formRef = React.createRef();
  }

  openModal = parm => {
    this.getAbnormalLog(parm);
    const data = [parm];
    this.setState(
      {
        open: true,
        dataSource: data,
        solutionTypeId: data[0].solutionTypeId,
        type: data?.[0]?.plateform === 'E键发' ? '1' : '2',
      },
      () => {
        if (data[0]?.solutionTypeName === '修改信息') {
          this.formRef?.current?.setFieldsValue({
            ...data[0],
            ...data[0]?.parcel,
            currency: data[0]?.parcel?.declaredCurrency,
            poPStation: {
              pointId: data[0]?.parcel?.pointId,
            },
            receiverInfo: {
              name: data[0]?.parcel?.receiverName,
              company: data[0]?.parcel?.receiverCompany,
              state: data[0]?.parcel?.receiverState,
              city: data[0]?.parcel?.receiverCity,
              zipCode: data[0]?.parcel?.postcode,
              houseNumber: data[0]?.parcel?.receiverHouse,
              phone: data[0]?.parcel?.receiverPhone,
              mobile: data[0]?.parcel?.receiverMobile,
              email: data[0]?.parcel?.receiverEmail,
              taxNumber: data[0]?.parcel?.receiverTaxNumber,
              pinflNumber: data[0]?.parcel?.receiverPinflNumber,
              address: data[0]?.parcel?.receiverAddress1,
              address2: data[0]?.parcel?.receiverAddress2,
            },
          });
        }
      }
    );
  };

  basicRender = () => (
    <Row>
      <Col span={8}>
        <ProFormText label="运单号" name="waybillNumber" readonly />
      </Col>
      <Col span={8}>
        <ProFormText label="产品名称" name="productName" readonly />
      </Col>
      <Col span={8}>
        <ProFormText label="目的国" name="regionName" readonly />
      </Col>
      <Col span={8}>
        <ProFormText label="申报币种" name="currency" readonly />
      </Col>
      {this.state.type === '2' && (
        <Col span={8}>
          <ProFormText label="申报价值" placeholder="请输入申报价值" name="declareValue" readonly />
        </Col>
      )}
      <Col span={8}>
        <ProFormText readonly label="备注" name="remark" />
      </Col>
    </Row>
  );

  recipientRender = () => {
    return (
      <Row>
        <Col span={8}>
          <ProFormText
            label="姓名"
            readonly
            name={['receiverInfo', 'name']}
            placeholder="请输入姓名"
          />
        </Col>
        {this.state.type === '1' && (
          <Col span={8}>
            <ProFormText
              label="公司"
              name={['receiverInfo', 'company']}
              readonly
              placeholder="请输入公司"
            />
          </Col>
        )}
        <Col span={8}>
          <ProFormText
            label="省/州"
            name={['receiverInfo', 'state']}
            placeholder="请输入省/州"
            readonly
          />
        </Col>
        <Col span={8}>
          <ProFormText
            label="城市"
            name={['receiverInfo', 'city']}
            placeholder="请输入城市"
            readonly
          />
        </Col>
        <Col span={8}>
          <ProFormText
            label="邮编"
            name={['receiverInfo', 'zipCode']}
            placeholder="请输入邮编"
            readonly
          />
        </Col>
        {this.state.type === '1' && (
          <Col span={8}>
            <ProFormText
              label="门牌号"
              name={['receiverInfo', 'houseNumber']}
              placeholder="请输入门牌号"
              readonly
            />
          </Col>
        )}
        {this.state.type == '1' && (
          <Col span={8}>
            <ProFormText
              label="自提点ID"
              name={['poPStation', 'pointId']}
              placeholder="请输入自提点ID"
              readonly
            />
          </Col>
        )}
        <Col span={8}>
          <ProFormText
            label="电话"
            name={['receiverInfo', 'phone']}
            placeholder="请输入电话"
            readonly
          />
        </Col>
        {this.state.type === '2' && (
          <Col span={8}>
            <ProFormText
              label="手机号"
              name={['receiverInfo', 'mobile']}
              placeholder="请输入手机号"
              readonly
            />
          </Col>
        )}
        <Col span={8}>
          <ProFormText
            label="邮箱"
            name={['receiverInfo', 'email']}
            placeholder="请输入邮箱"
            readonly
          />
        </Col>
        <Col span={8}>
          <ProFormText
            label="税号"
            name={['receiverInfo', 'taxNumber']}
            placeholder="请输入税号"
            readonly
          />
        </Col>
        {this.state.type == '1' && (
          <Col span={8}>
            <ProFormText
              label="PINFL号码"
              name={['receiverInfo', 'pinflNumber']}
              placeholder="请输入PINFL号码"
              readonly
            />
          </Col>
        )}
        <Col span={8}>
          <ProFormText
            label="地址1"
            name={['receiverInfo', 'address']}
            placeholder="请输入地址1"
            readonly
          />
        </Col>
        {this.state.type === '2' && (
          <Col span={8}>
            <ProFormText
              label="地址2"
              name={['receiverInfo', 'address2']}
              placeholder="请输入地址2"
              readonly
            />
          </Col>
        )}
      </Row>
    );
  };

  customsDeclarationRender = () => {
    const RenderUrlComponent = ({ value }) => (
      <Typography.Text
        style={{ width: 100, marginBottom: 0, marginTop: 0 }}
        ellipsis={{ tooltip: value }}
      >
        {value}
      </Typography.Text>
    );

    return (
      <Form.List name="productList">
        {(fields, { add, remove }) => (
          <>
            {fields.map(field => (
              <div className="column-itemDiv" key={field.key}>
                <div
                  className="ant-badge ant-badge-not-a-wrapper number"
                  style={{
                    backgroundColor: field.key == 0 ? '#eecd63' : '#43cf7c',
                    marginBottom: this.state.type === '1' ? '0' : '20px',
                  }}
                >
                  {field.key + 1}
                </div>
                <Card
                  bordered={false}
                  style={{ backgroundColor: field.key == 0 ? '#fffaf4' : 'fcfdfc' }}
                >
                  <Row>
                    <Col span={6}>
                      <Form.Item
                        {...field}
                        labelAlign="right"
                        label="中文品名"
                        name={[field.name, 'goodsNameCh']}
                      >
                        <Input readOnly bordered={false} allowClear />
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item
                        {...field}
                        labelAlign="right"
                        label="英文品名"
                        name={[field.name, 'goodsNameEn']}
                        rules={[
                          {
                            required: true,
                            message: '请输入英文品名',
                          },
                          {
                            max: 200,
                            message: '请输入200位以下的英文品名',
                          },
                        ]}
                      >
                        <Input readOnly bordered={false} allowClear />
                      </Form.Item>
                    </Col>
                    {this.state.type === '1' && (
                      <>
                        <Col span={6}>
                          <Form.Item
                            {...field}
                            labelAlign="right"
                            label="单票数量"
                            name={[field.name, 'quantity']}
                            rules={[
                              {
                                required: true,
                                message: '请输入单票数量',
                              },
                              {
                                max: 11,
                                message: '请输入11位以下的单品数量',
                              },
                              {
                                pattern: /^\d+$|^\d+[.]?\d+$/,
                                message: '请输入正整数数字',
                              },
                            ]}
                          >
                            <Input readOnly bordered={false} allowClear />
                          </Form.Item>
                        </Col>
                        <Col span={6}>
                          <Form.Item
                            {...field}
                            labelAlign="right"
                            label="单品重量(g)"
                            name={[field.name, 'weight']}
                            rules={[
                              {
                                required: true,
                                message: '请输入单品重量',
                              },
                              {
                                max: 11,
                                message: '请输入11位以下的单品重量',
                              },
                              {
                                pattern: /^[0-9]*[1-9][0-9]*$/,
                                message: '请输入正整数数字',
                              },
                            ]}
                          >
                            <Input readOnly bordered={false} allowClear />
                          </Form.Item>
                        </Col>
                        <Col span={6}>
                          <Form.Item
                            {...field}
                            labelAlign="right"
                            label="申报单价"
                            name={[field.name, 'price']}
                            rules={[
                              {
                                required: true,
                                message: '请输入申报单价',
                              },
                              {
                                pattern: /^([1-9]\d{0,15}\.\d{1,2}|0\.\d{1,2}|[1-9]\d{0,15})$/,
                                message: '请输入最大16位数字+小数点两位',
                              },
                            ]}
                          >
                            <Input readOnly bordered={false} allowClear />
                          </Form.Item>
                        </Col>
                        <Col span={6}>
                          <Form.Item
                            {...field}
                            labelAlign="right"
                            label="商品材质"
                            name={[field.name, 'material']}
                            rules={[
                              {
                                required: false,
                                message: '请输入商品材质',
                              },
                              {
                                max: 500,
                                message: '请输入500位以下的商品材质',
                              },
                            ]}
                          >
                            <Input readOnly bordered={false} allowClear />
                          </Form.Item>
                        </Col>
                      </>
                    )}

                    <Col span={6}>
                      <Form.Item
                        {...field}
                        labelAlign="right"
                        label="商品海关编码"
                        name={[field.name, 'hscode']}
                        rules={[
                          {
                            required: false,
                            message: '请输入商品海关编码',
                          },
                          {
                            max: 500,
                            message: '请输入500位以下的海关编码',
                          },
                        ]}
                      >
                        <Input readOnly bordered={false} allowClear />
                      </Form.Item>
                    </Col>
                    {this.state.type === '1' && (
                      <>
                        <Col span={6}>
                          <Form.Item
                            {...field}
                            labelAlign="right"
                            label="商品链接"
                            name={[field.name, 'url']}
                            rules={[
                              {
                                pattern: /^(((ht|f)tps?):\/\/)?[\w-]+(\.[\w-]+)+([\w.,@?^=%&:/~+#-]*[\w@?^=%&/~+#-])?$/,
                                required: false,
                                message: '请输入正确的商品链接',
                              },
                            ]}
                          >
                            <RenderUrlComponent />
                          </Form.Item>
                        </Col>
                        <Col span={6}>
                          <Form.Item
                            {...field}
                            labelAlign="right"
                            label="SKU"
                            name={[field.name, 'sku']}
                          >
                            <Input readOnly bordered={false} allowClear />
                          </Form.Item>
                        </Col>
                        <Col span={6}>
                          <Form.Item
                            {...field}
                            labelAlign="right"
                            label="IMEI"
                            name={[field.name, 'imei']}
                          >
                            <Input readOnly bordered={false} allowClear />
                          </Form.Item>
                        </Col>
                      </>
                    )}
                  </Row>
                </Card>
              </div>
            ))}
          </>
        )}
      </Form.List>
    );
  };

  // 图片
  handlePreview = file => {
    this.setState({
      previewImage: file.url || file.thumbUrl,
      fileVisible: true,
    });
  };

  accessoryCancel = () => this.setState({ fileVisible: false });

  handleCancel = value => {
    this.setState({
      open: false,
    });
  };

  getAbnormalLog = param => {
    const { dispatch } = this.props;
    let workOrderNo = param.workOrderNo;
    dispatch({
      type: 'anomalyOrder/getOperaLog',
      payload: { workOrderNo: workOrderNo },
      callback: result => {
        if (result.success) {
          this.setState({
            abnormalLog: result.data,
          });
        }
      },
    });
  };

  render() {
    const {
      fileList,
      fileVisible,
      previewImage,
      dataSource,
      showListId,
      solutionTypeId,
      open,
      type,
      totalNum,
      totalPrice,
      totalWeight,
      abnormalLog,
    } = this.state;
    const { loading } = this.props;
    const dataNews = dataSource != undefined && dataSource.length > 0 ? dataSource[0] : {};
    const showUploadList = { showRemoveIcon: false };
    const columns = [
      // {
      //   title: '工单编号',
      //   dataIndex: 'workOrderNo',
      //   width: 250,
      // },
      {
        title: '制单账号',
        dataIndex: 'ywCustomerCode',
        fixed: 'left',
        width: 100,
      },
      {
        title: '运单号',
        dataIndex: 'waybillNumber',
        width: 140,
        render: (text, record) => {
          return (
            <span>
              {record.waybillNumber} <br />
            </span>
          );
        },
      },
      {
        title: '新运单号',
        dataIndex: 'waybillNumber',
        width: 140,
        filterType: solutionTypeId == 10 ? undefined : true,
        render: (text, record) => {
          return <span>{record.newWaybillNumber}</span>;
        },
      },
      {
        title: '目的国',
        dataIndex: 'regionName',
        width: 140,
      },
      {
        title: '产品名称',
        dataIndex: 'productName',
        width: 140,
      },
      {
        title: '异常原因',
        dataIndex: 'exceptionTypeName',
        width: 250,
      },
      {
        title: '异常具体原因',
        dataIndex: 'memo',
        width: 150,
        render: text => {
          let arr = text?.split(':');
          return arr?.length > 1 && text !== null ? arr[arr.length - 1] : text;
        },
      },
      {
        title: '长(cm)',
        dataIndex: 'expressLength',
        filterType: showListId.includes(+dataNews.exceptionTypeId) ? undefined : true,
        width: 140,
      },
      {
        title: '宽(cm)',
        dataIndex: 'expressWidth',
        filterType: showListId.includes(+dataNews.exceptionTypeId) ? undefined : true,
        width: 140,
      },
      {
        title: '高(cm)',
        dataIndex: 'expressHigh',
        filterType: showListId.includes(+dataNews.exceptionTypeId) ? undefined : true,
        width: 140,
      },
      {
        title: '实重(g)',
        dataIndex: 'weight',
        filterType: showListId.includes(+dataNews.exceptionTypeId) ? undefined : true,
        width: 140,
      },
      {
        title: '姓名',
        dataIndex: 'name',
        width: 250,
        filterType: solutionTypeId == 12 ? undefined : true,
        render: (text, record) => {
          // return record.parcel.receiverName;
          return record.parcel.receiverName == record.originalOrderDto.name ? (
            <span>{record.originalOrderDto.name}</span>
          ) : (
            <span>
              {record.parcel.receiverName} <br />
              <p
                style={{
                  display: record.originalOrderDto.name != null ? '' : 'none',
                  backgroundColor: '#FAE6E6',
                  color: 'red',
                  borderRadius: '10px',
                }}
              >
                {record.originalOrderDto.name}
              </p>
            </span>
          );
        },
      },
      {
        title: '电话',
        dataIndex: 'name',
        width: 140,
        filterType: solutionTypeId == 12 ? undefined : true,
        render: (text, record) => {
          // return record.parcel.receiverPhone;
          return record.parcel.receiverPhone == record.originalOrderDto.mobile ? (
            <span>{record.originalOrderDto.mobile}</span>
          ) : (
            <span>
              {record.parcel.receiverPhone}
              <br />
              <p
                style={{
                  display: record.originalOrderDto.mobile != null ? '' : 'none',
                  backgroundColor: '#FAE6E6',
                  color: 'red',
                  borderRadius: '10px',
                }}
              >
                {record.originalOrderDto.mobile}
              </p>
            </span>
          );
        },
      },
      {
        title: '邮编',
        dataIndex: 'name',
        width: 140,
        filterType: solutionTypeId == 12 ? undefined : true,
        render: (text, record) => {
          // return record.parcel.postcode;
          return record.parcel.postcode == record.originalOrderDto.postalCode ? (
            <span>{record.originalOrderDto.postalCode}</span>
          ) : (
            <span>
              {record.parcel.postcode}
              <br />
              <p
                style={{
                  display: record.originalOrderDto.postalCode != null ? '' : 'none',
                  backgroundColor: '#FAE6E6',
                  color: 'red',
                  borderRadius: '10px',
                }}
              >
                {record.originalOrderDto.postalCode}
              </p>
            </span>
          );
        },
      },
      {
        title: '省/州',
        dataIndex: 'name',
        width: 200,
        filterType: solutionTypeId == 12 ? undefined : true,
        render: (text, record) => {
          // return record.parcel.receiverState;
          return record.parcel.receiverState == record.originalOrderDto.province ? (
            <span>{record.originalOrderDto.province}</span>
          ) : (
            <span>
              {record.parcel.receiverState}
              <br />
              <p
                style={{
                  display: record.originalOrderDto.province != null ? '' : 'none',
                  backgroundColor: '#FAE6E6',
                  color: 'red',
                  borderRadius: '10px',
                }}
              >
                {record.originalOrderDto.province}
              </p>
            </span>
          );
        },
      },
      {
        title: '城市',
        dataIndex: 'name',
        width: 200,
        filterType: solutionTypeId == 12 ? undefined : true,
        render: (text, record) => {
          // return record.parcel.receiverCity;
          return record.parcel.receiverCity == record.originalOrderDto.city ? (
            <span>{record.originalOrderDto.city}</span>
          ) : (
            <span>
              {record.parcel.receiverCity}
              <br />
              <p
                style={{
                  display: record.originalOrderDto.city != null ? '' : 'none',
                  backgroundColor: '#FAE6E6',
                  color: 'red',
                  borderRadius: '10px',
                }}
              >
                {record.originalOrderDto.city}
              </p>
            </span>
          );
        },
      },
      {
        title: '地址1',
        dataIndex: 'name',
        width: 300,
        filterType: solutionTypeId == 12 ? undefined : true,
        render: (text, record) => {
          // return record.parcel.receiverAddress1;
          return record.parcel.receiverAddress1 == record.originalOrderDto.address1 ? (
            <span>{record.originalOrderDto.address1}</span>
          ) : (
            <span>
              {record.parcel.receiverAddress1}
              <br />
              <p
                style={{
                  display: record.originalOrderDto.address1 != null ? '' : 'none',
                  backgroundColor: '#FAE6E6',
                  color: 'red',
                  borderRadius: '10px',
                }}
              >
                {record.originalOrderDto.address1}
              </p>
            </span>
          );
        },
      },
      {
        title: '地址2',
        dataIndex: 'name',
        width: 300,
        filterType: solutionTypeId == 12 ? undefined : true,
        render: (text, record) => {
          // return record.parcel.receiverAddress1;
          return record.parcel.receiverAddress2 == record.originalOrderDto.address2 ? (
            <span>{record.originalOrderDto.address2}</span>
          ) : (
            <span>
              {record.parcel.receiverAddress2}
              <br />
              <p
                style={{
                  display: record.originalOrderDto.address2 != null ? '' : 'none',
                  backgroundColor: '#FAE6E6',
                  color: 'red',
                  borderRadius: '10px',
                }}
              >
                {record.originalOrderDto.address2}
              </p>
            </span>
          );
        },
      },
      {
        title: '邮箱',
        dataIndex: 'name',
        width: 140,
        filterType: solutionTypeId == 12 ? undefined : true,
        render: (text, record) => {
          // return record.parcel.receiverEmail;
          return record.parcel.receiverEmail == record.originalOrderDto.mail ? (
            <span>{record.originalOrderDto.mail}</span>
          ) : (
            <span>
              {record.parcel.receiverEmail}
              <br />
              <p
                style={{
                  display: record.originalOrderDto.mail != null ? '' : 'none',
                  backgroundColor: '#FAE6E6',
                  color: 'red',
                  borderRadius: '10px',
                }}
              >
                {record.originalOrderDto.mail}
              </p>
            </span>
          );
        },
      },
      {
        title: '收件人税号',
        dataIndex: 'name',
        width: 140,
        filterType:
          solutionTypeId == 12 && dataNews.solutionTypeName != '补齐税号' ? undefined : true,
        render: (text, record) => {
          // return record.parcel.dutyParagraph;
          return record.parcel.dutyParagraph == record.originalOrderDto.taxNo ? (
            <span>{record.originalOrderDto.taxNo}</span>
          ) : (
            <span>
              {record.parcel.dutyParagraph}
              <br />
              <p
                style={{
                  display: record.originalOrderDto.taxNo != null ? '' : 'none',
                  backgroundColor: '#FAE6E6',
                  color: 'red',
                  borderRadius: '10px',
                }}
              >
                {record.originalOrderDto.taxNo}
              </p>
            </span>
          );
        },
      },
      {
        title: '中文品名',
        dataIndex: 'name',
        width: 200,
        filterType: solutionTypeId == 12 ? undefined : true,
        render: (text, record) => {
          // return record.parcel.cnName;
          return record.parcel.cnName == record.originalOrderDto.chineseName ? (
            <span>{record.originalOrderDto.chineseName}</span>
          ) : (
            <span>
              {record.parcel.cnName}
              <br />
              <p
                style={{
                  display: record.originalOrderDto.chineseName != null ? '' : 'none',
                  backgroundColor: '#FAE6E6',
                  color: 'red',
                  borderRadius: '10px',
                }}
              >
                {record.originalOrderDto.chineseName}
              </p>
            </span>
          );
        },
      },
      {
        title: '英文品名',
        dataIndex: 'name',
        width: 200,
        filterType: solutionTypeId == 12 ? undefined : true,
        render: (text, record) => {
          // return record.parcel.enName;
          return record.parcel.enName == record.originalOrderDto.englishName ? (
            <span>{record.originalOrderDto.englishName}</span>
          ) : (
            <span>
              {record.parcel.enName}
              <br />
              <p
                style={{
                  display: record.originalOrderDto.englishName != null ? '' : 'none',
                  backgroundColor: '#FAE6E6',
                  color: 'red',
                  borderRadius: '10px',
                }}
              >
                {record.originalOrderDto.englishName}
              </p>
            </span>
          );
        },
      },
      {
        title: '申报价值',
        dataIndex: 'name',
        width: 140,
        filterType: solutionTypeId == 12 ? undefined : true,
        render: (text, record) => {
          // return record.parcel.declareValue;
          return record.parcel.declareValue == record.originalOrderDto.declaredValue ? (
            <span>{record.originalOrderDto.declaredValue}</span>
          ) : (
            <span>
              {record.parcel.declareValue}
              <br />
              <p
                style={{
                  display: record.originalOrderDto.declaredValue != null ? '' : 'none',
                  backgroundColor: '#FAE6E6',
                  color: 'red',
                  borderRadius: '10px',
                }}
              >
                {record.originalOrderDto.declaredValue}
              </p>
            </span>
          );
        },
      },
      {
        title: '申报币种',
        dataIndex: 'name',
        width: 100,
        filterType: solutionTypeId == 12 ? undefined : true,
        render: (text, record) => {
          return record.parcel.declaredCurrency;
        },
      },
      {
        title: '海关编码',
        dataIndex: 'name',
        width: 140,
        filterType: solutionTypeId == 12 ? undefined : true,
        render: (text, record) => {
          return record.parcel.hsCode == record.originalOrderDto.hsCode ? (
            <span>{record.originalOrderDto.hsCode}</span>
          ) : (
            <span>
              {record.parcel.hsCode}
              <br />
              <p
                style={{
                  display: record.originalOrderDto.hsCode != null ? '' : 'none',
                  backgroundColor: '#FAE6E6',
                  color: 'red',
                  borderRadius: '10px',
                }}
              >
                {record.originalOrderDto.hsCode}
              </p>
            </span>
          );
        },
      },
      {
        title: '寄件人税号',
        dataIndex: 'taxationNumber',
        width: 140,
        filterType:
          solutionTypeId == 12 && dataNews.solutionTypeName == '补齐税号' ? undefined : true,
        render: (text, record, index) => {
          return <span>{record.parcel.taxationNumber}</span>;
        },
      },
      {
        title: '收件人税号',
        // dataIndex: 'ReceiverCity',
        width: 140,
        filterType:
          solutionTypeId == 12 && dataNews.solutionTypeName == '补齐税号' ? undefined : true,
        render: (text, record, index) => {
          return <span>{record.parcel.receiverTaxNumber}</span>;
        },
      },
      {
        title: '修改时间',
        dataIndex: 'handlingTime',
        width: 250,
        filterType: solutionTypeId == 12 ? undefined : true,
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
        width: 250,
        filterType: solutionTypeId == 12 ? undefined : true,
      },
      {
        title: '备注',
        dataIndex: 'remark',
        width: 250,
      },
    ];
    let recipientsInfo = '';

    const titleStyle = {
      fontSize: '16px',
      display: 'block',
      color: '#52C41A',
      marginBottom: '10px',
    };

    return (
      <Modal
        title={'异常工单详情'}
        footer={null}
        open={open}
        width={'70%'}
        onCancel={() => this.handleCancel()}
      >
        <div className="abnormalDetail_info">
          {/* 时间信息 */}
          <Card>
            <div
              style={{
                borderBottom: '1px #f0f0f0 solid',
                marginBottom: '16px',
                paddingBottom: '24px',
              }}
            >
              <Descriptions title="处理信息" column={3}>
                <Descriptions.Item label="状态">
                  {dataNews.status == '0' ? '待处理' : '已处理'}
                </Descriptions.Item>
                <Descriptions.Item
                  label="处理方式"
                  style={{ display: dataNews.status == 0 ? 'none' : '' }}
                >
                  {dataNews.solutionTypeName == '客户封存'
                    ? '弃件'
                    : dataNews.solutionTypeName == '退回客户-快递（到付）'
                    ? '退回-快递到付'
                    : dataNews.solutionTypeName == '退回客户-快递（寄付）'
                    ? '退回-快递寄付'
                    : dataNews.solutionTypeName}
                </Descriptions.Item>
                {dataNews?.contacts && (
                  <Descriptions.Item label="联系人">{dataNews?.contacts}</Descriptions.Item>
                )}
                {dataNews?.tel && (
                  <Descriptions.Item label="联系人手机号">{dataNews?.tel}</Descriptions.Item>
                )}
                {dataNews?.contactPost && (
                  <Descriptions.Item label="联系人岗位">{dataNews?.contactPost}</Descriptions.Item>
                )}
                {dataNews?.address && (
                  <Descriptions.Item label="退回地址">{dataNews?.address}</Descriptions.Item>
                )}
              </Descriptions>
              {dataNews.accessoryAddr != null || dataNews.accessoryAddr != undefined ? (
                <Descriptions column={1} layout="vertical">
                  <Descriptions.Item label="附件">
                    {/* 换单图片 */}
                    <div style={{ width: '110px', float: 'left' }}>
                      <Upload
                        listType="picture-card"
                        fileList={[
                          {
                            uid: '-1',
                            status: 'done',
                            url: dataNews.accessoryAddr,
                          },
                        ]}
                        onPreview={this.handlePreview}
                        showUploadList={showUploadList}
                      />
                    </div>
                    {dataNews.accessoryAddr != undefined &&
                      (dataNews.accessoryAddr
                        .split('?')[0]
                        .substring(dataNews.accessoryAddr.split('?')[0].length - 4) == '.rar' ||
                      dataNews.accessoryAddr
                        .split('?')[0]
                        .substring(dataNews.accessoryAddr.split('?')[0].length - 4) == '.zip' ||
                      dataNews.accessoryAddr
                        .split('?')[0]
                        .substring(dataNews.accessoryAddr.split('?')[0].length - 4) == '.doc' ||
                      dataNews.accessoryAddr
                        .split('?')[0]
                        .substring(dataNews.accessoryAddr.split('?')[0].length - 4) == 'docx' ||
                      dataNews.accessoryAddr
                        .split('?')[0]
                        .substring(dataNews.accessoryAddr.split('?')[0].length - 4) == '.xls' ||
                      dataNews.accessoryAddr
                        .split('?')[0]
                        .substring(dataNews.accessoryAddr.split('?')[0].length - 4) == 'xlsx' ||
                      dataNews.accessoryAddr
                        .split('?')[0]
                        .substring(dataNews.accessoryAddr.split('?')[0].length - 4) == '.pdf' ? (
                        <Button
                          type="link"
                          style={{ marginTop: '75px' }}
                          onClick={() => {
                            let urlimg = `${portalUrl}/index/getImage?url=${
                              dataNews.accessoryAddr.split('?')[0]
                            }&&name=附件`;
                            window.open(urlimg);
                          }}
                        >
                          下载附件
                        </Button>
                      ) : null)}
                    <Modal
                      visible={fileVisible}
                      footer={[
                        <Button type="primary" onClick={this.accessoryCancel}>
                          {' '}
                          取消{' '}
                        </Button>,
                      ]}
                      onCancel={this.accessoryCancel}
                    >
                      {dataNews.accessoryAddr != undefined &&
                        (dataNews.accessoryAddr
                          .split('?')[0]
                          .substring(dataNews.accessoryAddr.split('?')[0].length - 4) == '.rar' ||
                        dataNews.accessoryAddr
                          .split('?')[0]
                          .substring(dataNews.accessoryAddr.split('?')[0].length - 4) == '.zip' ||
                        dataNews.accessoryAddr
                          .split('?')[0]
                          .substring(dataNews.accessoryAddr.split('?')[0].length - 4) == '.doc' ||
                        dataNews.accessoryAddr
                          .split('?')[0]
                          .substring(dataNews.accessoryAddr.split('?')[0].length - 4) == 'docx' ||
                        dataNews.accessoryAddr
                          .split('?')[0]
                          .substring(dataNews.accessoryAddr.split('?')[0].length - 4) == '.xls' ||
                        dataNews.accessoryAddr
                          .split('?')[0]
                          .substring(dataNews.accessoryAddr.split('?')[0].length - 4) == 'xlsx' ||
                        dataNews.accessoryAddr
                          .split('?')[0]
                          .substring(dataNews.accessoryAddr.split('?')[0].length - 4) == '.pdf' ? (
                          '该文件不支持浏览！'
                        ) : (
                          <img
                            alt="example"
                            style={{ width: '100%' }}
                            src={dataNews.accessoryAddr}
                          />
                        ))}
                    </Modal>
                  </Descriptions.Item>
                </Descriptions>
              ) : null}
            </div>
            <div
              style={{
                borderBottom: '1px solid #f0f0f0',
                marginBottom: '16px',
                paddingBottom: '24px',
              }}
            >
              <Descriptions title="时间信息">
                <Descriptions.Item label="创建时间">{dataNews.createTime}</Descriptions.Item>
                <Descriptions.Item label="处理时间">{dataNews.handlingTime}</Descriptions.Item>
              </Descriptions>
            </div>
            <h3 style={{ fontWeight: 540 }}>工单信息</h3>
            {dataNews?.solutionTypeName === '修改信息' ? (
              <ProForm
                className="ant-portal-radio-button"
                formRef={this.formRef}
                layout="horizontal"
                labelAlign="right"
                labelCol={{ flex: '105px' }}
                submitter={{
                  render: (_, dom) => null,
                }}
              >
                <div className="mb-2">
                  <span style={titleStyle}>基础信息</span>
                  {this.basicRender()}
                </div>
                <div className="mb-2">
                  <span style={titleStyle}>收件人信息</span>
                  {this.recipientRender()}
                </div>
                <div className="mb-2">
                  <span
                    style={{
                      fontSize: '16px',
                      display: 'block',
                      color: '#52C41A',
                      marginBottom: '10px',
                    }}
                  >
                    报关信息
                    {type === '1' && (
                      <span
                        style={{
                          color: 'rgba(253, 111, 111, 1)',
                          marginLeft: '16px',
                          padding: '5px 15px',
                          fontSize: '14px',
                          backgroundColor: 'rgba(250, 236, 221, 1)',
                        }}
                      >
                        请注意：报关单上的申报品名为第一条报关信息的中英文品名，报关数量、重量和价值则取申报总信息
                      </span>
                    )}
                  </span>
                  {this.customsDeclarationRender()}
                  {type === '1' && (
                    <p className="ant-typographys">
                      申报总数量：{dataNews?.parcel?.totalQuantity}&nbsp;&nbsp;申报总重量（g）：
                      {dataNews?.parcel?.totalWeight}
                      &nbsp;&nbsp;申报总价值（{dataNews?.parcel?.declaredCurrency}）：
                      {dataNews?.parcel?.totalPrice}
                    </p>
                  )}
                </div>
              </ProForm>
            ) : (
              <Table
                bordered
                pagination={false}
                columns={columns.filter(item => item.filterType == undefined)}
                loading={loading}
                dataSource={dataSource}
                scroll={{
                  x: 1600,
                }}
              />
            )}
          </Card>
          {/* 新增的操作记录折叠面板 */}
          {abnormalLog.length > 0 && (
            <Collapse style={{ marginTop: 16 }}>
              <Collapse.Panel header="操作记录" key="operationLogs">
                <Timeline mode="left">
                  {abnormalLog.map((log, index) => (
                    <Timeline.Item key={index}>
                      <p>
                        {' '}
                        {log.nodeName} - {log.createName} - {log.createTime}
                      </p>
                    </Timeline.Item>
                  ))}
                </Timeline>
              </Collapse.Panel>
            </Collapse>
          )}
        </div>
      </Modal>
    );
  }
}
export default AbnormalDetail;
