import React, { useState, useRef, useImperativeHandle } from 'react';
import { Modal, Descriptions } from 'antd';
import { ProFormInstance, ProColumns, ProTable } from '@ant-design/pro-components';

const AbnormalProductDetail = props => {
  const { dispatch, modalRef, productDetailLoading } = props;
  const formRef = useRef<ProFormInstance>();
  const [open, setOpen] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [detailInfo, setDetailInfo] = useState({
    handleTime: '',
    handlerName: '',
    handleName: '',
    notes: '',
  });

  useImperativeHandle(modalRef, () => ({
    openModal: record => {
      console.log(record);

      setDataSource(Array.isArray(record) ? record : [record]);
      initialFunction(record);
      setOpen(true);
    },
  }));

  const initialFunction = record => {
    dispatch({
      type: 'anomalyOrder/productNameChangeDetail',
      payload: {
        orderId: record.orderId,
      },
      callback: response => {
        if (response.success) {
          setDetailInfo({
            ...detailInfo,
            ...response.data,
          });
        }
      },
    });
  };

  function getMatchNameList(productList = []) {
    return new Promise(() => {
      const resultData = productList?.map(item => dispatchGetMatchNameList(item.goodsNameCh));
      Promise.allSettled(resultData).then(res => {
        const data = res.map(item => {
          if (item.status === 'fulfilled') {
            return item.value;
          } else {
            return [];
          }
        });
        // setGoodsNameList(data);
      });
    });
  }

  function dispatchGetMatchNameList(name: string) {
    return new Promise(resolve => {
      dispatch({
        type: 'anomalyOrder/getMatchNameList',
        payload: {
          name,
        },
        callback: response => {
          if (response.success) {
            resolve(
              response.data.map(item => ({
                value: item.standardGoodsName,
                label: item.standardGoodsName,
                goodsNameEnData: item.standardGoodsnameEn,
              }))
            );
          }
        },
      });
    });
  }

  function reset() {
    formRef.current?.resetFields();
    setDataSource([]);
    setDetailInfo({
      handleTime: '',
      handlerName: '',
      handleName: '',
      notes: '',
    });
  }

  function handleCancel() {
    setOpen(false);
    reset();
  }

  const columns: ProColumns<any>[] = [
    {
      title: '运单号',
      dataIndex: 'waybillNumber',
      fixed: 'left',
      width: 150,
    },
    {
      title: '制单账号',
      dataIndex: 'customerCode',
      width: 100,
    },

    {
      title: '订单号',
      dataIndex: 'userOrderNumber',
      width: 140,
    },
    {
      title: '下单时间',
      dataIndex: 'orderTime',
      width: 200,
      readonly: true,
    },
    {
      title: 'SKU',
      dataIndex: 'sku',
      width: 100,
      readonly: true,
    },
    {
      title: '创建时间',
      width: 160,
      dataIndex: 'createTime',
      readonly: true,
    },
    {
      title: '是否已到仓',
      dataIndex: 'arrivedStateName',
      width: 160,
      readonly: true,
    },
    {
      title: '中文品名',
      dataIndex: 'oldGoodsNameCh',
      width: 160,
      readonly: true,
    },
    {
      title: '英文品名',
      dataIndex: 'oldGoodsNameEh',
      width: 160,
      readonly: true,
    },
    {
      title: '新中文品名',
      dataIndex: 'newGoodsNameCh',
      width: 160,
    },
    {
      title: '新英文品名',
      dataIndex: 'newGoodsNameEh',
      width: 160,
      readonly: true,
    },
    {
      title: '备注',
      dataIndex: 'handleNotes',
      width: 160,
    },
  ];

  return (
    <Modal
      title="品名工单详情"
      open={open}
      width="70%"
      footer={null}
      onCancel={() => handleCancel()}
    >
      <Descriptions column={4}>
        <Descriptions.Item label="处理时间">{detailInfo.handleTime}</Descriptions.Item>
        <Descriptions.Item label="处理人">{detailInfo.handlerName}</Descriptions.Item>
        <Descriptions.Item label="处理方式">{detailInfo.handleName}</Descriptions.Item>
        <Descriptions.Item label="备注">{detailInfo.notes}</Descriptions.Item>
      </Descriptions>
      <ProTable
        rowKey="waybillNumber"
        columns={columns}
        loading={productDetailLoading}
        scroll={{ x: 'max-content' }}
        dataSource={dataSource}
        search={false}
        options={false}
      />
    </Modal>
  );
};

export default AbnormalProductDetail;
