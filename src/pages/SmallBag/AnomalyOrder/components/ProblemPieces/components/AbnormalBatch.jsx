import React, { Component } from 'react';
import router from 'umi/router';
import { connect } from 'dva';
import { PlusOutlined } from '@ant-design/icons';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {
  Input,
  Button,
  Card,
  Table,
  Divider,
  Modal,
  message,
  Upload,
  Radio,
  Row,
  Col,
  Descriptions,
} from 'antd';
// import styles from './AbnormalDetail.less';
// import defaultSettings from '@/defaultSettings';
import { portalUrl } from '../../../../../../../config/defaultSettings';
import TextArea from 'antd/es/input/TextArea';
import ModalImage from '@/components/ModalImage';
const commonHandleTypes = [4, 8, 11, 12, 13, 14, 15, 16, 17, 18, 19];
// @connect(({ anomalyOrder, loading }) => ({
//   anomalyOrder,
//   loading: loading.effects['anomalyOrder/problemPiecesList'],
// }))
// @Form.create()
class AbnormalBatch extends Component {
  constructor(props) {
    super(props);
    this.state = {
      dataSource: [],
      selectTag: undefined,
      voucherList: [],
      fileVisible: false,
      previewImage: '',
      isUploadOver: false,
      visible: false,
      oneInfo: '',
      type: undefined,
      abnormalType: undefined,
      oneData: [
        { key: '客户IOSS税号错误', value: 1 },
        { key: '燕文已代缴税', value: 1 },
        { key: '申报价值过低', value: 5 },
        { key: '国外海关扣关', value: 2 },
        { key: '国外海关扣关并销毁', value: 3 },
        { key: '国内海关扣关', value: 2 },
        { key: '国内海关扣关并销毁', value: 3 },
        { key: '收件人地址不正确', value: 2 },
        { key: '收件人地址不详', value: 2 },
        { key: '收件人待支付关税', value: 2 },
        { key: '收件人不在家', value: 2 },
        { key: '收件人拒收', value: 2 },
        { key: '联系不到收件人', value: 2 },
        { key: '包裹破损', value: 2 },
        { key: '包裹退回', value: 2 },
        { key: '无人认领', value: 2 },
        { key: '电话无法接通', value: 2 },
        { key: '邮箱不正确', value: 2 },
        { key: '无法进入收件人住宅区', value: 2 },
        { key: '重派失败', value: 2 },
        { key: '电话不正确', value: 2 },
        { key: '邮编错误', value: 2 },
        { key: '其他', value: 2 },
        { key: '重量确认', value: 4 },
        { key: '提供发票', value: 2 },
        { key: '疑似丢失待提供资料', value: 2 },
        { key: '提供交易证明', value: 2 },
      ],
      handleTypeOptions: [
        {
          label: '修改税号',
          value: 1,
        },
        {
          label: '燕文代缴',
          value: 2,
        },
        {
          label: '税号无误',
          value: 3,
        },
        {
          // 对异常原因=燕文已代缴税进行【确认】
          label: '确认',
          value: 4,
        },
        {
          label: '修改申报价值',
          value: 5,
        },
        {
          label: '重派',
          value: 6,
        },
        {
          label: '销毁',
          value: 7,
        },
        {
          label: '提供资料',
          value: 8,
        },
        {
          label: '原单重发',
          value: 9,
        },
        {
          label: '退回',
          value: 10,
        },
        {
          label: '自取',
          value: 11,
        },
        {
          label: '再次派送',
          value: 12,
        },
        {
          label: '其他',
          value: 13,
        },
        {
          label: '关税预付',
          value: 14,
        },
        {
          label: '确认费用',
          value: 15,
        },
        {
          label: '确认罚款',
          value: 16,
        },
        {
          label: '确认换渠道发',
          value: 17,
        },
        {
          label: '申报无误-买单报关',
          value: 18,
        },
        {
          label: '申报无误-正式报关',
          value: 19,
        },
      ],
      selectTypeOptions: [],
      open: false,
    };
  }

  openModal = data => {
    const { oneData, handleTypeOptions } = this.state;
    if (data instanceof Array) {
      this.setState({
        dataSource: data,
        type: 1,
        selectTag: 1,
        open: true,
      });
    } else {
      let handleValues = data?.supportHandle.split(',').map(item => parseInt(item.trim()));
      const filteredOptions = handleTypeOptions.filter(option =>
        handleValues.includes(option.value)
      );
      if (filteredOptions.length == 0) {
        message.error('未查询到对应的处理方式');
        return;
      }
      let find = oneData.find(item => item.key === data.abnormalType);
      let value = null;
      if (find) {
        value = find.value;
      } else {
        value = filteredOptions[0].value;
      }
      this.setState(
        {
          open: true,
          dataSource: [data],
          type: value,
          selectTypeOptions: filteredOptions,
          abnormalType: data.abnormalType,
          selectTag: filteredOptions[0].value,
          voucherList: data?.voucher
            ? [
                {
                  uid: '-1',
                  name: '资料',
                  status: 'done',
                  url: data.voucher,
                },
              ]
            : [],
        },
        () => {
          if (value === 2) {
            this.props.form.setFieldsValue({
              receiverName: data.receiverName,
              receiverZipCode: data.receiverZipCode,
              receiverPhone: data.receiverPhone,
              receiverCity: data.receiverCity,
              receiverState: data.receiverState,
              receiverAddress1: data.receiverAddress1,
              voucher: data?.voucher,
              handleNotes: data?.handleNotes,
            });
          } else if (value === 5) {
            this.props.form.setFieldsValue({
              newDeclaredValue: data.declaredValue,
            });
          }
        }
      );
    }
  };

  //   componentDidMount() {
  //     let data = JSON.parse(sessionStorage.getItem(this.props.history.location.query.data));
  //     const { oneData } = this.state;
  //   }

  declareValueReasonCompare = (oldValue, newValue) => {
    const { abnormalType } = this.state;
    if (abnormalType === '申报价值过高') {
      if (oldValue <= newValue) {
        message.error('新申报价值只能小于原申报价值');
        return false;
      }
    }
    if (abnormalType === '申报价值过低') {
      if (oldValue >= newValue) {
        message.error('新申报价值只能大于原申报价值');
        return false;
      }
    }
    return true;
  };

  // 点击处理方式
  onChangeTagTypeSelect = e => {
    const { form } = this.props;
    this.setState({
      selectTag: e.target.value,
    });
  };

  // 上传税号证明
  beforeUpload = file => {
    const isJPG =
      file.type === 'image/jpeg' ||
      file.type === 'image/jpg' ||
      file.type === 'image/png' ||
      file.type === 'image/gif';
    const isLt5M = file.size / 1024 / 1024 < 5;
    const isLt1M = file.size / 1024 / 1024 < 1;

    if (isJPG) {
      if (!isLt1M) {
        message.error('请上传小于1M的图片');
        return false || Upload.LIST_IGNORE;
      }
    } else {
      if (!isLt5M) {
        message.error('请上传小于5M的文件');
        return false || Upload.LIST_IGNORE;
      }
    }
    const { form, dispatch } = this.props;
    const { dataSource } = this.state;
    const formData = new FormData();
    formData.append('attach', file);
    formData.append('objName', 'tc_outside_abnormal');
    formData.append('objId', dataSource[0].orderId);
    formData.append('fileType', '0');
    formData.append('appId', 'work-order-2.0');
    formData.append('singleFile', true);
    dispatch({
      type: 'paymentAccount/upload',
      payload: formData,
      callback: response => {
        if (response.success) {
          this.setState({
            voucherList: [
              {
                uid: '-1',
                name: file.name,
                status: 'done',
                url: response.data,
              },
            ],
            isUploadOver: true,
          });
        } else {
          message.error(response.message);
        }
      },
    });
  };

  // 删除上传照片
  handleRemove = file => {
    this.setState({
      voucherList: [],
      isUploadOver: false,
    });
  };

  // 提交
  submit = () => {
    const { dispatch, form } = this.props;
    const { dataSource, selectTag, isUploadOver, voucherList, abnormalType } = this.state;
    form.validateFields((error, values) => {
      if (!error) {
        let params;
        if (selectTag === 1) {
          // 修改税号
          params = dataSource.map(item => {
            item.handleType = selectTag;
            item.modifyTax = values.modifyTax;
            return item;
          });
        } else if (selectTag === 2) {
          // 燕文代缴
          params = dataSource.map(item => {
            item.handleType = selectTag;
            return item;
          });
        } else if (selectTag === 3) {
          // 税号无误
          if (isUploadOver && voucherList.length !== 0) {
            params = dataSource.map(item => {
              item.voucher = voucherList[0].url.split('?')[0];
              item.handleType = selectTag;
              return item;
            });
          } else if (!isUploadOver && voucherList.length === 0) {
            params = dataSource.map(item => {
              item.handleType = selectTag;
              return item;
            });
          } else {
            message.warning('证明正在上传中....');
            return;
          }
        } else if (selectTag === 6) {
          // 重派

          if (isUploadOver && voucherList.length !== 0) {
            params = dataSource.map(item => {
              item.handleType = selectTag;
              item.receiverName = values.receiverName;
              item.receiverZipCode = values.receiverZipCode;
              item.receiverPhone = values.receiverPhone;
              item.receiverCity = values.receiverCity;
              item.receiverState = values.receiverState;
              item.receiverAddress1 = values.receiverAddress1;
              item.voucher = voucherList?.[0]?.url?.split('?')?.[0];
              item.handleNotes = values.handleNotes;
              return item;
            })[0];
          } else if (!isUploadOver && voucherList.length === 0) {
            params = dataSource.map(item => {
              item.handleType = selectTag;
              item.receiverName = values.receiverName;
              item.receiverZipCode = values.receiverZipCode;
              item.receiverPhone = values.receiverPhone;
              item.receiverCity = values.receiverCity;
              item.receiverState = values.receiverState;
              item.receiverAddress1 = values.receiverAddress1;
              item.voucher = voucherList?.[0]?.url?.split('?')?.[0];
              item.handleNotes = values.handleNotes;
              return item;
            })[0];
          } else {
            message.warning('资料正在上传中....');
          }
        } else if (selectTag === 7) {
          // 销毁
          params = dataSource.map(item => {
            item.handleType = selectTag;
            return item;
          })[0];
        } else if (selectTag === 5) {
          params = dataSource.map(item => {
            item.handleType = selectTag;
            return item;
          })[0];
          if (!this.declareValueReasonCompare(+params.apDeclaredValue, +params.newDeclaredValue)) {
            return;
          }
        } else if (selectTag === 9 || selectTag === 10) {
          // 原单重发 or 退回
          params = dataSource.map(item => {
            item.handleType = selectTag;
            return item;
          })[0];
        } else if (commonHandleTypes.indexOf(selectTag)) {
          params = dataSource.map(item => {
            item.handleType = selectTag;
            item.handleNotes = values.handleNotes;
            item.voucher = voucherList?.[0]?.url?.split('?')?.[0];
            return item;
          });
        }
        dispatch({
          type: `anomalyOrder/${
            selectTag === 7 ||
            selectTag === 6 ||
            selectTag === 5 ||
            selectTag === 9 ||
            selectTag === 10
              ? 'outsideTheWarehouseCustomerHandle'
              : 'outsideTheWarehouseCustomerBatchHandle'
          }`,
          payload: params,
          callback: result => {
            if (result.success) {
              message.success('处理成功!');
              this.handleCancel(result.success);
              //   router.push(`/express/abnormalList?type=2`);
            }
          },
        });
      }
    });
  };

  // 修改信息 和税号
  updateInfo = (record, type) => {
    const { form } = this.props;
    if (type === 1) {
      form.setFieldsValue({
        modifyTaxModel: record.modifyTax,
      });
    } else if (type === 6) {
      form.setFieldsValue({
        receiverNameModel: record.receiverName,
        receiverZipCodeModel: record.receiverZipCode,
        receiverPhoneModel: record.receiverPhone,
        receiverCityModel: record.receiverCity,
        receiverStateModel: record.receiverState,
        receiverAddress1Model: record.receiverAddress1,
      });
    } else if (type === 5) {
      form.setFieldsValue({
        newDeclaredValueModel: record.newDeclaredValue,
      });
    }

    this.setState({
      oneInfo: record,
      visible: true,
    });
  };

  // 修改单个的税号确认
  handleOk = type => {
    const { oneInfo, dataSource, abnormalType } = this.state;
    const { form } = this.props;
    form.validateFields((error, values) => {
      if (!error) {
        if (type === 1) {
          // 修改税号
          oneInfo['modifyTax'] = values.modifyTaxModel;
          dataSource.map(item => {
            if (item.orderId === oneInfo.orderId) {
              item.modifyTax = values.modifyTaxModel;
            }
            return item;
          });
        } else if (type === 6) {
          // 重派
          oneInfo['receiverName'] = values.receiverNameModel;
          oneInfo['receiverZipCode'] = values.receiverZipCodeModel;
          oneInfo['receiverPhone'] = values.receiverPhoneModel;
          oneInfo['receiverCity'] = values.receiverCityModel;
          oneInfo['receiverState'] = values.receiverStateModel;
          oneInfo['receiverAddress1'] = values.receiverAddress1Model;
          this.props.form.setFieldsValue({
            receiverName: values.receiverNameModel,
            receiverZipCode: values.receiverZipCodeModel,
            receiverPhone: values.receiverPhoneModel,
            receiverCity: values.receiverCityModel,
            receiverState: values.receiverStateModel,
            receiverAddress1: values.receiverAddress1Model,
          });
          dataSource.map(item => {
            if (item.orderId === oneInfo.orderId) {
              item.receiverName = values.receiverNameModel;
              item.receiverZipCode = values.receiverZipCodeModel;
              item.receiverPhone = values.receiverPhoneModel;
              item.receiverCity = values.receiverCityModel;
              item.receiverState = values.receiverState;
              item.receiverAddress1 = values.receiverAddress1Model;
            }
            return item;
          });
        } else if (type === 5) {
          // 新申报价值
          oneInfo['newDeclaredValue'] = +values.newDeclaredValueModel;
          this.props.form.setFieldsValue({
            newDeclaredValue: +values.newDeclaredValueModel,
          });
          dataSource.map(item => {
            if (item.orderId === oneInfo.orderId) {
              if (
                !this.declareValueReasonCompare(
                  +item.apDeclaredValue,
                  +values.newDeclaredValueModel
                )
              ) {
                return item;
              } else {
                item.newDeclaredValue = +values.newDeclaredValueModel;
              }
            }
            return item;
          });
        }
        this.setState({
          oneInfo: oneInfo,
          dataSource: dataSource,
          visible: false,
        });
      }
    });
  };
  // 修改单个的税号的取消
  handleVisibleCancel = () => {
    this.setState({
      visible: false,
    });
  };
  // 监听输入框内容
  changeInputText = (e, type) => {
    const { form } = this.props;
    const { dataSource, abnormalType } = this.state;

    form.validateFields((error, values) => {
      if (!error) {
        let data = dataSource.map(item => {
          if (type === 'newDeclaredValue') {
            if (!this.declareValueReasonCompare(+item.apDeclaredValue, +values[type])) {
              return item;
            } else {
              item[type] = +values[type];
            }
          } else {
            item[type] = values[type];
          }
          return item;
        });
        this.setState({
          dataSource: data,
        });
      }
    });
  };

  handleCancel = value => {
    const { onCancel, form } = this.props;
    this.setState(
      {
        open: false,
      },
      () => {
        form.resetFields();
      }
    );
    if (value) {
      onCancel();
    }
  };

  render() {
    const {
      voucherList,
      oneInfo,
      fileVisible,
      previewImage,
      dataSource,
      selectTag,
      visible,
      open,
      selectTypeOptions,
    } = this.state;
    const {
      loading,
      form: { getFieldDecorator, getFieldValue },
      outsideSubmitLoading,
    } = this.props;
    const formInputLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
        md: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 12 },
        md: { span: 12 },
      },
    };

    const columns = [
      {
        title: '工单编号',
        dataIndex: 'orderId',
        fixed: 'left',
        width: 250,
      },
      {
        title: '运单号',
        dataIndex: 'waybillNumber',
        width: 100,
      },
      {
        title: '制单账号',
        dataIndex: 'customerCode',
        width: 150,
      },
      {
        title: '转单号',
        dataIndex: 'exchangeNumber',
        width: 140,
      },
      {
        title: '异常原因',
        dataIndex: 'abnormalType',
        width: 140,
      },
      {
        title: '产品名称',
        dataIndex: 'productName',
        width: 250,
      },
      {
        title: '姓名',
        dataIndex: 'receiverName',
        width: 100,
      },
      {
        title: '电话',
        dataIndex: 'receiverPhone',
        width: 100,
      },
      {
        title: '目的地',
        dataIndex: 'regionCh',
        width: 250,
      },
      {
        title: '邮编',
        dataIndex: 'receiverZipCode',
        width: 100,
      },
      {
        title: '地址',
        dataIndex: 'receiverAddress1',
        width: 100,
      },
      {
        title: '城市',
        dataIndex: 'receiverCity',
        width: 100,
      },
      {
        title: '州',
        dataIndex: 'receiverState',
        width: 100,
      },
      {
        title: '原申报价值',
        dataIndex: 'apDeclaredValue',
        width: 140,
      },
      {
        title: '申报币种',
        dataIndex: 'declaredCurrency',
        width: 140,
      },
      {
        title: '申报品名',
        dataIndex: 'chineseName',
        width: 140,
      },
      {
        title: '建议处理方案',
        dataIndex: 'suggestHandleType',
        width: 140,
      },
      {
        title: '处理方案',
        dataIndex: 'handleType',
        width: 140,
        render: (text, record) => {
          return text == 1
            ? '修改税号'
            : text == 2
            ? '燕文代缴'
            : text == 6
            ? '重派'
            : text == 7
            ? '销毁'
            : text;
        },
      },
      {
        title: '修改后税号',
        dataIndex: 'modifyTax',
        filterType: selectTag === 9 || selectTag === 10 ? true : undefined,
        width: 140,
      },
      {
        title: '长',
        dataIndex: 'length',
        filterType: selectTag === 9 || selectTag === 10 ? undefined : true,
        width: 140,
      },
      {
        title: '宽',
        dataIndex: 'width',
        filterType: selectTag === 9 || selectTag === 10 ? undefined : true,
        width: 140,
      },
      {
        title: '高',
        dataIndex: 'high',
        filterType: selectTag === 9 || selectTag === 10 ? undefined : true,
        width: 140,
      },
      {
        title: '是否计泡',
        dataIndex: 'whetherThrow',
        filterType: selectTag === 9 || selectTag === 10 ? undefined : true,
        width: 140,
      },
      {
        title: '计费重量（g）',
        dataIndex: 'calcWeight',
        filterType: selectTag === 9 || selectTag === 10 ? undefined : true,
        width: 140,
      },
      {
        title: '预估费用（元）',
        dataIndex: 'estimatedCost',
        filterType: selectTag === 9 || selectTag === 10 ? undefined : true,
        width: 140,
      },
      {
        title: '操作',
        dataIndex: 'operation',
        width: '100px',
        fixed: 'right',
        // filterType: selectTag !== 1 ? true : undefined,
        render: (text, record) => {
          if (selectTag === 1 || selectTag === 6 || selectTag === 5) {
            return (
              <a
                onClick={() => {
                  this.updateInfo(record, selectTag);
                }}
              >
                {`修改${selectTag === 1 ? '税号' : '信息'}`}
              </a>
            );
          } else {
            return null;
          }
        },
      },
    ];

    const dataNews = dataSource != undefined && dataSource.length > 0 ? dataSource[0] : {};
    const showUploadList = { showRemoveIcon: false };
    let recipientsInfo = '';
    //上传按钮
    const uploadButton = (
      <div>
        <PlusOutlined />
        <div className="ant-upload-text">上传</div>
      </div>
    );

    return (
      <Modal
        open={open}
        footer={null}
        width={'70%'}
        onCancel={() => this.handleCancel()}
        destroyOnClose
      >
        <Card style={{ marginBottom: '10px' }} className="disposeCardClass">
          {open && (
            <>
              <Form>
                <div style={{ marginBottom: '10px' }}>
                  <span style={{ textAlign: 'left', fontSize: '16px' }}> 选择处理方式</span>
                  <Button
                    type="primary"
                    style={{ marginLeft: 15, float: 'right' }}
                    loading={outsideSubmitLoading}
                    onClick={() => {
                      this.submit();
                    }}
                  >
                    提交
                  </Button>
                  <Button
                    style={{ marginLeft: 15, float: 'right' }}
                    onClick={() => this.handleCancel()}
                  >
                    取消
                  </Button>
                </div>
                <div style={{ marginBottom: '20px' }} className="ant-portal-radio-button">
                  <Radio.Group
                    value={selectTag}
                    onChange={this.onChangeTagTypeSelect}
                    optionType="button"
                    options={selectTypeOptions}
                  ></Radio.Group>
                </div>
                {/* 修改税号 start */}
                <div style={{ width: '23%' }}>
                  <Form.Item
                    {...formInputLayout}
                    style={{ display: selectTag === 1 ? '' : 'none' }}
                    label="税号"
                  >
                    {getFieldDecorator('modifyTax', {
                      rules: [
                        {
                          required: selectTag === 1 && !visible,
                          message: '请输入税号',
                        },
                        {
                          pattern: /^[I][M]\d{10}$/,
                          message: '请输入IM开头+10位数字',
                        },
                      ],
                    })(
                      <Input
                        placeholder="请输入正确税号"
                        onBlur={e => this.changeInputText(e, 'modifyTax')}
                      />
                    )}
                  </Form.Item>
                  <Form.Item
                    {...formInputLayout}
                    style={{ display: commonHandleTypes.indexOf(selectTag) !== -1 ? '' : 'none' }}
                    label="处理备注"
                  >
                    {getFieldDecorator('handleNotes', {
                      rules: [
                        {
                          required: false,
                          message: '请输入备注信息',
                        },
                      ],
                    })(<TextArea placeholder="请输入备注信息" />)}
                  </Form.Item>
                  {commonHandleTypes.indexOf(selectTag) !== -1 && !visible && (
                    <Form.Item
                      {...formInputLayout}
                      label="上传资料"
                      help="提示信息：请上传小于1M的图片或小于5M的文件"
                    >
                      {getFieldDecorator('voucher')(
                        <Upload
                          listType="picture-card"
                          fileList={voucherList}
                          beforeUpload={this.beforeUpload}
                          customRequest={({ file }) => {}}
                          accept="image/jpeg,image/png,image/gif,.pdf,.doc,.docx,.xls,.xlsx,.rar,.zip"
                          onPreview={file => {
                            if (
                              file.name.includes('pdf') ||
                              file.name.includes('doc') ||
                              file.name.includes('docx') ||
                              file.name.includes('xls') ||
                              file.name.includes('xlsx')
                            ) {
                              message.warning('该文件不支持预览！');
                            } else {
                              this.setState({
                                fileVisible: true,
                                previewImage: file.url,
                              });
                            }
                          }}
                          onRemove={this.handleRemove}
                        >
                          {voucherList.length >= 1 ? null : uploadButton}
                        </Upload>
                      )}
                    </Form.Item>
                  )}
                  {selectTag === 3 && (
                    <Form.Item
                      {...formInputLayout}
                      label="税号证明"
                      help="提示信息：请上传小于1M的图片或小于5M的文件"
                    >
                      {getFieldDecorator('voucher')(
                        <Upload
                          listType="picture-card"
                          fileList={voucherList}
                          beforeUpload={this.beforeUpload}
                          accept="image/jpeg,image/png,image/gif,.pdf,.doc,.docx,.xls,.xlsx,.rar,.zip"
                          onPreview={file => {
                            if (
                              file.name.includes('pdf') ||
                              file.name.includes('doc') ||
                              file.name.includes('docx') ||
                              file.name.includes('xls') ||
                              file.name.includes('xlsx')
                            ) {
                              message.warning('该文件不支持预览！');
                            } else {
                              this.setState({
                                fileVisible: true,
                                previewImage: file.url,
                              });
                            }
                          }}
                          onRemove={this.handleRemove}
                        >
                          {voucherList.length >= 1 ? null : uploadButton}
                        </Upload>
                      )}
                    </Form.Item>
                  )}
                </div>
                {/* 修改税号 end */}
                {/* 新申报价值 start */}
                <div style={{ width: '30%' }}>
                  <Form.Item
                    {...formInputLayout}
                    style={{ display: selectTag === 5 ? '' : 'none' }}
                    label="新申报价值"
                  >
                    {getFieldDecorator('newDeclaredValue', {
                      rules: [
                        {
                          required: selectTag === 5 && !visible,
                          message: '请输入新申报价值',
                        },
                        {
                          pattern: /^\d{0,6}.\d{0,2}$/,
                          message: '请输入数字',
                        },
                      ],
                    })(
                      <Input
                        placeholder="请输入新申报价值"
                        onBlur={e => this.changeInputText(e, 'newDeclaredValue')}
                      />
                    )}
                  </Form.Item>
                </div>
                {/* 新申报价值 end */}
                {selectTag === 6 && (
                  <div>
                    <p>收货人信息:</p>
                    <Row>
                      <Col span={8}>
                        <Form.Item {...formInputLayout} label="姓名">
                          {getFieldDecorator('receiverName', {
                            rules: [
                              {
                                required: selectTag === 6 && !visible,
                                message: '请输入姓名',
                              },
                            ],
                          })(
                            <Input
                              placeholder="请输入姓名"
                              onBlur={e => this.changeInputText(e, 'receiverName')}
                            />
                          )}
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item {...formInputLayout} label="邮编">
                          {getFieldDecorator('receiverZipCode', {
                            rules: [
                              {
                                required: selectTag === 6 && !visible,
                                message: '请输入邮编',
                              },
                            ],
                          })(
                            <Input
                              placeholder="请输入邮编"
                              onBlur={e => this.changeInputText(e, 'receiverZipCode')}
                            />
                          )}
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item {...formInputLayout} label="电话">
                          {getFieldDecorator('receiverPhone', {
                            rules: [
                              {
                                required: selectTag === 6 && !visible,
                                message: '请输入电话',
                              },
                            ],
                          })(
                            <Input
                              placeholder="请输入电话"
                              onBlur={e => this.changeInputText(e, 'receiverPhone')}
                            />
                          )}
                        </Form.Item>
                      </Col>
                    </Row>
                    <Row>
                      <Col span={8}>
                        <Form.Item {...formInputLayout} label="城市">
                          {getFieldDecorator('receiverCity', {
                            rules: [
                              {
                                required: selectTag === 6 && !visible,
                                message: '请输入城市',
                              },
                            ],
                          })(
                            <Input
                              placeholder="请输入城市"
                              onBlur={e => this.changeInputText(e, 'receiverCity')}
                            />
                          )}
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item {...formInputLayout} label="州">
                          {getFieldDecorator('receiverState', {
                            rules: [
                              {
                                required: selectTag === 6 && !visible,
                                message: '请输入州',
                              },
                            ],
                          })(
                            <Input
                              placeholder="请输入州"
                              onBlur={e => this.changeInputText(e, 'receiverState')}
                            />
                          )}
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item {...formInputLayout} label="地址">
                          {getFieldDecorator('receiverAddress1', {
                            rules: [
                              {
                                required: selectTag === 6 && !visible,
                                message: '请输入地址',
                              },
                            ],
                          })(
                            <Input
                              placeholder="请输入地址"
                              onBlur={e => this.changeInputText(e, 'receiverAddress1')}
                            />
                          )}
                        </Form.Item>
                      </Col>
                    </Row>
                    <Row>
                      <Col span={8}>
                        {selectTag === 6 && !visible && (
                          <Form.Item
                            {...formInputLayout}
                            label="上传资料"
                            help="提示信息：请上传小于1M的图片或小于5M的文件"
                          >
                            {getFieldDecorator('voucher')(
                              <Upload
                                listType="picture-card"
                                fileList={voucherList}
                                beforeUpload={this.beforeUpload}
                                accept="image/jpeg,image/png,image/gif,.pdf,.doc,.docx,.xls,.xlsx,.rar,.zip"
                                onPreview={file => {
                                  if (
                                    file.name.includes('pdf') ||
                                    file.name.includes('doc') ||
                                    file.name.includes('docx') ||
                                    file.name.includes('xls') ||
                                    file.name.includes('xlsx')
                                  ) {
                                    message.warning('该文件不支持预览！');
                                  } else {
                                    this.setState({
                                      fileVisible: true,
                                      previewImage: file.url,
                                    });
                                  }
                                }}
                                onRemove={this.handleRemove}
                              >
                                {voucherList.length >= 1 ? null : uploadButton}
                              </Upload>
                            )}
                          </Form.Item>
                        )}
                      </Col>
                      <Col span={8}>
                        <Form.Item {...formInputLayout} label="备注">
                          {getFieldDecorator('handleNotes')(
                            <Input.TextArea
                              placeholder="请输入备注"
                              rows={4}
                              onBlur={e => this.changeInputText(e, 'handleNotes')}
                            />
                          )}
                        </Form.Item>
                      </Col>
                    </Row>
                  </div>
                )}
              </Form>
            </>
          )}
        </Card>

        <div className="abnormalDetail_info">
          {/* 时间信息  */}
          <Card>
            <Table
              bordered
              pagination={false}
              columns={columns.filter(item => item.filterType == undefined)}
              loading={loading}
              dataSource={dataSource}
              scroll={{ x: 1700 }}
            />
          </Card>
        </div>
        <Modal
          title={selectTag === 1 ? '修改税号' : '修改收件人信息'}
          open={visible}
          onCancel={this.handleVisibleCancel}
          footer={[
            <Button type="primary" onClick={() => this.handleOk(selectTag)}>
              确定
            </Button>,
            <Button onClick={this.handleVisibleCancel}>取消</Button>,
          ]}
        >
          <Form>
            <Form.Item {...formInputLayout} label="运单号">
              {getFieldDecorator('yundan')(<span>{oneInfo.waybillNumber}</span>)}
            </Form.Item>
            <Form.Item
              style={{ display: selectTag === 1 ? '' : 'none' }}
              {...formInputLayout}
              label="税号"
            >
              {getFieldDecorator('modifyTaxModel', {
                rules: [
                  {
                    required: selectTag === 1 && visible,
                    message: '请输入税号',
                  },
                  {
                    pattern: /^[I][M]\d{10}$/,
                    message: '请输入IM开头+10位数字',
                  },
                ],
              })(<Input placeholder="请输入正确税号" />)}
            </Form.Item>
            <Form.Item
              {...formInputLayout}
              style={{ display: selectTag === 5 ? '' : 'none' }}
              label="新申报价值"
            >
              {getFieldDecorator('newDeclaredValueModel', {
                rules: [
                  {
                    required: selectTag === 5 && visible,
                    message: '请输入新申报价值',
                  },
                  {
                    pattern: /^\d{0,6}.\d{0,2}$/,
                    message: '请输入数字',
                  },
                ],
              })(<Input placeholder="请输入新申报价值" />)}
            </Form.Item>
            <Form.Item
              style={{ display: selectTag === 6 ? '' : 'none' }}
              {...formInputLayout}
              label="姓名"
            >
              {getFieldDecorator('receiverNameModel', {
                rules: [
                  {
                    required: selectTag === 6 && visible,
                    message: '请输入姓名',
                  },
                ],
              })(<Input placeholder="请输入姓名" />)}
            </Form.Item>
            <Form.Item
              style={{ display: selectTag === 6 ? '' : 'none' }}
              {...formInputLayout}
              label="邮编"
            >
              {getFieldDecorator('receiverZipCodeModel', {
                rules: [
                  {
                    required: selectTag === 6 && visible,
                    message: '请输入邮编',
                  },
                ],
              })(<Input placeholder="请输入邮编" />)}
            </Form.Item>
            <Form.Item
              style={{ display: selectTag === 6 ? '' : 'none' }}
              {...formInputLayout}
              label="电话"
            >
              {getFieldDecorator('receiverPhoneModel', {
                rules: [
                  {
                    required: selectTag === 6 && visible,
                    message: '请输入电话',
                  },
                ],
              })(<Input placeholder="请输入电话" />)}
            </Form.Item>
            <Form.Item
              style={{ display: selectTag === 6 ? '' : 'none' }}
              {...formInputLayout}
              label="城市"
            >
              {getFieldDecorator('receiverCityModel', {
                rules: [
                  {
                    required: selectTag === 6 && visible,
                    message: '请输入城市',
                  },
                ],
              })(<Input placeholder="请输入城市" />)}
            </Form.Item>
            <Form.Item
              style={{ display: selectTag === 6 ? '' : 'none' }}
              {...formInputLayout}
              label="州"
            >
              {getFieldDecorator('receiverStateModel', {
                rules: [
                  {
                    required: selectTag === 6 && visible,
                    message: '请输入州',
                  },
                ],
              })(<Input placeholder="请输入州" />)}
            </Form.Item>
            <Form.Item
              style={{ display: selectTag === 6 ? '' : 'none' }}
              {...formInputLayout}
              label="地址"
            >
              {getFieldDecorator('receiverAddress1Model', {
                rules: [
                  {
                    required: selectTag === 6 && visible,
                    message: '请输入地址',
                  },
                ],
              })(<Input placeholder="请输入地址" />)}
            </Form.Item>
          </Form>
        </Modal>
        {fileVisible && (
          <ModalImage
            open={fileVisible}
            url={previewImage}
            onCancel={() => this.setState({ fileVisible: false })}
          />
        )}
      </Modal>
    );
  }
}

export default AbnormalBatch;
