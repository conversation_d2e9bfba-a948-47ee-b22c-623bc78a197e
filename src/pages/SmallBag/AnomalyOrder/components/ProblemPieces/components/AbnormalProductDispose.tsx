import React, { useState, useRef, useImperativeHandle } from 'react';
import { Button, Modal, Space, Row, Alert, Input, message, Select, Typography } from 'antd';
import { ProFormInstance, ProForm, EditableProTable, ProColumns } from '@ant-design/pro-components';
import { onFormErrorMessage } from '@/utils/utils';
import { useDebounceFn } from 'ahooks';

const AbnormalProductDispose = props => {
  const { dispatch, modalRef, onSubmit, productBatchLoading } = props;
  const formRef = useRef<ProFormInstance>();
  const [open, setOpen] = useState(false);
  const [editableKeys, setEditableKeys] = useState([]);
  const [goodsNameList, setGoodsNameList] = useState([]);
  const [inputNewGoodNameCn, setInputNewGoodNameCn] = useState<string>();
  const [inputGoodsNameList, setInputGoodsNameList] = useState([]);
  const [inputModalShow, setInputModalShow] = useState(false);

  useImperativeHandle(modalRef, () => ({
    openModal: record => {
      setTimeout(() => {
        formRef.current?.setFieldsValue({
          dataSource: Array.isArray(record) ? record : [record],
        });
      }, 300);
      getMatchNameList(Array.isArray(record) ? record : [record]);
      setEditableKeys(
        Array.isArray(record) ? record.map(item => item.waybillNumber) : [record.waybillNumber]
      );
      setOpen(true);
    },
  }));

  const { run: runInput } = useDebounceFn(
    value => {
      if (!value) return;
      Promise.allSettled([dispatchGetMatchNameList(value)]).then(res => {
        const data: any[] = res.map((item, index) =>
          item.status === 'fulfilled' ? item.value : []
        );
        setInputGoodsNameList(data?.[0]);
      });
    },
    { wait: 300 }
  );

  const { run } = useDebounceFn<(value: any, index?: any) => void>(
    (value, index) => {
      if (!value) return;
      const resultData = goodsNameList?.map((item, i) =>
        index === i ? dispatchGetMatchNameList(value) : item
      );
      Promise.allSettled(resultData).then(res => {
        const data = res.map((item, index) => {
          if (item.status === 'fulfilled') {
            return item.value;
          } else {
            return [];
          }
        });
        setGoodsNameList(data);
      });
    },
    { wait: 300 }
  );

  function getMatchNameList(productList = []) {
    return new Promise((resolve, reject) => {
      const resultData = productList?.map(item => dispatchGetMatchNameList(item.goodsNameCh));
      Promise.allSettled(resultData).then(res => {
        const data = res.map((item, index) => {
          if (item.status === 'fulfilled') {
            return item.value;
          } else {
            return [];
          }
        });
        setGoodsNameList(data);
      });
    });
  }

  function dispatchGetMatchNameList(name: string) {
    return new Promise((resolve, reject) => {
      dispatch({
        type: 'anomalyOrder/getMatchNameList',
        payload: {
          name,
        },
        callback: response => {
          if (response.success) {
            resolve(
              response.data.map(item => ({
                value: item.standardGoodsName,
                label: item.standardGoodsName,
                goodsNameEnData: item.standardGoodsnameEn,
              }))
            );
          }
        },
      });
    });
  }

  function handleChangeGoodsNameCh(value: string, key: number) {
    const goodsNameEnData = goodsNameList?.[key]?.find(item => item.value === value)
      ?.goodsNameEnData;
    formRef.current?.setFieldsValue({
      dataSource: [
        ...formRef.current?.getFieldValue('dataSource').slice(0, key),
        {
          ...formRef.current?.getFieldValue('dataSource')[key],
          newGoodsNameEh: goodsNameEnData,
        },
        ...formRef.current?.getFieldValue('dataSource').slice(key + 1),
      ],
    });
  }

  function reset() {
    formRef.current?.resetFields();
    setEditableKeys([]);
    setGoodsNameList([]);
    setInputGoodsNameList([]);
    setInputModalShow(false);
    setInputNewGoodNameCn('');
  }

  async function handleSubmit(formData: Record<string, any>): Promise<boolean | void> {
    try {
      const data = formData.dataSource;
      dispatch({
        type: 'anomalyOrder/productNameChangeBatchHandle',
        payload: data?.map(d => ({ ...d })),
        callback: response => {
          if (response.success) {
            message.success('操作成功');
            handleCancel();
            onSubmit();
          } else {
            handleCancel();
            onSubmit();
          }
        },
      });
    } catch (error) {
      console.log(error);
    }
  }

  function handleCancel() {
    setOpen(false);
    reset();
  }
  // 一键修改
  function handleAllFill() {
    setInputModalShow(true);
  }

  const columns: ProColumns<any>[] = [
    {
      title: '运单号',
      dataIndex: 'waybillNumber',
      fixed: 'left',
      width: 150,
      readonly: true,
      onCell: (row, index) => ({
        rowSpan: row.rowSpan,
      }),
    },
    {
      title: '制单账号',
      dataIndex: 'customerCode',
      width: 100,
      readonly: true,
      onCell: (row, index) => ({
        rowSpan: row.rowSpan,
      }),
    },

    {
      title: '订单号',
      dataIndex: 'userOrderNumber',
      width: 140,
      readonly: true,
      onCell: (row, index) => ({
        rowSpan: row.rowSpan,
      }),
    },
    {
      title: '下单时间',
      dataIndex: 'orderTime',
      width: 200,
      readonly: true,
      onCell: (row, index) => ({
        rowSpan: row.rowSpan,
      }),
    },
    {
      title: '创建时间',
      width: 160,
      dataIndex: 'createTime',
      readonly: true,
      onCell: (row, index) => ({
        rowSpan: row.rowSpan,
      }),
    },
    {
      title: 'SKU',
      dataIndex: 'sku',
      width: 100,
      readonly: true,
    },
    {
      title: '是否已到仓',
      dataIndex: 'arrivedStateName',
      width: 160,
      readonly: true,
    },
    {
      title: '中文品名',
      dataIndex: 'oldGoodsNameCh',
      width: 160,
      readonly: true,
    },
    {
      title: '英文品名',
      dataIndex: 'oldGoodsNameEh',
      width: 160,
      readonly: true,
    },
    {
      title: '新中文品名',
      dataIndex: 'newGoodsNameCh',
      width: 160,
      formItemProps: (form, { rowIndex }) => {
        return {
          rules: [
            {
              required: true,
              message: '请输入新中文品名',
            },
            {
              max: 200,
              message: '请输入200位以下的新中文品名',
            },
          ],
        };
      },
      valueType: 'select',
      fieldProps: (form, { rowKey, rowIndex }) => {
        return {
          options: goodsNameList?.[rowIndex] ?? [],
          showSearch: true,
          filterOption: (input, option) =>
            ((option?.label ?? '') as string).toLowerCase().includes(input.toLowerCase()),
          onChange: (value, option) => {
            handleChangeGoodsNameCh(value, rowIndex);
          },
          onSearch: value => {
            run(value, rowIndex);
          },
        };
      },
    },
    {
      title: '新英文品名',
      dataIndex: 'newGoodsNameEh',
      width: 160,
      readonly: true,
    },
    {
      title: '备注',
      dataIndex: 'handleNotes',
      width: 160,
    },
  ];

  return (
    <Modal
      title="品名工单处理"
      open={open}
      width="70%"
      footer={null}
      onCancel={() => handleCancel()}
    >
      <ProForm
        formRef={formRef}
        labelAlign="left"
        layout="horizontal"
        labelCol={{ flex: '130px' }}
        onFinish={handleSubmit}
        onFinishFailed={onFormErrorMessage}
        submitter={{
          render: () => {
            return [];
          },
        }}
      >
        <Row justify="space-between" className="mb-2">
          <Alert
            type="warning"
            message={'若您所选运单属于同一品名，您可使用右侧的【一键填充】功能。'}
          />
          <Button
            onClick={() => {
              handleAllFill();
            }}
          >
            一键修改
          </Button>
        </Row>
        <EditableProTable
          rowKey="waybillNumber"
          name="dataSource"
          columns={columns}
          loading={productBatchLoading}
          title={() => (
            <Typography.Text type="danger" style={{ fontSize: '14px' }}>
              温馨提示：请在【中文品名】填写品名后点击搜索，再从列表中选择最匹配的产品类型名称进行提交。
            </Typography.Text>
          )}
          scroll={{ x: 'max-content' }}
          // footer={() => (
          //   <Row justify="end">
          //     <span style={{ color: '#52c41a' }}>申报总价值：{totalPrice}</span>
          //   </Row>
          // )}
          recordCreatorProps={false}
          editable={{
            type: 'multiple',
            editableKeys,
            actionRender: (row, config, defaultDoms) => {
              return [];
            },
            onValuesChange: (record, recordList) => {
              // const price = calculateTotals('price');
              // setTotalPrice(price);
            },
            onChange: setEditableKeys,
          }}
        />
        <div className="flex justify-center">
          <Space>
            <Button
              onClick={() => {
                reset();
                handleCancel();
              }}
            >
              取消
            </Button>
            <Button type="primary" htmlType="submit" loading={productBatchLoading}>
              提交
            </Button>
          </Space>
        </div>
      </ProForm>
      <Modal
        open={inputModalShow}
        title="一键修改中文品名"
        footer={null}
        onCancel={() => {
          setInputNewGoodNameCn('');
          setInputModalShow(false);
          setInputGoodsNameList([]);
        }}
      >
        <div className="flex items-center justify-center mb-3">
          <div className="ant-col ant-form-item-label" style={{ flex: '0 0 100px' }}>
            <label htmlFor="upload" className="ant-form-item-required" title="新中文品名">
              新中文品名
            </label>
          </div>
          <Select
            style={{ width: 200 }}
            options={inputGoodsNameList}
            value={inputNewGoodNameCn}
            showSearch
            filterOption={(input, option) =>
              ((option?.label ?? '') as string).toLowerCase().includes(input.toLowerCase())
            }
            onChange={(value, option) => {
              setInputNewGoodNameCn(value);
            }}
            onSearch={value => {
              runInput(value);
            }}
          />
        </div>
        <div className="flex justify-center">
          <Space>
            <Button
              onClick={() => {
                setInputNewGoodNameCn('');
                setInputModalShow(false);
                setInputGoodsNameList([]);
              }}
            >
              取消
            </Button>
            <Button
              type="primary"
              onClick={async () => {
                if (!inputNewGoodNameCn) {
                  message.error('请输入新的中文品名');
                  return;
                }
                const promises = goodsNameList?.map((item, i) =>
                  dispatchGetMatchNameList(inputNewGoodNameCn)
                );
                const promiseData: any[] = await Promise.allSettled(promises);
                const resultData = promiseData.map((item, index) =>
                  item.status === 'fulfilled' ? item.value : []
                );
                setGoodsNameList(resultData);
                const newGoodsNameEh = inputGoodsNameList?.find(
                  item => item.value === inputNewGoodNameCn
                )?.goodsNameEnData;
                formRef.current?.setFieldsValue({
                  dataSource:
                    formRef.current?.getFieldValue('dataSource')?.map(item => ({
                      ...item,
                      newGoodsNameCh: inputNewGoodNameCn,
                      newGoodsNameEh,
                    })) ?? [],
                });
                setInputNewGoodNameCn('');
                setInputModalShow(false);
                setInputGoodsNameList([]);
              }}
            >
              确认
            </Button>
          </Space>
        </div>
      </Modal>
    </Modal>
  );
};

export default AbnormalProductDispose;
