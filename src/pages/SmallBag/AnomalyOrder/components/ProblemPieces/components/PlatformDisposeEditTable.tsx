import React, { useRef, useState } from 'react';
import { ProForm, EditableProTable, ProFormInstance, ProColumns } from '@ant-design/pro-components';
import { Button, Input, message, Popconfirm, Space, Tooltip } from 'antd';
import { useUpdateEffect, useMount, useUnmount, useDebounceFn } from 'ahooks';
import { transformOrderData, reverseOrderData, onFormErrorMessage } from '@/utils/utils';
import { InfoCircleOutlined } from '@ant-design/icons';

/**
 * 调整修改信息改为表格更改
 * @param props
 * @returns
 */
const PlatformDisposeEditTable = props => {
  const {
    dataSource,
    dispatch,
    subAbnormal,
    handleCancel,
    labelname,
    loading,
    handleTableCancel,
  } = props;
  const formRef = useRef<ProFormInstance>();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [editDataSource, setEditDataSource] = useState([]);
  const [show, setShow] = useState(false);
  const [currencyCode, setCurrencyCode] = useState(''); // 申报币种
  const [totalNum, setTotalNum] = useState(0); // 申报的总数量
  const [totalWeight, setTotalWeight] = useState(0); // 申报的总重量
  const [totalPrice, setTotalPrice] = useState(0); // 申报的总价值
  const [currencies, setCurrencies] = useState({}); // 所有币种
  const [goodsNameList, setGoodsNameList] = useState([]);
  const [isEJF, setIsEJF] = useState<number>(0); // 1: 全是E键发, 2: 全是亚马逊, 3: 混合

  useMount(() => {
    setShow(true);
  });

  useUnmount(() => {
    reset();
  });

  const reset = () => {
    formRef?.current?.resetFields();
    setShow(false);
    setEditDataSource([]);
    setIsEJF(0);
    setEditableRowKeys([]);
  };

  useUpdateEffect(() => {
    if (dataSource?.length > 0 && show) {
      Promise.all([getCurrency()]).then(res => {
        const data = transformOrderData(dataSource, labelname);
        setEditDataSource(dataSource);
        getMatchNameList(data);
        // 新的判断逻辑
        const isAllEJF = dataSource?.every(s => s.plateform === 'E键发');
        const isAllAmazon = dataSource?.every(s => s.plateform !== 'E键发');
        setIsEJF(isAllEJF ? 1 : isAllAmazon ? 2 : 3);
        formRef?.current?.setFieldsValue({
          dataSource: data,
        });
        setEditableRowKeys(data?.map((_, index) => index) ?? []);
      });
    }
  }, [dataSource, show]);

  const { run } = useDebounceFn(
    (value, index) => {
      if (!value) return;
      const resultData = goodsNameList?.map((item, i) => {
        if (index === i) {
          return dispatchGetMatchNameList(value);
        } else {
          return item;
        }
      });
      Promise.allSettled(resultData).then(res => {
        const data = res.map((item, index) => {
          if (item.status === 'fulfilled') {
            return item.value;
          } else {
            return [];
          }
        });
        setGoodsNameList(data);
      });
    },
    { wait: 300 }
  );

  // 获取所有币种
  const getCurrency = () =>
    new Promise((resolve, reject) => {
      dispatch({
        type: 'Order/getCurrency',
        callback: result => {
          if (result.success) {
            let info = {};
            result?.data?.forEach(v => {
              const label = `${v.name}/${v.code}`;
              const value = v.code;
              info[value] = { text: label, status: 'Default', ...v };
            });
            setCurrencies(info);
            resolve(result.success);
          } else {
            reject(result.success);
          }
        },
      });
    });

  function getMatchNameList(productList = []) {
    return new Promise((resolve, reject) => {
      const resultData = productList?.map(item => dispatchGetMatchNameList(item.goodsNameCh));
      Promise.allSettled(resultData).then(res => {
        const data = res.map((item, index) => {
          if (item.status === 'fulfilled') {
            return item.value;
          } else {
            return [];
          }
        });
        setGoodsNameList(data);
      });
    });
  }

  function dispatchGetMatchNameList(name: string) {
    return new Promise((resolve, reject) => {
      dispatch({
        type: 'anomalyOrder/getMatchNameList',
        payload: {
          name,
        },
        callback: response => {
          if (response.success) {
            resolve(
              response.data.map(item => ({
                value: item.standardGoodsName,
                label: item.standardGoodsName,
                goodsNameEnData: item.standardGoodsnameEn,
              }))
            );
          }
        },
      });
    });
  }

  function handleChangeGoodsNameCh(value: string, key: number) {
    const goodsNameEnData = goodsNameList?.[key]?.find(item => item.value === value)
      ?.goodsNameEnData;
    formRef.current?.setFieldsValue({
      dataSource: [
        ...formRef.current?.getFieldValue('dataSource').slice(0, key),
        {
          ...formRef.current?.getFieldValue('dataSource')[key],
          goodsNameEn: goodsNameEnData,
        },
        ...formRef.current?.getFieldValue('dataSource').slice(key + 1),
      ],
    });
  }

  const columns: ProColumns<any>[] = [
    {
      title: '运单号',
      dataIndex: 'waybillNumber',
      fixed: 'left',
      width: 150,
      readonly: true,
      onCell: (row, index) => ({
        rowSpan: row.rowSpan,
      }),
    },
    {
      title: '制单账号',
      dataIndex: 'ywCustomerCode',
      width: 100,
      readonly: true,
      onCell: (row, index) => ({
        rowSpan: row.rowSpan,
      }),
    },
    {
      title: '目的国',
      dataIndex: 'regionName',
      width: 140,
      readonly: true,
      onCell: (row, index) => ({
        rowSpan: row.rowSpan,
      }),
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      width: 200,
      readonly: true,
      onCell: (row, index) => ({
        rowSpan: row.rowSpan,
      }),
    },
    {
      title: '交货仓',
      width: 160,
      dataIndex: 'companyCodeName',
      readonly: true,
      onCell: (row, index) => ({
        rowSpan: row.rowSpan,
      }),
    },
    {
      title: '异常原因',
      dataIndex: 'exceptionTypeName',
      width: 160,
      readonly: true,
      onCell: (row, index) => ({
        rowSpan: row.rowSpan,
      }),
    },
    {
      title: '异常具体原因',
      dataIndex: 'memo',
      width: 160,
      readonly: true,
      onCell: (row, index) => ({
        rowSpan: row.rowSpan,
      }),
      render: (text, row) => {
        let arr = (row?.memo as string)?.split(':');
        return {
          children: arr?.length > 1 && text !== null ? arr[arr.length - 1] : text,
        };
      },
    },
    {
      title: '姓名',
      dataIndex: 'receiverName',
      width: 160,
      formItemProps: (form, { rowIndex }) => {
        return {
          rules: [
            {
              required: true,
              message: '请输入姓名',
            },
            {
              max: 300,
              message: '请输入300位以下的姓名',
            },
          ],
        };
      },
      hideInTable: labelname != '收件人信息修改',
    },
    {
      title: '公司',
      dataIndex: 'receiverCompany',
      width: 160,
      formItemProps: (form, { rowIndex }) => {
        return {
          rules: [
            {
              required: false,
              message: '请输入公司',
            },
            {
              max: 300,
              message: '请输入300位以下的公司名称',
            },
          ],
        };
      },

      hideInTable: labelname != '收件人信息修改' || isEJF === 2,
      //不允许编辑
      editable: (text, record, index) => {
        // 不允许编辑
        // editable=false
        return record?.plateform === 'E键发';
      },
    },
    {
      title: '省/州',
      dataIndex: 'receiverState',
      width: 160,
      formItemProps: (form, { rowIndex }) => {
        const isRequired = editDataSource?.[rowIndex]?.plateform === 'E键发';
        return {
          rules: [
            {
              required: isRequired ? false : true,
              message: '请输入省/州',
            },
            {
              max: 50,
              message: '请输入50位以下的省/州名',
            },
          ],
        };
      },

      hideInTable: labelname != '收件人信息修改',
    },
    {
      title: '城市',
      dataIndex: 'receiverCity',
      width: 160,
      formItemProps: (form, { rowIndex }) => {
        return {
          rules: [
            {
              required: true,
              message: '请输入城市',
            },
            {
              max: 200,
              message: '请输入200位以下的城市名',
            },
          ],
        };
      },

      hideInTable: labelname != '收件人信息修改',
    },
    {
      title: '邮编',
      dataIndex: 'postcode',
      width: 160,
      formItemProps: (form, { rowIndex }) => {
        return {
          rules: [
            {
              required: false,
              message: '请输入邮编',
            },
            {
              max: 100,
              message: '请输入100位以下的邮编',
            },
          ],
        };
      },
      hideInTable: labelname != '收件人信息修改',
    },
    {
      title: '门牌号',
      dataIndex: 'receiverHouse',
      width: 160,
      formItemProps: (form, { rowIndex }) => {
        return {
          rules: [
            {
              required: false,
              message: '请输入门牌号',
            },
            {
              max: 100,
              message: '请输入100位以下的门牌号',
            },
          ],
        };
      },
      hideInTable: labelname != '收件人信息修改' || isEJF === 2,
      //不允许编辑
      editable: (text, record, index) => {
        // editable=false
        return record?.plateform === 'E键发';
      },
    },
    {
      title: '自提点ID',
      dataIndex: 'pointId',
      width: 160,
      formItemProps: (form, { rowIndex }) => {
        return {
          rules: [
            {
              required: false,
              message: '请输入自提点ID',
            },
            {
              max: 50,
              message: '请输入50位以下的自提点ID',
            },
          ],
        };
      },
      hideInTable: labelname != '收件人信息修改' || isEJF === 2,
      //不允许编辑
      editable: (text, record, index) => {
        // editable=false
        return record?.plateform === 'E键发';
      },
    },
    {
      title: '电话',
      dataIndex: 'receiverPhone',
      width: 160,
      formItemProps: (form, { rowIndex }) => {
        return {
          rules: [
            {
              required: true,
              message: '请输入电话',
            },
            {
              max: 50,
              message: '请输入50位以下的电话号码',
            },
          ],
        };
      },
      hideInTable: labelname != '收件人信息修改',
    },
    {
      title: '手机号',
      dataIndex: 'receiverMobile',
      width: 160,
      formItemProps: (form, { rowIndex }) => {
        return {
          rules: [
            {
              required: true,
              message: '请输入手机号',
            },
            {
              max: 50,
              message: '请输入50位以下的手机号号码',
            },
          ],
        };
      },
      hideInTable: labelname != '收件人信息修改' || isEJF === 1,
      //不允许编辑
      editable: (text, record, index) => {
        // editable=false
        return record?.plateform !== 'E键发';
      },
    },
    {
      title: '邮箱',
      dataIndex: 'receiverEmail',
      width: 160,
      formItemProps: (form, { rowIndex }) => {
        return {
          rules: [
            {
              required: false,
              message: '请输入邮箱',
            },
            {
              max: 100,
              message: '请输入100位以下的邮箱地址',
            },
          ],
        };
      },
      hideInTable: labelname != '收件人信息修改',
    },
    {
      title: '税号',
      dataIndex: 'receiverTaxNumber',
      width: 160,
      formItemProps: (form, { rowIndex }) => {
        return {
          rules: [
            {
              required: false,
              message: '请输入税号',
            },
            {
              max: 50,
              message: '请输入50位以下的税号',
            },
          ],
        };
      },
      hideInTable: labelname != '收件人信息修改',
    },
    {
      title: 'PINFL号码',
      dataIndex: 'receiverPinflNumber',
      width: 160,
      formItemProps: (form, { rowIndex }) => {
        return {
          rules: [
            {
              required: false,
              message: '请输入PINFL号码',
            },
            {
              max: 50,
              message: '请输入50位以下的PINFL号码',
            },
          ],
        };
      },
      hideInTable: labelname != '收件人信息修改' || isEJF === 2,
      //不允许编辑
      editable: (text, record, index) => {
        // editable=false
        return record?.plateform === 'E键发';
      },
    },
    {
      title: '地址1',
      dataIndex: 'receiverAddress1',
      width: 160,
      formItemProps: (form, { rowIndex }) => {
        return {
          rules: [
            {
              required: true,
              message: '请输入地址1',
            },
            {
              max: 500,
              message: '请输入500位以下的地址1',
            },
          ],
        };
      },

      hideInTable: labelname != '收件人信息修改',
    },
    {
      title: '地址2',
      dataIndex: 'receiverAddress2',
      width: 160,
      formItemProps: (form, { rowIndex }) => {
        return {
          rules: [
            {
              required: false,
              message: '请输入地址2',
            },
            {
              max: 500,
              message: '请输入500位以下的地址2',
            },
          ],
        };
      },
      hideInTable: labelname != '收件人信息修改' || isEJF === 1,
      //不允许编辑
      editable: (text, record, index) => {
        return record?.plateform !== 'E键发';
      },
    },
    {
      title: '申报币种',
      dataIndex: 'declaredCurrency',
      width: 160,
      formItemProps: (form, { rowIndex }) => {
        return {
          rules: [
            {
              required: true,
              message: '请选择申报币种',
            },
          ],
        };
      },
      valueType: 'select',
      valueEnum: currencies,
      hideInTable: labelname != '申报信息修改',
      onCell: (row, index) => ({
        rowSpan: row.rowSpan,
      }),
    },
    {
      title: '申报价值',
      dataIndex: 'declareValue',
      width: 160,
      formItemProps: (form, { rowIndex }) => {
        return {
          rules: [
            {
              required: true,
              message: '请选择申报价值',
            },
          ],
        };
      },
      onCell: (row, index) => ({
        rowSpan: row.rowSpan,
      }),
      hideInTable: labelname != '申报信息修改' || isEJF === 1,
      //不允许编辑
      editable: (text, record, index) => {
        return record?.plateform !== 'E键发';
      },
    },
    {
      title: '中文品名',
      dataIndex: 'goodsNameCh',
      width: 160,
      formItemProps: (form, { rowIndex }) => {
        return {
          rules: [
            {
              required: true,
              message: '请输入中文品名',
            },
            {
              max: 200,
              message: '请输入200位以下的中文品名',
            },
          ],
        };
      },
      valueType: 'select',
      fieldProps: (form, { rowKey, rowIndex }) => {
        return {
          options: goodsNameList?.[rowIndex] ?? [],
          showSearch: true,
          filterOption: (input, option) =>
            ((option?.label ?? '') as string).toLowerCase().includes(input.toLowerCase()),
          onChange: (value, option) => {
            handleChangeGoodsNameCh(value, rowIndex);
          },
          onSearch: value => {
            run(value, rowIndex);
          },
        };
      },
      hideInTable: labelname != '申报信息修改',
    },
    {
      title: '英文品名',
      dataIndex: 'goodsNameEn',
      width: 160,
      readonly: true,
      hideInTable: labelname != '申报信息修改',
    },
    {
      title: '单票数量',
      dataIndex: 'quantity',
      width: 160,
      formItemProps: (form, { rowIndex }) => {
        return {
          rules: [
            {
              required: true,
              message: '请输入单票数量',
            },
            {
              max: 11,
              message: '请输入11位以下的单品数量',
            },
            {
              pattern: /^\d+$|^\d+[.]?\d+$/,
              message: '请输入正整数数字',
            },
          ],
        };
      },
      hideInTable: labelname != '申报信息修改' || isEJF === 2,
      //不允许编辑
      editable: (text, record, index) => {
        return record?.plateform === 'E键发';
      },
    },
    {
      title: '单品重量(g)',
      dataIndex: 'weight',
      width: 160,
      formItemProps: (form, { rowIndex }) => {
        return {
          rules: [
            {
              required: true,
              message: '请输入单品重量',
            },
            {
              pattern: /^[1-9]\d{0,10}$/,
              message: '请输入正整数数字',
            },
          ],
        };
      },
      hideInTable: labelname != '申报信息修改' || isEJF === 2,
      //不允许编辑
      editable: (text, record, index) => {
        return record?.plateform === 'E键发';
      },
    },
    {
      title: '申报单价',
      dataIndex: 'price',
      width: 160,
      formItemProps: (form, { rowIndex }) => {
        return {
          rules: [
            {
              required: true,
              message: '请输入申报单价',
            },
            {
              pattern: /^([1-9]\d{0,15}\.\d{1,2}|0\.\d{1,2}|[1-9]\d{0,15})$/,
              message: '请输入最大16位数字+小数点两位',
            },
          ],
        };
      },
      hideInTable: labelname != '申报信息修改' || isEJF === 2,
      //不允许编辑
      editable: (text, record, index) => {
        return record?.plateform === 'E键发';
      },
    },
    {
      title: '商品材质',
      dataIndex: 'material',
      width: 160,
      formItemProps: (form, { rowIndex }) => {
        return {
          rules: [
            {
              required: false,
              message: '请输入商品材质',
            },
            {
              max: 500,
              message: '请输入500位以下的商品材质',
            },
          ],
        };
      },
      hideInTable: labelname != '申报信息修改' || isEJF === 2,
      //不允许编辑
      editable: (text, record, index) => {
        return record?.plateform === 'E键发';
      },
    },
    {
      title: '商品海关编码',
      dataIndex: 'hscode',
      width: 160,
      formItemProps: (form, { rowIndex }) => {
        return {
          rules: [
            {
              required: false,
              message: '请输入商品材质',
            },
            {
              max: 500,
              message: '请输入500位以下的商品材质',
            },
          ],
        };
      },
      hideInTable: labelname != '申报信息修改',
    },
    {
      title: '商品链接',
      dataIndex: 'url',
      width: 160,
      formItemProps: (form, { rowIndex }) => {
        return {
          rules: [
            {
              pattern: /^(((ht|f)tps?):\/\/)?[\w-]+(\.[\w-]+)+([\w.,@?^=%&:/~+#-]*[\w@?^=%&/~+#-])?$/,
              required: false,
              message: '请输入正确的商品链接',
            },
          ],
        };
      },
      hideInTable: labelname != '申报信息修改' || isEJF === 2,
      //不允许编辑
      editable: (text, record, index) => {
        return record?.plateform === 'E键发';
      },
    },
    {
      title: 'SKU',
      dataIndex: 'sku',
      width: 160,
      hideInTable: labelname != '申报信息修改' || isEJF === 2,
      //不允许编辑
      editable: (text, record, index) => {
        return record?.plateform === 'E键发';
      },
    },
    {
      title: 'IMEI',
      dataIndex: 'imei',
      width: 160,
      hideInTable: labelname != '申报信息修改' || isEJF === 2,
      //不允许编辑
      editable: (text, record, index) => {
        return record?.plateform === 'E键发';
      },
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 260,
      renderFormItem: () => {
        return (
          <Input
            placeholder="请输入"
            suffix={
              <Tooltip title="信息请在对应框修改，填写在备注栏无法生效。">
                <InfoCircleOutlined style={{ color: 'rgba(0,0,0,.45)' }} />
              </Tooltip>
            }
          />
        );
      },
    },
    {
      title: '操作',
      width: 160,
      valueType: 'option',
      align: 'center',
      fixed: 'right',
      hideInTable: dataSource?.length === 1,
    },
  ];

  const handleSubmit = async values => {
    const params = reverseOrderData(values?.dataSource, currencies, labelname);
    dispatch({
      type: 'anomalyOrder/orderInformation',
      payload: params,
      callback: res => {
        if (res.success) {
          message.success(res.message, 1).then(() => {
            // router.push(`/express/abnormalList`);
            subAbnormal(params);
          });
        }
      },
    });
  };

  return (
    <>
      <ProForm
        formRef={formRef}
        labelAlign="left"
        layout="horizontal"
        labelCol={{ flex: '130px' }}
        onFinish={handleSubmit}
        onFinishFailed={onFormErrorMessage}
        submitter={{
          render: () => {
            return [];
          },
        }}
      >
        <EditableProTable
          rowKey="id"
          name="dataSource"
          columns={columns}
          loading={loading}
          scroll={{ x: 'max-content' }}
          // footer={() => (
          //   <Row justify="end">
          //     <span style={{ color: '#52c41a' }}>申报总价值：{totalPrice}</span>
          //   </Row>
          // )}
          recordCreatorProps={false}
          editable={{
            type: 'multiple',
            editableKeys,
            actionRender: (row, config) => {
              return [
                <Popconfirm
                  title="确定暂不处理吗？"
                  onConfirm={() => {
                    handleTableCancel(
                      row,
                      reverseOrderData(
                        formRef?.current?.getFieldValue('dataSource'),
                        currencies,
                        labelname
                      ),
                      labelname
                    );
                  }}
                >
                  <a>暂不处理</a>
                </Popconfirm>,
              ];
            },
            onValuesChange: (record, recordList) => {
              // const price = calculateTotals('price');
              // setTotalPrice(price);
            },
            onChange: setEditableRowKeys,
          }}
        />
        <div className="flex justify-center">
          <Space>
            <Button
              onClick={() => {
                reset();
                handleCancel();
              }}
            >
              取消
            </Button>
            <Button type="primary" htmlType="submit" loading={loading}>
              提交
            </Button>
          </Space>
        </div>
      </ProForm>
    </>
  );
};

export default PlatformDisposeEditTable;
