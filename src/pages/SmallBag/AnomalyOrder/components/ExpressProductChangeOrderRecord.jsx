import React, { Component, Fragment } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Button, Card, Col, DatePicker, Input, message, Row, Select, Space } from 'antd';
// import styles from '@/pages/Express/ExpressChangeRecord/expressChangeRecord.less';
import SingleTagSelect from '@/components/SingleTagSelect';
import StandardFormRow from '@/components/StandardFormRow';
import { connect } from 'dva';
import ExportJsonExcel from 'js-export-excel';
import moment from 'moment';
import ProTableList from '@/components/ProTable';
import { formatMessage } from 'umi-plugin-react/locale';
import PageContainerComponent from '@/components/PageContainer';
import PageHeaderBreadcrumb from '@/components/PageHeaderBreadcrumb';
import { logSave, LogType } from '@/utils/logSave';
import { CopyOutlined } from '@ant-design/icons';
import { clickCopy } from '@/utils/utils';

const { RangePicker } = DatePicker;
// const channels = [
//   { channelCode: '1034', channelName: '{formatMessage({id: '燕文美国快线'})}-{formatMessage({id: '普货'})}' },
//   { channelCode: '1035', channelName: '{formatMessage({id: '燕文美国快线'})}-{formatMessage({id: '特货'})}' },
// ];

@connect(({ loading }) => ({ queryLoading: loading.effects['anomalyOrder/changeRecordList'] }))
@Form.create()
class ExpressProductChangeOrderRecord extends Component {
  constructor(props) {
    super(props);
    this.state = {
      shippers: [],
      data: [],
      channels: [],
      pageSize: 10,
      current: 1,
    };
  }

  componentDidMount() {
    this.getLoadAccounts();
    this.getChannel();
  }

  getLoadAccounts = async () => {
    const { dispatch, form } = this.props;
    dispatch({
      type: 'anomalyOrder/makeAccountLoad',
      payload: {
        accountType: 0,
        scene: 1,
      },
      callback: response => {
        if (response.success) {
          // let dataArray = response.data.map(item => item.accountCode);
          // form.setFieldsValue({ customerCode: dataArray });
          this.setState(
            {
              shippers: response.data,
            },
            () => {
              const { shippers } = this.state;
              form.setFieldsValue({
                customerCode: shippers && shippers.length ? [shippers[0].accountCode] : '',
              });
            }
          );
        } else {
          this.setState({
            shippers: [],
          });
        }
      },
    });
  };

  getChannelName = channelId => {
    let result = '';
    this.state.channels.map(value => {
      if (value.id === channelId) {
        result = value.nameCh;
        return;
      }
    });
    return result;
  };

  // {formatMessage({id: '获取渠道'})}
  getChannel = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'ExpressRecord/getExpressChannels',
      callback: result => {
        this.setState({
          channels: result.data,
        });
      },
    });
  };

  column = [
    {
      title: formatMessage({ id: '制单账号' }),
      dataIndex: 'customerCode',
      key: 'customerCode',
    },
    {
      title: formatMessage({ id: '运单号' }),
      dataIndex: 'expressNumber',
      key: 'expressNumber',
      render: text => {
        return (
          <Space>
            {text}
            {text && (
              <CopyOutlined
                style={{ color: '#52c41a' }}
                onClick={() => {
                  clickCopy(text ?? '');
                }}
              />
            )}
          </Space>
        );
      },
    },
    {
      title: formatMessage({ id: '原转单号' }),
      dataIndex: 'oldTransferNumber',
      key: 'oldTransferNumber',
      render: text => {
        return (
          <Space>
            {text}
            {text && (
              <CopyOutlined
                style={{ color: '#52c41a' }}
                onClick={() => {
                  clickCopy(text ?? '');
                }}
              />
            )}
          </Space>
        );
      },
    },
    {
      title: formatMessage({ id: '新转单号' }),
      dataIndex: 'newTransferNumber',
      key: 'newTransferNumber',
      render: text => {
        return (
          <Space>
            {text}
            {text && (
              <CopyOutlined
                style={{ color: '#52c41a' }}
                onClick={() => {
                  clickCopy(text ?? '');
                }}
              />
            )}
          </Space>
        );
      },
    },
    {
      title: formatMessage({ id: '产品名称' }),
      dataIndex: 'channel',
      key: 'channel',
      render: text => {
        return this.getChannelName(text);
      },
    },
    {
      title: formatMessage({ id: '国家' }),
      dataIndex: 'country',
      key: 'country',
    },
    {
      title: formatMessage({ id: '制单日期' }),
      dataIndex: 'orderTime',
      key: 'orderTime',
      render: text => {
        return moment(text).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: formatMessage({ id: '转单日期' }),
      dataIndex: 'transferTime',
      key: 'transferTime',
      render: text => {
        return moment(text).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: formatMessage({ id: '制单重量' }),
      dataIndex: 'orderWeight',
      key: 'orderWeight',
    },
    {
      title: formatMessage({ id: '计费重量' }),
      dataIndex: 'chargedWeight',
      key: 'chargedWeight',
    },
    {
      title: formatMessage({ id: '重量差' }),
      dataIndex: 'diffWeight',
      key: 'diffWeight',
    },
    {
      title: formatMessage({ id: '转单号是否变更' }),
      dataIndex: 'isChanged',
      key: 'isChanged',
      render: text => (text ? formatMessage({ id: '是' }) : formatMessage({ id: '否' })),
    },
    {
      title: formatMessage({ id: '转单号是否已读' }),
      dataIndex: 'isExtract',
      key: 'isExtract',
      render: text => (text ? formatMessage({ id: '已读' }) : formatMessage({ id: '未读' })),
    },
    {
      title: formatMessage({ id: '操作' }),
      dataIndex: 'operate',
      key: 'operate',
      render: (text, record, index) => {
        return (
          <>
            {record.isExtract == 0 && (
              <Button
                type="link"
                onClick={async () => {
                  try {
                    const success = await this.extract({
                      userId: record.customerCode,
                      listId: [record.id],
                    });

                    if (success) {
                      this.setState(prevState => ({
                        data: prevState.data.map((item, i) =>
                          i === index ? { ...item, isExtract: 1 } : item
                        ),
                      }));
                    }
                  } catch (error) {
                    console.error('Extraction failed:', error);
                    // 可以添加错误处理，如显示提示信息
                  }
                }}
              >
                {formatMessage({ id: '转单号已读' })}
              </Button>
            )}
          </>
        );
      },
    },
  ];

  validateDatePicker = (rule, value, callback) => {
    if (value === undefined) {
      callback();
      return;
    }
    if (value.length == 0) {
      callback(formatMessage({ id: '请选择制单日期' }));
      return;
    }
    let start = value[0];
    let end = value[1];
    let day = end.diff(start, 'day');
    if (day > 13) {
      callback(`${formatMessage({ id: '查询范围仅支持' })}14${formatMessage({ id: '天之内' })}`);
      return;
    }
    callback();
  };
  // {formatMessage({id: '验证运单号'})}
  validateExpressNumber = (rule, value, callback) => {
    const str = value.replace(/[\r\n]/g, '');
    let counts = value.split('\n').length;
    let valrlurs = new RegExp('^[0-9a-zA-Z ]+$');
    if (counts > 200) {
      callback(`${formatMessage({ id: '最多输入' })}200${formatMessage({ id: '单' })}`);
    } else if (value && !valrlurs.test(str)) {
      callback(`${formatMessage({ id: '不能出现数字及字母外的字符' })}`);
    } else {
      callback();
    }
  };
  reset = () => {
    logSave(LogType.packet27);
    this.props.form.resetFields();
  };
  getDataList = () => {
    logSave(LogType.packet26);
    const { dispatch, form } = this.props;
    form.validateFieldsAndScroll((err, values) => {
      if (err) {
        return;
      }
      let epCodes = values.epCodes;
      let nums = epCodes.split(/[\r\n]/g).join();
      let param = {};
      param.customerCode = values.customerCode.join(',');
      if (values.channelType !== '0') {
        param.channel = values.channelType;
      }
      param.expressNumbers = nums;
      let orderDate = values.orderDate;
      param.isChanged = values.isChanged;
      param.isExtract = values.isExtract == 'all' ? undefined : values.isExtract;
      param.startTime = orderDate[0].format('YYYY-MM-DD');
      param.endTime = orderDate[1].format('YYYY-MM-DD');
      param.pageNo = this.state.current;
      param.pageSize = this.state.pageSize;
      dispatch({
        type: 'ExpressRecord/changeRecordList',
        payload: param,
        callback: result => {
          if (result.success) {
            this.setState({
              data: result.data.records,
              loading: false,
              total: result.data.total,
            });
            if (result.data.length == 0) {
              message.warn(formatMessage({ id: '请核实运单是否属于查询时间内的快线产品' }), 2);
            }
          } else {
            this.setState({
              data: [],
            });
          }
        },
      });
    });
  };

  extract = param => {
    return new Promise(resolve => {
      const { dispatch } = this.props;
      dispatch({
        type: 'ExpressRecord/extract',
        payload: param,
        callback: result => {
          if (result.success) {
            message.success(result.message);
            resolve(true);
          } else {
            resolve(false);
          }
        },
      });
    });
  };

  exportData = () => {
    logSave(LogType.packet28);
    const { data } = this.state;
    if (data.length == 0) {
      message.warn(formatMessage({ id: '暂无数据' }), 2);
      return;
    }
    let option = {};
    let dataTable = [];
    data.map(value => {
      let temp = {
        [formatMessage({ id: '制单账号' })]: value.customerCode,
        [formatMessage({ id: '运单号' })]: value.expressNumber,
        [formatMessage({ id: '原转单号' })]: value.oldTransferNumber,
        [formatMessage({ id: '新转单号' })]: value.newTransferNumber,
        [formatMessage({ id: '产品名称' })]: this.getChannelName(value.channel),
        [formatMessage({ id: '国家' })]: value.country,
        [formatMessage({ id: '制单日期' })]: value.orderTime,
        [formatMessage({ id: '转单日期' })]: value.transferTime,
        [formatMessage({ id: '制单重量' })]: value.orderWeight,
        [formatMessage({ id: '计费重量' })]: value.chargedWeight,
        [formatMessage({ id: '重量差' })]: value.diffWeight,
        [formatMessage({ id: '转单号是否变更' })]: value.isChanged
          ? formatMessage({ id: '是' })
          : formatMessage({ id: '否' }),
        [formatMessage({ id: '转单号是否已读' })]: value.isExtract
          ? formatMessage({ id: '已读' })
          : formatMessage({ id: '未读' }),
      };
      dataTable.push(temp);
    });
    option.datas = [
      {
        fileName: formatMessage({ id: '快线产品换单记录' }),
        sheetData: dataTable,
        sheetName: 'sheet1',
        sheetHeader: [
          formatMessage({ id: '制单账号' }),
          formatMessage({ id: '运单号' }),
          formatMessage({ id: '原转单号' }),
          formatMessage({ id: '新转单号' }),
          formatMessage({ id: '产品名称' }),
          formatMessage({ id: '国家' }),
          formatMessage({ id: '制单日期' }),
          formatMessage({ id: '转单日期' }),
          formatMessage({ id: '制单重量' }),
          formatMessage({ id: '计费重量' }),
          formatMessage({ id: '重量差' }),
          formatMessage({ id: '转单号是否变更' }),
        ],
      },
    ];
    option.fileName = formatMessage({ id: '快线产品换单记录' });
    let toExcel = new ExportJsonExcel(option); //new
    toExcel.saveExcel(); //{formatMessage({id: '保存'})}
  };

  render() {
    const {
      queryLoading,
      form: { getFieldDecorator },
    } = this.props;
    const { shippers, data, pageSize, current, total } = this.state;
    const actionsTextMap = {
      expandText: formatMessage({ id: '展开' }),
      collapseText: formatMessage({ id: '收起' }),
      selectAllText: formatMessage({ id: '全部' }),
    };
    let shipperOpen = shippers.length > 8 ? true : false;
    const { TextArea } = Input;
    const pagination = {
      total: total,
      current: current,
      pageSize: pageSize,
      showSizeChanger: true,
      pageSizeOptions: ['10', '20', '50', '100'],
      onChange: (page, pageSize) => {
        this.setState(
          {
            current: page,
            pageSize,
          },
          this.getDataList
        );
      },
      showTotal: () => {
        return `${formatMessage({ id: '共' })}- ${total} -${formatMessage({ id: '条记录' })}`;
      },
    };
    return (
      <PageContainerComponent
        header={{
          title: null,
          breadcrumb: {},
          breadcrumbRender: props => <PageHeaderBreadcrumb {...props} />,
        }}
      >
        <Card
          bordered={false}
          className="listShipper"
          bodyStyle={{
            padding: '24px 24px 0px 24px',
          }}
        >
          <Fragment>
            <Form layout="block">
              <StandardFormRow
                title={formatMessage({ id: '制单账号' })}
                block
                style={{ paddingBottom: 0, border: 'none', marginBottom: 0 }}
              >
                <Form.Item>
                  {getFieldDecorator('customerCode')(
                    <SingleTagSelect
                      actionsText={actionsTextMap}
                      className="open"
                      expandable={shipperOpen}
                    >
                      {shippers &&
                        shippers.map((element, index) => (
                          <SingleTagSelect.Option key={index} value={element.accountCode}>
                            {element.warehouseName}
                            {element.accountCode}
                          </SingleTagSelect.Option>
                        ))}
                    </SingleTagSelect>
                  )}
                </Form.Item>
              </StandardFormRow>
              <StandardFormRow
                title={formatMessage({ id: '运单号/转单号' })}
                block
                style={{ paddingBottom: 0, border: 'none', marginBottom: 0 }}
              >
                <Row>
                  <Col xs={{ span: 24 }} sm={{ span: 24 }} md={{ span: 24 }} lg={{ span: 6 }}>
                    <Form.Item>
                      {getFieldDecorator('epCodes', {
                        initialValue: '',
                        rules: [
                          {
                            validator: this.validateExpressNumber,
                          },
                        ],
                      })(
                        <TextArea
                          placeholder={`${formatMessage({ id: '最多' })}200${formatMessage({
                            id: '单',
                          })},${formatMessage({ id: '每单一行' })}`}
                          style={{ width: '260px', display: 'inline-block', height: '120px' }}
                        />
                      )}
                    </Form.Item>
                  </Col>
                  <Col xs={{ span: 24 }} sm={{ span: 24 }} md={{ span: 24 }} lg={{ span: 7 }}>
                    <Form.Item label={formatMessage({ id: '产品名称' })}>
                      {getFieldDecorator('channelType', {
                        initialValue: '0',
                      })(
                        <Select style={{ width: '250px' }}>
                          <option value="0" key={'-1'}>
                            {formatMessage({ id: '全部' })}
                          </option>
                          {this.state.channels
                            ?.filter(item => item.status === '1')
                            ?.map((element, index) => (
                              <option value={element.id} key={index}>
                                {element.nameCh}
                              </option>
                            ))}
                        </Select>
                      )}
                    </Form.Item>
                    <Form.Item
                      label={formatMessage({ id: '制单日期' })}
                      style={{ marginTop: '18px' }}
                    >
                      {getFieldDecorator('orderDate', {
                        rules: [
                          {
                            required: true,
                            message: formatMessage({ id: '请选择制单日期' }),
                          },
                          {
                            validator: this.validateDatePicker,
                          },
                        ],
                        initialValue: [moment().subtract(13, 'days'), moment()],
                      })(<RangePicker format="YYYY-MM-DD" style={{ width: '250px' }} />)}
                    </Form.Item>
                  </Col>
                  <Col xs={{ span: 24 }} sm={{ span: 24 }} md={{ span: 24 }} lg={{ span: 8 }}>
                    <Form.Item label={formatMessage({ id: '转单号是否变更' })}>
                      {getFieldDecorator('isChanged', {
                        initialValue: 1,
                      })(
                        <Select allowClear style={{ width: '250px' }}>
                          <option value={1}>{formatMessage({ id: '是' })}</option>
                          <option value={0}>{formatMessage({ id: '否' })}</option>
                        </Select>
                      )}
                    </Form.Item>
                    <Form.Item label={formatMessage({ id: '转单号是否已读' })}>
                      {getFieldDecorator('isExtract', {
                        initialValue: 'all',
                      })(
                        <Select placeholder={'是否已读'} allowClear style={{ width: '250px' }}>
                          <option value={'all'}>{formatMessage({ id: '全部' })}</option>
                          <option value={1}>{formatMessage({ id: '已读' })}</option>
                          <option value={0}>{formatMessage({ id: '未读' })}</option>
                        </Select>
                      )}
                    </Form.Item>
                  </Col>
                  <Col>
                    <Space direction="vertical" size="large" className="pt-2">
                      <Button type="primary" onClick={this.getDataList} loading={queryLoading}>
                        {formatMessage({ id: '查询' })}
                      </Button>
                      <Button type="primary" onClick={this.reset}>
                        {formatMessage({ id: '重置' })}
                      </Button>
                    </Space>
                  </Col>
                </Row>
              </StandardFormRow>
            </Form>
          </Fragment>
        </Card>
        <div style={{ background: '#fff', padding: '10px', marginTop: '16px' }}>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              padding: '10px',
            }}
          >
            <span>
              <span style={{ fontSize: '16px', fontWeight: 'bold' }}>
                {formatMessage({ id: '查询列表' })}
              </span>{' '}
              <span>
                {formatMessage({ id: '当前共' })}
                {total}
                {formatMessage({ id: '条运单' })}
              </span>
            </span>
            <Button type="primary" onClick={this.exportData} style={{ marginTop: '0px' }}>
              {formatMessage({ id: '导出' })}
            </Button>
          </div>
          <ProTableList
            columns={this.column}
            dataSource={data}
            loading={queryLoading}
            search={false}
            toolBarRender={false}
            rowSelection={false}
            pagination={pagination}
          />
        </div>
      </PageContainerComponent>
    );
  }
}

export default ExpressProductChangeOrderRecord;
