import React, { useState, useRef } from 'react';
import router from 'umi/router';
import { Table, Card, Button, Radio, message } from 'antd';
import styles from '../WaybillCollected.less';
import ProTable from '@/components/ProTable';
import WaybillCollectedDetails from '../WaybillCollectedDetails';

const WaybillCollectedTable = props => {
  const {
    loading,
    radioValue,
    currentPage,
    pageSize,
    total,
    data,
    onSetRadioValue,
    onChangePage,
    selectedRowKeys,
    onSetSelectedRowKeys,
    activeKey,
    onTabChange,
    tabList,
    dispatch,
  } = props;
  const modalRef = useRef();
  const [saveReadLoading, setSaveReadLoading] = useState(false);
  // 处理已读
  const handleRead = (record, params) => {
    dispatch({
      type: 'smallBag/recordTheWaybill',
      payload: record ? [record.id] : selectedRowKeys,
      callback: response => {
        if (response.success) {
          if (params) {
            modalRef.current?.openModal(params);
          } else {
            message.success('已读成功');
            onChangePage(1, 10);
            onSetSelectedRowKeys([]);
          }
        }
      },
    });
  };
  // 选择框
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys, selectedRows) => {
      onSetSelectedRowKeys(selectedRowKeys);
    },
    getCheckboxProps: record => ({
      disabled: record.read === true,
      // Column configuration not to be checked
      name: record.read,
    }),
  };

  // 节点时间升降序
  const sortByNodeTime = value => ({
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
      <div style={{ padding: '15px 10px 10px 10px', height: 120, width: 140, lineHeight: '35px' }}>
        <Radio.Group onChange={radioChange} value={radioValue}>
          <Radio className={styles.radioStyle} value={0}>
            全局升序
          </Radio>
          <Radio className={styles.radioStyle} value={1}>
            全局降序
          </Radio>
        </Radio.Group>
        <Button
          type="primary"
          size="small"
          style={{ width: '100%', marginRight: 8 }}
          onClick={() => handleSortQuery(clearFilters, confirm)}
        >
          确定
        </Button>
      </div>
    ),
  });

  const handleSortQuery = (clearFilters, confirm) => {
    confirm();
    clearFilters();
    onChangePage(1, pageSize);
    // this.handleQuery();
  };

  const radioChange = e => {
    onSetRadioValue(e.target.value);
  };

  const paginationProps = {
    showQuickJumper: true,
    total: total,
    current: currentPage,
    pageSize: pageSize,
    showSizeChanger: true,
    pageSizeOptions: ['10', '20', '50', '100', '500'],
    onChange: onChangePage,
    showTotal: () => {
      return `共- ${total} -条记录`;
    },
  };

  const columns = [
    {
      title: '制单账号',
      dataIndex: 'accountCode',
      key: 'accountCode',
    },
    {
      title: '运单号',
      dataIndex: 'soleNumber',
      key: 'soleNumber',
      render: (text, record) => {
        let param = {
          shipper: record.accountCode,
          product: record.productName,
          orderNumber: record.orderCode,
          sendTime: record.timeOfExpress,
          waybillNumber: record.soleNumber,
          transferNumber: record.exchangeNumber,
          receiver: record.receiverName,
          destination: record.countryName,
        };

        return <a onClick={() => handleRead(record, param)}>{text}</a>;
      },
    },
    {
      title: '转单号',
      dataIndex: 'exchangeNumber',
      key: 'exchangeNumber',
    },
    {
      title: '订单号',
      dataIndex: 'orderCode',
      key: 'orderCode',
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      key: 'productName',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
    },
    {
      title: '投递失败原因',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: '目的国',
      dataIndex: 'countryName',
      key: 'countryName',
    },
    {
      title: '收件人姓名',
      dataIndex: 'receiverName',
      key: 'receiverName',
    },
    {
      title: '发货日期',
      dataIndex: 'timeOfExpress',
      key: 'timeOfExpress',
    },
    {
      title: '节点时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      sorter: (a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime(),
      sortDirections: ['descend', 'ascend'],
      ...sortByNodeTime('timestamp'),
    },
    {
      title: '尾程电话',
      dataIndex: 'lastPhone',
      key: 'lastPhone',
    },
    {
      title: '尾程网站',
      dataIndex: 'lastWebsite',
      key: 'lastWebsite',
    },
    {
      title: '尾程名称',
      dataIndex: 'lastName',
      key: 'lastName',
    },
    {
      title: '是否可重派',
      dataIndex: 'isRedelivery',
      key: 'isRedelivery',
    },
    {
      title: '操作',
      key: 'action',
      dataIndex: 'action',
      render: (text, record) => {
        return !record.read ? (
          <Button type="link" style={{ color: 'red' }} onClick={() => handleRead(record)}>
            未读
          </Button>
        ) : (
          <span style={{ color: '#52c41a' }}>已读</span>
        );
      },
    },
  ];

  return (
    <Card
      tabList={tabList}
      defaultActiveTabKey={activeKey}
      onTabChange={onTabChange}
      style={{ marginTop: '20px' }}
      bodyStyle={{ padding: '5px' }}
      bordered={false}
      tabBarExtraContent={
        <Button
          loading={saveReadLoading}
          disabled={selectedRowKeys.length <= 0}
          type="primary"
          onClick={() => handleRead()}
        >
          已读
        </Button>
      }
      tabProps={{
        size: 'small',
        style: {
          marginTop: '10px',
        },
      }}
    >
      <ProTable
        rowSelection={{ ...rowSelection }}
        columns={columns}
        loading={loading}
        dataSource={data}
        pagination={paginationProps}
        rowKey={(record, index) => record.id}
        scroll={{ x: 1500, y: 500 }}
        toolBarRender={false}
      />
      <WaybillCollectedDetails modalRef={modalRef} {...props} />
    </Card>
  );
};

export default WaybillCollectedTable;
