import React, { useState, useRef, useEffect } from 'react';
import { Ta<PERSON>, <PERSON>, Button, Badge } from 'antd';
import PageContainerComponent from '@/components/PageContainer';
import PageHeaderBreadcrumb from '@/components/PageHeaderBreadcrumb';
import ExpressProductChangeOrderRecord from './components/ExpressProductChangeOrderRecord';
import InterceptionApplications from './components/InterceptionApplications';
import WaybillCollected from './components/WaybillCollected';
import ProblemPieces from './components/ProblemPieces';
import { useMount } from 'ahooks';
import { formatMessage } from 'umi-plugin-react/locale';
import { connect } from 'dva';

const Index = props => {
  const { location, problemMap, dispatch, getProblemPiecesCountLoading } = props;
  const [activeKey, setActiveKey] = useState('1');
  const [problemType, setProblemType] = useState([0]);
  const problemPiecesRef = useRef();
  const [problemPiecesCount, setProblemPiecesCount] = useState(0);
  useMount(() => {
    handlePageParams();
    fetchProblemPiecesCount();
  });

  useEffect(() => {
    if (problemMap) {
      setProblemPiecesCount(handleAddProblemNumber(problemMap));
    }
  }, [problemMap]);

  const fetchProblemPiecesCount = () => {
    dispatch({
      type: 'homePage/popupMessage',
    });
  };

  const handleAddProblemNumber = data => {
    if (
      data.hasOwnProperty('problemNumber') ||
      data.hasOwnProperty('warehouseNumber') ||
      data.hasOwnProperty('overseasNumber') ||
      data.hasOwnProperty('returnNumber') ||
      data.hasOwnProperty('productNameNumber')
    ) {
      const dataObject = {
        problemNumber: data?.problemNumber,
        warehouseNumber: data?.warehouseNumber,
        overseasNumber: data?.overseasNumber,
        returnNumber: data?.returnNumber,
        productNameNumber: data?.productNameNumber,
      };
      // {{CHENGQI:
      // Action: Modified
      // Timestamp: [2024-07-30 10:00:00 +08:00]
      // Reason: 统一处理字符串和数字类型，确保参与计算的值为整数，避免因类型不一致导致的计算错误。
      // Principle_Applied: KISS (修改直接明了) / Robustness (增强了对不同数据类型的处理能力，使其更加健壮)
      // Optimization: N/A
      // Architectural_Note (AR): 无
      // Documentation_Note (DW): 无
      // }}
      // {{START MODIFICATIONS}}
      return Object.values(dataObject).reduce((acc, curr) => acc + (parseInt(curr, 10) || 0), 0);
      // {{END MODIFICATIONS}}
    }
    return 0;
  };

  const handlePageParams = () => {
    // lineType  formatMessage({id: '是小包专线跳转过来的变量'})
    // const { activeKey } = query;
    if (location?.state) {
      // formatMessage({id: '跳转到运单截留记录页面'})
      setActiveKey('2');
    }
    if (location?.query?.type == 0) {
      setActiveKey('1');
    }
    if (location?.query?.lineType == 1) {
      // formatMessage({id: '问题件'})
      setActiveKey('1');
    } else if (location?.query?.lineType == 3) {
      // formatMessage({id: '待领取运单'})
      setActiveKey('3');
    } else if (location?.query?.lineType == 2) {
      // formatMessage({id: '待领取运单'})
      setActiveKey('2');
    }

    // else if (location?.query?.lineType == 4) {
    //   // formatMessage({id: '转单号查询'})
    //   setActiveKey('4');
    // }
  };

  const tabItems = [
    {
      // tab: <Badge count={10}>{formatMessage({ id: '问题件' })}</Badge>,
      label: (
        <Badge offset={[18, -3]} count={problemPiecesCount}>
          {formatMessage({ id: '问题件' })}
        </Badge>
      ),
      key: '1',
      children: (
        <ProblemPieces
          outActiveKey={activeKey}
          onProblemTypeChange={setProblemType}
          location={location}
          problemMap={problemMap}
        />
      ),
    },
    {
      label: formatMessage({ id: '截留申请' }),
      key: '2',
      children: <InterceptionApplications location={location} />,
    },
    { label: formatMessage({ id: '待领取运单' }), key: '3', children: <WaybillCollected /> },
    // {
    //   label: formatMessage({ id: '转单号查询' }),
    //   key: '4',
    //   children: <ExpressProductChangeOrderRecord />,
    // },
  ];

  return (
    <PageContainerComponent
      header={{
        title: null,
        breadcrumb: {},
        breadcrumbRender: props => <PageHeaderBreadcrumb {...props} />,
      }}
      loading={getProblemPiecesCountLoading}
    >
      <Tabs
        tabBarStyle={{ margin: '0' }}
        activeKey={activeKey}
        onChange={key => setActiveKey(key)}
        type="card"
        items={tabItems}
        tabBarGutter={20}
        // tabBarExtraContent={
        //   activeKey === '1' && problemType?.[0] === 0 ? (
        //     <Button type="primary" onClick={() => handleUpload()}>
        //       formatMessage({id: '导入处理'})
        //     </Button>
        //   ) : null
        // }
      />
    </PageContainerComponent>
  );
};

export default connect(({ homePage, loading }) => ({
  problemMap: homePage.problemMap,
  getProblemPiecesCountLoading: loading.effects['homePage/popupMessage'],
}))(Index);
