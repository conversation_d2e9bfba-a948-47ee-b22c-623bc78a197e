import React from 'react';
import { ProCard } from '@ant-design/pro-components';
import { Divider } from 'antd';
import style from './SmallPacketLineFreightTrack.less';
import freightTrial from '@/assets/freightTrial.svg';
import queryTrack from '@/assets/queryTrack.png';
import LinkHref from '@/components/LinkHref';
import { formatMessage } from 'umi-plugin-react/locale';

const SmallPacketLineFreightTrack = () => {
  return (
    <div className="SmallPacketLineFreightTrack" style={{ display: 'flex', background: '#fff' }}>
      <ProCard
        title={
          <span style={{ fontSize: '14px', fontWeight: 'bold' }}>
            {formatMessage({ id: '运费试算' })}
          </span>
        }
      >
        <LinkHref href="/financialManagement/priceInquiry/freightTrial" route>
          <img style={{ width: 150 }} src={freightTrial} alt="" srcset="" />
        </LinkHref>
      </ProCard>
      <Divider type="vertical" style={{ height: 120, background: '#e2e2e2', marginTop: 32 }} />
      <ProCard
        className="queryTrack"
        title={
          <span style={{ fontSize: '14px', fontWeight: 'bold' }}>
            {formatMessage({ id: '轨迹查询' })}
          </span>
        }
      >
        <LinkHref href="/serviceManagement/tracking" route>
          <img style={{ width: 150 }} src={queryTrack} alt="" srcset="" />
        </LinkHref>
      </ProCard>
    </div>
  );
};

export default SmallPacketLineFreightTrack;
