import React, { useState, useRef } from 'react';
import { ProCard } from '@ant-design/pro-components';
import style from './SmallPacketLineCard.less';
import MerchantStatusModal from '@/pages/HomePage/components/MerchantStatusModal';
import { useMount, useUpdateEffect } from 'ahooks';
import { Space, Button, Modal } from 'antd';
import SmallPacketLineDataCompleteness from './SmallPacketLineDataCompleteness';
import { formatMessage } from 'umi-plugin-react/locale';

const SmallPacketLineAccountInfo = props => {
  const { packetBaseInfo, dispatch, location } = props;
  const modalRef = useRef();
  const [merchantStatusOpen, setMerchantStatusOpen] = useState(false); // 冻结弹窗
  const [merchantInfo, setMerchantInfo] = useState();

  useMount(() => {
    initialFunc();
  });

  useUpdateEffect(() => {
    if (merchantInfo && location?.query?.openFreeze) {
      handleFrozen();
    }
  }, [merchantInfo]);

  const initialFunc = () => {
    dispatch({
      type: 'smallBag/getCustomerData',
      callback: response => {
        if (response.success) {
          setMerchantInfo({
            ...merchantInfo,
            ...response.data,
          });
        }
      },
    });
  };

  const merchantStatus = ejfStatus => {
    let str = '不活动'; // 0
    if (ejfStatus === 1) {
      str = '活动';
    } else if (ejfStatus === 2) {
      str = '冻结';
    } else if (ejfStatus === 3) {
      str = '其他';
    }
    return str;
  };

  const handleFrozen = () => {
    setMerchantStatusOpen(true);
    // 创建一个 URLSearchParams 对象来处理查询参数
    let params = new URLSearchParams(props.location.search);
    // 移除 openFreeze 参数
    params.delete('openFreeze');
    // 更新 URL，但不刷新页面
    props.history.replace({
      pathname: props.location.pathname,
      search: params.toString() ? '?' + params.toString() : '',
    });
  };

  return (
    <ProCard style={{ flex: '1' }}>
      <p style={{ color: '#bebebe' }} className="flex items-center justify-between">
        <span>
          <span>{formatMessage({ id: '业务账号' })}：</span>
          <span style={{ fontSize: 18, color: '#66b318' }}>{packetBaseInfo.merchantCode}</span>
        </span>
        <div
          style={{
            height: 20,
            lineHeight: '20px',
            padding: '0 15px',
            textAlign: 'center',
            borderRadius: '10px',
            color: '#90b35f',
            border: '1px solid #90b35f',
          }}
          className={style.extra}
          onClick={packetBaseInfo?.frozenStatus === 2 ? handleFrozen : null}
        >
          {merchantStatus(packetBaseInfo?.frozenStatus)}
        </div>
      </p>
      <p style={{ color: '#bebebe' }}>
        {formatMessage({ id: '支付周期' })}：
        <span style={{ color: '#8d8d8d' }}>{packetBaseInfo.payCycleName}</span>
      </p>
      <p style={{ color: '#bebebe' }} className="flex items-center justify-between">
        <span>
          <span>{formatMessage({ id: '资料完整度' })}：</span>
          <span style={{ color: '#8d8d8d' }}>
            <Space>
              {merchantInfo?.allRate}
              {merchantInfo?.allRate && merchantInfo?.allRate != '100%' && (
                <a
                  onClick={() => {
                    modalRef.current?.openModal(merchantInfo);
                  }}
                >
                  {formatMessage({ id: '立即完善' })}
                </a>
              )}
            </Space>
          </span>
        </span>
        {packetBaseInfo?.frozenStatus === 2 && (
          <span className="text-right text-red-600 cursor-pointer" onClick={handleFrozen}>
            {formatMessage({ id: '去解冻' })}
          </span>
        )}
      </p>
      <MerchantStatusModal
        {...props}
        visible={merchantStatusOpen}
        onCancel={() => {
          setMerchantStatusOpen(false);
        }}
        customerData={merchantInfo}
        onDataCompleteness={() => {
          modalRef.current?.openModal(merchantInfo);
        }}
      />
      <SmallPacketLineDataCompleteness {...props} modalRef={modalRef} />
    </ProCard>
  );
};

export default SmallPacketLineAccountInfo;
