import { connect } from 'dva';
import PageHeaderBreadcrumb from '@/components/PageHeaderBreadcrumb';
import React, { useRef, useState } from 'react';
import { PageContainer, ProForm, ProFormTextArea } from '@ant-design/pro-components';
import {
  But<PERSON>,
  Card,
  Col,
  Collapse,
  Divider,
  Empty,
  Row,
  Space,
  Tabs,
  Timeline,
  Typography,
} from 'antd';
import { ExclamationCircleOutlined, MinusOutlined, PlusOutlined } from '@ant-design/icons';
import { formatMessage } from 'umi-plugin-react/locale';
import { textWaybill } from '@/utils/utils';
import PU10 from '@/assets/PU10.png';
import LH20 from '@/assets/LH20.png';
import LM10 from '@/assets/LM10.png';
import LM40 from '@/assets/LM40.png';
import timeline_none from '@/assets/timeline_none.png';

const { TabPane } = Tabs;
const { Panel } = Collapse;
const Index = props => {
  const { dispatch, searchLoading } = props;
  const formRef = useRef();
  const [trackingData, setTrackingData] = useState([]);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [activeTab, setActiveTab] = useState('all');
  const [orgData, setOrgData] = useState({});
  const [counts, setCounts] = useState({
    allYwOrder: 0,
    unknownYwOrder: 0,
    orderDoneYwOrder: 0,
    transportingYwOrder: 0,
    outOfDeliveryYwOrder: 0,
    unclaimedYwOrder: 0,
    succeedYwOrder: 0,
    traceEndYwOrder: 0,
    failYwOrder: 0,
    exceptionYwOrder: 0,
    returnYwOrder: 0,
  });

  const handleCollapseChange = keys => {
    setExpandedKeys(keys);
  };

  const handleSubmit = async () => {
    let values = await formRef.current?.validateFields();
    console.log(values);
    let nums = textWaybill(values?.waybillNumbers);
    console.log(nums);
    dispatch({
      type: 'track/getTrackData',
      payload: { nums: nums },
      callback: result => {
        console.log(result);
        if (result.success) {
          setActiveTab('allYwOrder');
          setOrgData(result.data);
          console.log(result.data['allYwOrder']);
          setTrackingData(result.data['allYwOrder']);
          setCounts({
            allYwOrder: result.data.allYwOrder?.length || 0,
            unknownYwOrder: result.data.unknownYwOrder?.length || 0,
            orderDoneYwOrder: result.data.orderDoneYwOrder?.length || 0,
            transportingYwOrder: result.data.transportingYwOrder?.length || 0,
            outOfDeliveryYwOrder: result.data.outOfDeliveryYwOrder?.length || 0,
            unclaimedYwOrder: result.data.unclaimedYwOrder?.length || 0,
            succeedYwOrder: result.data.succeedYwOrder?.length || 0,
            traceEndYwOrder: result.data.traceEndYwOrder?.length || 0,
            failYwOrder: result.data.failYwOrder?.length || 0,
            exceptionYwOrder: result.data.exceptionYwOrder?.length || 0,
            returnYwOrder: result.data.returnYwOrder?.length || 0,
          });
        } else {
          message.error(result.message);
        }
      },
    });
  };
  const handleReset = () => {
    formRef.current?.resetFields();
  };

  // 获取当前显示的运单数据
  const getCurrentOrders = () => {
    return trackingData[activeTab] || [];
  };

  // 获取状态对应的徽标颜色
  const getStatusColor = key => {
    const colorMap = {
      succeedYwOrder: 'green', // 投递成功 - 绿色
      failYwOrder: 'red', // 投递失败 - 红色
      exceptionYwOrder: 'orange', // 包裹异常 - 橙色
      returnYwOrder: 'volcano', // 包裹退回 - 火山红
      unknownYwOrder: 'gray', // 查询不到 - 灰色
      orderDoneYwOrder: 'blue', // 制单完成 - 蓝色
      transportingYwOrder: 'cyan', // 运输途中 - 青色
      outOfDeliveryYwOrder: 'geekblue', // 正在派送 - 极客蓝
      unclaimedYwOrder: 'purple', // 到达待取 - 紫色
      traceEndYwOrder: 'gold', // 追踪结束 - 金色
      allYwOrder: 'green', // 全部 - 绿色
    };
    return colorMap[key] || 'blue';
  };

  const handleSwitchValue = value => {
    switch (value) {
      case 'PU10':
        return PU10;
      case 'LH20':
        return LH20;
      case 'LM10':
        return LM10;
      case 'LM40':
        return LM40;
      default:
        return timeline_none;
    }
  };

  const renderTimeLIne = checkpoints => {
    return (
      <div style={{ marginTop: 16 }}>
        {checkpoints.map((item, index) => (
          <Timeline.Item
            dot={
              <img
                src={handleSwitchValue(item?.tracking_status)}
                style={{ width: '20px' }}
                alt=""
              />
            }
          >
            <Space style={{ marginTop: '2px' }}>
              <span style={{ color: '#99a9bf' }} className="text-sm leading-5 ">
                {item?.time_stamp}
              </span>
              {item?.location != '' && item?.location != null && (
                <span className="text-sm leading-5 px-2" style={{ color: '#99a9bf' }}>
                  [{item?.location}]
                </span>
              )}
              <span
                className="text-sm leading-5"
                style={{ color: '#1f2033', wordBreak: 'break-word' }}
              >
                {item?.message}
              </span>
            </Space>
          </Timeline.Item>
        ))}
      </div>
    );
  };

  const onChangeTab = value => {
    setActiveTab(value);
    setTrackingData(orgData?.[value]);
  };

  return (
    <PageContainer
      header={{
        title: null,
        breadcrumb: {},
        breadcrumbRender: props => <PageHeaderBreadcrumb col {...props}></PageHeaderBreadcrumb>,
      }}
    >
      <Card>
        <ProForm
          formRef={formRef}
          layout="horizontal"
          labelAlign="right"
          labelCol={{ flex: '68px' }}
          submitter={{
            render: (_, dom) => null,
          }}
        >
          <Row>
            <Col span={6}>
              <ProFormTextArea
                name="waybillNumbers"
                label={formatMessage({ id: '运单号' })}
                placeholder={`请输入运单号，多单号请以逗号、空格或回车隔开`}
                fieldProps={{
                  rows: 4,
                  style: { display: 'inline-block', height: '100px', width: '260px' },
                }}
                rules={[{ required: true, message: `运单号不能为空` }]}
              />
            </Col>
            <Col span={6}>
              <Space className="pl-8" direction="vertical" align="center">
                <Button type="primary" loading={searchLoading} onClick={handleSubmit}>
                  {formatMessage({ id: '查询' })}
                </Button>
                <Button style={{ marginTop: '20px' }} onClick={handleReset}>
                  {formatMessage({ id: '重置' })}
                </Button>
              </Space>
            </Col>
          </Row>
        </ProForm>
      </Card>
      <Card style={{ marginTop: 24 }}>
        <Tabs activeKey={activeTab} onChange={onChangeTab}>
          <TabPane tab={`全部（${counts.allYwOrder}）`} key="allYwOrder" />
          <TabPane tab={`查询不到（${counts.unknownYwOrder}）`} key="unknownYwOrder" />
          <TabPane tab={`制单完成（${counts.orderDoneYwOrder}）`} key="orderDoneYwOrder" />
          <TabPane tab={`运输途中（${counts.transportingYwOrder}）`} key="transportingYwOrder" />
          <TabPane tab={`正在派送（${counts.outOfDeliveryYwOrder}）`} key="outOfDeliveryYwOrder" />
          <TabPane tab={`到达待取（${counts.unclaimedYwOrder}）`} key="unclaimedYwOrder" />
          <TabPane tab={`投递成功（${counts.succeedYwOrder}）`} key="succeedYwOrder" />
          <TabPane tab={`追踪结束（${counts.traceEndYwOrder}）`} key="traceEndYwOrder" />
          <TabPane tab={`投递失败（${counts.failYwOrder}）`} key="failYwOrder" />
          <TabPane tab={`包裹异常（${counts.exceptionYwOrder}）`} key="exceptionYwOrder" />
          <TabPane tab={`包裹退回（${counts.returnYwOrder}）`} key="returnYwOrder" />
        </Tabs>
        {trackingData?.length > 0 ? (
          <div style={{ marginTop: 16 }}>
            <Collapse
              activeKey={expandedKeys}
              onChange={handleCollapseChange}
              expandIconPosition="right"
              expandIcon={({ isActive }) => (isActive ? <MinusOutlined /> : <PlusOutlined />)}
            >
              {trackingData?.length > 0 &&
                trackingData.map(item => (
                  <Panel
                    header={
                      item.tracking_status === 'NOTFOUND' ? (
                        <div>
                          <Space
                            split={<Divider type="vertical" />}
                            size="middle"
                            style={{
                              padding: '8px 12px',
                              backgroundColor: '#f5f5f5',
                              borderRadius: 4,
                            }}
                          >
                            <span style={{ fontWeight: 500 }}>
                              运单号:
                              <Typography.Text copyable>{item.tracking_number}</Typography.Text>
                            </span>
                            <span style={{ color: '#ff4d4f' }}>
                              <ExclamationCircleOutlined /> 没有查到物流信息
                            </span>
                          </Space>
                        </div>
                      ) : (
                        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                          <div style={{ fontWeight: 500, flex: '0 0 180px' }}>
                            运单号:{' '}
                            <Typography.Text copyable>{item.tracking_number}</Typography.Text>
                          </div>
                          <Divider type="vertical" style={{ height: '1em' }} />
                          <div style={{ flex: '0 0 180px' }}>订单号: {item.order_number}</div>
                          <Divider type="vertical" style={{ height: '1em' }} />
                          <div style={{ flex: '0 0 120px' }}>
                            目的国: {item.destinationCountryCN}
                          </div>
                          <Divider type="vertical" style={{ height: '1em' }} />
                          <div style={{ color: '#52c41a', flex: '0 0 500px' }}>
                            最新轨迹: {item.timeMessage}
                          </div>
                        </div>
                      )
                    }
                    key={item.tracking_number}
                  >
                    {item.checkpoints && renderTimeLIne(item.checkpoints)}
                  </Panel>
                ))}
            </Collapse>
          </div>
        ) : (
          <div style={{ padding: '40px 0' }}>
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={<span>暂无运单数据，请先输入运单号并查询</span>}
            />
          </div>
        )}
      </Card>
    </PageContainer>
  );
};

export default connect(({ loading }) => ({
  searchLoading: loading.effects['track/getTrackData'],
}))(Index);
