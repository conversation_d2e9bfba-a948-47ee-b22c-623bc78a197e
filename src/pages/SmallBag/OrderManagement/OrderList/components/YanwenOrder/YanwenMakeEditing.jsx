import React, { Component } from 'react';
import _ from 'lodash';
import { connect } from 'dva';
import moment from 'moment';
import { PlusOutlined } from '@ant-design/icons';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {
  Avatar,
  Button,
  Card,
  Col,
  DatePicker,
  Input,
  message,
  Modal,
  Radio,
  Row,
  Select,
  Space,
  Tabs,
  Typography,
  Upload,
} from 'antd';
import '../../../CreatOrder/createOrder.less';
import { router } from 'umi';
import StandardFormRow from '@/components/StandardFormRow';
import SingleTagSelect from '@/components/SingleTagSelect';
import loadPinYinInit from '@/utils/ChineseHelper';
import { downloadFile } from '@/utils/download';
import IconFont from '@/components/IconFont';
import PageContainerComponent from '@/components/PageContainer';
import PageHeaderBreadcrumb from '@/components/PageHeaderBreadcrumb';
import { Decimal } from 'decimal.js';
import AuthStatusNode from '@/components/AuthStatusNode';
import { formatMessage } from 'umi-plugin-react/locale';
import { temuShowList } from '@/utils/commonConstant';

const { Text } = Typography;
const { TextArea } = Input;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

/**
 * shenbaojiazhi
 * @param value
 * @constructor
 */
const CustomerEachPrice = ({ value }) => {
  return <span>{value ?? 0}</span>;
};

@connect(({ Order, user, loading }) => ({
  Order,
  authorizedLoading: loading.effects['user/qryMerchantState'],
  chargeLoading: loading.effects['Order/chargePriceSimple'],
}))
class CreateOrder extends Component {
  constructor(props) {
    super(props);
    // 绑定防抖函数到类实例
    this.goodsSearch = _.debounce(this.goodsSearch.bind(this), 300);
    this.state = {
      temuShow: false,
      goods: [],
      moneyNum: 0, //试算价格
      tabNumber: '1',
      shipperInitOpt: [],
      deliveryAccount: [], // 制单账号
      value: 0,
      transactionNumber: undefined,
      salesPlatform: '', //销售平台
      handoverCode: undefined, //销售平台
      handoverCodes: undefined, //销售平台列表
      currencys: [],
      currencyName: '', // 单个币种名
      currencyCode: undefined,
      activeCurrency: '', // 所选币种
      countriesData: [], // 所有国家数据
      warehouseList: [], // 交货仓数据
      customsInfo: [
        {
          goodsNameCh: undefined, // 中文名
          goodsNameEn: undefined, // 英文名
          quantity: undefined, // 单品数量
          weight: undefined, // 单品重量
          price: undefined, // 申报价格
          productMaterial: undefined, // 商品材质
          productHsCode: undefined, // 商品海关编码
          productLink: undefined, // 商品链接
          productSku: undefined, // sku
          productImei: undefined, // imei
        },
      ], // 报关信息
      channels: [], // 产品数据
      totalNum: 0, // 申报的总数量
      totalWeight: 0, // 申报的总质量
      totalPrice: 0, // 申报的总价值
      heights: undefined, // 高
      widths: undefined, // 宽
      lengths: undefined, // 长
      pickingOrders: undefined, // 拣货单
      formData: {
        userId: undefined, // 选中的制单账号
        channelId: undefined, // 产品编码
        orderNumber: undefined, // 订单号
        companyCode: undefined, // 交货仓
        dateOfReceipt: undefined, // 收款到账日期
      },
      receiverInfo: {
        address: undefined, //收件人地址
        city: undefined, //收件人城市
        company: undefined, // 收件人公司
        countryId: undefined, // 目的国ID
        email: undefined, //邮箱
        houseNumber: undefined, //门牌号
        name: undefined, //姓名
        phone: undefined, //电话
        state: undefined, //州(省)
        receiverTaxNumber: undefined, //税号
        zipCode: undefined, //邮编
        pinflNumber: undefined, //PINFL号码
      },
      pointId: undefined, //自提点ID
      isHaveCountry: false, // 是否有这个国家
      isProduct: false, //是否有这个产品
      senderTaxNumber: undefined, // 发件人税号
      importCustomsInfo: {},
      senderName: undefined,
      senderCompany: undefined,
      senderPhone: undefined,
      senderEmail: undefined,
      senderZipCode: undefined,
      senderCountry: undefined,
      senderState: undefined,
      senderCity: undefined,
      senderAddress: undefined,
      senderHouseNumber: undefined,
      fileList: [],
      uploading: false,
      btnLoading: false,
      EJFtemplates: [], // EJF模板数据
      currentAuthorityValue: null, // 商户状态
      isHaveDeliveryAccount: false, // 制单账号是否只有一个
      fileName: '', // 批量创建订单上传的文件名
      countryDisabled: true,
      IOSS: '', // 税号
      isModalVisible: false, // 批量上传失败弹框显示状态
      uploadErrorCon: '', // 上传失败提示
      uploadErrorMsg: '', // 上传失败成功与失败条数
      initData: {}, // 一键换单初始化数据
      userIds: '', // 一键换单运单号
      isEdit: false, // 是否可以编辑
      DAccount: {},
      waybillUserIds: '', // 已制单编辑制单账号
      waybillState: '',
      waybillExpressCode: '',
      isLock: false,
      waybillNumber: '', // 运单号
      keyCopyData: {}, // 一键复制数据
    };
  }

  componentDidMount() {
    const { dispatch } = this.props;
    const { userId, expressCode, id, para, state } = this.props.history.location.query;
    if (para != undefined) {
      this.setState({
        initData: JSON.parse(para),
        userIds: userId,
      });
    }
    const result = new URLSearchParams(this.props.location.search);
    const type = result.get('type');
    const {
      props: {
        location: {
          query: { number },
        },
      },
    } = this;

    // 获取交货仓
    this.getWarehouseByType();
    if (number) {
      this.state.tabNumber = number;
      this.setState({
        tabNumber: number,
      });
    }
    if (userId != undefined && expressCode != undefined && state == '已制单') {
      this.getOrderDetails(userId, expressCode);
      this.setState({
        countryDisabled: true,
        waybillUserIds: userId,
        waybillState: state,
        waybillExpressCode: expressCode,
        isLock: true,
      });
    }
    // 获取制单账号
    dispatch({
      type: 'Order/getShippingAccount',
      callback: response => {
        if (response.success) {
          this.setState({
            deliveryAccount: response.data,
          });
          let arr = response.data.filter(item => {
            return item.accountCode == userId;
          });
          this.setState({
            DAccount: arr[0],
          });
          if (response.data.length == 1) {
            this.setState({
              isHaveDeliveryAccount: true,
              formData: {
                userId: response.data[0].accountCode, // 选中的制单账号
                channelId: this.state.formData.channelId
                  ? this.state.formData.channelId
                  : undefined, // 产品编码
                orderNumber: this.state.formData.orderNumber
                  ? this.state.formData.orderNumber
                  : undefined, // 订单号
                companyCode: this.state.formData.companyCode
                  ? this.state.formData.companyCode
                  : undefined, // 交货仓
                dateOfReceipt: this.state.formData.dateOfReceipt
                  ? this.state.formData.dateOfReceipt
                  : undefined, // 收款到账日期
              },
            });
            // this.getProductName(response.data[0].accountCode);
          } else {
            if (para != undefined) {
              let arr = response.data.filter(item => {
                return item.accountCode == userId;
              });
              this.setState({
                deliveryAccount: arr,
                formData: {
                  userId: userId, // 选中的制单账号
                  channelId: this.state.formData.channelId
                    ? this.state.formData.channelId
                    : undefined, // 产品编码
                  orderNumber: this.state.formData.orderNumber
                    ? this.state.formData.orderNumber
                    : undefined, // 订单号
                  companyCode: this.state.formData.companyCode
                    ? this.state.formData.companyCode
                    : undefined, // 交货仓
                  dateOfReceipt: this.state.formData.dateOfReceipt
                    ? this.state.formData.dateOfReceipt
                    : undefined, // 收款到账日期
                },
              });
              // this.getProductName(userId);
            } else {
              this.setState({
                isHaveDeliveryAccount: false,
              });
            }
          }
          this.getEJFGenericTemplates();
        }
      },
    });

    // 调用获取所有币种方法
    this.getCurrency();
    this.getHandoverCodes();
  }

  // 根据制单账号与运单号获取订单详情
  getOrderDetails = (id, code) => {
    const { dispatch } = this.props;
    let params = {
      userId: id,
      waybillNumber: code,
    };

    dispatch({
      type: 'Order/getOrderDetails',
      payload: params,
      callback: result => {
        if (result.success && result.data) {
          let forms = {
            userId: result.data.userId, // 选中的制单账号
            channelId: result.data.channelId, // 产品编码
            orderNumber: result.data.orderNumber, // 订单号
            companyCode: result.data.companyCode, // 交货仓
            dateOfReceipt: result.data.dateOfReceipt, // 收款到账日期
          };
          this.setState(
            {
              formData: forms,
              transactionNumber: result.data.transactionNumber, // 平台交易号
              salesPlatform: result.data.salesPlatform, //销售平台
              handoverCode: result.data.handoverCode, //海外交货地
              senderTaxNumber: result.data.senderInfo.taxNumber,
              importCustomsInfo: result.data.importCustomsInfo,
              pointId: result.data.poPStation?.pointId,
              senderName: result.data.senderInfo.name,
              senderCompany: result.data.senderInfo.company,
              senderPhone: result.data.senderInfo.phone,
              senderEmail: result.data.senderInfo.email,
              senderZipCode: result.data.senderInfo.zipCode,
              senderCountry: result.data.senderInfo.country,
              senderState: result.data.senderInfo.state,
              senderCity: result.data.senderInfo.city,
              senderAddress: result.data.senderInfo.address,
              senderHouseNumber: result.data.senderInfo.houseNumber,
              receiverInfo: result.data.receiverInfo,
              customsInfo: result.data.parcelInfo.productList,
              totalNum: result.data.parcelInfo.totalQuantity, // 申报的总数量
              totalWeight: result.data.parcelInfo.totalWeight, // 申报的总质量
              totalPrice: result.data.parcelInfo.totalPrice, // 申报的总价值
              heights: result.data.parcelInfo.height ? result.data.parcelInfo.height : undefined, // 高
              widths: result.data.parcelInfo.width ? result.data.parcelInfo.width : undefined, // 宽
              lengths: result.data.parcelInfo.length ? result.data.parcelInfo.length : undefined, // 长
              pickingOrders: result.data.remark, // 拣货单
              currencyName: result.data.parcelInfo.currencyInfo.name,
              currencyCode: result.data.parcelInfo.currencyInfo.code,
              IOSS: result.data.parcelInfo.ioss,
              value: result.data.parcelInfo.hasBattery,
              waybillNumber: result.data.waybillNumber,
            },
            () => {
              let goodsArr = result.data.parcelInfo.productList.map(x =>
                this.dispatchGetMatchNameList(x.goodsNameCh)
              );
              Promise.allSettled(goodsArr).then(res => {
                const data = res.map((item, index) => {
                  if (item.status === 'fulfilled') {
                    return item.value;
                  } else {
                    return [];
                  }
                });
                this.setState({
                  goods: data,
                });
              });
            }
          );
          // if (result.data.userId) {
          let params = {
            countryId: result.data.receiverInfo.countryId,
            userId:
              typeof result.data.userId == 'string' ? result.data.userId : result.data.userId[0],
          };
          this.getProductName(params);
          // }
          if (result.data.channelId) {
            this.getCountriesName();
          }
          const { state } = this.props.history.location.query;
          if (state == '已制单') {
            this.getVoucherIsEdit(result.data.channelId);
          }

          // 添加一键复制
          let param = {
            receiverInfo: result.data.receiverInfo,
            importCustomsInfo: result.data.importCustomsInfo,
            parcelInfo: {
              currency: result.data.parcelInfo.currency,
              totalPrice: result.data.parcelInfo.totalPrice,
              totalQuantity: result.data.parcelInfo.totalQuantity,
              totalWeight: result.data.parcelInfo.totalWeight,
              height: result.data.parcelInfo.height,
              width: result.data.parcelInfo.width,
              length: result.data.parcelInfo.length,
              pickingOrders: result.data.remark,
              productList: result.data.parcelInfo.productList,
              isBattery: result.data.parcelInfo.hasBattery,
              IOSS: result.data.parcelInfo.ioss,
            },
            poPStation: {
              pointId: result.data.poPStation?.pointId,
            },
            senderInfo: {
              senderTaxNumber: result.data.senderInfo.taxNumber,
              senderName: result.data.senderInfo.name,
              senderCompany: result.data.senderInfo.company,
              senderPhone: result.data.senderInfo.phone,
              senderEmail: result.data.senderInfo.email,
              senderZipCode: result.data.senderInfo.zipCode,
              senderCountry: result.data.senderInfo.country,
              senderState: result.data.senderInfo.state,
              senderCity: result.data.senderInfo.city,
              senderAddress: result.data.senderInfo.address,
              senderHouseNumber: result.data.senderInfo.houseNumber,
            },
            channelId: result.data.channelId,
            userId: [result.data.userId],
            orderNumber: result.data.orderNumber,
            companyCode: result.data.companyCode,
            dateOfReceipt: result.data.dateOfReceipt,
            transactionNumber: result.data.transactionNumber,
            salesPlatform: result.data.salesPlatform,
            waybillNumber: result.data.waybillNumber,
            handoverCode: result.data.handoverCode,
          };
          this.setState({
            keyCopyData: param,
            temuShow: temuShowList.includes(result.data.channelId),
          });
        }
      },
    });
  };

  // 获取当前页面是否可以编辑
  getVoucherIsEdit = ID => {
    const { dispatch } = this.props;
    let params = {
      productNumber: ID,
    };
    dispatch({
      type: 'Order/getVoucherIsEditData',
      payload: params,
      callback: res => {
        if (!res.success) {
          this.setState({
            isEdit: true,
            btnLoading: true,
          });
          message.error('该产品的订单不允许修改');
        }
      },
    });
  };

  // 获取所有币种
  getCurrency = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'Order/getCurrency',
      callback: result => {
        if (result.success) {
          if (this.state.currencyCode) {
            result.data.forEach(item => {
              if (item.currencyCode == this.state.currencyCode) {
                this.setState({
                  currencyCode: item.currencyCode,
                });
              }
            });
          }
          this.setState({
            currencys: result.data,
          });
        }
      },
    });
  };

  // 获取海外交货地
  getHandoverCodes = () =>
    new Promise((resolve, reject) => {
      const { dispatch } = this.props;
      dispatch({
        type: 'Order/getHandoverCodes',
        callback: result => {
          if (result.success) {
            this.setState(
              {
                handoverCodes: result.data
                  .filter(x => x.isEnable)
                  .map(item => {
                    item.label = `${item.countryName}/${item.overseasDeliveryLocation}`;
                    item.code = item.overseasDeliveryLocation;
                    return item;
                  }),
              },
              () => {
                resolve(result.success);
              }
            );
          } else {
            reject(result.success);
          }
        },
      });
    });

  //获取交货仓
  getWarehouseByType = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'Order/getWarehouseByType',
      payload: 0,
      callback: res => {
        if (res.success) {
          this.setState({
            warehouseList: res.data,
          });
        }
      },
    });
  };

  // 获取产品名称
  getProductName = data => {
    const { dispatch } = this.props;
    let params = data;
    dispatch({
      type: 'Order/getProductName',
      payload: params,
      callback: result => {
        if (result.success) {
          let arr = result.data.filter(item => {
            return item.status == '1';
          });
          this.setState({
            channels: arr,
            isProduct: true,
          });
        }
      },
    });
  };

  // 产品名称获取焦点
  selectOnfocus = () => {
    if (this.state.formData.userId == undefined) {
      message.warning('请确认制单账号后再制单');
      return;
    }
  };

  // 产品信息变化
  selectChange = id => {
    let ids = Object.assign({}, this.state.formData, { channelId: id });
    this.setState({
      formData: ids,
    });
    this.getCountriesName();
    this.props.form.setFieldsValue({
      countryId: undefined,
    });
    this.setState({
      temuShow: temuShowList.includes(id),
      countryId: '',
      countryDisabled: false,
    });
  };

  // 根据产品编号获取国家
  getCountriesName = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'Order/getCountriesName',
      // payload: productId,
      callback: result => {
        if (result.success) {
          result.data.forEach(item => {
            item.countryNamePinYin = loadPinYinInit.ConvertPinyin(item.nameCh);
          });
          this.setState(
            {
              countriesData: result.data,
            },
            () => {
              this.setState({
                isHaveCountry: true,
              });
            }
          );
        }
      },
    });
  };

  // 国家选项变化
  contryChange = id => {
    let countryId = Object.assign({}, this.state.receiverInfo, { countryId: id });
    this.setState({
      countryId: countryId,
    });
  };

  // 币种类型变化
  currencyChange = id => {
    this.setState({
      currencyCode: id,
    });
  };

  // 海外交货地变化
  handoverChange = id => {
    this.setState({
      handoverCode: id,
    });
  };

  // 到款日期选择
  timeChange = time => {
    let times = moment(time).format('yyyy-MM-DD');
    let date = Object.assign({}, this.state.formData, { dateOfReceipt: times });
    this.setState({
      formData: date,
    });
  };

  // 新增报关信息
  addCustomsInfo = () => {
    const { customsInfo, goods } = this.state;
    if (customsInfo.length == 20) {
      message.error('报关信息仅支持上传20条!');
      return;
    }
    const val = {
      goodsNameCh: undefined,
      goodsNameEn: undefined, // 英文名
      quantity: undefined, // 单品数量
      weight: undefined, // 单品重量
      price: undefined, // 申报价格
      productMaterial: undefined, // 商品材质
      productHsCode: undefined, // 商品海关编码
      productLink: undefined, // 商品链接
      productSku: undefined, // sku
      productImei: undefined, // imei
      currencys: undefined,
    };
    this.setState({
      customsInfo: [...customsInfo, val],
      goods: [...goods, []],
    });
  };

  // 删除报关信息
  delCustomsInfo = i => {
    let TheIndex = i;
    const { form } = this.props;
    const customsInfos = this.state.customsInfo;
    const goods = this.state.goods;
    goods[i] = [];
    let c = form.getFieldsValue();
    customsInfos.forEach((item, index) => {
      item.goodsNameCh = c[`goodsNameCh${index}`];
      item.goodsNameEn = c[`goodsNameEn${index}`];
      item.price = c[`price${index}`];
      item.productHsCode = c[`productHsCode${index}`];
      item.productLink = c[`productLink${index}`];
      item.productSku = c[`productSku${index}`];
      item.productImei = c[`productImei${index}`];
      item.productMaterial = c[`productMaterial${index}`];
      item.quantity = c[`quantity${index}`];
      item.weight = c[`weight${index}`];
      item.productPrice = c[`productPrice${index}`];
    });
    let a = customsInfos.filter((val, index) => {
      return index != i;
    });
    a.forEach((val, i) => {
      form.setFieldsValue({
        [`goodsNameCh${i}`]: val.goodsNameCh,
        [`goodsNameEn${i}`]: val.goodsNameEn,
        [`price${i}`]: val.price,
        [`productHsCode${i}`]: val.productHsCode,
        [`productLink${i}`]: val.productLink,
        [`productSku${i}`]: val.productSku,
        [`productImei${i}`]: val.productImei,
        [`productMaterial${i}`]: val.productMaterial,
        [`quantity${i}`]: val.quantity,
        [`weight${i}`]: val.weight,
        [`productPrice${i}`]: val.productPrice,
      });
    });
    this.setState(
      {
        customsInfo: [...a],
        goods: goods,
      },
      () => {
        let Num = 0;
        let price = 0;
        let weight = 0;
        this.state.customsInfo.forEach(value => {
          Num += Number(value?.quantity ?? 0);
          if (value.price && value.quantity) {
            price += Number(value.quantity * value.price);
          } else {
            price += 0;
          }
          if (value.weight && value.quantity) {
            weight += Number(value.quantity * value.weight);
          } else {
            weight += 0;
          }
        });
        this.setState({
          totalNum: Num,
          totalWeight: weight,
          totalPrice: price,
        });
      }
    );
  };

  // 事件
  tabOnChange = val => {
    this.state.tabNumber = val;
    this.setState({
      tabNumber: val,
    });
  };
  shipperChange = (tag, index) => {
    let userID = Object.assign({}, this.state.formData, { userId: tag });
    this.setState({
      formData: userID,
    });
    this.props.form.setFieldsValue({
      shippers: tag,
      channelId: undefined,
      countryId: undefined,
    });
    // this.getProductName(tag);
  };

  // 修改单品数量
  changeNum = (val, i) => {
    const arr = this.state.customsInfo;
    arr[i].quantity = val ? val : 0;
    let Num = 0;
    let price = 0;
    let weight = 0;
    arr.forEach(value => {
      if (value.quantity) {
        Num += Number(value.quantity);
      } else {
        Num += 0;
      }
      if (value.price && value.quantity) {
        price += Number(value.quantity * value.price);
      } else {
        price += 0;
      }
      if (value.weight && value.quantity) {
        weight += Number(value.quantity * value.weight);
      } else {
        weight += 0;
      }
    });
    const { form } = this.props;
    let onQuantity = val;
    let onPrice = arr[i]?.price ?? 0;
    if (
      /^\d+$|^\d+[.]?\d+$/.test(onQuantity) &&
      /^([1-9]\d{0,15}\.\d{1,2}|0\.\d{1,2}|[1-9]\d{0,15})$/.test(onPrice)
    ) {
      let productPrice = new Decimal(onPrice).mul(new Decimal(onQuantity));
      form.setFieldsValue({
        [`productPrice${i}`]: productPrice.toNumber(),
      });
    } else {
      form.setFieldsValue({
        [`productPrice${i}`]: 0,
      });
    }
    this.setState({
      customsInfo: arr,
      totalNum: Num,
      totalWeight: weight,
      totalPrice: price,
    });
  };

  // 修改单品重量
  changeWeight = (val, i) => {
    const arr = this.state.customsInfo;
    arr[i].weight = val ? val : 0;
    let weights = 0;
    arr.forEach(value => {
      if (value.quantity && value.weight) {
        weights += Number(value.quantity * value.weight);
      } else {
        weights += 0;
      }
    });
    this.setState({
      customsInfo: arr,
      totalWeight: weights,
    });
  };

  // 修改单品单价
  changePrice = (val, i) => {
    const arr = this.state.customsInfo;
    arr[i].price = val ? val : 0;
    let prices = 0;
    arr.forEach(value => {
      if (value.quantity && value.price) {
        prices += Number(value.quantity * value.price);
      } else {
        prices += 0;
      }
    });
    const { form } = this.props;
    let onQuantity = arr[i]?.quantity ?? 0;
    let onPrice = val;
    if (
      /^\d+$|^\d+[.]?\d+$/.test(onQuantity) &&
      /^([1-9]\d{0,15}\.\d{1,2}|0\.\d{1,2}|[1-9]\d{0,15})$/.test(onPrice)
    ) {
      let productPrice = new Decimal(onPrice).mul(new Decimal(onQuantity));
      form.setFieldsValue({
        [`productPrice${i}`]: productPrice.toNumber(),
      });
    } else {
      form.setFieldsValue({
        [`productPrice${i}`]: 0,
      });
    }
    this.setState({
      customsInfo: arr,
      totalPrice: prices,
    });
  };

  // 是否含电
  isElectricityChange = e => {
    this.setState({
      value: e.target.value,
    });
  };

  // 价格计算
  chargePriceSimple = () => {
    const { form, dispatch } = this.props;
    let fieldsValue = form.getFieldsValue();
    const { totalWeight } = this.state;
    // 获取userId
    let userId = fieldsValue.userId;
    if (!(userId && typeof userId == 'string')) {
      userId = userId ? userId[0] : undefined;
    }
    // 设置参数
    let params = {
      customerCode: userId,
      productCode: fieldsValue.channelId,
      cityId: fieldsValue.companyCode,
      countryId: fieldsValue.countryId,
      weight: totalWeight,
      high: fieldsValue.height,
      length: fieldsValue.length,
      width: fieldsValue.width,
      postCode: fieldsValue.zipCode,
    };
    // 进行试算
    dispatch({
      type: 'Order/chargePriceSimple',
      payload: params,
      callback: res => {
        if (res.success) {
          message.success('试算成功');
          let moneyNum = 0;
          res.data?.expenseItems?.forEach(item => {
            moneyNum = new Decimal(moneyNum).add(new Decimal(item.money));
          });
          this.setState({
            moneyNum: moneyNum.toNumber(),
          });
        } else {
          this.setState({
            moneyNum: 0,
          });
        }
      },
    });
  };

  // 接口暂存
  clickStag = () => {
    const { form, dispatch } = this.props;
    const {
      totalPrice,
      totalWeight,
      totalNum,
      heights,
      widths,
      lengths,
      pickingOrders,
      customsInfo,
    } = this.state;
    let arr = [];
    let fieldsValue = form.getFieldsValue();
    for (let i = 0; i < customsInfo.length; i++) {
      let param = {};
      param.goodsNameCh = fieldsValue[`goodsNameCh${i}`];
      param.goodsNameEn = fieldsValue[`goodsNameEn${i}`];
      param.price = fieldsValue[`price${i}`];
      param.productHsCode = fieldsValue[`productHsCode${i}`];
      param.productMaterial = fieldsValue[`productMaterial${i}`];
      param.quantity = fieldsValue[`quantity${i}`];
      param.weight = fieldsValue[`weight${i}`];
      param.productLink = fieldsValue[`productLink${i}`];
      param.productSku = fieldsValue[`productSku${i}`];
      param.productImei = fieldsValue[`productImei${i}`];
      arr.push(param);
      if (param.goodsNameCh == undefined) {
        this.props.form.setFieldsValue({
          [`goodsNameCh${i}`]: undefined,
        });
      }
      if (param.goodsNameEn == undefined) {
        this.props.form.setFieldsValue({
          [`goodsNameEn${i}`]: undefined,
        });
      }
      if (param.price == undefined) {
        this.props.form.setFieldsValue({
          [`price${i}`]: undefined,
        });
      }
      if (param.quantity == undefined) {
        this.props.form.setFieldsValue({
          [`quantity${i}`]: undefined,
        });
      }
      if (param.weight == undefined) {
        this.props.form.setFieldsValue({
          [`weight${i}`]: undefined,
        });
      }
    }
    let newArr = arr.splice(0, customsInfo.length);
    let userId = fieldsValue.userId;
    let params = {
      channelId: fieldsValue.channelId,
      orderNumber: fieldsValue.orderNumber,
      companyCode: fieldsValue.companyCode,
      transactionNumber: fieldsValue.transactionNumber,
      importCustomsInfo: {
        taxPolicyExtends: {
          csp: fieldsValue.importCustomsInfo.taxPolicyExtends.csp,
        },
      },
      salesPlatform: fieldsValue.salesPlatform,
      waybillNumber: fieldsValue.waybillNumber,
      handoverCode: fieldsValue.handoverCode,
      dateOfReceipt: fieldsValue.datePicker
        ? moment(fieldsValue.datePicker).format('yyyy-MM-DD')
        : '',
      remark: fieldsValue.pickingOrders,
      poPStation: {
        pointId: fieldsValue.pointId,
      },
      receiverInfo: {
        address: fieldsValue.address,
        city: fieldsValue.city,
        company: fieldsValue.company,
        country: fieldsValue.countryId,
        email: fieldsValue.email,
        houseNumber: fieldsValue.houseNumber,
        name: fieldsValue.name,
        phone: fieldsValue.phone,
        state: fieldsValue.state,
        taxNumber: fieldsValue.receiverTaxNumber,
        zipCode: fieldsValue.zipCode,
        pinflNumber: fieldsValue.pinflNumber,
      },
      parcelInfo: {
        ioss: fieldsValue.IOSS,
        currency: fieldsValue.currencys,
        totalPrice: totalPrice,
        totalQuantity: totalNum,
        totalWeight: totalWeight,
        height: fieldsValue.height,
        width: fieldsValue.width,
        length: fieldsValue.length,
        hasBattery: fieldsValue.isBattery,
        productList: newArr,
      },
      senderInfo: {
        taxNumber: fieldsValue.senderTaxNumber,
        name: fieldsValue.senderName,
        company: fieldsValue.senderCompany,
        phone: fieldsValue.senderPhone,
        email: fieldsValue.senderEmail,
        zipCode: fieldsValue.senderZipCode,
        country: fieldsValue.senderCountry,
        state: fieldsValue.senderState,
        city: fieldsValue.senderCity,
        address: fieldsValue.senderAddress,
        houseNumber: fieldsValue.senderHouseNumber,
      },
    };
    if (userId && typeof userId == 'string') {
      params.userId = userId;
    } else {
      params.userId = userId != undefined ? userId[0] : undefined;
    }
    dispatch({
      type: 'Order/temporaryData',
      payload: params,
      callback: res => {
        if (res.success) {
          let miscellaneous = JSON.parse(localStorage.getItem('miscellaneous'));
          miscellaneous.isRefreshList = true;
          miscellaneous.tabNumber = '9';
          let deliveryAccount = params.userId;
          localStorage.setItem('deliveryAccount', deliveryAccount);
          localStorage.setItem('miscellaneous', JSON.stringify(miscellaneous));
          message.success(res.message, 1).then(() => {
            router.push('/smallBag/orderManagement/orderList');
          });
        }
      },
    });
  };

  // 提交
  submitAction = text => {
    const { form, dispatch } = this.props;
    const { expressCode, para, status, state } = this.props.history.location.query;
    form
      .validateFields()
      .then(values => {})
      .catch(errorInfo => {
        if (errorInfo.errors.userId) {
          message.error('请选择制单账号！');
          return;
        } else if (errorInfo.errors.orderNumber) {
          message.error('请填写订单号！');
          return;
        } else if (errorInfo.errors.channelId) {
          message.error('请选择产品！');
          return;
        } else if (errorInfo.errors.countryId) {
          message.error('请选择国家！');
          return;
        } else if (errorInfo.errors.currencys) {
          message.error('请选择申报币种！');
          return;
        } else if (errorInfo.errors.name) {
          message.error('请填写姓名！');
          return;
        } else if (errorInfo.errors.state) {
          message.error('请填写省/州！');
          return;
        } else if (errorInfo.errors.city) {
          message.error('请填写城市！');
          return;
        } else if (errorInfo.errors.phone) {
          message.error('请填写电话！');
          return;
        } else if (errorInfo.errors.address) {
          message.error('请填写地址！');
          return;
        } else {
          message.error('请完善报关信息！');
          return;
        }
      });
    const { id } = this.props.history.location.query;
    const {
      totalPrice,
      totalWeight,
      totalNum,
      heights,
      widths,
      lengths,
      pickingOrders,
      customsInfo,
      currencyCode,
      initData,
      userIds,
      waybillUserIds,
      waybillState,
      waybillExpressCode,
    } = this.state;
    const { userId } = this.state.formData;
    let arr = [];
    customsInfo.forEach((item, index) => {
      arr.push(
        `goodsNameCh${index}`,
        `goodsNameEn${index}`,
        `price${index}`,
        `productHsCode${index}`,
        `productLink${index}`,
        `productSku${index}`,
        `productImei${index}`,
        `productMaterial${index}`,
        `quantity${index}`,
        `weight${index}`,
        'currencys',
        'transactionNumber',
        'userId',
        'orderNumber',
        'companyCode',
        'channelId',
        'name',
        'company',
        'countryId',
        'phone',
        'state',
        'city',
        'email',
        'zipCode',
        'receiverTaxNumber',
        'address',
        'houseNumber',
        'pointId',
        'pinflNumber',
        'senderTaxNumber',
        'importCustomsInfo.taxPolicyExtends.csp',
        'senderName',
        'senderCompany',
        'senderPhone',
        'senderEmail',
        'senderZipCode',
        'senderCountry',
        'senderState',
        'senderCity',
        'senderAddress',
        'senderHouseNumber',
        'length',
        'pickingOrders',
        'width',
        'height',
        'datePicker',
        'isBattery',
        'IOSS',
        'salesPlatform',
        'waybillNumber',
        'handoverCode'
      );
    });
    form.validateFieldsAndScroll(arr, (err, values) => {
      if (!err) {
        let newArr = [];
        newArr = customsInfo.map((value, i) => {
          value.goodsNameCh = values[`goodsNameCh${i}`];
          value.goodsNameEn = values[`goodsNameEn${i}`];
          value.price = values[`price${i}`];
          value.hscode = values[`productHsCode${i}`];
          value.url = values[`productLink${i}`];
          value.sku = values[`productSku${i}`];
          value.imei = values[`productImei${i}`];
          value.material = values[`productMaterial${i}`];
          value.quantity = values[`quantity${i}`];
          value.weight = values[`weight${i}`];
          return value;
        });
        this.setState({
          btnLoading: true,
        });
        let params = {
          channelId: values.channelId,
          orderNumber: values.orderNumber,
          companyCode: values.companyCode,
          remark: values.pickingOrders,
          transactionNumber: values.transactionNumber,
          importCustomsInfo: {
            taxPolicyExtends: {
              csp: values.importCustomsInfo.taxPolicyExtends.csp,
            },
          },
          salesPlatform: values.salesPlatform,
          waybillNumber: values.waybillNumber,
          handoverCode: values.handoverCode,
          dateOfReceipt: values.datePicker ? moment(values.datePicker).format('yyyy-MM-DD') : '',
          poPStation: {
            pointId: values.pointId,
          },
          receiverInfo: {
            address: values.address,
            city: values.city,
            company: values.company,
            countryId: values.countryId,
            email: values.email,
            houseNumber: values.houseNumber,
            name: values.name,
            phone: values.phone,
            state: values.state,
            taxNumber: values.receiverTaxNumber,
            zipCode: values.zipCode,
            pinflNumber: values.pinflNumber,
          }, // 收件人信息
          parcelInfo: {
            currency: currencyCode,
            totalPrice: totalPrice,
            totalQuantity: totalNum,
            totalWeight: totalWeight,
            height: values.height,
            width: values.width,
            length: values.length,
            productList: newArr,
            hasBattery: values.isBattery,
            ioss: values.IOSS,
          }, //货物信息
          senderInfo: {
            taxNumber: values.senderTaxNumber,
            name: values.senderName,
            company: values.senderCompany,
            phone: values.senderPhone,
            email: values.senderEmail,
            zipCode: values.senderZipCode,
            country: values.senderCountry,
            state: values.senderState,
            city: values.senderCity,
            address: values.senderAddress,
            houseNumber: values.senderHouseNumber,
          },
        };
        (params.userId = waybillUserIds),
          (params.waybillNumber = waybillExpressCode),
          this.changeWaybills(params);
      } else {
        let newArr = [];
        let params = {};
        newArr = customsInfo.map((value, i) => {
          value.goodsNameCh = values[`goodsNameCh${i}`];
          value.goodsNameEn = values[`goodsNameEn${i}`];
          value.price = values[`price${i}`];
          value.productHsCode = values[`productHsCode${i}`];
          value.productLink = values[`productLink${i}`];
          value.productSku = values[`productSku${i}`];
          value.productImei = values[`productImei${i}`];
          value.productMaterial = values[`productMaterial${i}`];
          value.quantity = values[`quantity${i}`];
          value.weight = values[`weight${i}`];
          return value;
        });
      }
    });
  };

  // 已制单编辑修改运单
  changeWaybills = params => {
    const { dispatch } = this.props;
    dispatch({
      type: 'Order/changeWaybill',
      payload: params,
      callback: res => {
        this.setState({
          btnLoading: false,
        });
        if (res.success) {
          let miscellaneous = JSON.parse(localStorage.getItem('miscellaneous'));
          miscellaneous.isRefreshList = true;
          let deliveryAccount = typeof params.userId == 'string' ? params.userId : params.userId[0];
          localStorage.setItem('deliveryAccount', deliveryAccount);
          localStorage.setItem('miscellaneous', JSON.stringify(miscellaneous));
          message.success(res.message, 1).then(() => {
            router.push('/smallBag/orderManagement/orderList');
          });
        }
      },
    });
  };

  // 保存更新订单信息
  updateOrder = (data, text) => {
    const { dispatch } = this.props;
    dispatch({
      type: 'Order/updateOrder',
      payload: data,
      callback: result => {
        if (result.success) {
          if (text == 'add') {
            this.setState({
              btnLoading: false,
            });
            message.success(result.message);
            this.props.history.push('/smallBag/orderManagement/orderList');
          } else {
            let param = {
              userId: data.userId,
              ids: [data.id],
            };
            this.orderToWaybill(param);
          }
        } else {
          this.setState({
            btnLoading: false,
          });
        }
      },
    });
  };

  // 订单生成运单号
  orderToWaybill = data => {
    const { dispatch } = this.props;
    dispatch({
      type: 'Order/submitVoucher',
      payload: data,
      callback: result => {
        if (result.success) {
          this.setState({
            btnLoading: false,
          });
          message.success(result.message);
          this.props.history.push('/smallBag/orderManagement/orderList');
        } else {
          message.error(result.message);
          this.setState({
            btnLoading: false,
          });
        }
      },
    });
  };

  // 获取EJF导入模板
  getEJFGenericTemplates = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'Order/getEJFGenericTemplate',
      callback: result => {
        if (result.success) {
          this.setState({
            EJFtemplates: result.data,
          });
        }
      },
    });
  };

  dispatchGetMatchNameList = name => {
    const { dispatch } = this.props;
    return new Promise((resolve, reject) => {
      dispatch({
        type: 'anomalyOrder/getMatchNameList',
        payload: {
          name,
        },
        callback: response => {
          if (response.success) {
            resolve(
              response.data.map(item => ({
                value: item.standardGoodsName,
                label: item.standardGoodsName,
                goodsNameEnData: item.standardGoodsnameEn,
              }))
            );
          }
        },
      });
    });
  };

  goodsSearch = (value, index) => {
    if (value) {
      let { goods } = this.state;
      const resultData = goods?.map((item, i) => {
        if (index === i) {
          return this.dispatchGetMatchNameList(value);
        } else {
          return item;
        }
      });
      Promise.allSettled(resultData).then(res => {
        const data = res.map((item, index) => {
          if (item.status === 'fulfilled') {
            return item.value;
          } else {
            return [];
          }
        });
        this.setState({
          goods: data,
        });
      });
    }
  };

  goodsChange = (option, index) => {
    let { customsInfo } = this.state;
    let { form } = this.props;
    customsInfo[index].goodsNameEn = option.goodsNameEnData;
    form.setFieldsValue({
      [`goodsNameEn${index}`]: option.goodsNameEnData,
    });
    this.setState({
      customsInfo: customsInfo,
    });
  };

  // 点击模板进行下载
  clickDownload = id => {
    downloadFile({
      url: `/csc/ejf/draft/downloadEJFGenericTemplate/${id}`,
      method: 'POST',
    });
  };

  // 上传文档
  handleUpload = () => {
    const { userId } = this.state.formData;
    if (!userId) {
      message.error('请选择制单账号！');
      return;
    }
    const { fileList } = this.state;
    const formData = new FormData();
    fileList.forEach(file => {
      formData.append('files', file);
    });
    this.setState({
      uploading: true,
    });
    fetch(`/csc/ejf/draft/createOrder/${userId}`, {
      method: 'POST',
      body: formData,
    })
      .then(res => res.json())
      .then(res => {
        if (res.success) {
          this.setState({
            fileList: [],
          });
          message
            .success('导入成功')
            .then(() => this.props.history.push('/smallBag/orderManagement/orderList'));
        } else {
          if (res.code == 501) {
            message.error({
              content: res.message,
              style: {
                whiteSpace: 'pre-wrap',
              },
            });
          } else if (res.code == 502) {
            // message.error({
            //   content:
            //     '成功条数：' +
            //     res.successCount +
            //     ',失败条数：' +
            //     res.failuresCount +
            //     '\n' +
            //     res.message,
            //   style: {
            //     whiteSpace: 'pre-wrap',
            //   },
            // });
            this.setState({
              isModalVisible: true,
              uploadErrorMsg:
                '成功条数：' +
                (res.successCount ?? '--') +
                ',失败条数：' +
                (res.failuresCount ?? '--'),
              uploadErrorCon: res.message,
            });
            this.dataURLtoDownload(res.errorMsg, res.fileName);
          } else {
            // message.error({
            //   content:
            //     '成功条数：' +
            //     res.successCount +
            //     ',失败条数：' +
            //     res.failuresCount +
            //     '\n' +
            //     res.message,
            //   style: {
            //     whiteSpace: 'pre-wrap',
            //   },
            // });
            this.setState({
              isModalVisible: true,
              uploadErrorMsg:
                '成功条数：' +
                (res.successCount ?? '--') +
                ',失败条数：' +
                (res.failuresCount ?? '--'),
              uploadErrorCon: res.message,
            });
          }
          // this.setState({
          //   fileList: [],
          //   fileName: '',
          // });
        }
      })
      .catch(res => {
        message.error({
          content: res.message,
          style: {
            whiteSpace: 'pre-wrap',
          },
        });
      })
      .finally(() => {
        this.setState({
          uploading: false,
        });
      });
  };

  //base64解析下载 dataurl是后端返回的base64字符串，name是文件名
  dataURLtoDownload = (dataurl, name) => {
    let bstr = atob(dataurl), //解析 base-64 编码的字符串
      n = bstr.length,
      u8arr = new Uint8Array(n); //创建初始化为0的，包含length个元素的无符号整型数组
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n); //返回字符串第一个字符的 Unicode 编码
    }
    let blob = new Blob([u8arr]); //转化成blob
    let url = URL.createObjectURL(blob); //这个新的URL 对象表示指定的 File 对象或 Blob 对象
    let a = document.createElement('a'); //创建一个a标签
    a.href = url;
    a.download = name;
    a.click();
    URL.revokeObjectURL(a.href); //释放之前创建的url对象
  };

  // 点击一键复制
  clickCopy = () => {
    sessionStorage.setItem('copys', JSON.stringify(this.state.keyCopyData));
    this.props.history.push('/smallBag/orderManagement/creatOrder?type=add');
  };

  render() {
    const {
      temuShow,
      tabNumber,
      value,
      transactionNumber,
      currencys,
      currencyCode,
      customsInfo,
      deliveryAccount,
      channels,
      totalNum,
      totalWeight,
      totalPrice,
      lengths,
      widths,
      heights,
      countriesData,
      pointId,
      senderTaxNumber,
      importCustomsInfo,
      senderName,
      senderCompany,
      senderPhone,
      senderEmail,
      senderZipCode,
      senderCountry,
      senderState,
      senderCity,
      senderAddress,
      senderHouseNumber,
      receiverInfo,
      EJFtemplates,
      isHaveCountry,
      btnLoading,
      currentAuthorityValue,
      isHaveDeliveryAccount,
      isProduct,
      pickingOrders,
      fileName,
      countryDisabled,
      IOSS,
      salesPlatform,
      handoverCode,
      handoverCodes,
      handoverChange,
      isModalVisible,
      uploadErrorCon,
      uploadErrorMsg,
      isEdit,
      DAccount,
      isLock,
      waybillState,
      waybillNumber,
      warehouseList,
      moneyNum,
      goods,
    } = this.state;

    const { orderNumber, channelId, userId, dateOfReceipt, companyCode } = this.state.formData;
    const { id } = this.props.history.location.query;
    // let dochannels = channels.filter(item => item.channelNameCh.indexOf('燕文') == -1); //indexOf('xxx')==-1 是不存在，不等于-1 是存在
    const {
      form: { getFieldDecorator },
      shipperInitOpt,
      authorizedLoading,
      chargeLoading,
    } = this.props;
    doorplateLayout;
    const formItemLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
        md: { span: 6 },
        xxl: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
        md: { span: 16 },
        xxl: { span: 16 },
      },
    };
    const formItemLayoutes = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 3 },
        md: { span: 3 },
        xxl: { span: 2 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
        md: { span: 20 },
        xxl: { span: 20 },
      },
    };
    const formItemLayouts = {
      labelCol: { xs: { span: 24 }, sm: { span: 2 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 20 } },
    };
    const doorplateLayout = {
      labelCol: { xs: { span: 24 }, sm: { span: 4 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
    };
    const exportInfoLayout = {
      labelCol: { xs: { span: 24 }, sm: { span: 8 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 14 } },
    };
    const exportInfoLongLayout = {
      labelCol: { xs: { span: 24 }, sm: { span: 4 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
    };
    const packagingLayout = {
      wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
    };

    const actionsTextMap = {
      expandText: '展开',
      collapseText: '收起',
    };

    const account = deliveryAccount;
    let shipperOpen = account.length > 8 ? true : false;

    const { uploading, fileList } = this.state;
    const props = {
      onRemove: file => {
        this.setState(state => {
          const index = state.fileList.indexOf(file);
          const newFileList = state.fileList.slice();
          newFileList.splice(index, 1);
          return {
            fileList: newFileList,
          };
        });
      },
      beforeUpload: file => {
        const type = file.type;
        if (type != 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
          message.error('只能上传.xlsx格式文件');
          return;
        }
        this.setState(state => ({
          fileList: [file],
          fileName: file.name,
        }));
        return false;
      },
      fileList,
    };

    return (
      <PageContainerComponent
        header={{
          title: null,
          breadcrumb: {},
          breadcrumbRender: props => (
            <PageHeaderBreadcrumb {...props} col>
              <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
                <Button type="primary" onClick={() => this.clickCopy()}>
                  一键复制
                </Button>
                <Button style={{ marginLeft: '10px' }} onClick={() => window.history.back(-1)}>
                  返回
                </Button>
              </div>
            </PageHeaderBreadcrumb>
          ),
        }}
      >
        <div style={{ backgroundColor: '#EEF2F6', padding: '1px 0 30px' }}>
          {/*单票创建*/}
          <div style={{ display: tabNumber === '1' ? '' : 'none' }} className="conten_singleDiv">
            <Form {...formItemLayout} className="ant-advanced-search-form">
              {/*  制单账号*/}
              <div
                className="deliveryAccount"
                style={{
                  padding: '20px 20px 0',
                  backgroundColor: '#fff',
                  borderRadius: '10px',
                  marginBottom: '20px',
                  display: 'flex',
                }}
              >
                <Row>
                  <StandardFormRow title="制单账号" block>
                    {/* <Col span={20}> */}
                    <Form.Item
                      style={{ marginRight: 70, display: isLock ? 'none' : '' }}
                      className="form_item_tagSelect"
                      labelCol={{ xs: { span: 24 }, sm: { span: 8 } }}
                      wrapperCol={{
                        xs: { span: 24 },
                        sm: { span: 24 },
                        ms: { span: 24 },
                        xxl: { span: 24 },
                      }}
                    >
                      {getFieldDecorator('userId', {
                        initialValue: userId,
                        rules: [
                          {
                            required: true,
                            message: '请至少选择一个制单账号',
                          },
                        ],
                      })(
                        <SingleTagSelect
                          actionsText={actionsTextMap}
                          className="open"
                          expandable={shipperOpen}
                          onChange={this.shipperChange}
                          checked={isHaveDeliveryAccount}
                        >
                          {account &&
                            account.map((element, index) => (
                              <SingleTagSelect.Option key={index} value={element.accountCode}>
                                {element.warehouseName ?? ''}
                                {element.accountCode}
                              </SingleTagSelect.Option>
                            ))}
                        </SingleTagSelect>
                      )}
                    </Form.Item>
                    <Form.Item
                      style={{ marginRight: 70, display: isLock ? '' : 'none' }}
                      className="form_item_tagSelect"
                      labelCol={{ xs: { span: 24 }, sm: { span: 8 } }}
                      wrapperCol={{
                        xs: { span: 24 },
                        sm: { span: 24 },
                        ms: { span: 24 },
                        xxl: { span: 24 },
                      }}
                    >
                      <Button disabled={isLock} type="primary">
                        {DAccount?.accountCode}
                      </Button>
                    </Form.Item>
                    {/* </Col> */}
                  </StandardFormRow>
                </Row>
              </div>
              {/*    运单信息*/}
              <div
                style={{
                  padding: '20px 20px 0',
                  backgroundColor: '#fff',
                  borderRadius: '10px',
                  marginBottom: '20px',
                }}
              >
                <span
                  style={{
                    fontSize: '16px',
                    display: 'block',
                    color: '#52C41A',
                    marginBottom: '10px',
                  }}
                >
                  基础信息
                </span>
                <Row>
                  <Col span={8}>
                    <Form.Item label="交货仓" labelAlign="right">
                      {getFieldDecorator(`companyCode`, {
                        initialValue: companyCode,
                        rules: [
                          {
                            required: false,
                            message: '请选择交货仓',
                          },
                        ],
                      })(
                        <Select
                          showSearch
                          placeholder="请选择交货仓"
                          optionFilterProp="children"
                          disabled={isLock}
                          // onChange={this.currencyChange}
                        >
                          {warehouseList &&
                            warehouseList.map((element, index) => (
                              <Select.Option key={index} value={element.code}>
                                {element.name}
                              </Select.Option>
                            ))}
                        </Select>
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="订单号" {...formItemLayout} labelAlign="right">
                      {getFieldDecorator('orderNumber', {
                        initialValue: orderNumber,
                        rules: [
                          {
                            required: true,
                            message: '请输入订单号',
                          },
                          {
                            max: 50,
                            message: '请输入50位以下的订单号',
                          },
                        ],
                      })(<Input allowClear disabled={isLock} placeholder="请输入订单号" />)}
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="目的地" labelAlign="right">
                      {getFieldDecorator('countryId', {
                        initialValue: isHaveCountry ? receiverInfo.countryId : '',
                        rules: [
                          {
                            required: true,
                            message: '请选择国家',
                          },
                        ],
                      })(
                        <Select
                          showSearch
                          placeholder="请选择国家"
                          onChange={this.contryChange}
                          optionFilterProp="children"
                          disabled={countryDisabled}
                        >
                          {countriesData &&
                            countriesData.map((element, index) => (
                              <Select.Option key={index} value={element.id}>
                                {element.nameCh +
                                  '/' +
                                  element.code +
                                  '/' +
                                  element.nameEn +
                                  '/' +
                                  element.countryNamePinYin}
                              </Select.Option>
                            ))}
                        </Select>
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="产品名称" labelAlign="right">
                      {getFieldDecorator('channelId', {
                        initialValue: isProduct ? channelId : '',
                        rules: [
                          {
                            required: true,
                            message: '请选择产品',
                          },
                        ],
                      })(
                        <Select
                          showSearch
                          placeholder="请选择产品"
                          onChange={this.selectChange}
                          optionFilterProp="children"
                          onFocus={this.selectOnfocus}
                          disabled={isLock}
                        >
                          {channels &&
                            channels.map((element, index) => (
                              <Select.Option key={index} value={element.id}>
                                {element.nameCh}
                              </Select.Option>
                            ))}
                        </Select>
                      )}
                    </Form.Item>
                  </Col>
                  {/*{temuShow && (*/}
                  {/*  <>*/}
                  {/*    <Col span={8}>*/}
                  {/*      <Form.Item label="运单号" {...formItemLayout} labelAlign="right">*/}
                  {/*        {getFieldDecorator('waybillNumber', {*/}
                  {/*          initialValue: waybillNumber,*/}
                  {/*          rules: [*/}
                  {/*            {*/}
                  {/*              max: 50,*/}
                  {/*              message: '请输入50位以下的运单号',*/}
                  {/*            },*/}
                  {/*          ],*/}
                  {/*        })(<Input allowClear disabled={isLock} placeholder="请输入运单号" />)}*/}
                  {/*      </Form.Item>*/}
                  {/*    </Col>*/}
                  {/*    <Col span={8}>*/}
                  {/*      <Form.Item label={formatMessage({ id: '海外交货地' })} labelAlign="right">*/}
                  {/*        {getFieldDecorator('handoverCode', {*/}
                  {/*          initialValue: handoverCode,*/}
                  {/*        })(*/}
                  {/*          <Select*/}
                  {/*            showSearch*/}
                  {/*            allowClear*/}
                  {/*            disabled={isLock}*/}
                  {/*            placeholder={formatMessage({ id: '请选择海外交货地' })}*/}
                  {/*            optionFilterProp="children"*/}
                  {/*            onChange={handoverChange}*/}
                  {/*          >*/}
                  {/*            {handoverCodes &&*/}
                  {/*              handoverCodes.map((element, index) => (*/}
                  {/*                <Select.Option key={index} value={element.code}>*/}
                  {/*                  {element.label}*/}
                  {/*                </Select.Option>*/}
                  {/*              ))}*/}
                  {/*          </Select>*/}
                  {/*        )}*/}
                  {/*      </Form.Item>*/}
                  {/*    </Col>*/}
                  {/*  </>*/}
                  {/*)}*/}
                  <Col span={8}>
                    <Form.Item label="平台交易号" labelAlign="right">
                      {getFieldDecorator('transactionNumber', {
                        initialValue: transactionNumber,
                        rules: [
                          {
                            required: false,
                            message: '平台交易号',
                          },
                          {
                            max: 50,
                            message: '请输入50位以下的平台交易号',
                          },
                        ],
                      })(<Input disabled={isLock} placeholder="请输入平台交易号" />)}
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="申报币种" labelAlign="right">
                      {getFieldDecorator(`currencys`, {
                        initialValue: currencyCode,
                        rules: [
                          {
                            required: true,
                            message: '请选择申报币种',
                          },
                        ],
                      })(
                        <Select
                          showSearch
                          placeholder="请选择申报币种"
                          optionFilterProp="children"
                          onChange={this.currencyChange}
                          disabled={isEdit}
                        >
                          {currencys &&
                            currencys.map((element, index) => (
                              <Select.Option key={index} value={element.code}>
                                {element.name + '/' + element.code}
                              </Select.Option>
                            ))}
                        </Select>
                      )}
                    </Form.Item>
                  </Col>

                  <Col span={8}>
                    <Form.Item label="是否含电" labelAlign="right">
                      {getFieldDecorator(`isBattery`, {
                        initialValue: Number(value),
                        rules: [
                          {
                            required: false,
                            message: '请选择是否含电',
                          },
                        ],
                      })(
                        <Radio.Group disabled={isEdit} onChange={this.isElectricityChange}>
                          <Radio value={1}>是</Radio>
                          <Radio value={0}>否</Radio>
                        </Radio.Group>
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="IOSS税号" {...formItemLayout} labelAlign="right">
                      {getFieldDecorator('IOSS', {
                        initialValue: IOSS,
                        rules: [
                          {
                            required: false,
                            message: '请输入IOSS税号',
                          },
                          {
                            max: 50,
                            message: '请输入50位以下的IOSS税号',
                          },
                        ],
                      })(
                        <Input
                          allowClear
                          disabled={isEdit}
                          placeholder={formatMessage({ id: '欧盟国家未填默认燕文代缴税费' })}
                        />
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="销售平台" {...formItemLayout} labelAlign="right">
                      {getFieldDecorator('salesPlatform', {
                        initialValue: salesPlatform,
                        rules: [
                          {
                            required: false,
                            message: '请输入销售平台',
                          },
                          {
                            max: 50,
                            message: '请输入50位以下的销售平台',
                          },
                        ],
                      })(<Input allowClear disabled={isEdit} placeholder="请输入销售平台" />)}
                    </Form.Item>
                  </Col>
                </Row>
              </div>
              {/*    收件人信息*/}
              <div
                style={{
                  padding: '20px 20px 0',
                  backgroundColor: '#fff',
                  borderRadius: '10px',
                  marginBottom: '20px',
                }}
              >
                <span
                  style={{
                    fontSize: '16px',
                    display: 'block',
                    color: '#52C41A',
                    marginBottom: '10px',
                  }}
                >
                  收件人信息
                </span>
                <Row>
                  <Col span={8}>
                    <Form.Item label="姓名" labelAlign="right">
                      {getFieldDecorator('name', {
                        initialValue: receiverInfo.name,
                        rules: [
                          {
                            required: true,
                            message: '请输入姓名',
                          },
                          {
                            max: 300,
                            message: '请输入300位以下的姓名',
                          },
                        ],
                      })(<Input allowClear disabled={isEdit} placeholder="请输入姓名" />)}
                    </Form.Item>
                  </Col>

                  <Col span={8}>
                    <Form.Item label="公司" labelAlign="right">
                      {getFieldDecorator('company', {
                        initialValue: receiverInfo.company,
                        rules: [
                          {
                            required: false,
                            message: '请输入公司',
                          },
                          {
                            max: 300,
                            message: '请输入300位以下的公司名称',
                          },
                        ],
                      })(<Input allowClear disabled={isEdit} placeholder="请输入公司" />)}
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="省/州" labelAlign="right">
                      {getFieldDecorator('state', {
                        initialValue: receiverInfo.state,
                        rules: [
                          {
                            required: false,
                            message: '请输入省/州',
                          },
                          {
                            max: 50,
                            message: '请输入50位以下的省/州名',
                          },
                        ],
                      })(<Input allowClear disabled={isEdit} placeholder="请输入省/州" />)}
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="邮编" labelAlign="right">
                      {getFieldDecorator('zipCode', {
                        initialValue: receiverInfo.zipCode,
                        rules: [
                          {
                            required: false,
                            message: '请输入邮编',
                          },
                          {
                            max: 100,
                            message: '请输入100位以下的邮编',
                          },
                        ],
                      })(<Input allowClear disabled={isEdit} placeholder="请输入邮编" />)}
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="城市" labelAlign="right">
                      {getFieldDecorator('city', {
                        initialValue: receiverInfo.city,
                        rules: [
                          {
                            required: true,
                            message: '请输入城市',
                          },
                          {
                            max: 200,
                            message: '请输入200位以下的城市名',
                          },
                        ],
                      })(<Input allowClear disabled={isEdit} placeholder="请输入城市" />)}
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="门牌号" labelAlign="right">
                      {getFieldDecorator('houseNumber', {
                        initialValue: receiverInfo.houseNumber,
                        rules: [
                          {
                            required: false,
                            message: '请输入门牌号',
                          },
                          {
                            max: 100,
                            message: '请输入100位以下的门牌号',
                          },
                        ],
                      })(<Input allowClear disabled={isEdit} placeholder="请输入门牌号" />)}
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="自提点ID" labelAlign="right">
                      {getFieldDecorator('pointId', {
                        initialValue: pointId,
                        rules: [
                          {
                            required: false,
                            message: '请输入自提点ID',
                          },
                          {
                            max: 50,
                            message: '请输入50位以下的自提点ID',
                          },
                        ],
                      })(<Input allowClear disabled={isEdit} placeholder="请输入自提点ID" />)}
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="电话" labelAlign="right">
                      {getFieldDecorator('phone', {
                        initialValue: receiverInfo.phone,
                        rules: [
                          {
                            required: false,
                            message: '请输入电话',
                          },
                          {
                            max: 50,
                            message: '请输入50位以下的电话号码',
                          },
                        ],
                      })(<Input allowClear disabled={isEdit} placeholder="请输入电话" />)}
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="邮箱" labelAlign="right">
                      {getFieldDecorator('email', {
                        initialValue: receiverInfo.email,
                        rules: [
                          {
                            required: false,
                            message: '请输入邮箱',
                          },
                          {
                            max: 100,
                            message: '请输入100位以下的邮箱地址',
                          },
                        ],
                      })(<Input allowClear disabled={isEdit} placeholder="请输入邮箱" />)}
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="税号" labelAlign="right">
                      {getFieldDecorator('receiverTaxNumber', {
                        initialValue: receiverInfo.taxNumber,
                        rules: [
                          {
                            required: false,
                            message: '请输入税号',
                          },
                          {
                            max: 50,
                            message: '请输入50位以下的税号',
                          },
                        ],
                      })(
                        <Input
                          allowClear
                          disabled={isEdit}
                          placeholder="可填写CPF/CNP/VAT/护照ID/EORI"
                        />
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="PINFL号码" labelAlign="right">
                      {getFieldDecorator('pinflNumber', {
                        initialValue: receiverInfo.pinflNumber,
                        rules: [
                          {
                            required: false,
                            message: '请输入PINFL号码',
                          },
                          {
                            max: 50,
                            message: '请输入50位以下的PINFL号码',
                          },
                        ],
                      })(<Input allowClear disabled={isEdit} placeholder="请输入PINFL号码" />)}
                    </Form.Item>
                  </Col>
                  <Col span={16}>
                    <Form.Item
                      label="地址"
                      {...formItemLayoutes}
                      className="address"
                      style={{ flexWrap: 'nowrap' }}
                      labelAlign="right"
                    >
                      {getFieldDecorator('address', {
                        initialValue: receiverInfo.address,
                        rules: [
                          {
                            required: true,
                            message: '请输入地址',
                          },
                          {
                            max: 500,
                            message: '请输入500位以下的地址',
                          },
                        ],
                      })(<Input allowClear disabled={isEdit} placeholder="请输入地址" />)}
                    </Form.Item>
                  </Col>
                </Row>
              </div>
              {/*   发件人信息*/}
              <div
                style={{
                  padding: '20px 20px 0',
                  backgroundColor: '#fff',
                  borderRadius: '10px',
                  marginBottom: '20px',
                }}
              >
                <span
                  style={{
                    fontSize: '16px',
                    display: 'block',
                    color: '#52C41A',
                    marginBottom: '10px',
                  }}
                >
                  发件人信息
                </span>
                <Row>
                  <Col span={8}>
                    <Form.Item label="发件人税号" labelAlign="center">
                      {getFieldDecorator('senderTaxNumber', {
                        initialValue: senderTaxNumber,
                        rules: [
                          {
                            required: false,
                            message: '请输入英国店铺VAT税',
                          },
                          {
                            max: 50,
                            message: '请输入50位以下的英国店铺VAT税',
                          },
                        ],
                      })(<Input allowClear disabled={isEdit} placeholder="可填英国店铺VAT税号" />)}
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="CSP" labelAlign="center">
                      {getFieldDecorator('importCustomsInfo.taxPolicyExtends.csp', {
                        initialValue: importCustomsInfo?.taxPolicyExtends?.csp,
                        rules: [
                          {
                            max: 50,
                            message: '请输入50位以下的CSP',
                          },
                        ],
                      })(<Input allowClear disabled={isEdit} />)}
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="发件人姓名" labelAlign="center">
                      {getFieldDecorator('senderName', {
                        initialValue: senderName,
                        rules: [
                          {
                            required: false,
                            message: '请输入发件人姓名',
                          },
                        ],
                      })(<Input allowClear disabled={isEdit} placeholder="请输入发件人姓名" />)}
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="发件人公司" labelAlign="center">
                      {getFieldDecorator('senderCompany', {
                        initialValue: senderCompany,
                        rules: [
                          {
                            required: false,
                            message: '请输入发件人公司',
                          },
                        ],
                      })(<Input allowClear disabled={isEdit} placeholder="请输入发件人公司" />)}
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="发件人电话" labelAlign="center">
                      {getFieldDecorator('senderPhone', {
                        initialValue: senderPhone,
                        rules: [
                          {
                            required: false,
                            message: '请输入发件人电话',
                          },
                        ],
                      })(<Input allowClear disabled={isEdit} placeholder="请输入发件人电话" />)}
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="发件人邮箱" labelAlign="center">
                      {getFieldDecorator('senderEmail', {
                        initialValue: senderEmail,
                        rules: [
                          {
                            required: false,
                            message: '请输入发件人邮箱',
                          },
                        ],
                      })(<Input allowClear disabled={isEdit} placeholder="请输入发件人邮箱" />)}
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="发件人邮编" labelAlign="center">
                      {getFieldDecorator('senderZipCode', {
                        initialValue: senderZipCode,
                        rules: [
                          {
                            required: false,
                            message: '请输入发件人邮编',
                          },
                        ],
                      })(<Input allowClear disabled={isEdit} placeholder="请输入发件人邮编" />)}
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="发件人国家" labelAlign="center">
                      {getFieldDecorator('senderCountry', {
                        initialValue: isHaveCountry ? senderCountry : '',
                        rules: [
                          {
                            required: false,
                            message: '请选择国家',
                          },
                        ],
                      })(
                        <Select
                          showSearch
                          placeholder="请选择国家"
                          onChange={this.contryChange}
                          optionFilterProp="children"
                        >
                          {countriesData &&
                            countriesData.map((element, index) => (
                              <Select.Option key={index} value={element.id}>
                                {element.nameCh +
                                  '/' +
                                  element.code +
                                  '/' +
                                  element.nameEn +
                                  '/' +
                                  element.countryNamePinYin}
                              </Select.Option>
                            ))}
                        </Select>
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="发件人州(省)" labelAlign="center">
                      {getFieldDecorator('senderState', {
                        initialValue: senderState,
                        rules: [
                          {
                            required: false,
                            message: '请输入发件人州(省)',
                          },
                        ],
                      })(<Input allowClear disabled={isEdit} placeholder="请输入发件人州(省)" />)}
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="发件人城市" labelAlign="center">
                      {getFieldDecorator('senderCity', {
                        initialValue: senderCity,
                        rules: [
                          {
                            required: false,
                            message: '请输入发件人城市',
                          },
                        ],
                      })(<Input allowClear disabled={isEdit} placeholder="请输入发件人城市" />)}
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="发件人地址" labelAlign="center">
                      {getFieldDecorator('senderAddress', {
                        initialValue: senderAddress,
                        rules: [
                          {
                            required: false,
                            message: '请输入发件人地址',
                          },
                        ],
                      })(<Input allowClear disabled={isEdit} placeholder="请输入发件人地址" />)}
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="发件人门牌号" labelAlign="center">
                      {getFieldDecorator('senderHouseNumber', {
                        initialValue: senderHouseNumber,
                        rules: [
                          {
                            required: false,
                            message: '请输入发件人门牌号',
                          },
                        ],
                      })(<Input allowClear disabled={isEdit} placeholder="请输入发件人门牌号" />)}
                    </Form.Item>
                  </Col>
                </Row>
              </div>
              <div
                className="customsInfo"
                style={{
                  padding: '20px',
                  backgroundColor: '#fff',
                  borderRadius: '10px',
                  marginBottom: '20px',
                }}
              >
                <span
                  style={{
                    fontSize: '16px',
                    display: 'block',
                    color: '#52C41A',
                    marginBottom: '10px',
                  }}
                >
                  报关信息
                  {/* <Button
                        type="primary"
                        style={{ margin: '0px 20px' }}
                        onClick={this.addCustomsInfo}
                        disabled={isEdit}
                      >
                        新增
                      </Button> */}
                  <span
                    style={{
                      color: 'rgba(253, 111, 111, 1)',
                      marginLeft: '16px',
                      padding: '5px 15px',
                      fontSize: '14px',
                      backgroundColor: 'rgba(250, 236, 221, 1)',
                    }}
                  >
                    请注意：报关单上的申报品名为第一条报关信息的中英文品名，报关数量、重量和价值则取申报总信息
                  </span>
                </span>
                {customsInfo.map((value, index) => {
                  const priceReg = /^([1-9]\d{0,15}\.\d{1,2}|0\.\d{1,2}|[1-9]\d{0,15})$/;
                  const numReg = /^\d+$|^\d+[.]?\d+$/;
                  return (
                    <div className="column-itemDiv" key={index}>
                      <div
                        className="ant-badge ant-badge-not-a-wrapper number"
                        style={{ backgroundColor: index == '0' ? '#eecd63' : '#43cf7c' }}
                      >
                        {index + 1}
                      </div>
                      <Card
                        style={{ backgroundColor: index == '0' ? '#fffaf4' : 'fcfdfc' }}
                        bordered={false}
                      >
                        <Row>
                          <Col span={6}>
                            <Form.Item
                              label="中文品名"
                              labelAlign="right"
                              labelCol={{ xs: { span: 24 }, sm: { span: 8 } }}
                              wrapperCol={{ xs: { span: 24 }, sm: { span: 14 } }}
                            >
                              {getFieldDecorator(`goodsNameCh${index}`, {
                                initialValue: value.goodsNameCh,
                                rules: [
                                  {
                                    required: true,
                                    message: '请选择中文品名',
                                  },
                                ],
                              })(
                                <Select
                                  showSearch
                                  onChange={(value, option) => this.goodsChange(option, index)}
                                  onSearch={value => this.goodsSearch(value, index)}
                                  filterOption={(input, option) =>
                                    (option?.label ?? '')
                                      .toLowerCase()
                                      .includes(input.toLowerCase())
                                  }
                                  disabled={isEdit}
                                  options={goods[index]}
                                  placeholder="请选择中文品名"
                                />
                              )}
                            </Form.Item>
                          </Col>
                          <Col span={6}>
                            <Form.Item
                              label="英文品名"
                              labelAlign="right"
                              labelCol={{ xs: { span: 24 }, sm: { span: 8 } }}
                              wrapperCol={{ xs: { span: 24 }, sm: { span: 14 } }}
                            >
                              {getFieldDecorator(`goodsNameEn${index}`, {
                                initialValue: value.goodsNameEn,
                                rules: [
                                  {
                                    required: true,
                                    message: '请输入英文品名',
                                  },
                                  {
                                    max: 200,
                                    message: '请输入200位以下的英文品名',
                                  },
                                ],
                              })(<Input allowClear disabled={true} placeholder="请输入英文品名" />)}
                            </Form.Item>
                          </Col>
                          <Col span={6}>
                            <Form.Item
                              label="商品海关编码"
                              labelAlign="right"
                              labelCol={{ xs: { span: 24 }, sm: { span: 8 } }}
                              wrapperCol={{ xs: { span: 24 }, sm: { span: 14 } }}
                            >
                              {getFieldDecorator(`productHsCode${index}`, {
                                initialValue: value.hscode,
                                rules: [
                                  {
                                    required: false,
                                    message: '请输入商品海关编码',
                                  },
                                  {
                                    max: 500,
                                    message: '请输入500位以下的海关编码',
                                  },
                                ],
                              })(
                                <Input allowClear disabled={isEdit} placeholder="请输入海关编码" />
                              )}
                            </Form.Item>
                          </Col>
                          <Col span={6}>
                            <Form.Item
                              label="单品重量(g)"
                              labelAlign="right"
                              labelCol={{ xs: { span: 24 }, sm: { span: 8 } }}
                              wrapperCol={{ xs: { span: 24 }, sm: { span: 14 } }}
                            >
                              {getFieldDecorator(`weight${index}`, {
                                initialValue: value.weight,
                                rules: [
                                  {
                                    required: true,
                                    message: '请输入单品重量',
                                  },
                                  {
                                    max: 11,
                                    message: '请输入11位以下的单品重量',
                                  },
                                  {
                                    pattern: /^[0-9]*[1-9][0-9]*$/,
                                    message: '请输入正整数数字',
                                  },
                                ],
                              })(
                                <Input
                                  allowClear
                                  disabled={isEdit}
                                  placeholder="请输入单品重量"
                                  onChange={event => this.changeWeight(event.target.value, index)}
                                />
                              )}
                            </Form.Item>
                          </Col>
                          <Col span={6}>
                            <Form.Item
                              label="申报单价"
                              labelAlign="right"
                              labelCol={{ xs: { span: 24 }, sm: { span: 8 } }}
                              wrapperCol={{ xs: { span: 24 }, sm: { span: 14 } }}
                            >
                              {getFieldDecorator(`price${index}`, {
                                initialValue: value.price,
                                rules: [
                                  {
                                    required: true,
                                    message: '请输入申报单价',
                                  },
                                  {
                                    pattern: priceReg,
                                    message: '请输入最大16位数字+小数点两位',
                                  },
                                ],
                              })(
                                <Input
                                  allowClear
                                  disabled={isEdit}
                                  placeholder="请输入申报单价"
                                  addonBefore={currencyCode}
                                  onChange={event => this.changePrice(event.target.value, index)}
                                />
                              )}
                            </Form.Item>
                          </Col>
                          <Col span={6}>
                            <Form.Item
                              label="单票数量"
                              labelAlign="right"
                              labelCol={{ xs: { span: 24 }, sm: { span: 8 } }}
                              wrapperCol={{ xs: { span: 24 }, sm: { span: 14 } }}
                            >
                              {getFieldDecorator(`quantity${index}`, {
                                initialValue: value.quantity,
                                rules: [
                                  {
                                    required: true,
                                    message: '请输入单票数量',
                                  },
                                  {
                                    max: 11,
                                    message: '请输入11位以下的单品数量',
                                  },
                                  {
                                    pattern: numReg,
                                    message: '请输入正整数数字',
                                  },
                                ],
                              })(
                                <Input
                                  allowClear
                                  disabled={isEdit}
                                  placeholder="请输入单票数量"
                                  onChange={event => this.changeNum(event.target.value, index)}
                                />
                              )}
                            </Form.Item>
                          </Col>
                          <Col span={6}>
                            <Form.Item
                              label="商品材质"
                              labelAlign="right"
                              labelCol={{ xs: { span: 24 }, sm: { span: 8 } }}
                              wrapperCol={{ xs: { span: 24 }, sm: { span: 14 } }}
                            >
                              {getFieldDecorator(`productMaterial${index}`, {
                                initialValue: value.material,
                                rules: [
                                  {
                                    required: false,
                                    message: '请输入商品材质',
                                  },
                                  {
                                    max: 500,
                                    message: '请输入500位以下的商品材质',
                                  },
                                ],
                              })(
                                <Input allowClear disabled={isEdit} placeholder="请输入商品材质" />
                              )}
                            </Form.Item>
                          </Col>
                          <Col span={6}>
                            <Form.Item
                              label="商品链接"
                              labelAlign="right"
                              labelCol={{ xs: { span: 24 }, sm: { span: 8 } }}
                              wrapperCol={{ xs: { span: 24 }, sm: { span: 14 } }}
                            >
                              {getFieldDecorator(`productLink${index}`, {
                                initialValue: value.url,
                                rules: [
                                  {
                                    pattern: /^(((ht|f)tps?):\/\/)?[\w-]+(\.[\w-]+)+([\w.,@?^=%&:/~+#-]*[\w@?^=%&/~+#-])?$/,
                                    required: false,
                                    message: '请输入正确的商品链接',
                                  },
                                ],
                              })(
                                <Input allowClear disabled={isEdit} placeholder="请输入商品URL" />
                              )}
                            </Form.Item>
                          </Col>
                          <Col span={6}>
                            <Form.Item
                              label="SKU"
                              labelAlign="right"
                              labelCol={{ xs: { span: 24 }, sm: { span: 8 } }}
                              wrapperCol={{ xs: { span: 24 }, sm: { span: 14 } }}
                            >
                              {getFieldDecorator(`productSku${index}`, {
                                initialValue: value.sku,
                              })(<Input allowClear placeholder="请输入SKU" disabled={isEdit} />)}
                            </Form.Item>
                          </Col>
                          <Col span={6}>
                            <Form.Item
                              label="IMEI"
                              labelAlign="right"
                              labelCol={{ xs: { span: 24 }, sm: { span: 8 } }}
                              wrapperCol={{ xs: { span: 24 }, sm: { span: 14 } }}
                            >
                              {getFieldDecorator(`productImei${index}`, {
                                initialValue: value.imei,
                              })(<Input allowClear placeholder="请输入IMEI" disabled={isEdit} />)}
                            </Form.Item>
                          </Col>
                          {priceReg.test(value.price) &&
                            numReg.test(value.quantity) &&
                            currencyCode && (
                              <Col span={12}>
                                <Form.Item label={`申报价值（${currencyCode}）`} labelAlign="right">
                                  {getFieldDecorator(`productPrice${index}`, {
                                    initialValue: new Decimal(
                                      priceReg.test(value.price) ? value.price : 0
                                    )
                                      .mul(
                                        new Decimal(
                                          numReg.test(value.quantity) ? value.quantity : 0
                                        )
                                      )
                                      .toNumber(),
                                  })(<CustomerEachPrice />)}
                                </Form.Item>
                              </Col>
                            )}
                        </Row>
                      </Card>
                      <div
                        className="ant-badge ant-badge-not-a-wrapper reduce"
                        style={{
                          display: customsInfo.length > 1 ? null : 'none',
                        }}
                        onClick={() => this.delCustomsInfo(index)}
                      >
                        一
                      </div>
                    </div>
                  );
                })}
                <div style={{ textAlign: 'left' }}>
                  <Button
                    type="primary"
                    style={{ margin: '0px 32px' }}
                    onClick={this.addCustomsInfo}
                    disabled={isEdit}
                    icon={<PlusOutlined />}
                  >
                    新增报关信息
                  </Button>
                  <p className="ant-typographys">
                    申报总数量：{totalNum}&nbsp;&nbsp;申报总重量（g）：{totalWeight}
                    &nbsp;&nbsp;申报总价值（{currencyCode}）：{totalPrice}{' '}
                  </p>
                </div>
              </div>

              <div
                style={{
                  padding: '20px',
                  backgroundColor: '#fff',
                  borderRadius: '10px',
                  marginBottom: '20px',
                }}
              >
                <Row className="footer-itemDivs">
                  <Col span={14}>
                    <Form.Item
                      label="包装尺寸"
                      labelCol={{ xs: { span: 24 }, sm: { span: 4 } }}
                      wrapperCol={{ xs: { span: 24 }, sm: { span: 20 } }}
                      labelAlign="right"
                    >
                      <div style={{ display: 'flex', height: '30px', margin: '5px 0' }}>
                        {getFieldDecorator('length', {
                          initialValue: lengths,
                          rules: [
                            {
                              required: false,
                              message: '请输入长',
                            },
                          ],
                        })(<Input allowClear disabled={isEdit} prefix="长" suffix="cm" />)}
                        <span style={{ lineHeight: '30px', margin: '0 10px' }}>&nbsp;X&nbsp;</span>
                        {getFieldDecorator('width', {
                          initialValue: widths,
                          rules: [
                            {
                              required: false,
                              message: '请输入宽',
                            },
                          ],
                        })(<Input allowClear disabled={isEdit} prefix="宽" suffix="cm" />)}
                        <span style={{ lineHeight: '30px', margin: '0 10px' }}>&nbsp;X&nbsp;</span>
                        {getFieldDecorator('height', {
                          initialValue: heights,
                          rules: [
                            {
                              required: false,
                              message: '请输入高',
                            },
                          ],
                        })(<Input allowClear disabled={isEdit} prefix="高" suffix="cm" />)}
                      </div>
                    </Form.Item>
                  </Col>
                  <Col span={7}>
                    <Form.Item
                      label="收款到账日期"
                      labelAlign="right"
                      labelCol={{ xs: { span: 24 }, sm: { span: 8 } }}
                      wrapperCol={{ xs: { span: 24 }, sm: { span: 16 } }}
                    >
                      {getFieldDecorator('datePicker', {
                        initialValue: dateOfReceipt ? moment(dateOfReceipt) : '',
                        rules: [
                          {
                            required: false,
                            message: '请选择收款到账日期',
                          },
                        ],
                      })(
                        <DatePicker
                          style={{ width: '100%' }}
                          disabled={isEdit}
                          onChange={this.timeChange}
                        />
                      )}
                    </Form.Item>
                  </Col>
                </Row>
                <Row className="footer-itemDiv">
                  <Col span={14}>
                    <Form.Item
                      label="拣货单/备注"
                      labelAlign="right"
                      labelCol={{ xs: { span: 24 }, sm: { span: 4 } }}
                      wrapperCol={{ xs: { span: 24 }, sm: { span: 20 } }}
                    >
                      {getFieldDecorator('pickingOrders', {
                        initialValue: pickingOrders ? pickingOrders : '',
                        rules: [
                          {
                            required: false,
                            message: '请输入拣货单',
                          },
                          {
                            max: 150,
                            message: '请输入150位以下的拣货单/备注',
                          },
                        ],
                      })(
                        <TextArea
                          allowClear
                          disabled={isEdit}
                          placeholder="可添加拣货单信息及其他附加服务信息，最多输入150个字符"
                        />
                      )}
                    </Form.Item>
                  </Col>
                </Row>
              </div>

              <div className="w-full text-center bg-white rounded-xl px-3 py-5">
                <Space>
                  {isLock && (
                    <Button
                      type="primary"
                      disabled={btnLoading}
                      onClick={this.submitAction}
                      htmlType="submit"
                    >
                      保存
                    </Button>
                  )}
                  {id == undefined && (
                    <Button type="primary" disabled={btnLoading} onClick={this.clickStag}>
                      暂存
                    </Button>
                  )}
                  <AuthStatusNode authKey="bill:freightTrial">
                    <Button
                      disabled={btnLoading}
                      loading={chargeLoading}
                      onClick={this.chargePriceSimple}
                    >
                      金额试算
                    </Button>
                  </AuthStatusNode>
                  {isLock && (
                    <Button type="primary" onClick={() => window.history.back(-1)}>
                      取消
                    </Button>
                  )}
                  <AuthStatusNode authKey="bill:freightTrial">
                    <span>
                      预估费用总额
                      <span style={{ color: '#52c41a', fontSize: '25px', paddingInline: '8px' }}>
                        {moneyNum ? moneyNum : '--'}
                      </span>
                      RMB
                    </span>
                  </AuthStatusNode>
                </Space>
              </div>
            </Form>
          </div>
          {/*批量创建*/}
          <div
            style={{
              width: '100%',
              display: tabNumber === '2' ? '' : 'none',
            }}
            className="conten_batchDiv"
          >
            <div
              style={{
                padding: '20px 32px',
                backgroundColor: '#fff',
                borderRadius: '10px',
              }}
            >
              <Form {...formItemLayouts}>
                <Row>
                  <Col span={12}>
                    <StandardFormRow title="制单账号" block style={{ borderBottom: 'none' }}>
                      <Form.Item
                        style={{ marginRight: 70, display: countryDisabled ? 'none' : '' }}
                        className="form_item_bottom"
                      >
                        {getFieldDecorator('userId', {
                          initialValue: userId,
                          rules: [
                            {
                              required: true,
                              message: '请至少选择一个制单账号',
                            },
                          ],
                        })(
                          <SingleTagSelect
                            actionsText={actionsTextMap}
                            className="open"
                            expandable={shipperOpen}
                            checked={isHaveDeliveryAccount}
                            onChange={this.shipperChange}
                          >
                            {account &&
                              account.map((element, index) => (
                                <SingleTagSelect.Option key={index} value={element.accountCode}>
                                  {element.warehouseName ?? ''}
                                  {element.accountCode}
                                </SingleTagSelect.Option>
                              ))}
                          </SingleTagSelect>
                        )}
                      </Form.Item>
                      <Form.Item
                        style={{ marginRight: 70, display: countryDisabled ? '' : 'none' }}
                        className="form_item_bottom"
                      >
                        {getFieldDecorator('userId', {
                          initialValue: userId,
                          rules: [
                            {
                              required: true,
                              message: '请至少选择一个制单账号',
                            },
                          ],
                        })(
                          <SingleTagSelect
                            actionsText={actionsTextMap}
                            className="open"
                            expandable={shipperOpen}
                            checked={isHaveDeliveryAccount}
                            onChange={this.shipperChange}
                          >
                            {account &&
                              account.map((element, index) => (
                                <SingleTagSelect.Option key={index} value={element.accountCode}>
                                  {element.warehouseName ?? ''}
                                  {element.accountCode}
                                </SingleTagSelect.Option>
                              ))}
                          </SingleTagSelect>
                        )}
                      </Form.Item>
                    </StandardFormRow>
                    <Col span={24} style={{ marginBottom: '5px' }}>
                      <span style={{ color: 'red', marginRight: '10px' }}>*</span>上传Excel文件
                    </Col>
                    <Col span={24} style={{ display: 'flex' }}>
                      <Col span={24}>
                        <Form.Item
                          style={{ marginBottom: '16px' }}
                          labelCol={{ xs: { span: 24 }, sm: { span: 8 } }}
                          wrapperCol={{ xs: { span: 24 }, sm: { span: 14 } }}
                        >
                          {getFieldDecorator(
                            'channel',
                            {}
                          )(
                            <Upload {...props}>
                              <Col style={{ display: 'flex' }}>
                                <Button
                                  style={{ borderRadius: '4px 0 0 4px', borderRight: 'none' }}
                                >
                                  上传文件
                                </Button>
                                <Input
                                  readOnly={true}
                                  value={fileName}
                                  placeholder="请选择您的文件"
                                  style={{
                                    width: '280px',
                                    cursor: 'pointer',
                                    borderRadius: '0 4px 4px 0',
                                  }}
                                />
                              </Col>
                            </Upload>
                          )}
                        </Form.Item>
                      </Col>
                    </Col>
                    <Col span={12} style={{ textAlign: 'center' }}>
                      <Button
                        type="primary"
                        onClick={this.handleUpload}
                        disabled={fileList.length === 0}
                        loading={uploading}
                      >
                        {uploading ? 'Uploading' : '导入'}
                      </Button>
                    </Col>
                  </Col>
                  <Col span={8} style={{ border: '1px dashed #e3e3e3', padding: '15px 20px' }}>
                    <div className="">
                      <div style={{ marginBottom: '10px' }}>燕文模板</div>
                      <div className="template" style={{ display: 'flex', flexWrap: 'wrap' }}>
                        {EJFtemplates.map((value, index) => {
                          return (
                            <a
                              href="javascript:void(0)"
                              onClick={() => this.clickDownload(value.id)}
                              download={value.originalName}
                              className="template-list"
                              style={{ marginRight: '10px', textAlign: 'center' }}
                              key={index}
                            >
                              <Avatar
                                size={50}
                                icon={<IconFont type="icon-Microsoft-Excel" />}
                                style={{
                                  backgroundColor: '#fff',
                                  verticalAlign: 'middle',
                                  marginLeft: 20,
                                  border: '1px solid #ccc',
                                }}
                              />
                              <div
                                style={{
                                  color: '#52c41a',
                                  marginTop: 10,
                                  fontSize: 12,
                                  marginLeft: 20,
                                }}
                              >
                                {value.originalName}
                              </div>
                            </a>
                          );
                        })}
                      </div>
                    </div>
                  </Col>
                </Row>
              </Form>
            </div>
            <Row>
              <Col span={24} className="footer-tipItem" style={{ backgroundColor: '#fff' }}>
                <h2>【温馨提示】</h2>
                <div className="tipItem-content">
                  <h3>导入前注意事项：</h3>
                  1、请按照模板填写数据，不可变更模板，否则会导致订单导入失败。
                  <br />
                  2、表格内示例数据仅用于用户填写时参考，导入前需要删除。
                  <br />
                  3、模板表头"产品名称"可参考sheet2燕文产品名称搜索查看。
                  <br />
                  <br />
                  <h3>导入后注意事项：</h3>
                  1、导入后若有错误订单，请修改【导入错误记录】后重新上传
                  <br />
                  2、提交制单时遇报错"账户余额不足"，请查看余额是否充足且制单账号是否活动
                </div>
              </Col>
            </Row>
          </div>

          {/* 批量创建错误弹框 */}
          <Modal
            title="操作信息"
            visible={isModalVisible}
            onOk={() => {
              router.push('/smallBag/orderManagement/orderList');
            }}
            onCancel={() => {
              this.setState({ isModalVisible: false, fileList: [], fileName: '' });
            }}
            footer={[
              <Button
                key="back"
                onClick={() => {
                  this.setState({ isModalVisible: false, fileList: [], fileName: '' });
                }}
              >
                继续导入
              </Button>,
              <Button
                key="submit"
                type="primary"
                onClick={() => {
                  router.push('/smallBag/orderManagement/orderList');
                }}
              >
                去制单
              </Button>,
            ]}
          >
            <p>{uploadErrorMsg}</p>
            <p>{uploadErrorCon}</p>
          </Modal>
        </div>
      </PageContainerComponent>
    );
  }
}
const mapStateToProps = ({ exportNamespace, resource, loading, state }) => ({
  state,
  resource,
  loading,
  exportNamespace,
});
export default Form.create({})(connect(mapStateToProps)(CreateOrder));
