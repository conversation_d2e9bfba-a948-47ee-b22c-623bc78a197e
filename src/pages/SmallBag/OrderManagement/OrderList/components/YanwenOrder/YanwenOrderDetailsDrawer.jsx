import React, { useState, useImperativeHandle, useRef } from 'react';
import { Drawer, Tabs, Space, Button } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import DrawerOrderDetails from './DrawerOrderDetails';
import { router } from 'umi';
import DrawerLogisticsDetails from './DrawerLogisticsDetails';
import { useUpdateEffect } from 'ahooks';
import FreightInformation from './FreightInformation';
import { formatMessage } from 'umi-plugin-react/locale';
import { hongKongDHLOpenWindow } from '@/utils/utils';
import { hongKongDHLProductCode } from '@/utils/commonConstant';

const YanwenOrderDetailsDrawer = props => {
  const { modalRef } = props;
  const yanwenOrderRef = useRef();
  const logisticsRef = useRef();
  const freightRef = useRef();
  const [open, setOpen] = useState(false);
  const [tabActiveKey, setTabActiveKey] = useState('1');
  const [paramsInfo, setParamsInfo] = useState({});
  const [channelId, setChannelId] = useState('');
  useImperativeHandle(modalRef, () => ({
    open: params => {
      setOpen(true);
      setTabActiveKey('1');
      setParamsInfo({
        ...paramsInfo,
        ...params,
      });
    },
  }));

  useUpdateEffect(() => {
    setTimeout(() => {
      if (paramsInfo) {
        if (tabActiveKey === '1') {
          yanwenOrderRef.current?.getInit(paramsInfo);
        } else if (tabActiveKey === '2') {
          logisticsRef.current?.getInit(paramsInfo);
        } else {
          console.log(`paramsInfo=${paramsInfo}`);
          freightRef.current?.getInit(paramsInfo);
        }
      }
    }, 100);
  }, [tabActiveKey, paramsInfo]);

  useUpdateEffect(() => {
    if (yanwenOrderRef.current?.getChannelId()) {
      setChannelId(yanwenOrderRef.current?.getChannelId());
    }
  }, [yanwenOrderRef.current?.getChannelId()]);

  const tabItems = [
    {
      key: '1',
      label: formatMessage({ id: '订单信息' }),
    },
    {
      key: '2',
      label: formatMessage({ id: '物流信息' }),
    },
    {
      key: '3',
      label: formatMessage({ id: '运费信息' }),
    },
  ];

  const reset = () => {
    setOpen(false);
    setTabActiveKey('1');
    setParamsInfo({});
  };

  return (
    <Drawer
      open={open}
      title={
        <Space direction="vertical">
          <span>{formatMessage({ id: '订单详情' })}</span>
          <Tabs
            style={{ marginBottom: '-32px' }}
            items={tabItems}
            activeKey={tabActiveKey}
            onChange={key => {
              setTabActiveKey(key);
            }}
          />
        </Space>
      }
      headerStyle={{
        borderBottom: 'none',
        background: '#fff',
      }}
      destroyOnClose
      drawerStyle={{
        background: '#f9f5f5',
      }}
      width="50%"
      extra={
        <Space direction="vertical" align="end">
          <CloseOutlined
            onClick={() => {
              reset();
            }}
            className="cursor-pointer"
          />
          <Space>
            {hongKongDHLProductCode?.includes(channelId) && (
              <Button type="primary" onClick={hongKongDHLOpenWindow}>
                下载发票
              </Button>
            )}
            {tabActiveKey === '1' && (
              <Button
                type="primary"
                onClick={() => {
                  yanwenOrderRef.current?.clickCopy();
                }}
              >
                {formatMessage({ id: '一键复制' })}
              </Button>
            )}
            {tabActiveKey === '2' && (
              <Button
                type={'primary'}
                onClick={() => {
                  router.push(
                    `/smallBag/orderManagement/proofApply?expressCode=${paramsInfo?.expressCode}&expressType=0`
                  );
                }}
              >
                {formatMessage({ id: '证明申请' })}
              </Button>
            )}
          </Space>
        </Space>
      }
      onClose={reset}
      closable={false}
    >
      {tabActiveKey === '1' && (
        <DrawerOrderDetails {...props} modalRef={yanwenOrderRef} reset={reset} />
      )}
      {tabActiveKey === '2' && <DrawerLogisticsDetails {...props} modalRef={logisticsRef} />}
      {tabActiveKey === '3' && <FreightInformation {...props} modalRef={freightRef} />}
    </Drawer>
  );
};

export default YanwenOrderDetailsDrawer;
