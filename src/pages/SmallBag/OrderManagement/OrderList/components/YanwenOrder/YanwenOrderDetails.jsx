import React, { Component } from 'react';
import { connect } from 'dva';
import '@ant-design/compatible/assets/index.css';
import { Button, Descriptions, Divider, Typography } from 'antd';
import PageContainerComponent from '@/components/PageContainer';
import PageHeaderBreadcrumb from '@/components/PageHeaderBreadcrumb';
import './YanwenOrderDetails.less';
import { router } from 'umi';
import { Decimal } from 'decimal.js';
import { temuShowList } from '@/utils/commonConstant';

const { Text } = Typography;

@connect(({ Order }) => ({}))
class YanwenOrderDetails extends Component {
  constructor(props) {
    super(props);
    this.state = {
      receiverInfo: {}, // 收件人信息
      poPStation: {}, // 自提点信息
      parcelInfo: {}, // 货物信息
      senderInfo: {}, // 发件人税号
      channel: '', // 产品信息
      importCustomsInfo: {}, // 进口清关信息
      countryName: '', // 国家名
      productList: [], // 货物信息
      orderNumber: '', // 订单号
      channelId: '', //产品号
      handoverCode: '', // 海外交货地
      salesPlatform: '', // 销售平台
      companyCode: '', // 交货仓一键复制使用
      warehouseName: '', // 交货仓页面展示使用
      dateOfReceipt: '', // 到账日期
      pickingOrders: '', // 拣货单
      oldExpressCode: '', // 原单号
      newExpressCode: '', // 新单号
      keyCopyData: {}, // 一键复制数据
    };
  }

  // 页面加载完毕执行
  componentDidMount() {
    document.documentElement.scrollTop = 0;
    const { dispatch } = this.props;
    const { userId, expressCode } = this.props.history.location.query;
    let params = {
      userId: userId,
      waybillNumber: expressCode,
    };

    dispatch({
      type: 'Order/getOrderDetails',
      payload: params,
      callback: result => {
        if (result.success) {
          this.setState({
            receiverInfo: result.data.receiverInfo,
            parcelInfo: result.data.parcelInfo,
            poPStation: result.data.poPStation,
            senderInfo: result.data.senderInfo,
            importCustomsInfo: result.data.importCustomsInfo,
            channel: result.data.channel.nameCh,
            channelId: result.data.channel.id,
            countryName: result.data.receiverInfo.countryInfo.nameCh,
            productList: result.data.parcelInfo.productList,
            orderNumber: result.data.orderNumber,
            handoverCode: result.data.handoverCode,
            dateOfReceipt: result.data.dateOfReceipt,
            pickingOrders: result.data.remark,
            salesPlatform: result.data.salesPlatform,
            oldExpressCode: result.data.oldExpressCode,
            newExpressCode: result.data.newExpressCode,
            warehouseName: result.data.warehouse.name,
            transactionNumber: result.data.transactionNumber,
            yanwenOrderNumber: result.data.yanwenOrderNumber,
          });
          let param = {
            receiverInfo: result.data.receiverInfo,
            importCustomsInfo: result.data.importCustomsInfo,
            parcelInfo: {
              currency: result.data.parcelInfo.currency,
              totalPrice: result.data.parcelInfo.totalPrice,
              totalQuantity: result.data.parcelInfo.totalQuantity,
              totalWeight: result.data.parcelInfo.totalWeight,
              height: result.data.parcelInfo.height,
              width: result.data.parcelInfo.width,
              length: result.data.parcelInfo.length,
              pickingOrders: result.data.remark,
              productList: result.data.parcelInfo.productList,
              isBattery: result.data.parcelInfo.hasBattery,
              IOSS: result.data.parcelInfo.ioss,
            },
            poPStation: result.data.poPStation,
            senderInfo: result.data.senderInfo,
            channelId: result.data.channelId,
            userId: [result.data.userId],
            orderNumber: result.data.orderNumber,
            handoverCode: result.data.handoverCode,
            companyCode: result.data.companyCode,
            dateOfReceipt: result.data.dateOfReceipt,
            salesPlatform: result.data.salesPlatform,
          };
          this.setState({
            keyCopyData: param,
          });
        }
      },
    });
  }

  desensitizeString = function(str) {
    if (!str || str.length <= 4) {
      // 字符串长度小于等于4时无法按规则脱敏
      return str;
    }
    return str.substring(0, 2) + '*****' + str.substring(str.length - 2);
  };

  straightHairBusiness = () => {
    this.props.history.push('/smallBag/orderManagement/creatOrder');
  };

  clickCopy = () => {
    sessionStorage.setItem('copys', JSON.stringify(this.state.keyCopyData));
    this.props.history.push('/smallBag/orderManagement/creatOrder?type=add');
  };

  render() {
    const {
      details,
      channelId,
      channel,
      transactionNumber,
      yanwenOrderNumber,
      receiverInfo,
      poPStation,
      parcelInfo,
      senderInfo,
      countryName,
      productList,
      orderNumber,
      handoverCode,
      importCustomsInfo,
      warehouseName,
      salesPlatform,
      dateOfReceipt,
      pickingOrders,
      oldExpressCode,
      newExpressCode,
    } = this.state;
    const { userId, expressCode } = this.props.history.location.query;
    return (
      <PageContainerComponent
        header={{
          title: null,
          breadcrumb: {},
          breadcrumbRender: props => (
            <PageHeaderBreadcrumb {...props} col>
              <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: '16px' }}>
                <div style={{ display: 'flex' }}>
                  <Button
                    type="primary"
                    style={{ marginRight: '20px' }}
                    onClick={() => this.clickCopy()}
                  >
                    一键复制
                  </Button>
                  <Button onClick={() => router.push(`/smallBag/orderManagement/orderList`)}>
                    返回
                  </Button>
                </div>
              </div>
            </PageHeaderBreadcrumb>
          ),
        }}
      >
        <div style={{ backgroundColor: '#fff', padding: '30px', borderRadius: '10px' }}>
          <div className="conten_singleDiv">
            <Descriptions title="制单账号">
              <Descriptions.Item label="制单账号">{userId}</Descriptions.Item>
            </Descriptions>
            <Divider />
            <Descriptions title="运单信息">
              <Descriptions.Item label="交货仓">{warehouseName}</Descriptions.Item>
              <Descriptions.Item label="订单号">{orderNumber}</Descriptions.Item>
              <Descriptions.Item label="目的国">{countryName}</Descriptions.Item>
              <Descriptions.Item label="产品名称">{channel}</Descriptions.Item>
              <Descriptions.Item label="运单号">{expressCode}</Descriptions.Item>
              {temuShowList.includes(channelId) && (
                <Descriptions.Item label="海外交货地">{handoverCode}</Descriptions.Item>
              )}
              <Descriptions.Item label="平台交易号">{transactionNumber}</Descriptions.Item>
              <Descriptions.Item label="流水号">{yanwenOrderNumber}</Descriptions.Item>
              <Descriptions.Item label="币种类型">{parcelInfo.currency}</Descriptions.Item>
              {oldExpressCode && newExpressCode && (
                <Descriptions.Item label={oldExpressCode == expressCode ? '新单号' : '原单号'}>
                  {oldExpressCode == expressCode ? newExpressCode : oldExpressCode}
                </Descriptions.Item>
              )}
              <Descriptions.Item label="是否含电">
                {Number(parcelInfo.hasBattery) ? '是' : '否'}
              </Descriptions.Item>
              <Descriptions.Item label="IOSS税号">
                {this.desensitizeString(parcelInfo.ioss)}
              </Descriptions.Item>
              <Descriptions.Item label="销售平台">{this.salesPlatform}</Descriptions.Item>
            </Descriptions>
            <Divider />
            <Descriptions title="收件人信息">
              <Descriptions.Item label="姓名">{receiverInfo.name}</Descriptions.Item>
              <Descriptions.Item label="公司">{receiverInfo.company}</Descriptions.Item>
              <Descriptions.Item label="自提点ID">{poPStation?.pointId}</Descriptions.Item>
              <Descriptions.Item label="电话">{receiverInfo.phone}</Descriptions.Item>
              <Descriptions.Item label="省/州">{receiverInfo.state}</Descriptions.Item>
              <Descriptions.Item label="城市">{receiverInfo.city}</Descriptions.Item>
              <Descriptions.Item label="邮箱">{receiverInfo.email}</Descriptions.Item>
              <Descriptions.Item label="邮编">{receiverInfo.zipCode}</Descriptions.Item>
              <Descriptions.Item label="税号">{receiverInfo.taxNumber}</Descriptions.Item>
              <Descriptions.Item label="PINFL号码">{receiverInfo.pinflNumber}</Descriptions.Item>
            </Descriptions>
            <Descriptions>
              <Descriptions.Item label="地址">{receiverInfo.address}</Descriptions.Item>
            </Descriptions>
            <Descriptions>
              <Descriptions.Item label="门牌号">{receiverInfo.houseNumber}</Descriptions.Item>
            </Descriptions>
            <Divider />
            <Descriptions title="发件人信息">
              <Descriptions.Item label="发件人税号">
                {this.desensitizeString(senderInfo.taxNumber)}
              </Descriptions.Item>
              <Descriptions.Item label="CSP">
                {importCustomsInfo?.taxPolicyExtends?.csp}
              </Descriptions.Item>
              <Descriptions.Item label="发件人姓名">{senderInfo.name}</Descriptions.Item>
              <Descriptions.Item label="发件人公司">{senderInfo.company}</Descriptions.Item>
              <Descriptions.Item label="发件人电话">{senderInfo.phone}</Descriptions.Item>
              <Descriptions.Item label="发件人邮箱">{senderInfo.email}</Descriptions.Item>
              <Descriptions.Item label="发件人邮编">{senderInfo.zipCode}</Descriptions.Item>
              <Descriptions.Item label="发件人国家">{senderInfo.countryName}</Descriptions.Item>
              <Descriptions.Item label="发件人州(省)">{senderInfo.state}</Descriptions.Item>
              <Descriptions.Item label="发件人城市">{senderInfo.city}</Descriptions.Item>
              <Descriptions.Item label="发件人地址">{senderInfo.address}</Descriptions.Item>
              <Descriptions.Item label="发件人门牌号">{senderInfo.houseNumber}</Descriptions.Item>
            </Descriptions>
            <Divider />
            {productList &&
              productList.map((val, index) => {
                const priceReg = /^([1-9]\d{0,15}\.\d{1,2}|0\.\d{1,2}|[1-9]\d{0,15})$/;
                const numReg = /^\d+$|^\d+[.]?\d+$/;
                return (
                  <Descriptions title={'报关信息' + (index + 1)}>
                    <Descriptions.Item label="中文品名">{val.goodsNameCh}</Descriptions.Item>
                    <Descriptions.Item label="英文品名">{val.goodsNameEn}</Descriptions.Item>
                    <Descriptions.Item label="商品海关编码">{val.hscode}</Descriptions.Item>
                    <Descriptions.Item label="单品重量(g)">{val.weight}</Descriptions.Item>
                    <Descriptions.Item label={`申报单价(${parcelInfo.currency})`}>
                      {val.price}
                    </Descriptions.Item>
                    <Descriptions.Item label="单票数量">{val.quantity}</Descriptions.Item>
                    <Descriptions.Item label="商品材质">{val.material}</Descriptions.Item>
                    <Descriptions.Item label="商品链接">{val.url}</Descriptions.Item>
                    <Descriptions.Item label="SKU">{val.sku}</Descriptions.Item>
                    <Descriptions.Item label="IMEI">{val.imei}</Descriptions.Item>
                    <Descriptions.Item span={2} label={`申报价值(${parcelInfo.currency})`}>
                      {new Decimal(priceReg.test(val.price) ? val.price : 0)
                        .mul(new Decimal(numReg.test(val.quantity) ? val.quantity : 0))
                        .toNumber()}{' '}
                    </Descriptions.Item>
                  </Descriptions>
                );
              })}
            <Divider />
            <Text>
              <span
                style={{
                  display: 'inline-block',
                  width: '120px',
                  textAlign: 'left',
                  marginBottom: '10px',
                }}
              >
                申报总数量：
              </span>
              {parcelInfo && parcelInfo.totalQuantity ? parcelInfo.totalQuantity : 0}；
              &nbsp;&nbsp;申报总重量（g）：
              {parcelInfo && parcelInfo.totalWeight ? parcelInfo.totalWeight : 0}；
              &nbsp;&nbsp;申报总价值（{parcelInfo.currency}）：
              {parcelInfo && parcelInfo.totalPrice ? parcelInfo.totalPrice : 0}；
            </Text>
            <Descriptions>
              <Descriptions.Item label="拣货单/备注">{pickingOrders}</Descriptions.Item>
            </Descriptions>
            <Descriptions>
              <Descriptions.Item
                label="包装尺寸"
                style={{
                  display:
                    (parcelInfo.length && parcelInfo.length != '0') ||
                    (parcelInfo.width && parcelInfo.width != '0') ||
                    (parcelInfo.height && parcelInfo.height != '0')
                      ? ''
                      : 'none',
                }}
              >
                {(parcelInfo && parcelInfo.length ? parcelInfo.length : 0) +
                  'x' +
                  (parcelInfo && parcelInfo.width ? parcelInfo.width : 0) +
                  'x' +
                  (parcelInfo && parcelInfo.height ? parcelInfo.height : 0)}
                &nbsp;&nbsp;(长x宽x高)
              </Descriptions.Item>
              <Descriptions.Item
                label="包装尺寸"
                style={{
                  display:
                    !parcelInfo.length && !parcelInfo.width && !parcelInfo.height ? '' : 'none',
                }}
              ></Descriptions.Item>
              <Descriptions.Item
                label="包装尺寸"
                style={{
                  display:
                    parcelInfo.length === '0' &&
                    parcelInfo.width === '0' &&
                    parcelInfo.height === '0'
                      ? ''
                      : 'none',
                }}
              ></Descriptions.Item>
            </Descriptions>
            <Descriptions>
              <Descriptions.Item label="收款到账日期">
                {dateOfReceipt ? dateOfReceipt.split(' ')[0] : ''}
              </Descriptions.Item>
            </Descriptions>
          </div>
        </div>
      </PageContainerComponent>
    );
  }
}

export default YanwenOrderDetails;
