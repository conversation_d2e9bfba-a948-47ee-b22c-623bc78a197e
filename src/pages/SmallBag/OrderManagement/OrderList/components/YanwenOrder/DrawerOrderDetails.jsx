import React, { useState, useImperativeHandle } from 'react';
import { useMount, useUnmount, useUpdateEffect } from 'ahooks';
// import './YanwenOrderDetails.less';
import { router } from 'umi';
import { Descriptions, Divider, Typography, Card } from 'antd';
import { Decimal } from 'decimal.js';
import { temuShowList } from '@/utils/commonConstant';

const { Text } = Typography;

/**
 * Renders a drawer component for displaying order details.
 *
 * @param {function} dispatch - The Redux dispatch function.
 * @param {string} userId - The ID of the user.
 * @param {string} expressCode - The express code.
 * @return {JSX.Element} The rendered drawer component.
 */
const DrawerOrderDetails = ({ dispatch, modalRef, reset, getOrderDetailsLoading }) => {
  const [userId, setUserId] = useState('');
  const [expressCode, setExpressCode] = useState('');
  const [handoverCode, setHandoverCode] = useState('');
  const [receiverInfo, setReceiverInfo] = useState({});
  const [poPStation, setPoPStation] = useState({});
  const [parcelInfo, setParcelInfo] = useState({});
  const [senderInfo, setSenderInfo] = useState({});
  const [importCustomsInfo, setImportCustomsInfo] = useState({});
  const [channel, setChannel] = useState('');
  const [channelId, setChannelId] = useState('');
  const [countryName, setCountryName] = useState('');
  const [productList, setProductList] = useState([]);
  const [orderNumber, setOrderNumber] = useState('');
  const [salesPlatform, setSalesPlatform] = useState('');
  const [warehouseName, setWarehouseName] = useState('');
  const [dateOfReceipt, setDateOfReceipt] = useState('');
  const [pickingOrders, setPickingOrders] = useState('');
  const [oldExpressCode, setOldExpressCode] = useState('');
  const [newExpressCode, setNewExpressCode] = useState('');
  const [transactionNumber, setTransactionNumber] = useState('');
  const [yanwenOrderNumber, setYanwenOrderNumber] = useState('');
  const [keyCopyData, setKeyCopyData] = useState({});

  useImperativeHandle(modalRef, () => ({
    getInit: params => {
      setUserId(params.userId);
      setExpressCode(params.expressCode);
    },
    getList: () => {
      init();
    },
    clickCopy,
    getChannelId: () => {
      return channelId;
    },
  }));

  const init = function() {
    const params = {
      userId: userId,
      waybillNumber: expressCode,
    };
    dispatch({
      type: 'Order/getOrderDetails',
      payload: params,
      callback: result => {
        if (result.success) {
          setReceiverInfo(result.data.receiverInfo);
          setHandoverCode(result.data.handoverCode);
          setPoPStation(result.data.poPStation);
          setParcelInfo(result.data.parcelInfo);
          setSenderInfo(result.data.senderInfo);
          setImportCustomsInfo(result.data.importCustomsInfo);
          setChannel(result.data.channel.nameCh);
          setChannelId(result.data.channel.id);
          setCountryName(result.data.receiverInfo.countryInfo.nameCh);
          setProductList(result.data.parcelInfo.productList);
          setOrderNumber(result.data.orderNumber);
          setSalesPlatform(result.data.salesPlatform);
          setDateOfReceipt(result.data.dateOfReceipt);
          setPickingOrders(result.data.remark);
          setOldExpressCode(result.data.oldExpressCode);
          setNewExpressCode(result.data.newExpressCode);
          setWarehouseName(result.data.warehouse.name);
          setTransactionNumber(result.data.transactionNumber);
          setYanwenOrderNumber(result.data.yanwenOrderNumber);
          let param = {
            receiverInfo: result.data.receiverInfo,
            importCustomsInfo: result.data.importCustomsInfo,
            parcelInfo: {
              currency: result.data.parcelInfo.currency,
              totalPrice: result.data.parcelInfo.totalPrice,
              totalQuantity: result.data.parcelInfo.totalQuantity,
              totalWeight: result.data.parcelInfo.totalWeight,
              height: result.data.parcelInfo.height,
              width: result.data.parcelInfo.width,
              length: result.data.parcelInfo.length,
              pickingOrders: result.data.remark,
              productList: result.data.parcelInfo.productList,
              isBattery: result.data.parcelInfo.hasBattery,
              IOSS: result.data.parcelInfo.ioss,
            },
            senderInfo: {
              senderTaxNumber: result.data.senderInfo.taxNumber,
            },
            poPStation: result.data.poPStation,
            channelId: result.data.channelId,
            userId: [result.data.userId],
            orderNumber: result.data.orderNumber,
            companyCode: result.data.companyCode,
            dateOfReceipt: result.data.dateOfReceipt,
          };
          setKeyCopyData({
            ...keyCopyData,
            ...param,
          });
        }
      },
    });
  };

  useUpdateEffect(() => {
    if (userId && expressCode) {
      init();
    }
  }, [userId, expressCode]);

  useUnmount(() => {
    setReceiverInfo({});
    setPoPStation({});
    setParcelInfo({});
    setSenderInfo({});
    setImportCustomsInfo({});
    setChannel('');
    setChannelId('');
    setCountryName('');
    setProductList([]);
    setOrderNumber('');
    setTransactionNumber('');
    setYanwenOrderNumber('');
    setWarehouseName('');
    setDateOfReceipt('');
    setPickingOrders('');
    setOldExpressCode('');
    setNewExpressCode('');
    setUserId('');
    setExpressCode('');
    setKeyCopyData({});
  });

  const desensitizeString = function(str) {
    if (!str || str.length <= 4) {
      // 字符串长度小于等于4时无法按规则脱敏
      return str;
    }
    return str.substring(0, 2) + '*****' + str.substring(str.length - 2);
  };

  const clickCopy = () => {
    sessionStorage.setItem('copys', JSON.stringify(keyCopyData));
    router.push('/smallBag/orderManagement/creatOrder?type=add');
    reset();
  };

  return (
    <Card loading={getOrderDetailsLoading}>
      {/* className="conten_singleDiv" */}
      <div>
        <Descriptions title="制单账号">
          <Descriptions.Item label="制单账号">{userId}</Descriptions.Item>
        </Descriptions>
        <Divider />
        <Descriptions title="运单信息">
          <Descriptions.Item label="交货仓">{warehouseName}</Descriptions.Item>
          <Descriptions.Item label="订单号">{orderNumber}</Descriptions.Item>
          <Descriptions.Item label="目的国">{countryName}</Descriptions.Item>
          <Descriptions.Item label="产品名称">{channel}</Descriptions.Item>
          <Descriptions.Item label="运单号">{expressCode}</Descriptions.Item>
          {temuShowList.includes(channelId) && (
            <Descriptions.Item label="海外交货地">{handoverCode}</Descriptions.Item>
          )}
          <Descriptions.Item label="平台交易号">{transactionNumber}</Descriptions.Item>
          <Descriptions.Item label="流水号">{yanwenOrderNumber}</Descriptions.Item>
          <Descriptions.Item label="币种类型">{parcelInfo.currency}</Descriptions.Item>
          {oldExpressCode && newExpressCode && (
            <Descriptions.Item label={oldExpressCode == expressCode ? '新单号' : '原单号'}>
              {oldExpressCode == expressCode ? newExpressCode : oldExpressCode}
            </Descriptions.Item>
          )}
          <Descriptions.Item label="是否含电">
            {Number(parcelInfo.hasBattery) ? '是' : '否'}
          </Descriptions.Item>
          <Descriptions.Item label="IOSS税号">
            {desensitizeString(parcelInfo.ioss)}
          </Descriptions.Item>
          <Descriptions.Item label="销售平台">{salesPlatform}</Descriptions.Item>
        </Descriptions>
        <Divider />
        <Descriptions title="收件人信息">
          <Descriptions.Item label="姓名">{receiverInfo.name}</Descriptions.Item>
          <Descriptions.Item label="公司">{receiverInfo.company}</Descriptions.Item>
          <Descriptions.Item label="自提点ID">{poPStation?.pointId}</Descriptions.Item>
          <Descriptions.Item label="电话">{receiverInfo.phone}</Descriptions.Item>
          <Descriptions.Item label="省/州">{receiverInfo.state}</Descriptions.Item>
          <Descriptions.Item label="城市">{receiverInfo.city}</Descriptions.Item>
          <Descriptions.Item label="邮箱">{receiverInfo.email}</Descriptions.Item>
          <Descriptions.Item label="邮编">{receiverInfo.zipCode}</Descriptions.Item>
          <Descriptions.Item label="税号">{receiverInfo.taxNumber}</Descriptions.Item>
          <Descriptions.Item label="PINFL号码">{receiverInfo.pinflNumber}</Descriptions.Item>
        </Descriptions>
        <Descriptions>
          <Descriptions.Item label="地址">{receiverInfo.address}</Descriptions.Item>
        </Descriptions>
        <Descriptions>
          <Descriptions.Item label="门牌号">{receiverInfo.houseNumber}</Descriptions.Item>
        </Descriptions>
        <Divider />
        <Descriptions title="发件人信息">
          <Descriptions.Item label="发件人税号">
            {desensitizeString(senderInfo.taxNumber)}
          </Descriptions.Item>
          <Descriptions.Item label="CSP">
            {importCustomsInfo?.taxPolicyExtends?.csp}
          </Descriptions.Item>
          <Descriptions.Item label="发件人姓名">{senderInfo.name}</Descriptions.Item>
          <Descriptions.Item label="发件人公司">{senderInfo.company}</Descriptions.Item>
          <Descriptions.Item label="发件人电话">{senderInfo.phone}</Descriptions.Item>
          <Descriptions.Item label="发件人邮箱">{senderInfo.email}</Descriptions.Item>
          <Descriptions.Item label="发件人邮编">{senderInfo.zipCode}</Descriptions.Item>
          <Descriptions.Item label="发件人国家">{senderInfo.countryName}</Descriptions.Item>
          <Descriptions.Item label="发件人州(省)">{senderInfo.state}</Descriptions.Item>
          <Descriptions.Item label="发件人城市">{senderInfo.city}</Descriptions.Item>
          <Descriptions.Item label="发件人地址">{senderInfo.address}</Descriptions.Item>
          <Descriptions.Item label="发件人门牌号">{senderInfo.houseNumber}</Descriptions.Item>
        </Descriptions>
        <Divider />
        {productList &&
          productList.map((val, index) => {
            const priceReg = /^([1-9]\d{0,15}\.\d{1,2}|0\.\d{1,2}|[1-9]\d{0,15})$/;
            const numReg = /^\d+$|^\d+[.]?\d+$/;
            return (
              <Descriptions title={'报关信息' + (index + 1)}>
                <Descriptions.Item label="中文品名">{val.goodsNameCh}</Descriptions.Item>
                <Descriptions.Item label="英文品名">{val.goodsNameEn}</Descriptions.Item>
                <Descriptions.Item label="商品海关编码">{val.hscode}</Descriptions.Item>
                <Descriptions.Item label="单品重量(g)">{val.weight}</Descriptions.Item>
                <Descriptions.Item label={`申报单价（${parcelInfo.currency}）`}>
                  {val.price}
                </Descriptions.Item>
                <Descriptions.Item label="单票数量">{val.quantity}</Descriptions.Item>
                <Descriptions.Item label="商品材质">{val.material}</Descriptions.Item>
                <Descriptions.Item label="商品链接">{val.url}</Descriptions.Item>
                <Descriptions.Item label="SKU">{val.sku}</Descriptions.Item>
                <Descriptions.Item label="IMEI">{val.imei}</Descriptions.Item>
                <Descriptions.Item label={`申报价值（${parcelInfo.currency}）`}>
                  {new Decimal(priceReg.test(val.price) ? val.price : 0)
                    .mul(new Decimal(numReg.test(val.quantity) ? val.quantity : 0))
                    .toNumber()}
                </Descriptions.Item>
              </Descriptions>
            );
          })}
        <Divider />
        <Text>
          <span
            style={{
              display: 'inline-block',
              width: '120px',
              textAlign: 'left',
              marginBottom: '10px',
            }}
          >
            申报总数量：
          </span>
          {parcelInfo && parcelInfo.totalQuantity ? parcelInfo.totalQuantity : 0}；
          &nbsp;&nbsp;申报总重量（g）：
          {parcelInfo && parcelInfo.totalWeight ? parcelInfo.totalWeight : 0}；
          &nbsp;&nbsp;申报总价值（{parcelInfo.currency}）：
          {parcelInfo && parcelInfo.totalPrice ? parcelInfo.totalPrice : 0}；
        </Text>
        <Descriptions>
          <Descriptions.Item label="拣货单/备注">{pickingOrders}</Descriptions.Item>
        </Descriptions>
        <Descriptions>
          <Descriptions.Item
            label="包装尺寸"
            style={{
              display:
                (parcelInfo.length && parcelInfo.length != '0') ||
                (parcelInfo.width && parcelInfo.width != '0') ||
                (parcelInfo.height && parcelInfo.height != '0')
                  ? ''
                  : 'none',
            }}
          >
            {(parcelInfo && parcelInfo.length ? parcelInfo.length : 0) +
              'x' +
              (parcelInfo && parcelInfo.width ? parcelInfo.width : 0) +
              'x' +
              (parcelInfo && parcelInfo.height ? parcelInfo.height : 0)}
            &nbsp;&nbsp;(长x宽x高)
          </Descriptions.Item>
          <Descriptions.Item
            label="包装尺寸"
            style={{
              display: !parcelInfo.length && !parcelInfo.width && !parcelInfo.height ? '' : 'none',
            }}
          ></Descriptions.Item>
          <Descriptions.Item
            label="包装尺寸"
            style={{
              display:
                parcelInfo.length === '0' && parcelInfo.width === '0' && parcelInfo.height === '0'
                  ? ''
                  : 'none',
            }}
          ></Descriptions.Item>
        </Descriptions>
        <Descriptions>
          <Descriptions.Item label="收款到账日期">
            {dateOfReceipt ? dateOfReceipt.split(' ')[0] : ''}
          </Descriptions.Item>
        </Descriptions>
      </div>
    </Card>
  );
};

export default DrawerOrderDetails;
