import React, { Component } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import Link from 'umi/link';
import {
  Button,
  Checkbox,
  Col,
  DatePicker,
  Dropdown,
  Image,
  Input,
  Menu,
  message,
  Modal,
  Popconfirm,
  Radio,
  Row,
  Select,
  Space,
  Table,
  Tabs,
  notification,
} from 'antd';
import { DownOutlined } from '@ant-design/icons';
import router from 'umi/router';
import './YanwenOrderMakingInquiry.less';
import { connect } from 'dva';
import StandardFormRow from '@/components/StandardFormRow';
import SingleTagSelect from '@/components/SingleTagSelect';
import OrderInquiryTabs from './YanwenOrderInquiryTabs';
import PlamodalInfo from '../PlatformOrder/PlatformModalInfo';
import moment from 'moment';
import { downloadFile } from '@/utils/download';
import loadPinYinInit from '@/utils/ChineseHelper';
import YanwenOrderDetailsDrawer from './YanwenOrderDetailsDrawer';
import { formatMessage } from 'umi-plugin-react/locale';
import AuthStatusNode from '@/components/AuthStatusNode';
import { ProTable } from '@ant-design/pro-components';
import { changeColumnState } from '@/utils/utils';
import CustomerRangePicker from '@/components/CustomerRangePicker';
import { logSave, LogType } from '@/utils/logSave';

const { TextArea } = Input;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;
const printType = {
  LC: formatMessage({ id: '报关签条' }),
  LI: formatMessage({ id: '拣货单' }),
  LCI: `${formatMessage({ id: '报关签条' })}&${formatMessage({ id: '拣货单' })}`,
};
const timeStatus = undefined;
@Form.create()
@connect(({ Order, PlatformShopify, anomalyOrder, user, loading }) => ({
  Order,
  anomalyOrder,
  signatureLoading: loading.effects['Order/getSignature'],
  printLabelLoading: loading.effects['Order/getPrintLabelData'],
  downloadSignatureLoading: loading.effects['Order/downloadSignature'],
  authorizedLoading: loading.effects['user/qryMerchantState'],
  getOrderDetailsLoading: loading.effects['Order/getOrderDetails'],
  getCheckPointLoading: loading.effects['Order/getCheckPoint'],
  getNumberListLoading: loading.effects['bill/getNumberList'],
}))
class YanwenOrderMakingInquiry extends Component {
  constructor(props) {
    super(props);
    this.state = {
      HKShow: false,
      boxImageVisible: false,
      boxImage: [],
      account: [], // {formatMessage({id: '制单账号'})}
      userId: '', // {formatMessage({id: '选中的制单账号'})}
      shipperInitOpt: [],
      isExpanded: false,
      createModalShow: false,
      modalTitle: formatMessage({ id: '制单信息' }),
      quantityPrinted: 0, //已打印运单数量
      // ----------------
      tabNumber: '9',
      // ===============
      unCreate: '0',
      created: '0',
      cancelCreate: '0',
      createFail: '0',
      tempStash: '0',
      isPrint: true,
      loading: true,
      dataSource: [], // {formatMessage({id: '列表数据'})}
      total: 0,
      currentPage: 1, // {formatMessage({id: '页码'})}
      pageSize: 10,
      childrenData: {},
      printTypeName: formatMessage({ id: '报关签条' }),
      printTypeKey: 'LC',
      paperType: 'A4',
      channels: [],
      selectedRows: [],
      selectedRowKeys: [],
      batchMakeOrderLoading: false,
      countriesData: [],
      productCode: '', // {formatMessage({id: '选中的产品'})}
      countriesId: '', // {formatMessage({id: '选中的国家'})}
      isPrints: undefined, // {formatMessage({id: '打印状态'})}
      startTimes: undefined, // {formatMessage({id: '开始时间'})}
      endTimes: undefined, // {formatMessage({id: '结束时间'})}
      awbNum: [], // {formatMessage({id: '输入的运单号'})}
      loadingButton: [false],
      queryState: true, // {formatMessage({id: '查询状态'})}
      voucherNum: '', // {formatMessage({id: '制单数量'})}
      noVoucherNum: '', // {formatMessage({id: '未制单数量'})}
      confirmDeliveryNum: '', // {formatMessage({id: '确认发货数量'})}
      haveGoods: '', // {formatMessage({id: '已收货的数量'})}
      deliveryNum: '', // {formatMessage({id: '已发货的数量'})}
      delieveredNum: '', // {formatMessage({id: '已妥投的数量'})}
      cancelNum: '', // {formatMessage({id: '已取消数量'})}
      interceptNum: '', // {formatMessage({id: '已截留数量'})}
      sendAbnormal: '', // {formatMessage({id: '派送异常数量'})}
      productNum: '', // {formatMessage({id: '退件'})}/{formatMessage({id: '异常数量'})}
      makeFailureNum: '', // {formatMessage({id: '制单失败的数量'})}
      totalNum: '', // {formatMessage({id: '总数量'})}
      checkedNum: 0, // {formatMessage({id: '选中的申请截留的数量'})}
      visible: false, //{formatMessage({id: '弹窗显示'})}
      valNumber: 104, //{formatMessage({id: '弹窗选择值'})}
      MFvisable: false, //{formatMessage({id: '备注选择可见'})}
      currentAuthorityValue: null, // {formatMessage({id: '商户状态'})}
      value: false,
      isCancelWayBill: false, // {formatMessage({id: '取消订单弹框是否显示'})}
      cancelOrderReason: '',
      confirmLoading: false, // {formatMessage({id: '取消订单确定按钮'})}buttonloading
      // printLabelLoading: false, // {formatMessage({id: '打印标签'})}buttonloading
      HKPrintLabelLoading: false, // 打印揽收面单loading
      isPrintLabel: false, // {formatMessage({id: '打印标签弹框展示'})}
      HKPrintLabel: false, // 打印揽收面单弹窗
      awbRrintLabel: [], // {formatMessage({id: '需要打印标签的运单号'})}
      isCancel: false, // {formatMessage({id: '是否取消截留二次确认框'})}
      isCancel1: false,
      loadingModal: false,
      allTabs: '-2',
      preview: false,
      previewURL: '',
      selectTypeRadioValue: undefined,
      modalErrorContent: '', // {formatMessage({id: '检测订单号是否已经存在弹窗提示内容'})}
      isModalErrorVisible: false, // {formatMessage({id: '检测订单号是否已经存在弹窗显示状态'})}
      menuClickKey: undefined,
    };
    this.drawerRef = React.createRef();
  }
  componentDidMount() {
    this.setState({
      value: localStorage.getItem('radioValue'),
      startTimes: moment()
        .utcOffset('+08:00')
        .subtract(1, 'month')
        .format('YYYY-MM-DD 00:00:00'),
      endTimes: moment()
        .utcOffset('+08:00')
        // .add(1, 'days')
        .format('YYYY-MM-DD 23:59:59'),
    });

    const { dispatch } = this.props;
    let NODATA = localStorage.getItem('NODATA');
    // {formatMessage({id: '判定当前是否需要使用缓存数据'})}
    let productData = this.encapsulationAccess('take', 'productData');
    let countriesData = this.encapsulationAccess('take', 'countriesData');
    let tableData = this.encapsulationAccess('take', 'tableData')
      ? this.encapsulationAccess('take', 'tableData')
      : [];
    let number = this.encapsulationAccess('take', 'number');
    let miscellaneous = this.encapsulationAccess('take', 'miscellaneous');
    let deliveryAccount = localStorage.getItem('deliveryAccount'); // {formatMessage({id: '选中的制单账号'})}
    let page = this.encapsulationAccess('take', 'PAGE');
    if (page) {
      this.setState({
        currentPage: page.currentPages,
        pageSize: page.pageSizes,
      });
    }
    // {formatMessage({id: '不需要刷新'})}
    if (miscellaneous && !miscellaneous.isRefresh && !miscellaneous.isRefreshList) {
      let objData = {
        channel2: miscellaneous.printingStatus,
        receiverName: miscellaneous.receiverName,
      };
      this.props.form.setFieldsValue(objData);
      let endTimes = moment()
        .utcOffset('+08:00')
        // .add(1, 'days')
        .format('YYYY-MM-DD 23:59:59');
      let startTimes = moment()
        .utcOffset('+08:00')
        .subtract(1, 'month')
        .format('YYYY-MM-DD 00:00:00');
      this.loadShippers();
      this.setState(
        {
          startTimes: startTimes,
          endTimes: endTimes,
          tabNumber: localStorage.getItem('NODATA') == 1 ? '0' : miscellaneous?.tabNumber,
          isPrints: miscellaneous.printingStatus.value,
          queryState: miscellaneous.queryState,
          awbNum: miscellaneous.awbNum,
          allTabs: miscellaneous.allTabs,
        },
        () => {
          localStorage.removeItem('NODATA');
        }
      );

      if (productData) {
        // {formatMessage({id: '渲染产品名称'})}
        this.setState(
          {
            channels: productData.data,
            productCode: productData.channelId,
          },
          () => {
            this.props.form.setFieldsValue({
              channel: productData.channelId ? productData.channelId : undefined,
            });
          }
        );
      }
      if (countriesData) {
        // {formatMessage({id: '渲染目的地'})}
        this.setState(
          {
            countriesData: countriesData.data,
            countryId: countriesData.countriesCode,
            countriesId: countriesData.countriesCode,
          },
          () => {
            this.props.form.setFieldsValue({
              countries: countriesData.countriesCode ? countriesData.countriesCode : undefined,
            });
          }
        );
      }
      if (tableData) {
        // {formatMessage({id: '渲染表格数据'})}
        this.setState({
          dataSource: tableData,
          loading: false,
        });
      }

      if (number) {
        this.setState(
          {
            voucherNum: number.voucherNum,
            confirmDeliveryNum: number.confirmDeliveryNum,
            haveGoods: number.haveGoods,
            deliveryNum: number.deliveryNum,
            delieveredNum: number.delieveredNum,
            cancelNum: number.cancelNum,
            interceptNum: number.interceptNum,
            sendAbnormal: number.sendAbnormal,
            productNum: number.productNum,
            noVoucherNum: number.noVoucherNum,
            makeFailureNum: number.makeFailureNum,
            totalNum: number.totalNum,
          },
          () => {
            let totals = 0;
            switch (this.state.tabNumber) {
              case '9':
                totals = number.noVoucherNum;
                break;
              case '0':
                totals = number.voucherNum;
                break;
              case '1':
                totals = number.confirmDeliveryNum;
                break;
              case '2':
                totals = number.haveGoods;
                break;
              case '3':
                totals = number.deliveryNum;
                break;
              case '4':
                totals = number.delieveredNum;
                break;
              case '5':
                totals = number.cancelNum;
                break;
              case '6':
                totals = number.interceptNum;
                break;
              case '7':
                totals = number.sendAbnormal;
                break;
              case '8':
                totals = number.productNum;
                break;
              case '10':
                totals = number.makeFailureNum;
                break;
              case '-2':
                totals = number.totalNum;
                break;
            }
            this.setState({
              total: totals,
            });
          }
        );
      }
    }
    // {formatMessage({id: '需要刷新表格数据'})}
    if (miscellaneous && miscellaneous.isRefreshList) {
      this.loadShippers();
      if (productData) {
        // {formatMessage({id: '渲染产品名称'})}
        this.setState(
          {
            channels: productData.data,
            productCode: productData.channelId,
          },
          () => {
            this.props.form.setFieldsValue({
              channel: productData.channelId ? productData.channelId : undefined,
            });
          }
        );
      }
      if (countriesData) {
        // {formatMessage({id: '渲染目的地'})}
        this.setState(
          {
            countriesData: countriesData.data,
            countryId: countriesData.countriesCode,
            countriesId: countriesData.countriesCode,
          },
          () => {
            this.props.form.setFieldsValue({
              countries: countriesData.countriesCode ? countriesData.countriesCode : undefined,
            });
          }
        );
      }
      let objData = {
        channel2: miscellaneous.printingStatus,
        receiverName: miscellaneous.receiverName,
      };
      this.props.form.setFieldsValue(objData);
      this.setState(
        {
          startTimes: moment()
            .utcOffset('+08:00')
            .subtract(1, 'month')
            .format('YYYY-MM-DD 00:00:00'),
          endTimes: moment()
            .utcOffset('+08:00')
            // .add(1, 'days')
            .format('YYYY-MM-DD 23:59:59'),
          tabNumber: localStorage.getItem('NODATA') == 1 ? '0' : miscellaneous?.tabNumber,
          isPrints: miscellaneous.printingStatus.value,
          loading: true,
          queryState: miscellaneous.queryState,
          awbNum: miscellaneous.awbNum,
          allTabs: miscellaneous.allTabs,
          userId: deliveryAccount,
        },
        () => {
          this.getOrderLists(this.state.tabNumber);
          miscellaneous.isRefreshList = false;
          this.encapsulationAccess('save', 'miscellaneous', miscellaneous);
          localStorage.removeItem('NODATA');
        }
      );
    }
    // {formatMessage({id: '需要刷新页面'})}，{formatMessage({id: '但是需要保留制单账号'})}
    if (miscellaneous && miscellaneous.isRefresh) {
      let temp = [];
      let tempIndex = 0;
      dispatch({
        type: 'Order/getShippingAccount',
        payload: { scene: 2 },
        callback: response => {
          if (response.success) {
            if (response.data.length > 0) {
              response.data.forEach((item, index) => {
                if (deliveryAccount && deliveryAccount == item.accountCode) {
                  tempIndex = index;
                }
                temp.push({
                  accountCode: item.accountCode,
                  warehouseName: item.warehouseName,
                });
              });
            }

            // {formatMessage({id: '默认制单账号放在第一位'})}
            let defaultCustomerCode = temp[tempIndex];
            temp[tempIndex] = temp[0];
            temp[0] = defaultCustomerCode;
            this.props.form.setFieldsValue({
              shippers: [deliveryAccount ? deliveryAccount : temp[0]?.accountCode ?? ''],
            });
            let endTimes = moment()
              .utcOffset('+08:00')
              // .add(1, 'days')
              .format('YYYY-MM-DD 23:59:59');
            let startTimes = moment()
              .utcOffset('+08:00')
              .subtract(1, 'month')
              .format('YYYY-MM-DD 00:00:00');

            this.setState(
              {
                startTimes,
                endTimes,
                account: temp,
                userId: temp[0]?.accountCode ?? '',
                HKShow: temp[0]?.warehouseName == '香港燕文',
                tabNumber: miscellaneous?.tabNumber,
                queryState: true,
                awbNum: [],
                allTabs: '-2',
              },
              () => {
                this.getProductName();
                this.getCountriesName();
                this.getOrderLists(this.state.tabNumber);
                miscellaneous.isRefresh = false;
                miscellaneous.date = [startTimes, endTimes];
                this.encapsulationAccess('save', 'miscellaneous', miscellaneous);
              }
            );
          }
        },
      });
    }
    // {formatMessage({id: '全部为'})}null{formatMessage({id: '的时候需要请求接口'})}
    if (!miscellaneous) {
      this.encapsulationAccess('save', 'miscellaneous', {
        date: [this.state.startTimes, this.state.endTimes],
        tabNumber: localStorage.getItem('NODATA') == 1 ? '0' : '9',
        printingStatus: '',
        isRefresh: false,
        isRefreshList: false,
        queryState: true,
        awbNum: [],
        allTabs: '-2',
        isClickDeliveryProof: false,
        isClickSubmit: false,
        receiverName: '',
      });
      this.loadShipper();
    }
  }

  componentWillUnmount() {
    if (timeStatus) {
      clearTimeout(timeStatus);
    }
  }

  // {formatMessage({id: '获取订单列表'})}
  getOrderLists = num => {
    const { dispatch, form } = this.props;
    const {
      currentPage,
      userId,
      productCode,
      countriesId,
      isPrints,
      startTimes,
      endTimes,
      awbNum,
      pageSize,
      queryState,
    } = this.state;
    let { receiverName, WaybillNumbers, quickQuery, channel } = form.getFieldsValue();
    let waybillNumber;
    if (WaybillNumbers == undefined) {
      waybillNumber = undefined;
    } else if (WaybillNumbers && typeof WaybillNumbers == 'string') {
      waybillNumber = WaybillNumbers !== undefined ? this.textWaybill(WaybillNumbers) : undefined;
    } else {
      waybillNumber =
        WaybillNumbers.length && WaybillNumbers[0] !== undefined
          ? this.textWaybill(WaybillNumbers[0])
          : undefined;
    }
    let params = {
      pageNum: currentPage,
      pageSize: pageSize,
      status: num,
      userId: userId,
      channelId: channel === 'all' ? undefined : productCode,
      countryId: countriesId,
      isPrint: isPrints,
      startTime: startTimes,
      endTime: endTimes,
      receiverName: receiverName ? receiverName : '', //{formatMessage({id: '收件人名称'})}
      listNumber: waybillNumber,
    };
    dispatch({
      type: 'Order/getOrderLists',
      payload: params,
      callback: result => {
        const newLoadings = [...this.state.loadingButton];
        newLoadings[0] = false;
        if (result.success) {
          let data =
            result?.data?.records?.map((item, index) => {
              return {
                ...item,
                key: index,
              };
            }) ?? [];
          if (quickQuery && data.length > 0) {
            const filterData = [...result?.data?.records];
            data =
              filterData
                ?.filter(item =>
                  Object.values(item).some(v =>
                    String(v)
                      .toLowerCase()
                      .includes(quickQuery.toLowerCase())
                  )
                )
                ?.map((item, index) => {
                  const isMatch = Object.values(item).some(v =>
                    String(v)
                      .toLowerCase()
                      .includes(quickQuery.toLowerCase())
                  );
                  const highlightFields = obj => {
                    return Object.entries(obj).reduce((acc, [key, value]) => {
                      if (typeof value === 'string' && quickQuery) {
                        acc[key] = value.replace(
                          new RegExp(quickQuery, 'gi'),
                          match =>
                            `<span style="background-color: yellow;font-weight: bold;">${match}</span>`
                        );
                      } else {
                        acc[key] = value;
                      }
                      return acc;
                    }, {});
                  };

                  return {
                    ...highlightFields(item),
                    key: index,
                    quickQueryFind: isMatch,
                  };
                }) ?? [];
          }

          this.setState(
            {
              loading: false,
              dataSource: data,
              total: result.data.totalRecord,
              selectedRows: [],
              selectedRowKeys: [],
              loadingButton: newLoadings,
              batchMakeOrderLoading: false,
            },
            () => {
              if (result?.data?.records) {
                localStorage.setItem('tableData', JSON.stringify(result?.data?.records));
              } else {
                localStorage.setItem('tableData', JSON.stringify([]));
              }
            }
          );
          this.statisticalQuantity(result.data.expressesSimple);
        } else {
          this.setState({
            loading: false,
            loadingButton: newLoadings,
            batchMakeOrderLoading: false,
          });
          message.error(result.message);
        }
      },
    });
  };

  // {formatMessage({id: '统计各个状态的数量'})}
  statisticalQuantity = data => {
    let totalNums = 0;
    let number = {};
    let returnExceptionNum = 0;
    number.voucherNum = data['0'];
    number.confirmDeliveryNum = data['1'];
    number.haveGoods = data['2'];
    number.deliveryNum = data['3'];
    number.delieveredNum = data['4'];
    number.cancelNum = data['5'];
    number.interceptNum = data['6'];
    number.sendAbnormal = data['7'];
    number.productNum = data['8'];
    number.noVoucherNum = data['9'];
    number.makeFailureNum = data['10'];
    returnExceptionNum = data['8'];
    for (let key in data) {
      if (key != 11) {
        totalNums += data[key];
      }
    }
    this.setState(
      {
        totalNum: totalNums,
        voucherNum: data['0'],
        confirmDeliveryNum: data['1'],
        haveGoods: data['2'],
        deliveryNum: data['3'],
        delieveredNum: data['4'],
        cancelNum: data['5'],
        interceptNum: data['6'],
        sendAbnormal: data['7'],
        productNum: returnExceptionNum,
        noVoucherNum: data['9'],
        makeFailureNum: data['10'],
      },
      () => {
        number.totalNum = totalNums;
        this.encapsulationAccess('save', 'number', number);
      }
    );
  };

  // {formatMessage({id: '获取时间'})}
  getDay = day => {
    var today = new Date();
    var targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day;
    today.setTime(targetday_milliseconds); //{formatMessage({id: '注意'})}，{formatMessage({id: '这行是关键代码'})}
    var tYear = today.getFullYear();
    var tMonth = today.getMonth();
    var tDate = today.getDate();
    tMonth = this.doHandleMonth(tMonth + 1);
    tDate = this.doHandleMonth(tDate);
    return tYear + '-' + tMonth + '-' + tDate;
  };
  doHandleMonth = month => {
    var m = month;
    if (month.toString().length == 1) {
      m = '0' + month;
    }
    return m;
  };
  // {formatMessage({id: '获取产品名称'})}
  getProductName = () => {
    const { dispatch } = this.props;
    const { userId, countriesId } = this.state;
    let params = {
      userId: userId,
      countryId: countriesId,
    };
    dispatch({
      type: 'Order/getProductName',
      payload: {},
      callback: result => {
        this.setState(
          {
            channels: result.data,
          },
          () => {
            let productData = {
              data: result.data,
              channelId: '',
            };
            localStorage.setItem('productData', JSON.stringify(productData));
          }
        );
      },
    });
  };

  // {formatMessage({id: '国家发生变化时'})}
  changeCountries = id => {
    this.setState({
      countriesId: id,
    });
  };

  // {formatMessage({id: '产品选中值发生变化'})}
  changeProductName = value => {
    this.setState({
      productCode: value,
    });
  };

  // {formatMessage({id: '打印状态改变'})}
  changeIsPrint = value => {
    if (value != undefined) {
      this.setState({
        isPrints: value.key,
      });
    } else {
      this.setState({
        isPrints: '',
      });
    }
  };

  // {formatMessage({id: '制单日期变化'})}
  changeDate = (date, dateString) => {
    this.setState({
      startTimes: dateString[0] ? dateString[0] : undefined,
      endTimes: dateString[1] ? dateString[1] : undefined,
    });
  };

  // {formatMessage({id: '根据产品编号获取国家'})}
  getCountriesName = productId => {
    const { dispatch } = this.props;
    dispatch({
      type: 'Order/getCountriesName',
      payload: productId,
      callback: result => {
        if (result.success) {
          result.data.forEach(item => {
            item.countryNamePinYin = loadPinYinInit.ConvertPinyin(item.nameCh);
          });
          this.setState(
            {
              countriesData: result.data,
            },
            () => {
              let countriesDatas = this.encapsulationAccess('take', 'countriesData');
              let countriesData = {
                data: result.data,
                countriesCode:
                  countriesDatas && countriesDatas.countriesCode
                    ? countriesDatas.countriesCode
                    : '',
              };
              localStorage.setItem('countriesData', JSON.stringify(countriesData));
            }
          );
        }
      },
    });
  };

  // {formatMessage({id: '获取制单账号'})}
  loadShipper = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'Order/getShippingAccount',
      payload: { scene: 2 },
      callback: response => {
        if (response.success) {
          this.setState(
            {
              account: response.data,
              userId: response.data[0].accountCode,
              HKShow: response.data[0].warehouseName == '香港燕文',
            },
            () => {
              // 根据仓号获取产品名称
              this.getProductName();
              this.getCountriesName();
              this.getOrderLists(this.state.tabNumber);
            }
          );
        }
      },
    });
  };

  // 获取制单账号
  loadShippers = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'Order/getShippingAccount',
      payload: { scene: 2 },
      callback: response => {
        if (response.success) {
          let deliveryAccount = localStorage.getItem('deliveryAccount'); // 选中的制单账号
          let temp = [];
          let tempIndex = 0;
          response.data.forEach((item, index) => {
            if (deliveryAccount && deliveryAccount == item.accountCode) {
              tempIndex = index;
            }
            temp.push({
              accountCode: item.accountCode,
              warehouseName: item.warehouseName,
            });
          });
          // 默认制单账号放在第一位
          let defaultCustomerCode = temp[tempIndex];
          temp[tempIndex] = temp[0];
          temp[0] = defaultCustomerCode;
          this.props.form.setFieldsValue({
            shippers: [temp[0].accountCode],
          });
          this.setState({
            account: temp,
            userId: temp[0].accountCode,
            HKShow: temp[0].warehouseName == '香港燕文',
          });
        }
      },
    });
  };

  tabOnChange = v => {
    this.setState(
      {
        tabNumber: v,
        createModalShow: false,
        loading: true,
        currentPage: 1,
        selectedRowKeys: [],
      },
      () => {
        let miscellaneous = this.encapsulationAccess('take', 'miscellaneous');
        if (miscellaneous?.tabNumber) {
          miscellaneous.tabNumber = v;
          this.encapsulationAccess('save', 'miscellaneous', miscellaneous);
        }
        this.getOrderLists(v == -2 ? this.state.allTabs : v);
      }
    );
  };

  closeModal = () => {
    this.setState({ createModalShow: false });
  };
  madeSuccess = value => {
    // this.getShopifyOrderStatusNum();
    this.tabOnChange(value);
  };
  changePage = (current, size) => {
    this.setState(
      {
        pageSize: size,
        loading: true,
      },
      () => {
        this.state.currentPage = current;
        let currentPages = current;
        let pageSizes = size;
        this.encapsulationAccess('save', 'PAGE', { currentPages, pageSizes });
        this.getOrderLists(this.state.tabNumber);
      }
    );
  };
  // 列表选中取消
  onTableSelectChange = (selectedRowKeys, selectedRows) => {
    this.setState({ selectedRows: selectedRows, selectedRowKeys: selectedRowKeys });
  };

  // 切换制单账号
  shipperChange = (tag, index) => {
    const { account } = this.state;
    this.props.form.setFieldsValue({ shippers: tag, channel: undefined });
    this.setState(
      {
        userId: tag[0],
        loading: true,
        currentPage: 1,
        HKShow: account && account[index].warehouseName == '香港燕文',
      },
      () => {
        this.getProductName();
        this.getOrderLists(this.state.tabNumber);
        let deliveryAccount = tag[0];
        localStorage.setItem('deliveryAccount', deliveryAccount);
      }
    );
    // this.getShopifyOrderStatusNum()
  };

  // 点击查询按钮
  searchOrder = () => {
    logSave(LogType.packet10);
    const { form } = this.props;
    const { queryState, awbNum, startTimes, endTimes } = this.state;
    const {
      channel,
      countries,
      channel2,
      receiverName,
      WaybillNumbers,
      quickQuery,
    } = form.getFieldsValue();
    let miscellaneous = this.encapsulationAccess('take', 'miscellaneous');
    let productData = this.encapsulationAccess('take', 'productData');
    let countriesData = this.encapsulationAccess('take', 'countriesData');
    let page = this.encapsulationAccess('take', 'PAGE');
    if (!queryState && !awbNum.length) {
      message.error(formatMessage({ id: '请输入要查询的运单号' }));
      return;
    }
    // 创建日期
    miscellaneous['date'] = [startTimes ? startTimes : '', endTimes ? endTimes : ''];
    // 打印状态
    miscellaneous['printingStatus'] = channel2 ? channel2 : '';
    // 产品名称
    productData['channelId'] = channel ? channel : '';
    // 目的地
    countriesData['countriesCode'] = countries ? countries : '';
    // 收件人姓名
    miscellaneous['receiverName'] = receiverName ? receiverName : '';

    this.setState(
      {
        loading: true,
        currentPage: 1,
        tabNumber:
          typeof WaybillNumbers === 'string' && WaybillNumbers ? '-2' : this.state.tabNumber,
      },
      () => {
        this.enterLoading(0);
        this.getOrderLists(
          this.state.tabNumber == '-2' ? this.state.allTabs : this.state.tabNumber
        );
        if (page) {
          page.currentPage = 1;
          this.encapsulationAccess('save', 'PAGE', page);
        } else {
          let currentPages = 1;
          let pageSizes = 10;
          this.encapsulationAccess('save', 'PAGE', { currentPages, pageSizes });
        }
      }
    );
    miscellaneous.awbNum = !queryState ? awbNum : WaybillNumbers;
    this.encapsulationAccess('save', 'miscellaneous', miscellaneous);
    this.encapsulationAccess('save', 'productData', productData);
    this.encapsulationAccess('save', 'countriesData', countriesData);
  };
  // 查询按钮loading
  enterLoading = index => {
    this.setState(({ loadingButton }) => {
      const newLoadings = [...loadingButton];
      newLoadings[index] = true;

      return {
        loadingButton: newLoadings,
      };
    });
  };

  // 点击模糊查询
  accurateQuery = () => {
    this.setState(
      {
        queryState: true,
        startTimes: moment()
          .utcOffset('+08:00')
          .subtract(1, 'month')
          .format('YYYY-MM-DD 00:00:00'),
        endTimes: moment()
          .utcOffset('+08:00')
          // .add(1, 'days')
          .format('YYYY-MM-DD 23:59:59'),
      },
      () => {
        let miscellaneous = this.encapsulationAccess('take', 'miscellaneous');
        miscellaneous.queryState = true;
        this.encapsulationAccess('save', 'miscellaneous', miscellaneous);
      }
    );
    this.resetSelect();
  };

  // 点击精准查询
  fuzzyQuery = () => {
    this.setState(
      {
        queryState: false,
        startTimes: '',
        endTimes: '',
      },
      () => {
        let miscellaneous = this.encapsulationAccess('take', 'miscellaneous');
        miscellaneous.queryState = false;
        this.encapsulationAccess('save', 'miscellaneous', miscellaneous);
      }
    );
    this.resetSelect();
  };

  // 发货证明
  generateLabel = record => {
    let miscellaneous = this.encapsulationAccess('take', 'miscellaneous');
    miscellaneous.isClickDeliveryProof = true;
    miscellaneous.isClickSubmit = false;
    this.encapsulationAccess('save', 'miscellaneous', miscellaneous);
    sessionStorage.setItem('PATHNAME', '/smallBag/orderManagement/proofApply');
    router.push(
      `/smallBag/orderManagement/proofApply?expressCode=${record.waybillNumber}&expressType=0`
    );
  };

  // 预览签收图片
  getSignature = record => {
    const { dispatch } = this.props;
    dispatch({
      type: 'Order/getSignature',
      payload: { waybillNumber: record.waybillNumber, userId: record.userId },
      callback: response => {
        if (response.success) {
          this.setState({
            boxImageVisible: true,
            boxImage: response?.data?.map(item => <Image src={item} />),
          });
        }
      },
    });
  };

  // 下载签收图片
  downloadSignature = () => {
    const { dispatch } = this.props;
    const { selectedRows } = this.state;
    if (selectedRows.length == 0) {
      message.error(formatMessage({ id: '请至少勾选一条数据' }));
      return;
    }
    let waybillNumbers = selectedRows.map(item => {
      return item.waybillNumber;
    });
    dispatch({
      type: 'Order/downloadSignature',
      payload: { waybillNumbers, userId: selectedRows[0].userId },
      callback: response => {
        if (response.success) {
          this.dataURLtoDownload(
            response.data.base64,
            response.data.fileName,
            formatMessage({ id: '打印' })
          );
        }
      },
    });
  };

  //导出
  batchMakeOrder = () => {
    logSave(LogType.packet21);
    const { dispatch } = this.props;
    const { selectedRows, tabNumber, selectedRowKeys, allTabs } = this.state;
    if (selectedRows.length == 0) {
      message.error(formatMessage({ id: '请至少勾选一条数据' }));
      return;
    }
    this.setState({
      loading: true,
    });
    let arr = [];
    let ids = [];
    arr = selectedRows.map(item => {
      return item.waybillNumber;
    });
    ids = selectedRows.map(item => {
      return item.id;
    });
    let params = {
      userId: selectedRows[0].userId,
    };
    if (tabNumber == '9' || tabNumber == '10' || (tabNumber == -2 && allTabs == '-1')) {
      params.ids = ids;
    } else {
      params.waybillNumbers = arr;
    }
    downloadFile({
      url: `/csc/ejf/current/printInfo`,
      param: params,
      method: 'POST',
    });
    setTimeout(() => {
      this.setState({
        loading: false,
        selectedRows: [],
        selectedRowKeys: [],
      });
    }, 1500);
  };

  // 重置查询条件
  resetSelect = () => {
    logSave(LogType.packet11);
    this.props.form.setFieldsValue({
      orderNumbers: undefined,
      countries: undefined,
      channel2: undefined,
      channel: undefined,
      receiverName: undefined,
      WaybillNumbers: undefined,
      quickQuery: undefined,
    });
    this.setState({
      countriesId: '',
      productCode: '',
      isPrints: '',
      currentPage: 1,
      awbNum: [],
      startTimes: moment()
        .utcOffset('+08:00')
        .subtract(1, 'month')
        .format('YYYY-MM-DD 00:00:00'),
      endTimes: moment()
        .utcOffset('+08:00')
        // .add(1, 'days')
        .format('YYYY-MM-DD 23:59:59'),
    });
  };

  validateOrderNumbers = (rule, value, callback) => {
    if (value != undefined) {
      let orderArray = value.split(/[\r\n]/g);
      let patten = new RegExp('^[0-9a-zA-Z]*$');
      let flag = true;
      for (let i = 0; i < orderArray.length; i++) {
        if (!patten.test(orderArray[i])) {
          flag = false;
          break;
        }
      }
      if (!flag) {
        callback(formatMessage({ id: '请输入正确订单号' }));
        return;
      }
      let ar = orderArray.filter(item => {
        return item != '';
      });
      this.setState({
        awbNum: ar,
      });
    }
    callback();
  };

  printHKPackageNumber = () => {
    this.props.form.setFieldsValue({ weight: '' });
    this.setState({ HKPrintLabel: true });
  };

  // 点击打印标签
  clickPrintLabel = data => {
    if (data == 'all') {
      const { selectedRows } = this.state;
      if (!selectedRows.length) {
        message.error(formatMessage({ id: '请至少勾选一条数据' }));
        return;
      }
      let quantityPrintedTmp = selectedRows.filter(x => x.isPrint != 0)?.length;
      this.setState({
        quantityPrinted: quantityPrintedTmp,
        isPrintLabel: true,
      });
    } else {
      this.setState({
        isPrintLabel: true,
        quantityPrinted: data.isPrint,
        awbRrintLabel: [data.waybillNumber],
      });
    }
  };

  // 打印标签
  printLabel = type => {
    if (type == '打印') {
      logSave(LogType.packet18);
    }
    if (type == '预览') {
      logSave(LogType.packet19);
    }
    const { dispatch } = this.props;
    const { selectedRows, userId, awbRrintLabel, value, isPrint } = this.state;
    let arr = selectedRows.sort((a, b) => a.key - b.key);
    const expressCodes = arr.map(item => {
      return item.waybillNumber;
    });
    let params = {
      waybillNumbers: awbRrintLabel.length ? awbRrintLabel : expressCodes,
      userId: userId,
      printRemark: value ? '1' : '0',
      isPrint,
    };
    // if (type == formatMessage({ id: '打印' })) {
    //   this.setState({
    //     printLabelLoading: true,
    //   });
    // }
    dispatch({
      type: 'Order/getPrintLabelData',
      payload: params,
      callback: res => {
        if (res.success) {
          message.success({
            content: res.message,
            style: {
              whiteSpace: 'pre-wrap',
            },
          });
          this.setState(
            {
              loading: true,
              selectedRows: [],
              selectedRowKeys: [],
            },
            () => {
              this.getOrderLists(this.state.tabNumber);
            }
          );
          this.dataURLtoDownload(res.data, res.fileName, type);
        } else {
          if (res.data && res.data.length) {
            this.dataURLtoDownload(res.data, res.fileName, type);
          }
          notification.error({
            message: '错误提示',
            description: res.message,
            placement: 'top',
            duration: 0,
            style: {
              whiteSpace: 'pre-wrap',
            },
          });
        }
        this.setState({
          // printLabelLoading: false,
          quantityPrinted: 0,
          isPrintLabel: false,
          awbRrintLabel: [],
        });
      },
    });
  };

  HKPrintLabel = () => {
    const { form } = this.props;
    const { dispatch } = this.props;
    let values = form.getFieldsValue();
    let accountCodes;
    if (typeof values.shippers == 'string') {
      accountCodes = values.shippers;
    } else {
      accountCodes = values.shippers[0];
    }
    let weight = values.weight;
    if (weight) {
      this.setState({ HKPrintLabelLoading: true });
      dispatch({
        type: 'Order/HKPrintLabelData',
        payload: { userId: accountCodes, weight: weight },
        callback: response => {
          this.setState({
            HKPrintLabelLoading: false,
          });
          if (response.success) {
            message.success(response.message);
            this.dataURLtoDownload(
              response.data.base64String,
              '揽收大包标签_' + response.data.packageNumber + '_' + this.getDateString() + '.pdf',
              '打印'
            );
            this.setState({
              HKPrintLabel: false,
            });
          } else {
            message.error(response.message);
          }
        },
      });
    } else {
      message.error('请输入重量');
    }
  };

  getDateString = () => {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    return `${year}${month}${day}`;
  };

  // 打印标签弹框多选框选择
  onChangeM = val => {
    this.setState({
      valNumber: val.target.value,
    });
    if (val.target.value == 3) {
      this.setState({
        MFvisable: true,
      });
    } else {
      this.setState({
        MFvisable: false,
      });
    }
  };

  //base64解析下载 dataurl是后端返回的base64字符串，name是文件名
  dataURLtoDownload = (dataurl, name, type) => {
    const dataBase64Data = `data:application/pdf;base64,${dataurl}`;
    let bstr = atob(dataurl), //解析 base-64 编码的字符串
      n = bstr.length,
      u8arr = new Uint8Array(n); //创建初始化为0的，包含length个元素的无符号整型数组
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n); //返回字符串第一个字符的 Unicode 编码
    }
    let blob = new Blob([u8arr]); //转化成blob
    let url = URL.createObjectURL(blob); //这个新的URL 对象表示指定的 File 对象或 Blob 对象
    if (type == formatMessage({ id: '打印' })) {
      let a = document.createElement('a'); //创建一个a标签
      a.href = url;
      a.download = name;
      a.click();
      URL.revokeObjectURL(a.href); //释放之前创建的url对象
    } else {
      const parts = dataBase64Data.split(';base64,');
      const contentType = parts[0].split(':')[1];
      const raw = window.atob(parts[1]);
      const rawLength = raw.length;
      const uInt8Array = new Uint8Array(rawLength);
      for (let i = 0; i < rawLength; ++i) {
        uInt8Array[i] = raw.charCodeAt(i);
      }
      const blob = new Blob([uInt8Array], { type: contentType });
      const blobUrl = URL.createObjectURL(blob);
      this.setState({
        preview: true,
        previewURL: blobUrl,
      });
    }
  };

  // 关闭预览
  closeReview = () => {
    this.setState({ preview: false, previewURL: '' });
  };

  // 订单确认发货
  confirmDelivery = () => {
    logSave(LogType.orderConfirm01);
    const { dispatch } = this.props;
    const { selectedRows, userId } = this.state;
    const expressCodes = selectedRows.map(item => {
      return item.waybillNumber;
    });
    if (!expressCodes.length) {
      message.error(formatMessage({ id: '请至少勾选一条数据' }));
      return;
    }
    this.setState({ batchMakeOrderLoading: true, loading: true });
    let params = {
      waybillNumbers: expressCodes,
      userId: userId,
    };
    dispatch({
      type: 'Order/confirmDelivery',
      payload: params,
      callback: result => {
        if (result.success) {
          message.success(result.message, 2);
          this.state.currentPage = 1;
          this.getOrderLists(this.state.tabNumber);
        } else {
          message.error({
            content: result.message,
            style: {
              whiteSpace: 'pre-wrap',
            },
          });
          this.setState({ batchMakeOrderLoading: false, loading: false });
        }
      },
    });
  };

  // 点击生成交接单
  clickCreateReceipt = () => {
    logSave(LogType.packet20);
    const { dispatch } = this.props;
    const { selectedRows, userId } = this.state;
    const expressCodes = selectedRows.map(item => {
      return item.waybillNumber;
    });
    if (!expressCodes.length) {
      message.error(formatMessage({ id: '请至少勾选一条数据' }));
      return;
    }
    this.setState({ batchMakeOrderLoading: true, loading: true });
    let params = {
      waybillNumbers: expressCodes,
      userId: userId,
    };
    downloadFile({
      url: `/csc/ejf/order/generateTransferOrder`,
      param: params,
      method: 'POST',
    });
    setTimeout(() => {
      this.setState({
        selectedRows: [],
        selectedRowKeys: [],
        batchMakeOrderLoading: false,
        loading: false,
      });
    }, 1500);
  };

  // 点击取消
  cancelOrder = () => {
    logSave(LogType.packet16);
    const { selectedRows, userId } = this.state;
    const expressCodes = selectedRows.map(item => {
      return item.waybillNumber;
    });
    if (expressCodes.length == 0) {
      message.error(formatMessage({ id: '请至少勾选一条数据' }));
      return;
    }
    this.setState({
      isCancelWayBill: true,
    });
  };

  // 取消订单原因变化
  changeCancelOrderReason = e => {
    this.setState({
      cancelOrderReason: e.target.value,
    });
  };

  // 取消制单
  cancelMakeOrder = () => {
    const { selectedRows, userId, cancelOrderReason } = this.state;
    let expressCodes = selectedRows.map(item => {
      return item.waybillNumber;
    });
    this.setState({ batchMakeOrderLoading: true, loading: true, confirmLoading: true });
    let selectedRow = selectedRows[0];
    let param = { waybillNumbers: expressCodes, userId: userId, note: cancelOrderReason };
    const { dispatch } = this.props;
    dispatch({
      type: 'Order/cancelWaybill',
      payload: param,
      callback: result => {
        if (result.success) {
          message.success(result.message);
          this.state.currentPage = 1;
          this.getOrderLists(this.state.tabNumber);
          this.setState({
            confirmLoading: false,
            isCancelWayBill: false,
          });
        } else {
          message.error({
            content: result.message,
            style: {
              whiteSpace: 'pre-wrap',
            },
          });
          this.setState({
            batchMakeOrderLoading: false,
            loading: false,
            confirmLoading: false,
            isCancelWayBill: false,
          });
        }
      },
    });
  };

  // 点击取消确认发货
  cancelConfirmDelivery = () => {
    logSave(LogType.orderConfirm02);
    const { selectedRows, userId } = this.state;
    const { dispatch } = this.props;
    if (!selectedRows.length) {
      message.error(formatMessage({ id: '请至少勾选一条数据' }));
      return;
    }
    let waybillNumbers = [];
    selectedRows.forEach(item => {
      waybillNumbers.push(item.waybillNumber);
    });
    let params = {
      waybillNumbers: waybillNumbers,
      userId: userId,
    };
    this.setState({ batchMakeOrderLoading: true, loading: true });
    dispatch({
      type: 'Order/cancelConfirmDelivery',
      payload: params,
      callback: res => {
        if (res.success) {
          message.success(res.message);
          this.state.currentPage = 1;
          this.getOrderLists(this.state.tabNumber);
        } else {
          this.setState({
            batchMakeOrderLoading: false,
            loading: false,
          });
        }
      },
    });
  };

  // 点击申请截留
  applyWithholding = () => {
    const { selectedRows } = this.state;
    if (!selectedRows.length) {
      message.error(formatMessage({ id: '请至少勾选一条数据' }));
      return;
    }
    this.setState({
      visible: true,
      checkedNum: selectedRows.length,
    });
  };
  // 申请截留弹框点击了确定
  handleOk = e => {
    logSave(LogType.packet17);
    const { form, dispatch } = this.props;
    const { selectedRows } = this.state;
    let orders = [];
    selectedRows.forEach(item => {
      orders.push(item.waybillNumber);
    });
    let values = form.getFieldsValue();
    let accountCodes = '';
    if (typeof values.shippers == 'string') {
      accountCodes = values.shippers;
    } else {
      accountCodes = values.shippers[0];
    }
    // 添加判断
    if (values.optionWay === 3) {
      if (orders.length > 1) {
        message.error(formatMessage({ id: '如需换单请选择一个运单号进行截留' }));
        return;
      } else {
        if (orders.find(item => item === values.convertNumber)) {
          message.error(formatMessage({ id: '新单号不能和老单号一致' }));
          return;
        }
      }
    }

    let params = {
      accountCode: accountCodes,
      optionWay: values.optionWay,
      remark: values.remark,
      convertNumber: values.convertNumber == undefined ? '' : values.convertNumber,
      wayBillNos: orders,
    };
    this.setState({ loading: true, loadingModal: true });
    dispatch({
      type: 'Order/CliskApplyWithholding',
      payload: params,
      callback: result => {
        if (result.success) {
          message.success(formatMessage({ id: '截留成功' }));
          this.setState({
            visible: false,
            currentPage: 1,
            loadingModal: false,
          });
          this.getOrderLists(this.state.tabNumber);
        } else {
          this.setState({
            loading: false,
            loadingModal: false,
          });
        }
      },
    });
  };

  // 点击取消截留
  clickCanceIntercept = data => {
    const { selectedRows } = this.state;
    if (data == 'all' && !selectedRows.length) {
      message.error(formatMessage({ id: '请至少勾选一条数据' }));
      return;
    } else if (data == 'all' && selectedRows.length) {
      this.setState({
        isCancel: true,
      });
    }
  };
  // 取消截留
  canceIntercept = data => {
    logSave(LogType.cancelIntercept);
    const { dispatch } = this.props;
    const { selectedRows, userId } = this.state;
    this.setState({ batchMakeOrderLoading: true, loading: true });
    let orders = [];
    selectedRows.forEach(item => {
      orders.push(item.waybillNumber);
    });
    let params = {
      sourceType: 0,
      wayBillNo: data == 'all' ? orders : [data.waybillNumber],
      customerCode: data == 'all' ? userId : data.userId,
    };
    dispatch({
      type: 'Order/interceptCancel',
      payload: params,
      callback: result => {
        if (result.success) {
          message.success(formatMessage({ id: '取消截留成功' }));
          this.state.currentPage = 1;
          this.getOrderLists(this.state.tabNumber);
          this.setState({
            isCancel: false,
            isCancel1: false,
          });
        } else {
          this.setState({
            batchMakeOrderLoading: false,
            loading: false,
            isCancel: false,
            isCancel1: false,
          });
        }
      },
    });
  };

  handleCancel = e => {
    this.setState({
      visible: false,
    });
  };

  // 发货证明
  clickDeliveryProof = () => {
    const { selectedRows } = this.state;
    if (!selectedRows.length) {
      message.error(formatMessage({ id: '请至少勾选一条数据' }));
      return;
    }
    let orders = [];
    selectedRows.forEach(item => {
      orders.push(item.waybillNumber);
    });
    let miscellaneous = this.encapsulationAccess('take', 'miscellaneous');
    miscellaneous.isClickDeliveryProof = true;
    miscellaneous.isClickSubmit = false;
    this.encapsulationAccess('save', 'miscellaneous', miscellaneous);
    sessionStorage.setItem('PATHNAME', '/smallBag/orderManagement/proofApply');
    router.push(
      `/smallBag/orderManagement/proofApply?expressCode=${orders.join('-')}&expressType=0`
    );
  };

  // 点击取消选择
  clickUncheck = () => {
    this.setState({ selectedRows: [], selectedRowKeys: [] });
  };

  // 查看截留记录
  interceptRecord = record => {
    const { dispatch } = this.props;
    dispatch({
      type: 'anomalyOrder/saveindentifier',
      payload: {
        identifier: 'detail',
      },
    });
    dispatch({
      type: 'anomalyOrder/saveHistoryPayload',
      payload: {
        epCodes: [record.waybillNumber],
      },
    });
    sessionStorage.setItem('PATHNAME', '/smallBag/anomalyOrder');
    router.push({
      pathname: '/smallBag/anomalyOrder',
      state: { name: formatMessage({ id: '运单截留记录' }), wayBillNumber: record.waybillNumber },
    });
  };

  // 打印标签单选框
  onChangeRadio = e => {
    this.setState(
      {
        value: e.target.checked,
      },
      () => {
        localStorage.setItem('radioValue', e.target.checked);
      }
    );
  };

  // 是否变更打印状态单选框
  onChangeType = e => {
    this.setState({
      isPrint: e.target.value,
    });
  };

  // 修改分页展示数量
  onShowSizeChange = (current, pageSize) => {
    this.setState(
      {
        pageSize: pageSize,
        loading: true,
      },
      () => {
        this.getOrderLists(this.state.tabNumber);
      }
    );
  };

  onChangeRadios = e => {
    this.setState(
      {
        allTabs: e.target.value,
        currentPage: 1,
        loading: true,
      },
      () => {
        let miscellaneous = this.encapsulationAccess('take', 'miscellaneous');
        miscellaneous.allTabs = e.target.value;
        this.encapsulationAccess('save', 'miscellaneous', miscellaneous);
        this.getOrderLists(e.target.value);
      }
    );
  };

  // 删除订单
  deleteOrder = data => {
    logSave(LogType.packet14);
    const { dispatch } = this.props;
    const { selectedRows } = this.state;
    let params = {
      ids: [],
      userId: '',
    };
    if (data == 'all' && selectedRows && selectedRows.length) {
      params.userId = selectedRows[0].userId;
      selectedRows.forEach(item => {
        params.ids.push(item.id);
      });
    } else if (data == 'all' && !selectedRows.length) {
      message.error(formatMessage({ id: '请至少勾选一条数据' }));
      return;
    } else {
      params.userId = data.userId;
      params.ids.push(data.id);
    }
    dispatch({
      type: 'Order/deleteOrder',
      payload: params,
      callback: result => {
        if (result.success) {
          message.success(formatMessage({ id: '删除成功' }));
          this.setState({
            loading: true,
          });
          this.getOrderLists(this.state.tabNumber);
        } else {
          message.error({
            content: result.message,
            style: {
              whiteSpace: 'pre-wrap',
            },
          });
        }
      },
    });
  };

  // 提交制单前请求检测订单号是否已经存在
  checkOrderNumber = (params, type, callback) => {
    const { dispatch } = this.props;
    dispatch({
      type: `Order/${
        type === 'all' ? 'whetherTheOrderNumberExistsList' : 'whetherTheOrderNumberExists'
      }`,
      payload: params,
      callback: res => {
        this.setState({
          loading: false,
          batchMakeOrderLoading: false,
        });
        if (res.success) {
          if (callback) callback();
        } else {
          if (res.code == 501) {
            message.error(res.message);
          } else {
            this.setState(
              {
                modalErrorContent: res?.message,
              },
              () => {
                this.checkOrderNumberCallback(
                  { file: res.data?.file, base64: res.data?.base64, fileName: res.data?.fileName },
                  callback
                );
              }
            );
          }
        }
      },
    });
  };

  // 处理选择完是或否后的操作
  checkOrderNumberCallback = (params, callback) => {
    const { dispatch } = this.props;
    const { selectTypeRadioValue } = this.state;
    // 1为是  0 为否
    if (selectTypeRadioValue !== undefined && selectTypeRadioValue == 1) {
      this.setState({
        selectTypeRadioValue: undefined,
      });
      // 执行回调函数里的方法
      if (callback) callback();
    } else if (selectTypeRadioValue !== undefined && selectTypeRadioValue == 0) {
      this.setState({
        selectTypeRadioValue: undefined,
      });
    } else {
      // 打开弹窗去选择是或否
      this.setState({
        isModalErrorVisible: true,
      });
      if (params?.file) {
        this.dataURLtoDownload(params?.base64, params?.fileName, formatMessage({ id: '打印' }));
      }
    }
  };

  // 提交制单
  clickSubmitVoucher = () => {
    logSave(LogType.packet15);
    const { dispatch } = this.props;
    const { selectedRows } = this.state;
    if (!selectedRows.length) {
      message.error(formatMessage({ id: '请至少勾选一条数据' }));
      return;
    }
    this.setState({
      loading: true,
      batchMakeOrderLoading: true,
    });
    let orders = [];
    selectedRows.forEach(item => {
      orders.push(item.id);
    });
    let params = {
      userId: selectedRows[0].userId,
      ids: orders,
    };

    this.checkOrderNumber(
      { ...params, orderNumber: selectedRows.map(item => item?.orderNumber) },
      undefined,
      () => {
        dispatch({
          type: 'Order/submitVoucher',
          payload: params,
          callback: result => {
            if (result.success) {
              message.success(result.message);
              this.setState(
                {
                  loading: false,
                  batchMakeOrderLoading: false,
                },
                () => {
                  this.tabOnChange('0');
                  // this.getOrderLists(this.state.tabNumber);
                }
              );
            } else {
              message.error({
                content: result.message,
                style: {
                  whiteSpace: 'pre-wrap',
                },
              });
              this.setState(
                {
                  loading: false,
                  batchMakeOrderLoading: false,
                },
                () => {
                  this.getOrderLists(this.state.tabNumber);
                }
              );
            }
          },
        });
      }
    );
  };
  // 提交制单-全部
  submitVoucherAll = () => {
    const { dispatch, form } = this.props;
    const {
      userId,
      productCode,
      countriesId,
      isPrints,
      startTimes,
      endTimes,
      queryState,
      tabNumber,
      allTabs,
    } = this.state;
    let { receiverName, WaybillNumbers } = form.getFieldsValue();
    let waybillNumber;
    if (WaybillNumbers == undefined) {
      waybillNumber = undefined;
    } else if (WaybillNumbers && typeof WaybillNumbers == 'string') {
      waybillNumber = WaybillNumbers !== undefined ? this.textWaybill(WaybillNumbers) : undefined;
    } else {
      waybillNumber =
        WaybillNumbers.length && WaybillNumbers[0] !== undefined
          ? this.textWaybill(WaybillNumbers[0])
          : undefined;
    }
    let params = {
      status: tabNumber == -2 ? allTabs : tabNumber,
      userId: userId,
      channelId: productCode,
      countryId: countriesId,
      isPrint: isPrints,
      startTime: startTimes,
      endTime: endTimes,
      receiverName: receiverName ? receiverName : '', //收件人名称
      listNumber: waybillNumber,
    };
    this.checkOrderNumber(params, 'all', () => {
      dispatch({
        type: 'Order/submitVoucherAll',
        payload: params,
        callback: res => {
          if (res.success) {
            message.success(res.message);
            // this.getOrderLists(tabNumber);
            this.addSetTimeOut();
          } else {
            message.error({
              content: res.message,
              style: {
                whiteSpace: 'pre-wrap',
              },
            });
          }
        },
      });
    });
  };

  // 点击下拉框选项
  onClickMenu = data => {
    this.setState({
      menuClickKey: data.key,
    });
    switch (data.key) {
      case '1':
        this.clickCreateReceipt();
        break;
      case '2':
        this.applyWithholding();
        break;
      case '3':
        this.batchMakeOrder();
        break;
      case '4':
        this.cancelOrder();
        break;
      case '5':
        this.clickPrintLabel('all');
        break;
      case '6':
        this.clickPrintLabel('all');
        break;
      case '7':
        this.clickSubmitVoucher();
        break;
      case '8':
        this.submitVoucherAll();
        break;
    }
  };

  // 本地数据存取逻辑封装
  encapsulationAccess = (type, name, value) => {
    switch (type) {
      case 'save':
        localStorage.setItem(name, JSON.stringify(value));
        break;
      case 'take':
        return JSON.parse(localStorage.getItem(name));
        break;
    }
  };

  // 校验工单号
  textWorkOrder = value => {
    if (value === undefined || value === '') {
      message.error(formatMessage({ id: '请输入要搜索的订单号' }), 2);
      return;
    }
    const reg = /[`~!@#$%^&*()_\-+=<>?:"{}|.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'。、]/im;
    const lowercase = new RegExp('[a-z]+', 'g');
    const chineseReg = /^[^\u4E00-\u9FA5]+$/g;
    if (!chineseReg.test(value)) {
      message.error(formatMessage({ id: '订单号不能输入汉字' }));
      return;
    }
    // if (lowercase.test(value)) {
    //   message.error('订单号不能输入小写字母');
    //   return;
    // }
    // if (reg.test(value)) {
    //   message.error('订单号不能输入特殊字符');
    //   return;
    // }
    // if (this.state.account.length === 0) {
    //   message.error('暂工单号，请稍后重试');
    //   return;
    // }
    const workOrderNoArr = value.split('，');
    let workOrderNoArray = [];
    for (let i = 0; i < workOrderNoArr.length; i++) {
      if (workOrderNoArr[i].length !== 0) {
        workOrderNoArray.push(workOrderNoArr[i]);
      }
    }
    if (workOrderNoArray.length > 500) {
      message.error(
        `${formatMessage({ id: '运单号搜索最多输入' })}500${formatMessage({
          id: '个运单号',
        })}，${formatMessage({ id: '多单号请以逗号' })}、${formatMessage({ id: '空格或回车隔开' })}`
      );
      return;
    }
    return workOrderNoArray;
  };

  // 校验运单号
  textWaybill = value => {
    if (value === undefined || value === '') {
      message.error(
        `${formatMessage({ id: '请输入要搜索的运单编号' })}，${formatMessage({
          id: '最多输入',
        })}500${formatMessage({ id: '个运单号' })}，${formatMessage({
          id: '多单号请以逗号',
        })}、${formatMessage({ id: '空格或回车隔开' })}`,
        2
      );
      return;
    }
    value = value.replace(/(^\s*)|(\s*$)/g, '');
    value = value.replace(/[\s+|\t+|,]/g, '，');
    const reg = /[`~!@#$%^&*()_\-+=<>?:"{}|.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'。、]/im;
    const lowercase = new RegExp('[a-z]+', 'g');
    const chineseReg = /^[^\u4E00-\u9FA5]+$/g;
    if (!chineseReg.test(value)) {
      message.error(formatMessage({ id: '运单号不能输入汉字' }));
      return;
    }
    // if (lowercase.test(value)) {
    //   message.error('运单号不能输入小写字母');
    //   return;
    // }
    // if (reg.test(value)) {
    //   message.error('运单号不能输入特殊字符');
    //   return;
    // }
    const waybillarray = value.split('，');
    let tempArray = [];
    for (let i = 0; i < waybillarray.length; i++) {
      if (waybillarray[i].length !== 0) {
        tempArray.push(waybillarray[i]);
      }
    }
    if (tempArray.length > 500) {
      message.error(
        `${formatMessage({ id: '订单' })}/${formatMessage({ id: '运单号' })}/${formatMessage({
          id: '平台交易号搜索最多输入',
        })}500${formatMessage({ id: '个订单' })}/${formatMessage({
          id: '运单号',
        })}/${formatMessage({ id: '平台交易号' })}，${formatMessage({
          id: '多单号请以逗号',
        })}、${formatMessage({ id: '空格或回车隔开' })}`
      );
      return;
    }
    // if (this.state.account.length === 0) {
    //   message.error('暂无制单账号，请稍后重试');
    //   return;
    // }
    return tempArray;
  };

  // 定时任务
  addSetTimeOut = () => {
    timeStatus = setTimeout(() => {
      this.setState({
        loading: true,
      });
      this.tabOnChange('0');
      // this.getOrderLists(this.state.tabNumber);
    }, 90000);
  };

  // 未制单，制单失败，已制单点击link跳转保存路径
  clickLink = value => {
    sessionStorage.setItem('PATHNAME', value);
  };
  render() {
    const {
      form: { getFieldDecorator },
      authorizedLoading,
      signatureLoading,
      downloadSignatureLoading,
      printLabelLoading,
    } = this.props;
    const {
      account,
      shipperInitOpt,
      unCreate,
      created,
      cancelCreate,
      createFail,
      tempStash,
      tabNumber,
      createModalShow,
      modalTitle,
      dataSource,
      childrenData,
      printTypeName,
      channels,
      currentPage,
      pageSize,
      total,
      paperType,
      batchMakeOrderLoading,
      selectedRowKeys,
      countriesData,
      awbNum,
      startTimes,
      endTimes,
      loadingButton,
      queryState,
      voucherNum,
      confirmDeliveryNum,
      haveGoods,
      deliveryNum,
      delieveredNum,
      cancelNum,
      interceptNum,
      sendAbnormal,
      productNum,
      totalNum,
      userId,
      visible,
      boxImage,
      boxImageVisible,
      valNumber,
      HKShow,
      MFvisable,
      checkedNum,
      selectedRows,
      currentAuthorityValue,
      value,
      isCancelWayBill,
      confirmLoading,
      cancelOrderReason,
      quantityPrinted,
      // printLabelLoading,
      HKPrintLabelLoading,
      isPrintLabel,
      HKPrintLabel,
      isCancel,
      isCancel1,
      loadingModal,
      noVoucherNum,
      makeFailureNum,
      allTabs,
      isPrints,
      selectTypeRadioValue,
      isModalErrorVisible,
      modalErrorContent,
      menuClickKey,
      preview,
      previewURL,
    } = this.state;
    // let dochannels = this.state.channels.filter(item => item.channelNameCh.indexOf('燕文') == -1); //indexOf('xxx')==-1 是不存在，不等于-1 是存在
    const formItemLayout = {
      labelCol: { xs: { span: 24 }, sm: { span: 4 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 18 } },
    };
    const formItemLayoutCen = {
      labelCol: { xs: { span: 0 }, sm: { span: 6 } },
      wrapperCol: { xs: { span: 0 }, sm: { span: 16 } },
    };
    const actionsTextMap = {
      expandText: formatMessage({ id: '展开' }),
      collapseText: formatMessage({ id: '收起' }),
    };

    // 弹窗
    const radioStyle = {
      display: 'block',
      height: '30px',
      fontSize: '16px',
      lineHeight: '30px',
    };

    const dateFormat = 'YYYY-MM-DD HH:mm:ss';

    let shipperOpen = account.length > 7 ? true : false;

    const handleSorterDate = (a, b) => {
      return tabNumber == -1 ||
        tabNumber == 9 ||
        tabNumber == 10 ||
        (tabNumber == '-2' && allTabs == '-1')
        ? new Date(a.importDate) - new Date(b.importDate)
        : new Date(a.createTime) - new Date(b.createTime);
    };

    const columns = [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        hideItem: tabNumber == '-1' || tabNumber == '9' || tabNumber == '10' ? undefined : true,
      },
      {
        title: formatMessage({ id: '订单号' }),
        dataIndex: 'orderNumber',
        key: 'orderNumber',
        render: (text, record) => {
          return tabNumber == '9' || tabNumber == '10' ? (
            <Link
              style={{ cursor: 'pointer', color: '#52c41a' }}
              to={`/smallBag/orderManagement/creatOrder?userId=${record.userId}&id=${record.id}`}
              onClick={() => this.clickLink('/smallBag/orderManagement/creatOrder')}
            >
              <div dangerouslySetInnerHTML={{ __html: text }} />
            </Link>
          ) : (
            <div dangerouslySetInnerHTML={{ __html: record.orderNumber }} />
          );
        },
      },
      {
        title: formatMessage({ id: '运单号' }),
        dataIndex: 'waybillNumber',
        key: 'waybillNumber',
        render: (text, record) => {
          return (
            <a
              onClick={() => {
                this.drawerRef.current?.open({
                  userId: record.userId,
                  expressCode: record.waybillNumber,
                });
              }}
            >
              <div style={{ display: 'inline-block' }} dangerouslySetInnerHTML={{ __html: text }} />
              <br />
              {tabNumber != 5 && record.newExpressCode && record.oldExpressCode && (
                <p
                  style={{
                    backgroundColor: '#52c41a',
                    color: 'white',
                    height: '18px',
                    borderRadius: '10px',
                    fontSize: '12px',
                    paddingLeft: '5px',
                    paddingRight: '5px',
                  }}
                >
                  {text == record.newExpressCode
                    ? ` ${formatMessage({ id: '原' })} | ` + record.oldExpressCode
                    : ` ${formatMessage({ id: '新' })} | ` + record.newExpressCode}
                </p>
              )}
            </a>
            // <Link
            //   style={{ cursor: 'pointer', color: '#52c41a' }}
            //   to={
            //     tabNumber == '0'
            //       ? `/smallBag/orderManagement/makeEditing?userId=${record.userId}&expressCode=${record.waybillNumber}&state={formatMessage({id: '已制单'})}`
            //       : // :
            //         // tabNumber == '8' && record.status == '13'
            //         // ? `/smallBag/anomalyOrder?userId=${record.userId}&type=0&waybillNumber=${record.waybillNumber}`
            //         `/smallBag/orderManagement/orderDetails?userId=${record.userId}&expressCode=${record.waybillNumber}`
            //   }
            //   onClick={() => this.clickLink('/smallBag/orderManagement/makeEditing')}
            // >
            //   {text} <br />
            //   {tabNumber != 5 && record.newExpressCode && record.oldExpressCode && (
            //     <p
            //       style={{
            //         backgroundColor: '#52c41a',
            //         color: 'white',
            //         height: '18px',
            //         borderRadius: '10px',
            //         fontSize: '12px',
            //         paddingLeft: '5px',
            //         paddingRight: '5px',
            //       }}
            //     >
            //       {text == record.newExpressCode
            //         ? ' {formatMessage({id: '原'})} | ' + record.oldExpressCode
            //         : ' {formatMessage({id: '新'})} | ' + record.newExpressCode}
            //     </p>
            //   )}
            // </Link>
          );
        },
      },
      {
        title: formatMessage({ id: '转单号' }),
        dataIndex: 'referenceNumber',
        key: 'referenceNumber',
        hideItem:
          tabNumber === '5' ||
          tabNumber === '6' ||
          tabNumber === '8' ||
          tabNumber === '-1' ||
          tabNumber === '9' ||
          tabNumber === '10' ||
          (tabNumber == '-2' && allTabs == '-1')
            ? true
            : undefined,
        render: (text, record) => {
          return <div dangerouslySetInnerHTML={{ __html: text }} />;
        },
      },
      {
        title: formatMessage({ id: '制单账号' }),
        dataIndex: 'userId',
        key: 'userId',
        render: (text, record) => {
          return <div dangerouslySetInnerHTML={{ __html: text }} />;
        },
      },
      {
        title: formatMessage({ id: '交货仓' }),
        dataIndex: 'companyName',
        key: 'companyName',
        render: (text, record) => {
          return <div dangerouslySetInnerHTML={{ __html: text }} />;
        },
      },
      {
        title: formatMessage({ id: '产品名称' }),
        dataIndex: 'channelName',
        key: 'channelName',
        render: (text, record) => {
          return <div dangerouslySetInnerHTML={{ __html: text }} />;
        },
      },
      {
        title: formatMessage({ id: '目的地' }),
        dataIndex: 'countryName',
        key: 'countryName',
        render: (text, record) => {
          return <div dangerouslySetInnerHTML={{ __html: text }} />;
        },
      },
      {
        title: formatMessage({ id: '收件人姓名' }),
        dataIndex: 'receiverName',
        key: 'receiverName',
        render: (text, record) => {
          return <div dangerouslySetInnerHTML={{ __html: text }} />;
        },
      },
      {
        title: formatMessage({ id: '异常原因' }),
        dataIndex: 'returnReason',
        key: 'returnReason',
        hideItem: tabNumber === '7' ? undefined : true,
        render: (text, record) => {
          return <div dangerouslySetInnerHTML={{ __html: text }} />;
        },
      },
      {
        title: formatMessage({ id: '状态' }),
        dataIndex: 'status',
        key: 'status',
        hideItem:
          tabNumber == '6' ||
          (tabNumber == -2 && allTabs == '-1') ||
          tabNumber == '9' ||
          tabNumber == '10'
            ? true
            : undefined,
        render: text => {
          const statusList = [
            { code: '0', label: formatMessage({ id: '已制单' }) },
            { code: '1', label: formatMessage({ id: '已确认发货' }) },
            { code: '2', label: formatMessage({ id: '已收货' }) },
            { code: '3', label: formatMessage({ id: '运输中' }) },
            { code: '4', label: formatMessage({ id: '已妥投' }) },
            { code: '5', label: formatMessage({ id: '已取消' }) },
            { code: '6', label: formatMessage({ id: '已截留' }) },
            { code: '7', label: formatMessage({ id: '投递失败' }) },
            { code: '8', label: formatMessage({ id: '仓内异常' }) },
            { code: '9', label: formatMessage({ id: '仓内退件' }) },
            { code: '10', label: formatMessage({ id: '退件签收' }) },
            { code: '11', label: formatMessage({ id: '转运异常' }) },
            { code: '12', label: formatMessage({ id: '派送中' }) },
            { code: '13', label: formatMessage({ id: '待提取' }) },
            { code: '15', label: formatMessage({ id: '追踪结束' }) },
            { code: '17', label: formatMessage({ id: '国外退件' }) },
          ];
          let showStatus = '';
          const selectedStatus = statusList.find(status => status.code === text);
          if (selectedStatus) {
            showStatus = selectedStatus.label;
          }
          return <div dangerouslySetInnerHTML={{ __html: showStatus }} />;
        },
      },
      {
        title: formatMessage({ id: '状态' }),
        dataIndex: 'trappedState',
        key: 'trappedState',
        hideItem: tabNumber === '6' ? undefined : true,
        render: (text, record) => {
          return <div dangerouslySetInnerHTML={{ __html: text }} />;
        },
      },
      {
        title: formatMessage({ id: '取消原因' }),
        dataIndex: 'cancelNote',
        key: 'cancelNote',
        hideItem: tabNumber === '5' ? undefined : true,
        render: (text, record) => {
          return <div dangerouslySetInnerHTML={{ __html: text }} />;
        },
      },
      {
        title: formatMessage({ id: '取消时间' }),
        dataIndex: 'cancelTime',
        key: 'cancelTime',
        hideItem: tabNumber === '5' ? undefined : true,
        render: (text, record) => {
          return <div dangerouslySetInnerHTML={{ __html: text }} />;
        },
      },
      {
        title: formatMessage({ id: '是否打印' }),
        dataIndex: 'isPrint',
        key: 'isPrint',
        hideItem: tabNumber === '0' || tabNumber === '1' ? undefined : true,
        render: text => {
          let showStatus = '';
          if (text == 0) {
            showStatus = formatMessage({ id: '未打印' });
          } else if (text == 1) {
            showStatus = formatMessage({ id: '已打印' });
          }
          return <div dangerouslySetInnerHTML={{ __html: showStatus }} />;
        },
      },
      // {
      //   title: formatMessage({id: '是否录入'}),
      //   dataIndex: 'isInput',
      //   key: 'isInput',
      //   hideItem: tabNumber === '6' ? undefined : true,
      // },
      {
        title: formatMessage({ id: '处理方式' }),
        dataIndex: 'howToHandle',
        key: 'howToHandle',
        hideItem: tabNumber === '6' ? undefined : true,
        render: (text, record) => {
          return <div dangerouslySetInnerHTML={{ __html: text }} />;
        },
      },
      {
        title: formatMessage({ id: '创建时间' }),
        dataIndex:
          tabNumber == -1 ||
          tabNumber == 9 ||
          tabNumber == 10 ||
          (tabNumber == '-2' && allTabs == '-1')
            ? 'importDate'
            : 'createTime',
        key: 'createTime',
        sorter: (a, b) => handleSorterDate(a, b),
        render: (text, record) => {
          return <div dangerouslySetInnerHTML={{ __html: text }} />;
        },
      },
      {
        title:
          tabNumber === '3' ? formatMessage({ id: '更新时间' }) : formatMessage({ id: '签收时间' }),
        dataIndex: 'trackingGMT',
        key: tabNumber === '3' ? 'inTime' : 'signInTime',
        hideItem: tabNumber === '3' || tabNumber === '4' ? undefined : true,
        render: (text, record) => {
          return <div dangerouslySetInnerHTML={{ __html: text }} />;
        },
      },
      {
        title: formatMessage({ id: '失败原因' }),
        dataIndex: 'errorMessage',
        key: 'errorMessage',
        hideItem: tabNumber == '10' ? undefined : true,
        render: (text, record) => {
          return <div dangerouslySetInnerHTML={{ __html: text }} />;
        },
      },
      {
        title: formatMessage({ id: '国内快递公司' }),
        dataIndex: 'expressCompanyName',
        key: 'expressCompanyName',
        hideItem: tabNumber == '8' ? undefined : true,
        render: (text, record) => {
          return <div dangerouslySetInnerHTML={{ __html: text }} />;
        },
      },
      {
        title: formatMessage({ id: '国内快递单号' }),
        dataIndex: 'trackingNumber',
        key: 'trackingNumber',
        hideItem: tabNumber == '8' ? undefined : true,
        render: (text, record) => {
          return <div dangerouslySetInnerHTML={{ __html: text }} />;
        },
      },
      {
        title: formatMessage({ id: '平台交易号' }),
        dataIndex: 'transactionNumber',
        key: 'transactionNumber',
        render: (text, record) => {
          return <div dangerouslySetInnerHTML={{ __html: text }} />;
        },
      },
      {
        title: formatMessage({ id: '是否含电' }),
        dataIndex: 'hasBattery',
        key: 'hasBattery',
        render: (text, record) => {
          return <div dangerouslySetInnerHTML={{ __html: text == 1 ? '是' : '否' }} />;
        },
      },
      {
        title: formatMessage({ id: '申报币种' }),
        dataIndex: 'currencyName',
        key: 'currencyName',
        render: (text, record) => {
          return <div dangerouslySetInnerHTML={{ __html: text }} />;
        },
      },
      {
        title: formatMessage({ id: '预扣金额' }),
        dataIndex: 'withholdingArPrice',
        key: 'withholdingArPrice',
        render: (text, record) => {
          return <div dangerouslySetInnerHTML={{ __html: text }} />;
        },
      },
      {
        title: formatMessage({ id: '未出账单金额' }),
        dataIndex: 'unshippedArPrice',
        key: 'unshippedArPrice',
        render: (text, record) => {
          return <div dangerouslySetInnerHTML={{ __html: text }} />;
        },
      },
      {
        title: formatMessage({ id: '操作' }),
        dataIndex: 'action',
        key: 'action',
        fixed: 'right',
        hideItem:
          tabNumber === '7' ||
          tabNumber === '2' ||
          tabNumber === '3' ||
          (tabNumber === '-2' && allTabs === '-1')
            ? true
            : undefined,
        render: (text, record) => (
          <div>
            <Button
              type="link"
              style={{
                display: ['0', '1', '6'].includes(tabNumber) || record.status == '8' ? '' : 'none',
              }}
              onClick={() => {
                logSave(LogType.packet22);
                sessionStorage.setItem('PATHNAME', '/smallBag/orderManagement/makeEditing');
                router.push(
                  `/smallBag/orderManagement/makeEditing?userId=${record.userId}&expressCode=${record.waybillNumber}&state=已制单`
                );
              }}
            >
              {formatMessage({ id: '编辑' })}
            </Button>
            <Button
              type="link"
              style={{
                display:
                  tabNumber == '0' || tabNumber == '1' || tabNumber == '2' || tabNumber == '-2'
                    ? ''
                    : 'none',
              }}
              onClick={() => this.clickPrintLabel(record)}
            >
              {formatMessage({ id: '打印' })}
            </Button>
            <Button
              type="link"
              style={{ display: tabNumber === '4' ? '' : 'none' }}
              onClick={() => this.getSignature(record)}
            >
              {formatMessage({ id: '签收图片' })}
            </Button>

            <Button
              type="link"
              style={{ display: tabNumber === '5' ? '' : 'none' }}
              onClick={() => {
                logSave(LogType.packet25);
                sessionStorage.setItem('PATHNAME', '/smallBag/orderManagement/creatOrder');
                router.push(
                  `/smallBag/orderManagement/creatOrder?userId=${record.userId}&expressCode=${record.waybillNumber}`
                );
              }}
            >
              {formatMessage({ id: '重新制单' })}
            </Button>

            <Popconfirm
              title={formatMessage({ id: '是否取消' })}
              okText={formatMessage({ id: '是' })}
              cancelText={formatMessage({ id: '否' })}
              onConfirm={() => this.canceIntercept(record)}
              onCancel={() => this.setState({ isCancel1: false })}
            >
              <Button
                type="link"
                style={{ display: tabNumber === '6' ? '' : 'none' }}
                onClick={() => this.clickCanceIntercept(record)}
              >
                {formatMessage({ id: '取消截留' })}
              </Button>
            </Popconfirm>
            <Button
              type="link"
              style={{ display: tabNumber === '6' ? '' : 'none' }}
              onClick={() => this.interceptRecord(record)}
            >
              {formatMessage({ id: '查看截留记录' })}
            </Button>
            <Button
              type="link"
              style={{ display: tabNumber === '9' || tabNumber === '10' ? '' : 'none' }}
              onClick={() => {
                logSave(LogType.packet22);
                sessionStorage.setItem('PATHNAME', '/smallBag/orderManagement/creatOrder');
                router.push(
                  `/smallBag/orderManagement/creatOrder?userId=${record.userId}&id=${record.id}`
                );
              }}
            >
              {formatMessage({ id: '编辑' })}
            </Button>
            <Popconfirm
              title={formatMessage({ id: '是否确认删除' })}
              onConfirm={() => this.deleteOrder(record)}
              okText={formatMessage({ id: '是' })}
              cancelText={formatMessage({ id: '否' })}
            >
              <Button
                type="link"
                disabled={batchMakeOrderLoading}
                style={{ display: tabNumber === '9' || tabNumber === '10' ? '' : 'none' }}
              >
                {formatMessage({ id: '删除' })}
              </Button>
            </Popconfirm>
          </div>
        ),
      },
    ];

    const changeColumnsArray = (tabNumber, allTabs) => {
      const columnStateMap =
        localStorage.getItem(persistenceKey) ?? changeColumnState(tabNumber, allTabs)?.defaultValue;
      const persistenceKey = changeColumnState(tabNumber, allTabs)?.persistenceKey;

      const newColumns = columns?.map(item => {
        if (columnStateMap?.hasOwnProperty(item?.key)) {
          return {
            ...item,
            ...columnStateMap?.[item.key],
          };
        } else {
          return item;
        }
      });
      return newColumns;
    };

    const rowSelection = { onChange: this.onTableSelectChange, selectedRowKeys: selectedRowKeys };
    const paginationProps = {
      total: total,
      current: currentPage,
      pageSize: pageSize,
      onChange: this.changePage,
      showSizeChanger: true,
      pageSizeOptions: ['10', '20', '50', '100', '500', '1000'],
      // onShowSizeChange: this.onShowSizeChange,
      showTotal: () => {
        return `${formatMessage({ id: '共' })}- ${total} -${formatMessage({ id: '条记录' })}`;
      },
    };

    const menu = (
      <Menu
        onClick={this.onClickMenu}
        items={[
          {
            label: formatMessage({ id: '生成交接单' }),
            key: '1',
          },
          // {
          //   label: formatMessage({id: '申请截留'}),
          //   key: '2',
          // },
          {
            label: formatMessage({ id: '导出' }),
            key: '3',
          },
          // {
          //   label: formatMessage({id: '取消'}),
          //   key: '4',
          // },
        ]}
      />
    );
    const menus = (
      <Menu
        onClick={this.onClickMenu}
        items={[
          {
            label: formatMessage({ id: '打印标签' }),
            key: '5',
          },
          {
            label: formatMessage({ id: '导出' }),
            key: '3',
          },
        ]}
      />
    );
    const menues = (
      <Menu
        onClick={this.onClickMenu}
        items={[
          {
            label: formatMessage({ id: '打印标签' }),
            key: '5',
          },
          // {
          //   label: formatMessage({id: '申请截留'}),
          //   key: '2',
          // },
          // {
          //   label: formatMessage({id: '取消'}),
          //   key: '4',
          // },
        ]}
      />
    );
    const tabNineOrTenMenu = (
      <Menu
        onClick={this.onClickMenu}
        items={[
          {
            label: formatMessage({ id: '导出' }),
            key: '3',
          },
          {
            label: formatMessage({ id: '提交制单' }),
            key: '7',
          },
          {
            label: `${formatMessage({ id: '提交制单' })}-${formatMessage({ id: '全部' })}`,
            key: '8',
          },
        ]}
      />
    );

    const tabNineOrTenMenu1 = (
      <Menu
        onClick={this.onClickMenu}
        items={[
          {
            label: formatMessage({ id: '导出' }),
            key: '3',
          },
          {
            label: `${formatMessage({ id: '提交制单' })}-${formatMessage({ id: '全部' })}`,
            key: '8',
          },
        ]}
      />
    );

    const tabsStateNum = [
      {
        noVoucherNum,
        makeFailureNum,
        voucherNum,
        confirmDeliveryNum,
        haveGoods,
        deliveryNum,
        delieveredNum,
        cancelNum,
        interceptNum,
        sendAbnormal,
        productNum,
        totalNum,
      },
    ];

    return (
      <div>
        <div className="orderSearch" style={{ padding: '15px 20px', background: '#fff' }}>
          <Form labelAlign="right">
            <StandardFormRow title={formatMessage({ id: '制单账号' })} block>
              <Form.Item style={{ marginRight: 70 }} className="form_item_bottom">
                {getFieldDecorator('shippers', {
                  initialValue: userId,
                  rules: [
                    {
                      required: true,
                      message: formatMessage({ id: '请至少选择一个制单账号' }),
                    },
                  ],
                })(
                  <SingleTagSelect
                    actionsText={actionsTextMap}
                    className="open"
                    // expandable={shipperOpen}
                    onChange={this.shipperChange}
                  >
                    {account &&
                      account.map((element, index) => (
                        <SingleTagSelect.Option key={index} value={element?.accountCode}>
                          {element.warehouseName ?? ''}
                          {element?.accountCode}
                        </SingleTagSelect.Option>
                      ))}
                  </SingleTagSelect>
                )}
              </Form.Item>
            </StandardFormRow>
            <Row>
              <Col span={6} style={{ display: !queryState ? '' : 'none' }}>
                <Form.Item
                  label={formatMessage({ id: '运单号' })}
                  {...formItemLayout}
                  className="form_item_bottom"
                >
                  {getFieldDecorator('orderNumbers', {
                    initialValue: awbNum,
                    rules: [{ validator: this.validateOrderNumbers }],
                  })(<TextArea allowClear rows={4}></TextArea>)}
                </Form.Item>
              </Col>
              <Col
                span={6}
                style={{
                  textAlign: 'left',
                  height: '40px',
                  lineHeight: '40px',
                  display: !queryState ? '' : 'none',
                }}
              >
                <Button
                  type="primary"
                  style={{ marginRight: 20 }}
                  onClick={this.searchOrder}
                  loading={loadingButton[0]}
                >
                  {formatMessage({ id: '查询' })}
                </Button>
                <Button style={{ marginRight: 20 }} onClick={this.resetSelect}>
                  {formatMessage({ id: '重置' })}
                </Button>
                <Button onClick={this.accurateQuery}>{formatMessage({ id: '模糊查询' })}</Button>
              </Col>
              <Col span={24} style={{ display: queryState ? '' : 'none' }}>
                <Row>
                  <Col span={7}>
                    <Form.Item
                      label={formatMessage({ id: '产品名称' })}
                      name="productName"
                      {...formItemLayoutCen}
                      style={{ marginBottom: '14px' }}
                    >
                      {getFieldDecorator(
                        'channel',
                        {}
                      )(
                        <Select
                          showSearch
                          allowClear
                          placeholder={formatMessage({ id: '请选择产品' })}
                          onChange={this.changeProductName}
                          optionFilterProp="children"
                        >
                          {channels && [
                            <Select.Option key={'all'} value="all">
                              全部
                            </Select.Option>,
                            ...channels
                              ?.filter(v => v.status === '1')
                              ?.map((element, index) => (
                                <Select.Option key={index} value={element.id}>
                                  {element.nameCh}
                                </Select.Option>
                              )),
                          ]}
                        </Select>
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label={formatMessage({ id: '目的地' })}
                      {...formItemLayoutCen}
                      style={{ marginBottom: '14px' }}
                    >
                      {getFieldDecorator(
                        'countries',
                        {}
                      )(
                        <Select
                          showSearch
                          allowClear
                          placeholder={formatMessage({ id: '请选择国家' })}
                          optionFilterProp="children"
                          onChange={this.changeCountries}
                        >
                          {countriesData &&
                            countriesData.map((element, index) => (
                              <Select.Option key={index} value={element.id}>
                                {element.nameCh +
                                  '/' +
                                  element.countryNamePinYin +
                                  '/' +
                                  element.nameEn +
                                  '/' +
                                  element.code}
                              </Select.Option>
                            ))}
                        </Select>
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={7}>
                    <Form.Item
                      label={formatMessage({ id: '打印状态' })}
                      {...formItemLayoutCen}
                      style={{ marginBottom: '14px' }}
                    >
                      {getFieldDecorator('channel2', {
                        initialValue: isPrints,
                      })(
                        <Select
                          placeholder={formatMessage({ id: '请选择打印状态' })}
                          onChange={this.changeIsPrint}
                          labelInValue
                          // defaultValue={{ value: '' }}
                        >
                          <Option value="">{formatMessage({ id: '全部状态' })}</Option>
                          <Option value="1">{formatMessage({ id: '已打印' })}</Option>
                          <Option value="0">{formatMessage({ id: '未打印' })}</Option>
                        </Select>
                      )}
                    </Form.Item>
                  </Col>
                </Row>
                <Row>
                  <Col span={7} style={{ display: 'flex' }}>
                    <Form.Item
                      label={formatMessage({ id: '单号查询' })}
                      {...formItemLayoutCen}
                      style={{ marginBottom: '14px', width: '100%' }}
                    >
                      {getFieldDecorator('WaybillNumbers', {
                        initialValue: awbNum,
                      })(
                        <TextArea
                          allowClear
                          style={{ verticalAlign: 'top' }}
                          placeholder={`${formatMessage({ id: '请输入订单' })}/${formatMessage({
                            id: '运单号',
                          })}/${formatMessage({ id: '平台交易号' })}/${formatMessage({
                            id: '转单号',
                          })}，${formatMessage({
                            id: '最多输入',
                          })}500${formatMessage({
                            id: '个单号',
                          })}，${formatMessage({ id: '多单号请以逗号' })}、${formatMessage({
                            id: '空格或回车隔开',
                          })}`}
                          rows={4}
                        ></TextArea>
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <div style={{ marginBottom: '14px' }}>
                      <Form.Item
                        label={formatMessage({ id: '快速查询' })}
                        {...formItemLayoutCen}
                        className="form_item_bottom"
                      >
                        {getFieldDecorator(
                          'quickQuery',
                          {}
                        )(
                          <Input
                            allowClear
                            placeholder={formatMessage({ id: '可模糊搜索当前页列表信息' })}
                          />
                        )}
                      </Form.Item>
                    </div>

                    <div>
                      <Form.Item
                        label={formatMessage({ id: '创建日期' })}
                        {...formItemLayoutCen}
                        className="form_item_bottom"
                      >
                        {/* <RangePicker
                          format={dateFormat}
                          onChange={this.changeDate}
                          showTime
                          style={{ width: '100%' }}
                          value={
                            startTimes === undefined || endTimes === undefined
                              ? null
                              : [moment(startTimes, dateFormat), moment(endTimes, dateFormat)]
                          }
                        ></RangePicker> */}
                        <CustomerRangePicker
                          onChange={this.changeDate}
                          format={dateFormat}
                          value={
                            startTimes === undefined || endTimes === undefined
                              ? null
                              : [moment(startTimes, dateFormat), moment(endTimes, dateFormat)]
                          }
                        />
                      </Form.Item>
                    </div>
                  </Col>
                  <Col span={7} style={{ textAlign: 'left', height: '40px', lineHeight: '40px' }}>
                    <div style={{ marginBottom: '14px' }}>
                      <Form.Item
                        label={formatMessage({ id: '收件人姓名' })}
                        {...formItemLayoutCen}
                        className="form_item_bottom"
                      >
                        {getFieldDecorator(
                          'receiverName',
                          {}
                        )(
                          <Input
                            allowClear
                            placeholder={formatMessage({ id: '请输入收件人姓名' })}
                          />
                        )}
                      </Form.Item>
                    </div>
                    <div style={{ marginBottom: '14px', marginLeft: '40px' }}>
                      <Space>
                        <Button
                          type="primary"
                          style={{ marginRight: 20 }}
                          onClick={this.searchOrder}
                          loading={loadingButton[0]}
                        >
                          {formatMessage({ id: '查询' })}
                        </Button>
                        <Button style={{ marginRight: 20 }} onClick={this.resetSelect}>
                          {formatMessage({ id: '重置' })}
                        </Button>
                      </Space>
                    </div>
                    {/* <Button onClick={this.fuzzyQuery}>{formatMessage({id: '运单查询'})}</Button> */}
                  </Col>
                </Row>
                {/* <Row style={{ marginTop: '14px' }}>

                    </Row> */}
              </Col>
            </Row>
          </Form>
        </div>
        <div className="orderInquiry_content">
          <Row
            wrap
            gutter={[10, 10]}
            style={{ justifyContent: 'space-between', marginBottom: '15px' }}
          >
            {/* <KeepAlive> */}
            {/* <Tabs
                  activeKey={tabNumber}
                  className="tabsList"
                  size="samll"
                  animated={false}
                  onChange={this.tabOnChange}
                  tabPosition="top"
                  id="candidates-tab-wrap"
                >
                  <TabPane tab={`{formatMessage({id: '未制单'})}(${noVoucherNum})`} key={'9'}></TabPane>
                  <TabPane tab={`{formatMessage({id: '制单失败'})}(${makeFailureNum})`} key={'10'}></TabPane>
                  <TabPane tab={`{formatMessage({id: '已制单'})}(${voucherNum})`} key={'0'}></TabPane>
                  <TabPane tab={`{formatMessage({id: '已确认发货'})}(${confirmDeliveryNum})`} key={'1'}></TabPane>
                  <TabPane tab={`{formatMessage({id: '已收货'})}(${haveGoods})`} key={'2'}></TabPane>
                  <TabPane tab={`{formatMessage({id: '运输途中'})}(${deliveryNum})`} key={'3'}></TabPane>
                  <TabPane tab={`{formatMessage({id: '已妥投'})}(${delieveredNum})`} key={'4'}></TabPane>
                  <TabPane tab={`{formatMessage({id: '已取消'})}(${cancelNum})`} key={'5'}></TabPane>
                  <TabPane tab={`{formatMessage({id: '已截留'})}(${interceptNum})`} key={'6'}></TabPane>
                  <TabPane tab={`{formatMessage({id: '派送异常'})}(${sendAbnormal})`} key={'7'}></TabPane>
                  <TabPane tab={`{formatMessage({id: '异常'})}/{formatMessage({id: '退件'})}(${productNum})`} key={'8'}></TabPane>
                  <TabPane tab={`{formatMessage({id: '全部'})}(${totalNum})`} key={'-2'}></TabPane>
                </Tabs> */}
            <Col>
              {/* <Radio.Group
                    onChange={value => this.tabOnChange(value)}
                  >
                    {
                      tabsData.map((item, index) => (
                        <Tooltip title={item.title}>
                          <Radio.Button key={item.key} value={item.key}>
                            {item.label}
                            {item.num}
                          </Radio.Button>
                        </Tooltip>
                      ))}
                  </Radio.Group> */}
              <OrderInquiryTabs
                {...this.props}
                tabOnChange={value => this.tabOnChange(value)}
                tabsStateNum={tabsStateNum}
                defaultValue={tabNumber}
              />
              {tabNumber == '7' && (
                <span
                  style={{ color: 'red', fontSize: '12px', paddingLeft: '8px', lineHeight: '35px' }}
                >
                  {formatMessage({ id: '温馨提示' })}：
                  {formatMessage({ id: '仓内异常提供方案为确认费用' })}、
                  {formatMessage({ id: '修改信息时默认状态为' })}【
                  {formatMessage({ id: '追踪结束' })}】，
                  {formatMessage({ id: '但包裹有新的节点更新时会同步变更成新的状态' })}。
                </span>
              )}
            </Col>
            {/* </KeepAlive> */}

            <Col>
              <div className="tabel_o_i">
                <Radio.Group
                  style={{
                    display: tabNumber == '-2' || tabNumber == '-1' ? 'flex' : 'none',
                    alignItems: 'center',
                  }}
                  onChange={e => this.onChangeRadios(e)}
                  value={allTabs}
                >
                  <Radio value={'-2'}>{formatMessage({ id: '已制单' })}</Radio>
                  <Radio value={'-1'}>{formatMessage({ id: '未制单' })}</Radio>
                </Radio.Group>
                <Button
                  type="primary"
                  disabled={batchMakeOrderLoading}
                  style={{ display: tabNumber == 3 || tabNumber == 4 ? '' : 'none' }}
                  onClick={() => this.clickDeliveryProof()}
                >
                  {formatMessage({ id: '证明申请' })}
                </Button>
                <Button
                  disabled={batchMakeOrderLoading}
                  loading={downloadSignatureLoading}
                  style={{ display: tabNumber == 4 ? '' : 'none' }}
                  onClick={() => this.downloadSignature()}
                >
                  {formatMessage({ id: '签收图片' })}
                </Button>
                <Button type="primary" disabled={batchMakeOrderLoading} style={{ display: 'none' }}>
                  {formatMessage({ id: '签收证明' })}
                </Button>
                <AuthStatusNode authKey="selfSendForecast">
                  <Button
                    type="primary"
                    disabled={batchMakeOrderLoading}
                    onClick={() => {
                      router.push(`/smallBag/orderManagement/deliveredForecast`);
                    }}
                    style={{ display: !HKShow && tabNumber == 0 ? '' : 'none' }}
                  >
                    {formatMessage({ id: '去预报' })}
                  </Button>
                </AuthStatusNode>
                <Button
                  type="primary"
                  disabled={batchMakeOrderLoading}
                  onClick={this.confirmDelivery}
                  style={{ display: tabNumber == 0 ? '' : 'none' }}
                >
                  {formatMessage({ id: '确认发货' })}
                </Button>
                <Button
                  type={'primary'}
                  id="print"
                  style={{
                    display: HKShow && tabNumber == 0 ? '' : 'none',
                  }}
                  onClick={() => this.printHKPackageNumber()}
                >
                  <Space>{formatMessage({ id: '打印揽收面单' })}</Space>
                </Button>
                <Button
                  type={tabNumber == 3 || tabNumber == 4 || tabNumber == 7 ? '' : 'primary'}
                  id="print"
                  style={{
                    display:
                      tabNumber == 0 ||
                      tabNumber == 2 ||
                      tabNumber == 3 ||
                      tabNumber == 4 ||
                      tabNumber == 7
                        ? ''
                        : 'none',
                  }}
                  onClick={() => this.clickPrintLabel('all')}
                >
                  <Space>{formatMessage({ id: '打印标签' })}</Space>
                </Button>

                <Button
                  type="primary"
                  disabled={batchMakeOrderLoading}
                  style={{ display: tabNumber == 1 ? '' : 'none' }}
                  onClick={() => this.cancelConfirmDelivery()}
                >
                  {formatMessage({ id: '取消确认发货' })}
                </Button>
                <Popconfirm
                  title={formatMessage({ id: '是否取消' })}
                  visible={isCancel}
                  okText={formatMessage({ id: '是' })}
                  cancelText={formatMessage({ id: '否' })}
                  onConfirm={() => this.canceIntercept('all')}
                  onCancel={() => this.setState({ isCancel: false })}
                >
                  <Button
                    type="primary"
                    disabled={batchMakeOrderLoading}
                    onClick={() => this.clickCanceIntercept('all')}
                    style={{
                      display: tabNumber == 6 ? '' : 'none',
                    }}
                  >
                    {formatMessage({ id: '取消截留' })}
                  </Button>
                </Popconfirm>

                <Popconfirm
                  title={formatMessage({ id: '是否确认删除' })}
                  onConfirm={() => this.deleteOrder('all')}
                  okText={formatMessage({ id: '是' })}
                  cancelText={formatMessage({ id: '否' })}
                >
                  <Button
                    type={tabNumber == 9 || tabNumber == 10 ? '' : 'primary'}
                    style={{ display: tabNumber == 9 || tabNumber == 10 ? '' : 'none' }}
                    disabled={batchMakeOrderLoading}
                  >
                    {formatMessage({ id: '删除' })}
                  </Button>
                </Popconfirm>
                <Button
                  type={
                    tabNumber == 1 ||
                    tabNumber == 2 ||
                    tabNumber == 3 ||
                    tabNumber == 4 ||
                    tabNumber == 6 ||
                    tabNumber == 9 ||
                    tabNumber == 10
                      ? ''
                      : 'primary'
                  }
                  disabled={batchMakeOrderLoading}
                  onClick={this.batchMakeOrder}
                  style={{
                    display:
                      tabNumber == 0 || tabNumber == -2 || tabNumber == 9 || tabNumber == 10
                        ? 'none'
                        : '',
                  }}
                >
                  {formatMessage({ id: '导出' })}
                </Button>
                {tabNumber == 0 || tabNumber == 1 || tabNumber == 2 ? (
                  <>
                    <Button onClick={() => this.applyWithholding()}>
                      {formatMessage({ id: '申请截留' })}
                    </Button>
                  </>
                ) : null}
                {tabNumber == 0 || tabNumber == 1 ? (
                  <>
                    <Button onClick={() => this.cancelOrder()}>
                      {formatMessage({ id: '取消' })}
                    </Button>
                  </>
                ) : null}
                <Button
                  type="primary"
                  disabled={batchMakeOrderLoading}
                  onClick={() => {
                    this.setState(
                      {
                        menuClickKey: undefined,
                      },
                      this.clickSubmitVoucher
                    );
                  }}
                  style={{
                    display: tabNumber == 9 || tabNumber == 10 ? '' : 'none',
                  }}
                >
                  {formatMessage({ id: '提交制单' })}
                </Button>

                <Col style={{ display: tabNumber == 0 ? '' : 'none' }}>
                  <Dropdown overlay={menu} trigger={['click']} placement="bottomRight">
                    <a onClick={e => e.preventDefault()}>
                      <Space>
                        {formatMessage({ id: '更多' })}
                        <DownOutlined />
                      </Space>
                    </a>
                  </Dropdown>
                </Col>
                <Col style={{ display: tabNumber == 1 ? '' : 'none' }}>
                  <Dropdown overlay={menues} trigger={['click']} placement="bottomRight">
                    <a onClick={e => e.preventDefault()}>
                      <Space>
                        {formatMessage({ id: '更多' })}
                        <DownOutlined />
                      </Space>
                    </a>
                  </Dropdown>
                </Col>
                <Col style={{ display: tabNumber == -2 && allTabs == '-2' ? '' : 'none' }}>
                  <Dropdown overlay={menus} trigger={['click']} placement="bottomRight">
                    <a onClick={e => e.preventDefault()}>
                      <Space>
                        {formatMessage({ id: '更多' })}
                        <DownOutlined />
                      </Space>
                    </a>
                  </Dropdown>
                </Col>
                <Col
                  style={{
                    display:
                      tabNumber == 9 || tabNumber == 10 || (tabNumber == -2 && allTabs == -1)
                        ? ''
                        : 'none',
                  }}
                >
                  <Dropdown
                    overlay={
                      tabNumber == 9 || tabNumber == 10 ? tabNineOrTenMenu1 : tabNineOrTenMenu
                    }
                    trigger={['click']}
                    placement="bottomRight"
                  >
                    <a onClick={e => e.preventDefault()}>
                      <Space>
                        {formatMessage({ id: '更多' })}
                        <DownOutlined />
                      </Space>
                    </a>
                  </Dropdown>
                </Col>
              </div>
            </Col>
          </Row>

          <div className="checkNum" style={{ display: selectedRows.length ? '' : 'none' }}>
            <p>
              {formatMessage({ id: '已选' })}&nbsp;{selectedRows.length}&nbsp;
              {formatMessage({ id: '项' })}
            </p>
            <p style={{ color: '#1890ff', cursor: 'pointer' }} onClick={this.clickUncheck}>
              {formatMessage({ id: '取消选择' })}
            </p>
          </div>
          <div style={{ display: 'none' }}>
            <Image.PreviewGroup
              preview={{
                visible: boxImageVisible,
                onVisibleChange: value => {
                  this.setState({
                    boxImageVisible: value,
                  });
                },
              }}
            >
              {boxImage}
            </Image.PreviewGroup>
          </div>
          <ProTable
            rowKey={(render, index) => render?.id ?? render?.waybillNumber}
            rowSelection={rowSelection}
            pagination={paginationProps}
            dataSource={dataSource}
            loading={this.state.loading || signatureLoading}
            columns={changeColumnsArray(tabNumber, allTabs).filter(
              item => item.hideItem === undefined
            )}
            scroll={{ x: 'max-content' }}
            search={false}
            options={{
              reload: false,
              density: false,
            }}
            columnsState={{
              persistenceKey: changeColumnState(tabNumber, allTabs)?.persistenceKey,
              // defaultValue: changeColumnState(tabNumber, allTabs)?.defaultValue,
              persistenceType: 'localStorage',
            }}
          />
        </div>
        <Modal
          width="70%"
          title={<span style={{ fontSize: '20px' }}>{modalTitle}</span>}
          visible={createModalShow}
          onCancel={this.closeModal}
          footer={false}
          destroyOnClose={true}
        >
          <PlamodalInfo
            childrenData={childrenData}
            channels={channels}
            shippers={account}
            cancelCreate={this.closeModal}
            tabNumber={tabNumber}
            madeSuccess={this.madeSuccess}
          ></PlamodalInfo>
        </Modal>
        {/* {formatMessage({id: '申请截留弹框'})} */}
        <Modal
          title={`${formatMessage({ id: '选中' })} ${this.state.checkedNum} ${formatMessage({
            id: '票运件',
          })}`}
          visible={visible}
          width={600}
          onOk={() => this.handleOk()}
          onCancel={this.handleCancel}
          afterClose={e => {
            this.props.form.resetFields(['optionWay', 'remark']);
            this.setState({
              valNumber: 104,
              MFvisable: false,
            });
          }}
          footer={[
            <Button
              key="submit"
              type="primary"
              loading={loadingModal}
              onClick={() => this.handleOk()}
            >
              {formatMessage({ id: '提交' })}
            </Button>,
            <Button key="back" onClick={this.handleCancel}>
              {formatMessage({ id: '取消' })}
            </Button>,
          ]}
        >
          <Form {...formItemLayout}>
            <Form.Item label={formatMessage({ id: '处理方式' })}>
              {getFieldDecorator('optionWay', {
                initialValue: valNumber,
              })(
                <Radio.Group onChange={val => this.onChangeM(val)}>
                  {/* <Radio style={radioStyle} value={1}>
                          {formatMessage({id: '截留后退回'})}
                        </Radio>
                        <Radio style={radioStyle} value={3}>
                          {formatMessage({id: '截留后换单'})}
                        </Radio>
                        <Radio style={radioStyle} value={4}>
                          {formatMessage({id: '截留放置待发'})}
                        </Radio> */}
                  <Radio style={radioStyle} value={104}>
                    {formatMessage({ id: '退回' })}-{formatMessage({ id: '司机' })}
                  </Radio>
                  <Radio style={radioStyle} value={106}>
                    {formatMessage({ id: '退回' })}-{formatMessage({ id: '自取' })}
                  </Radio>
                  <Radio style={radioStyle} value={1051}>
                    {formatMessage({ id: '退回' })}-{formatMessage({ id: '快递寄付' })}
                  </Radio>
                  <Radio style={radioStyle} value={1052}>
                    {formatMessage({ id: '退回' })}-{formatMessage({ id: '快递到付' })}
                  </Radio>
                  <Radio style={radioStyle} value={3}>
                    {formatMessage({ id: '截留后换单' })}
                  </Radio>
                  <Radio style={radioStyle} value={4}>
                    {formatMessage({ id: '截留放置待发' })}
                  </Radio>
                </Radio.Group>
              )}
            </Form.Item>
            {MFvisable ? (
              <Form.Item label={formatMessage({ id: '新单号' })}>
                {getFieldDecorator('convertNumber', {
                  rules: [
                    { required: true, message: formatMessage({ id: '请填写新的单号' }) },
                    {
                      pattern: '^[0-9a-zA-Z]+$',
                      message: formatMessage({ id: '不能出现数字及字母外的字符' }),
                    },
                  ],
                })(<Input placeholder={formatMessage({ id: '请输入新单号' })} />)}
              </Form.Item>
            ) : null}
            <Form.Item label={formatMessage({ id: '备注' })}>
              {getFieldDecorator('remark')(<TextArea rows={4} />)}
            </Form.Item>
          </Form>
        </Modal>
        {/* {formatMessage({id: '取消运单弹框'})} */}
        <Modal
          title={formatMessage({ id: '取消订单' })}
          visible={isCancelWayBill}
          confirmLoading={confirmLoading}
          onOk={this.cancelMakeOrder}
          onCancel={() => {
            this.setState({ isCancelWayBill: false, cancelOrderReason: '' });
          }}
          footer={[
            <Button
              key="submit"
              type="primary"
              loading={loadingModal}
              onClick={() => this.cancelMakeOrder()}
            >
              {formatMessage({ id: '确定' })}
            </Button>,
            <Button
              key="back"
              onClick={() => {
                this.setState({ isCancelWayBill: false, cancelOrderReason: '' });
              }}
            >
              {formatMessage({ id: '取消' })}
            </Button>,
          ]}
        >
          <p>
            {formatMessage({ id: '请输入取消订单原因' })}（{formatMessage({ id: '最多支持' })}100
            {formatMessage({ id: '个字符' })}）
          </p>
          <TextArea
            rows={4}
            value={cancelOrderReason}
            onChange={this.changeCancelOrderReason}
            placeholder={`${formatMessage({ id: '请输入取消运单原因' })}(${formatMessage({
              id: '最多支持',
            })}100${formatMessage({ id: '个字符' })})`}
            maxLength={100}
          />
        </Modal>
        {/* {formatMessage({id: '打印标签弹框'})} */}
        <Modal
          title={formatMessage({ id: '打印标签' })}
          visible={isPrintLabel}
          onCancel={() => {
            this.setState({ quantityPrinted: 0, isPrintLabel: false, awbRrintLabel: [] });
          }}
          footer={[
            <Button
              key="submit"
              type="primary"
              loading={printLabelLoading}
              onClick={() => this.printLabel(formatMessage({ id: '打印' }))}
            >
              {formatMessage({ id: '下载' })}
            </Button>,
            <Button
              type="primary"
              loading={printLabelLoading}
              onClick={() => this.printLabel(formatMessage({ id: '预览' }))}
            >
              {formatMessage({ id: '预览' })}
            </Button>,
            <Button
              key="back"
              onClick={() => {
                this.setState({ quantityPrinted: 0, isPrintLabel: false, awbRrintLabel: [] });
              }}
            >
              {formatMessage({ id: '取消' })}
            </Button>,
          ]}
        >
          <Row style={{ paddingBottom: '16px' }}>
            <span>{formatMessage({ id: '打印' })}：</span>
            <Checkbox disabled checked>
              {formatMessage({ id: '标签' })}
            </Checkbox>
            <Checkbox onChange={this.onChangeRadio} checked={value}>
              {formatMessage({ id: '拣货单' })}
            </Checkbox>
          </Row>
          <Row style={{ paddingBottom: '16px' }}>
            <Space>
              <span>是否标记为已打印:</span>
              <Radio.Group onChange={this.onChangeType} defaultValue={true}>
                <Radio value={true}>是</Radio>
                <Radio value={false}>否</Radio>
              </Radio.Group>
            </Space>
          </Row>
          {quantityPrinted > 0 && (
            <Row style={{ paddingBottom: '16px' }}>
              <span style={{ fontSize: '12px', color: 'red' }}>
                温馨提醒：有{quantityPrinted}
                票运单系统显示已生成面单，请确认是否已打印，避免重复打印造成重单
              </span>
            </Row>
          )}
          <Row>
            <span style={{ fontSize: '12px', color: 'red' }}>
              注：标记为已打印的订单，预览页面的打印状态才会生效！
            </span>
          </Row>
        </Modal>
        <Modal
          title={formatMessage({ id: '打印揽收面单' })}
          visible={HKPrintLabel}
          onCancel={() => {
            this.setState({ HKPrintLabel: false });
          }}
          onOk={() => {
            this.HKPrintLabel();
          }}
          confirmLoading={HKPrintLabelLoading}
        >
          <Form>
            <Form.Item label={formatMessage({ id: '重量（KG）' })}>
              {getFieldDecorator('weight', {
                rules: [
                  { required: true, message: formatMessage({ id: '请输入重量' }) },
                  {
                    pattern: '^[1-9]\\d*(\\.\\d{1,3})?$',
                    message: formatMessage({ id: '请输入数字，最大三位小数' }),
                  },
                ],
              })(<Input allowClear placeholder={formatMessage({ id: '请输入重量' })} />)}
            </Form.Item>
          </Form>
          <p style={{ color: 'red', fontSize: '12px', marginTop: '20px' }}>
            {formatMessage({ id: '请填写真实大包毛重，以便获取邮寄大包号' })}
            <br />
            {formatMessage({
              id: '请注意：大包重量超过30公斤会被香港邮局柜台拒收，请务必控制大包重量。',
            })}
            <br />
            {formatMessage({
              id: '请注意：在邮局工作时间内，选择最合适的邮局进行寄递，具体信息请看',
            })}
            <a
              style={{ textDecoration: 'underline' }}
              onClick={() => downloadFile({ url: `/csc/ejf/order/postOfficeInformation/download` })}
            >
              {formatMessage({ id: '邮局清单' })}
            </a>
          </p>
        </Modal>
        {/* 预览pdf */}
        <Modal
          width="70%"
          open={preview}
          onCancel={this.closeReview}
          footer={false}
          destroyOnClose={true}
        >
          {/* <FileViewer
                fileType="pdf" //{formatMessage({id: '文件类型'})}
                filePath={previewURL} //{formatMessage({id: '文件地址'})}
                onError={this.onError.bind(this)}
                errorComponent="" //[{formatMessage({id: '可选'})}]：{formatMessage({id: '发生错误时呈现的组件'})}，{formatMessage({id: '而不是'})}react-file-
                // unsupportedComponent={console.log('{formatMessage({id: '不支持'})}')} //[{formatMessage({id: '可选'})}]：{formatMessage({id: '在不支持文件格式的情况下呈现的组件'})}
              /> */}
          <iframe src={previewURL} width="100%" height="600px" frameBorder="0" />
        </Modal>
        {/* {formatMessage({id: '检测订单号是否已经存在'})} */}
        <Modal
          title={formatMessage({ id: '提示' })}
          visible={isModalErrorVisible}
          closable={false}
          footer={[
            <Button
              key="submit"
              type="primary"
              onClick={() => {
                if (selectTypeRadioValue === undefined)
                  return message.error(formatMessage({ id: '请选择是否继续制单' }));
                this.setState(
                  {
                    isModalErrorVisible: false,
                  },
                  () => {
                    if (selectTypeRadioValue == 1) {
                      if (menuClickKey === '7') {
                        this.clickSubmitVoucher();
                      } else if (menuClickKey === '8') {
                        this.submitVoucherAll();
                      } else if (menuClickKey === undefined) {
                        this.clickSubmitVoucher();
                      }
                    } else {
                      this.setState({
                        selectTypeRadioValue: undefined,
                      });
                    }
                  }
                );
              }}
            >
              {formatMessage({ id: '确定' })}
            </Button>,
          ]}
        >
          <p>{modalErrorContent}</p>
          <Radio.Group
            value={selectTypeRadioValue}
            onChange={e => {
              this.setState({
                selectTypeRadioValue: e.target.value,
              });
            }}
          >
            <Radio value={1}>{formatMessage({ id: '是' })}</Radio>
            <Radio value={2}>{formatMessage({ id: '否' })}</Radio>
          </Radio.Group>
        </Modal>
        {/* {formatMessage({id: '检测订单号是否已经存在'})} */}
        <YanwenOrderDetailsDrawer {...this.props} modalRef={this.drawerRef} />
      </div>
    );
  }
}

export default YanwenOrderMakingInquiry;
