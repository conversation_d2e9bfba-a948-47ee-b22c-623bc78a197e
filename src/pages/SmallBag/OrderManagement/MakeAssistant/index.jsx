import React, { useState, useEffect } from 'react';
import { Tabs } from 'antd';
import { connect } from 'dva';
import PageHeaderBreadcrumb from '@/components/PageHeaderBreadcrumb';
import PageContainerComponent from '@/components/PageContainer';
import ComponentTable from './components/ComponentTable';
import VoucherChecking from './components/VoucherChecking';
import RestrictNameQuery from './components/RestrictNameQuery';
import { formatMessage } from 'umi-plugin-react/locale';
import ProductNameMatchIndex from '@/pages/SmallBag/OrderManagement/MakeAssistant/components/ProductNameMatchIndex';

const { TabPane } = Tabs;

const Index = props => {
  const [tabNum, setTabNum] = useState('6');

  const changeTab = key => {
    setTabNum(key);
  };

  return (
    <PageContainerComponent
      header={{
        title: null,
        breadcrumb: {},
        breadcrumbRender: props => (
          <PageHeaderBreadcrumb {...props} col>
            <Tabs
              defaultActiveKey="6"
              onChange={changeTab}
              style={{
                marginBottom: '-25px',
                borderBottom: '0 solid transparent',
              }}
            >
              <TabPane tab={`美国税率查询`} key="6"></TabPane>
              <TabPane tab={formatMessage({ id: '美国制单校验' })} key="2"></TabPane>
              <TabPane tab={formatMessage({ id: '限制品名查询' })} key="3"></TabPane>
              <TabPane tab={formatMessage({ id: '国家邮编有效城市查询' })} key="5"></TabPane>
              <TabPane tab={`${formatMessage({ id: '印度' })}KYC`} key="1"></TabPane>
            </Tabs>
          </PageHeaderBreadcrumb>
        ),
      }}
    >
      {tabNum == '1' && <ComponentTable tabNum={tabNum} {...props} />}
      {tabNum == '2' && <VoucherChecking tabNum={tabNum} {...props} />}
      {(tabNum == '3' || tabNum == '4' || tabNum == '5') && (
        <RestrictNameQuery tabNum={tabNum} {...props} />
      )}
      {tabNum == '6' && <ProductNameMatchIndex tabNum={tabNum} {...props} />}
    </PageContainerComponent>
  );
};

export default connect(({ loading }) => ({
  checkLoading: loading.effects['makeAssistant/USAddressCheck'],
}))(Index);
