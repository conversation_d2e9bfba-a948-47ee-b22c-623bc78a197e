import React, { useRef, useState } from 'react';
import {
  ProCard,
  ProForm,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import { <PERSON><PERSON>, But<PERSON>, Col, message, Popover, Row, Space, Tooltip } from 'antd';
import { onFormErrorMessage } from '@/utils/utils';
import { formatMessage } from 'umi-plugin-react/locale';
import { connect } from 'dva';

const ProductNameMatchIndex = props => {
  const formRef = useRef();

  const { dispatch, queryLoading, taxQueryLoading, calQueryLoading } = props;
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [typeSelect, setTypeSelect] = useState('1');
  const [productList, setProductList] = useState([]);
  const [showWeight, setShowWight] = useState(false);

  function handleSubmit(values) {
    if (typeSelect === '1' && !values.hsCode && !values.productDescription) {
      message.error('海关编码和商品英文描述至少填写一个');
      return;
    }
    let url = '';
    if (typeSelect == '0') {
      url = 'makeAssistant/getUSAHTSRateList';
    }
    if (typeSelect == '1') {
      url = 'makeAssistant/getUSATaxRateList';
    }
    if (typeSelect == '2') {
      if (values.channelCode) {
        if (!values.declaredValue) {
          message.error('请填写申报价值');
          return;
        }
        if (!values.weight) {
          message.error('请填写重量');
          return;
        }
      }
      url = 'makeAssistant/getUSACalculateRate';
    }
    try {
      dispatch({
        type: url,
        payload: values,
        callback: response => {
          if (response.success) {
            setDataSource(response.data);
          }
        },
      });
    } catch (error) {
      console.log(error);
    }
  }

  const USATaxColumn: any[] = [
    {
      title: '序号',
      dataIndex: 'index',
      valueType: 'index',
      width: 80,
    },
    {
      title: '海关编码',
      dataIndex: 'htsNumber',
      width: 100,
    },
    {
      title: '商品英文描述',
      dataIndex: 'description',
      width: 160,
      render: text => (
        <Tooltip color="#fff" title={<span style={{ color: '#000' }}>{text}</span>}>
          <span
            style={{
              display: 'inline-block',
              maxWidth: '200px',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
          >
            {text}
          </span>
        </Tooltip>
      ),
    },
    {
      title: '商品中文描述',
      dataIndex: 'trans',
      width: 160,
      render: text => (
        <Tooltip color="#fff" title={<span style={{ color: '#000' }}>{text}</span>}>
          <span
            style={{
              display: 'inline-block',
              maxWidth: '200px',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
          >
            {text}
          </span>
        </Tooltip>
      ),
    },
    {
      title: '级别',
      dataIndex: 'indent',
      width: 80,
    },
    {
      title: '基础关税',
      dataIndex: 'generalRate',
      width: 100,
    },
    {
      title: '301加征关税',
      dataIndex: 'tariff301',
      width: 100,
      render: (text, record) => (
        <Popover
          content={
            <div>
              税码: <span style={{ color: '#73d13d' }}>{record.tariff301Number}</span>
            </div>
          }
        >
          {text}
        </Popover>
      ),
    },
    {
      title: '芬太尼关税',
      dataIndex: 'trumpSpecialTariff',
      width: 100,
      render: (text, record) => (
        <Popover
          content={
            <div>
              税码: <span style={{ color: '#73d13d' }}>{record.trumpSpecialTariffNumber}</span>
            </div>
          }
        >
          {text}
        </Popover>
      ),
    },
    {
      title: '反倾销税',
      dataIndex: 'antiDumpingDutyRate',
      width: 100,
      render: (text, record) => (
        <Popover
          content={
            <div>
              税码: <span style={{ color: '#73d13d' }}>{record.antiDumpingNumber}</span>
            </div>
          }
        >
          {text}
        </Popover>
      ),
    },
    {
      title: '反补贴税',
      dataIndex: 'countervailingDutyRate',
      width: 100,
      render: (text, record) => (
        <Popover
          content={
            <div>
              税码: <span style={{ color: '#73d13d' }}>{record.countervailingNumber}</span>
            </div>
          }
        >
          {text}
        </Popover>
      ),
    },
    {
      title: '对等关税',
      dataIndex: 'reciprocalTariff',
      width: 100,
      render: (text, record) => (
        <Popover
          content={
            <div>
              税码: <span style={{ color: '#73d13d' }}>{record.reciprocalNumber}</span>
            </div>
          }
        >
          {text}
        </Popover>
      ),
    },
    {
      title: '232关税',
      dataIndex: 'steelAluminumDutyRate',
      width: 160,
      render: (text, record) => (
        <Popover
          content={
            <div>
              税码: <span style={{ color: '#73d13d' }}>{record.steelAluminumNumber}</span>
            </div>
          }
        >
          {text}
        </Popover>
      ),
    },
    {
      title: '201关税',
      dataIndex: 'tariff201',
      width: 100,
      render: (text, record) => (
        <Popover
          content={
            <div>
              税码: <span style={{ color: '#73d13d' }}>${record.tariff201Number}</span> 元
            </div>
          }
        >
          {text}
        </Popover>
      ),
    },
  ];

  const USAHTSColumn: any[] = [
    // {
    //   title: '序号',
    //   dataIndex: 'index',
    //   valueType: 'index',
    //   width: 80,
    // },
    {
      title: '中文品名',
      dataIndex: 'originName',
      width: 100,
    },
    // {
    //   title: '转义中文品名',
    //   dataIndex: 'aiNameCN',
    //   width: 100,
    // },
    {
      title: '英文品名',
      dataIndex: 'aiNameEN',
      width: 100,
    },
    {
      title: 'HTSCODE',
      dataIndex: 'htsCode',
      width: 100,
    },
    {
      title: '是否可发货',
      dataIndex: 'isWhiteList',
      width: 100,
      render: text => (text ? '是' : '否'),
    },
    {
      title: '基础关税',
      dataIndex: 'generalRate',
      width: 100,
      render: (text, record) => <div>{record.usARateVO?.generalRate ?? '-'}</div>,
    },
    {
      title: '301加征关税',
      dataIndex: 'tariff301',
      width: 100,
      render: (text, record) => (
        <Popover
          content={
            record.usARateVO?.tariff301Number ? (
              <div>
                税码: <span style={{ color: '#73d13d' }}>{record.usARateVO.tariff301Number}</span>
              </div>
            ) : null
          }
        >
          {record.usARateVO?.tariff301 ?? '-'}
        </Popover>
      ),
    },
    {
      title: '芬太尼关税',
      dataIndex: 'trumpSpecialTariff',
      width: 100,
      render: (text, record) => (
        <Popover
          content={
            record.usARateVO?.trumpSpecialTariffNumber ? (
              <div>
                税码:{' '}
                <span style={{ color: '#73d13d' }}>
                  {record.usARateVO.trumpSpecialTariffNumber}
                </span>
              </div>
            ) : null
          }
        >
          {record.usARateVO?.trumpSpecialTariff ?? '-'}
        </Popover>
      ),
    },
    {
      title: '反倾销税',
      dataIndex: 'antiDumpingDutyRate',
      width: 100,
      render: (text, record) => (
        <Popover
          content={
            record.usARateVO?.antiDumpingNumber ? (
              <div>
                税码: <span style={{ color: '#73d13d' }}>{record.usARateVO.antiDumpingNumber}</span>
              </div>
            ) : null
          }
        >
          {record.usARateVO?.antiDumpingDutyRate ?? '-'}
        </Popover>
      ),
    },
    {
      title: '反补贴税',
      dataIndex: 'countervailingDutyRate',
      width: 100,
      render: (text, record) => (
        <Popover
          content={
            record.usARateVO?.countervailingNumber ? (
              <div>
                税码:{' '}
                <span style={{ color: '#73d13d' }}>{record.usARateVO.countervailingNumber}</span>
              </div>
            ) : null
          }
        >
          {record.usARateVO?.countervailingDutyRate ?? '-'}
        </Popover>
      ),
    },
    {
      title: '对等关税',
      dataIndex: 'reciprocalTariff',
      width: 100,
      render: (text, record) => (
        <Popover
          content={
            record.usARateVO?.reciprocalNumber ? (
              <div>
                税码: <span style={{ color: '#73d13d' }}>{record.usARateVO.reciprocalNumber}</span>
              </div>
            ) : null
          }
        >
          {record.usARateVO?.reciprocalTariff ?? '-'}
        </Popover>
      ),
    },
    {
      title: '钢铝关税(232关税)',
      dataIndex: 'steelAluminumDutyRate',
      width: 160,
      render: (text, record) => (
        <Popover
          content={
            record.usARateVO?.steelAluminumNumber ? (
              <div>
                税码:{' '}
                <span style={{ color: '#73d13d' }}>{record.usARateVO.steelAluminumNumber}</span>
              </div>
            ) : null
          }
        >
          {record.usARateVO?.steelAluminumDutyRate ?? '-'}
        </Popover>
      ),
    },
    {
      title: '汽车零部件关税(232关税)',
      dataIndex: 'autoPartsDutyRate',
      width: 180,
      render: (text, record) => (
        <Popover
          content={
            record.usARateVO?.autoPartsNumber ? (
              <div>
                税码: <span style={{ color: '#73d13d' }}>{record.usARateVO.autoPartsNumber}</span>
              </div>
            ) : null
          }
        >
          {record.usARateVO?.autoPartsDutyRate ?? '-'}
        </Popover>
      ),
    },
    {
      title: '201关税',
      dataIndex: 'tariff201',
      width: 100,
      render: (text, record) => (
        <Popover
          content={
            record.usARateVO?.tariff201Number ? (
              <div>
                税码: <span style={{ color: '#73d13d' }}>{record.usARateVO.tariff201Number}</span>
              </div>
            ) : null
          }
        >
          {record.usARateVO?.tariff201 ?? '-'}
        </Popover>
      ),
    },
  ];

  const USACalColumn: any[] = [
    {
      title: '中文品名',
      dataIndex: 'originName',
      width: 100,
    },
    {
      title: '英文品名',
      dataIndex: 'aiNameEN',
      width: 100,
    },
    {
      title: 'HTSCODE',
      dataIndex: 'htsCode',
      width: 100,
    },
    {
      title: '是否可发货',
      dataIndex: 'isWhiteList',
      width: 100,
      render: text => (text ? '是' : '否'),
    },
    {
      title: '基础关税',
      dataIndex: 'generalRateOfDutyProportion',
      width: 100,
    },
    {
      title: '基础税费（CNY）',
      dataIndex: 'generalRateOfDutyProportionValue',
      width: 160,
    },
    {
      title: '301加征关税',
      dataIndex: 'rateOfDuty301',
      width: 100,
    },
    {
      title: '301加征税费（CNY）',
      dataIndex: 'rateOfDuty301Value',
      width: 180,
    },
    {
      title: '芬太尼关税',
      dataIndex: 'fentanylRateOfDuty',
      width: 100,
    },
    {
      title: '芬太尼税费（CNY）',
      dataIndex: 'fentanylRateOfDutyValue',
      width: 180,
    },
    {
      title: '反倾销税',
      dataIndex: 'antiDumpingRateOfDuty',
      width: 100,
    },
    {
      title: '反倾销税费（CNY）',
      dataIndex: 'antiDumpingRateOfDutyValue',
      width: 180,
    },
    {
      title: '反补贴税',
      dataIndex: 'trumpRateOfDuty',
      width: 100,
    },
    {
      title: '反补贴税费（CNY）',
      dataIndex: 'trumpRateOfDutyValue',
      width: 180,
    },
    {
      title: '对等关税',
      dataIndex: 'equivalentTariffRateOfDuty',
      width: 100,
    },
    {
      title: '对等关税税费（CNY）',
      dataIndex: 'equivalentTariffRateOfDutyValue',
      width: 180,
    },
    {
      title: '钢铝关税(232关税)',
      dataIndex: 'steelAndAluminumRateOfDuty',
      width: 160,
    },
    {
      title: '钢铝关税(232关税)税费（CNY）',
      dataIndex: 'steelAndAluminumRateOfDutyValue',
      width: 240,
    },
    {
      title: '总税费（CNY）',
      dataIndex: 'totalDutyValue',
      width: 160,
    },
  ];
  const columnMap = {
    '0': USAHTSColumn,
    '1': USATaxColumn,
    '2': USACalColumn,
  };

  const columns = columnMap[typeSelect];

  const handleReset = () => {
    // @ts-ignore
    formRef.current?.resetFields();
  };

  const onChangeTypeSelect = event => {
    setTypeSelect(event.target.value);
    // @ts-ignore
    formRef.current?.setFieldsValue({
      productName: undefined,
      declaredValue: undefined,
      hsCode: undefined,
      productDescription: undefined,
    });
    setDataSource([]);
    setCurrentPage(1);
    if (event.target.value === '2') {
      getProductList();
    }
  };

  const getProductList = () => {
    dispatch({
      type: 'makeAssistant/getUSAProductList',
      callback: result => {
        if (result.success) {
          const data = result.data.map(item => {
            item.value = item.productNumber;
            item.label = item.productCnName;
            return item;
          });
          setProductList(data);
        }
      },
    });
  };

  return (
    <div>
      <ProCard>
        <ProForm
          formRef={formRef}
          labelAlign="right"
          layout="horizontal"
          labelCol={{ flex: '80px' }}
          onFinish={handleSubmit}
          onFinishFailed={onFormErrorMessage}
          submitter={{
            render: () => {
              return [];
            },
          }}
        >
          {/*<ProFormRadio.Group*/}
          {/*  name="type"*/}
          {/*  label={'查询类型'}*/}
          {/*  radioType="button"*/}
          {/*  options={[*/}
          {/*    // {*/}
          {/*    //   label: '品名库查询',*/}
          {/*    //   value: '0',*/}
          {/*    // },*/}
          {/*    {*/}
          {/*      label: '税则查询',*/}
          {/*      value: '1',*/}
          {/*    },*/}
          {/*    // {*/}
          {/*    //   label: '美国品名试算金额',*/}
          {/*    //   value: '2',*/}
          {/*    // },*/}
          {/*  ]}*/}
          {/*  initialValue={typeSelect}*/}
          {/*  fieldProps={{*/}
          {/*    buttonStyle: 'solid',*/}
          {/*    size: 'small',*/}
          {/*    onChange: onChangeTypeSelect,*/}
          {/*    style: {*/}
          {/*      display: 'flex',*/}
          {/*      gap: '16px', // 设置间距*/}
          {/*    },*/}
          {/*  }}*/}
          {/*></ProFormRadio.Group>*/}

          {typeSelect === '0' && (
            <Row gutter={15}>
              <Col>
                <ProFormText
                  name="productName"
                  label={'中文品名'}
                  fieldProps={{
                    style: { width: '260px' },
                  }}
                  placeholder={'请输入中文品名'}
                  rules={[{ required: true, message: '中文品名不能为空' }]}
                />
              </Col>
              <Col>
                <Space>
                  <Button
                    style={{ marginRight: '18px' }}
                    type="primary"
                    htmlType="submit"
                    loading={queryLoading}
                  >
                    {formatMessage({ id: '查询' })}
                  </Button>
                  <Button onClick={handleReset}>{formatMessage({ id: '重置' })}</Button>
                </Space>
              </Col>
            </Row>
          )}
          {typeSelect === '1' && (
            <Row gutter={15}>
              <Col>
                <ProFormText
                  name="hsCode"
                  label={formatMessage({ id: '海关编码' })}
                  fieldProps={{
                    style: { width: '260px' },
                  }}
                  placeholder={'请输入海关编码'}
                  rules={[{ required: false, message: formatMessage({ id: '海关编码不能为空' }) }]}
                />
              </Col>
              <Col>
                <ProFormText
                  name="productDescription"
                  label={'商品描述'}
                  placeholder={'请输入商品描述'}
                  fieldProps={{
                    style: { width: '260px' },
                  }}
                  rules={[{ required: false, message: '请输入商品描述' }]}
                />
              </Col>
              <Col>
                <Space>
                  <Button
                    style={{ marginRight: '18px' }}
                    type="primary"
                    htmlType="submit"
                    loading={taxQueryLoading}
                  >
                    {formatMessage({ id: '查询' })}
                  </Button>
                  <Button onClick={handleReset}>{formatMessage({ id: '重置' })}</Button>
                </Space>
              </Col>
            </Row>
          )}
          {typeSelect === '2' && (
            <Row gutter={15}>
              <Col>
                <ProFormText
                  name="productName"
                  label={'中文品名'}
                  fieldProps={{
                    style: { width: '260px' },
                  }}
                  placeholder={'请输入中文品名'}
                  rules={[{ required: true, message: '中文品名不能为空' }]}
                />
              </Col>
              <Col>
                <ProFormSelect
                  name="channelCode"
                  label={'产品名称'}
                  showSearch
                  options={productList}
                  placeholder={'请选择产品'}
                  fieldProps={{
                    style: { width: '260px' },
                    onChange: (value, option) => {
                      if (!showWeight) {
                        setShowWight(true);
                      }
                    },
                  }}
                  rules={[{ required: false, message: '请选择产品' }]}
                />
              </Col>
              {showWeight && (
                <>
                  <Col>
                    <ProFormText
                      name="declaredValue"
                      label={'申报价值（USD）'}
                      labelCol={{ span: 8 }}
                      placeholder={'请输入申报价值'}
                      fieldProps={{
                        onKeyPress: event => {
                          // 允许数字、小数点，但不能重复输入小数点
                          const value = event.target.value + event.key;
                          if (!/^[0-9]*\.?[0-9]*$/.test(value)) {
                            event.preventDefault();
                          }
                        },
                        style: { width: '260px' },
                      }}
                      rules={[
                        {
                          required: false,
                          message: '请输入申报价值',
                        },
                        {
                          pattern: /^\d+(\.\d+)?$/,
                          message: '请输入有效的数字（可包含小数点）',
                        },
                      ]}
                    />
                  </Col>
                  <Col>
                    <ProFormText
                      name="weight"
                      label={'申报重量（g）'}
                      labelCol={{ span: 8 }}
                      placeholder={'请输入申报重量'}
                      fieldProps={{
                        style: { width: '260px' },
                        onKeyPress: event => {
                          // 允许数字、小数点，但不能重复输入小数点
                          const value = event.target.value + event.key;
                          if (!/^[0-9]*\.?[0-9]*$/.test(value)) {
                            event.preventDefault();
                          }
                        },
                      }}
                      rules={[
                        {
                          required: false,
                          message: '请输入申报重量',
                        },
                        {
                          pattern: /^\d+(\.\d+)?$/,
                          message: '请输入有效的数字（可包含小数点）',
                        },
                      ]}
                    />
                  </Col>
                  <Col>
                    <ProFormText
                      name="quality"
                      label={'件数'}
                      placeholder={'请输入件数'}
                      fieldProps={{
                        style: { width: '260px' },
                      }}
                      rules={[
                        { required: false, message: '请输入件数' },
                        {
                          validator: (_, value) => {
                            if (value && !/^[1-9]\d*$/.test(value)) {
                              return Promise.reject('请输入最小为1的整数');
                            }
                            return Promise.resolve();
                          },
                        },
                      ]}
                    />
                  </Col>
                </>
              )}
              <Col>
                <Space>
                  <Button
                    style={{ marginRight: '18px' }}
                    type="primary"
                    htmlType="submit"
                    loading={calQueryLoading}
                  >
                    {formatMessage({ id: '查询' })}
                  </Button>
                  <Button onClick={handleReset}>{formatMessage({ id: '重置' })}</Button>
                </Space>
              </Col>
            </Row>
          )}
        </ProForm>
      </ProCard>
      <ProCard style={{ marginTop: 16 }}>
        <Alert
          message={
            <span>
              数据来源于美国CBP官网(
              <a href="https://hts.usitc.gov/" target="_blank" rel="noopener noreferrer">
                https://hts.usitc.gov/
              </a>
              )及反倾销反补贴公开平台(
              <a
                href="https://trade.cbp.dhs.gov/ace/adcvd/adcvd-public/#"
                target="_blank"
                rel="noopener noreferrer"
              >
                https://trade.cbp.dhs.gov/ace/adcvd/adcvd-public/#
              </a>
              )，仅供参考。如需获取详细税则查询流程或更多信息，可参考文档
              <a
                href="#"
                onClick={e => {
                  e.preventDefault();
                  window.open(
                    `/serviceManagement/helpCenter?type=3&id=7330168426842820608&selectId=355`
                  );
                }}
                style={{ marginLeft: '4px' }}
              >
                【帮助中心-美国税则查询说明】
              </a>
              。
            </span>
          }
          type="warning"
          showIcon
          style={{
            marginBottom: 16,
            width: '100%', // 固定宽度
          }}
        />
        <ProTable
          toolBarRender={false}
          search={false}
          columns={columns}
          dataSource={dataSource}
          rowSelection={false}
          bordered={true}
          scroll={{ x: 'max-content' }}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            showSizeChanger: true,
            pageSizeOptions: ['10', '20', '50', '100'],
            showTotal: total => `共 ${total} 条`,
            onShowSizeChange: (page, size) => {
              setCurrentPage(1);
              setPageSize(size);
            },
            onChange: (page, size) => {
              if (size === pageSize) {
                // 只有页码变化时才更新
                setCurrentPage(page);
              }
            },
          }}
        />
      </ProCard>
    </div>
  );
};

export default connect(({ loading }) => ({
  // @ts-ignore
  queryLoading: loading.effects['makeAssistant/getUSAHTSRateList'],
  taxQueryLoading: loading.effects['makeAssistant/getUSATaxRateList'],
  calQueryLoading: loading.effects['makeAssistant/getUSACalculateRate'],
}))(ProductNameMatchIndex);
