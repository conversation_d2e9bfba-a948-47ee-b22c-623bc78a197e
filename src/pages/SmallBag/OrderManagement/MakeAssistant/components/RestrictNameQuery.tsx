import React, { useState, useEffect } from 'react';

import loadPinYinInit from '@/utils/ChineseHelper';
import { Row, Col, Select, Button, Input, message, Form } from 'antd';
import { useMount } from 'ahooks';
import usePrevious from '@/hooks/usePrevious';
import ProTableList from '@/components/ProTable';
import { formatMessage } from 'umi-plugin-react/locale';
import { logSave, LogType } from '@/utils/logSave';

const RestrictNameQuery = props => {
  const { dispatch, tabNum: tabNumParent } = props;
  const [form] = Form.useForm();
  const [tabNum, setTabNum] = useState(tabNumParent);
  // const [account, setAccount] = useState<Array<any>>([]);
  // const [userId, setUserId] = useState(undefined);
  const [channels, setChannels] = useState<Array<any>>([]);
  // const [productCode, setProductCode] = useState(undefined);
  const [countriesData, setCountriesData] = useState<Array<any>>([]);
  const [countriesId, setCountriesId] = useState(undefined);
  const [loadingButton, setLoadingButton] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [tableData, setTableData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [tableCue, setTableCue] = useState(false);

  const previous = usePrevious({
    currentPage,
  });

  useMount(() => {
    initialFunc();
  });

  const initialFunc = () => {
    getCountriesName();
    getProductName();
  };

  useEffect(() => {
    if (!previous) return;
    if (previous.currentPage !== currentPage) {
      if (currentPage == 1) {
        let dataArr = dataSource.filter((item, index) => {
          return index < pageSize;
        });
        setTableData(dataArr);
        setLoading(false);
      } else {
        const num = currentPage - 1;
        let dataArr = dataSource.slice(num * pageSize, currentPage * pageSize);

        setTableData(dataArr);
        setLoading(false);
      }
    } else {
      if (currentPage == 1) {
        let dataArr = dataSource.filter((item, index) => {
          return index < pageSize;
        });
        setTableData(dataArr);
        setLoading(false);
      } else {
        const num = currentPage - 1;
        let dataArr = dataSource.slice(num * pageSize, currentPage * pageSize);

        setTableData(dataArr);
        setLoading(false);
      }
    }
  }, [currentPage, pageSize]);

  useEffect(() => {
    if (tabNumParent) {
      setDataSource([]);
      setTableData([]);
      setTabNum(tabNumParent);
      setTotal(0);
      setTableCue(false);
    }
  }, [tabNumParent]);

  // {formatMessage({id: '获取制单账号'})}
  // const billingAccount = () => {
  //   dispatch({
  //     type: 'makeAssistant/getShippingAccount',
  //     callback: response => {
  //       if (response.success) {
  //         setAccount(response.data);
  //         setUserId(response.data[0].accountCode);

  //       }
  //     },
  //   });
  // };

  // {formatMessage({id: '切换制单账号'})}
  // const shipperChange = (tag, index) => {
  //   form.setFieldsValue({ shippers: tag });
  //   // this.setState({ userId: tag[0] })
  //   setUserId(tag[0]);
  // };

  // {formatMessage({id: '根据产品编号获取国家'})}
  const getCountriesName = async () => {
    dispatch({
      type: 'makeAssistant/getCountriesName',
      callback: result => {
        if (result.success) {
          const targetCountries = [
            '意大利',
            '澳大利亚',
            '法国',
            '加拿大',
            '葡萄牙',
            '西班牙',
            '日本',
            '瑞士',
            '德国',
          ];
          const data = result.data
            .filter(item => targetCountries.includes(item.nameCh))
            .map(item => {
              item.countryNamePinYin = loadPinYinInit.ConvertPinyin(item.nameCh);
              return item;
            });
          // this.setState({
          //   countriesData: result.data,
          // })
          setCountriesData(data);
        }
      },
    });
  };
  // {formatMessage({id: '国家发生变化时'})}
  const changeCountries = id => {
    // this.setState({
    //   countriesId: id,
    // })
    setCountriesId(id);
    setDataSource([]);
    setTableData([]);
    setTotal(0);
    setTableCue(false);
  };

  // {formatMessage({id: '获取产品名称'})}
  const getProductName = async () => {
    // const { userId, countriesId } = this.state
    let params = {
      countryId: countriesId,
    };
    dispatch({
      type: 'makeAssistant/getProductName',
      payload: params,
      callback: result => {
        // this.setState(
        //   {
        //     channels: result.data,
        //     productCode: result.data[0].id,
        //   }
        //   // () => {
        //   //   this.getOrderList();
        //   // }
        // )
        setChannels(result.data);
        // setProductCode(result.data[0].id);
      },
    });
  };
  // {formatMessage({id: '产品选中值发生变化'})}
  // const changeProductName = value => {
  //   // this.setState({
  //   //   productCode: value,
  //   // })
  //   setProductCode(value);
  // };
  // {formatMessage({id: '点击重置'})}
  const resetSelect = () => {
    form.resetFields([
      'countryName',
      'countries',
      'channel',
      'address',
      'zipCode',
      'zipCodes',
      'city',
    ]);
    setDataSource([]);
    setTableData([]);
    setTotal(0);
    setTableCue(false);
  };
  // {formatMessage({id: '点击查询'})}
  const searchOrder = values => {
    // const { userId, countriesId, productCode } = this.state
    // const { tabNum } = this.props
    // this.setState({
    //   tableData: [],
    // })
    setTableData([]);
    if (tabNum == 3) {
      logSave(LogType.packet41);
      console.log(values);
      getOrderList(values);
    } else if (tabNum == 4) {
      getGermanPostcodeValidAddressQuery();
    } else if (tabNum == 5) {
      logSave(LogType.packet42);
      getFrenchPostcodeValidForCityEnquiries();
    }
  };

  // {formatMessage({id: '查询限制品名列表数据'})}
  const getOrderList = values => {
    // const { currentPage, pageSize, userId, productCode, countriesId } =
    //   this.state
    // const { dispatch } = this.props
    const params = {
      userId: values?.shippers,
      channelId: values?.channel,
      countryId: values?.countries,
    };
    setLoading(true);
    dispatch({
      type: 'makeAssistant/restrictNameQuery',
      payload: params,
      callback: res => {
        // this.setState({
        //   loading: false,
        // })
        setLoading(false);
        if (res.success) {
          // this.setState({
          //   dataSource: res.data,
          //   total: res.data.length,
          //   tableData: res.data.length ? res.data.slice(0, 10) : [],
          // })
          setDataSource(res.data);
          setTotal(res.data.length);
          setTableData(res.data.length ? res.data.slice(0, 10) : []);
        } else {
          // this.setState({
          //   total: 0,
          // })
          setTotal(0);
        }
      },
    });
  };

  // {formatMessage({id: '查询德邮邮编有效地址查询'})}
  const getGermanPostcodeValidAddressQuery = () => {
    const { address, zipCode } = form.getFieldsValue();
    const params = {
      // userId: userId,
      address: address,
      zipCode: zipCode ? zipCode : '',
    };
    setLoading(true);
    dispatch({
      type: 'makeAssistant/germanPostcodeValidAddressQuery',
      payload: params,
      callback: res => {
        setLoading(false);
        if (res.success) {
          setDataSource(res.data ? res.data : []);
          setTotal(res.data ? res.data.length : 0);
          setTableData(res.data ? res.data.slice(0, 10) : []);
          setTableCue((!res.data || res.data.length === 0) && !address);
        } else {
          setTotal(0);
        }
      },
    });
  };
  // {formatMessage({id: '法邮邮编有效城市查询'})}
  const getFrenchPostcodeValidForCityEnquiries = () => {
    const { city, zipCodes, countryName } = form.getFieldsValue();
    if (!city && !zipCodes) {
      message.error('邮编和城市至少输入一项');
      return;
    }
    let params = {
      countryName: countryName,
      cityName: city,
      postCode: zipCodes ? zipCodes : '',
      address: city,
      zipCode: zipCodes,
      countryId: countriesData.find(item => item.nameCh === countryName)?.id,
    };
    setLoading(true);
    let url = 'common/getCityPostCodeList';
    // if ('法国' === countryName) {
    //   url = 'makeAssistant/frenchPostcodeValidForCityEnquiries';
    // }
    // if ('德国' === countryName) {
    //   url = 'makeAssistant/germanPostcodeValidAddressQuery';
    // }
    dispatch({
      type: url,
      payload: params,
      callback: res => {
        setLoading(false);
        if (res.success) {
          setDataSource(res.data ? res.data : []);
          setTotal(res.data ? res.data.length : 0);
          setTableData(res.data ? res.data.slice(0, 10) : []);
          setTableCue((!res.data || res.data.length === 0) && !city);
        } else {
          // this.setState({
          //   total: 0,
          // })
          setTotal(0);
        }
      },
    });
  };

  // {formatMessage({id: '选择页码'})}
  const changePage = (current, size) => {
    // const { dataSource } = this.state
    setPageSize(size);
    setCurrentPage(current);
    setLoading(true);
    // this.setState(
    //   {
    //     pageSize: size,
    //     loading: true,
    //     currentPage: current,
    //   },
    //   () => {
    //     if (this.state.currentPage == 1) {
    //       let dataArr = dataSource.filter((item, index) => {
    //         return index < size
    //       })
    //       this.setState(
    //         {
    //           tableData: dataArr,
    //         },
    //         () => {
    //           this.setState({
    //             loading: false,
    //           })
    //         }
    //       )
    //     } else {
    //       const num = this.state.currentPage - 1
    //       let dataArr = dataSource.slice(
    //         num * size,
    //         this.state.currentPage * size
    //       )
    //       this.setState(
    //         {
    //           tableData: dataArr,
    //         },
    //         () => {
    //           this.setState({
    //             loading: false,
    //           })
    //         }
    //       )
    //     }
    //   }
    // )
  };

  const formItemLayoutCen = {
    labelCol: { xs: { span: 0 }, sm: { span: 6 } },
    wrapperCol: { xs: { span: 0 }, sm: { span: 16 } },
  };

  const columns = [
    {
      title: formatMessage({ id: '产品名称' }),
      dataIndex: 'channelName',
      key: 'channelName',
    },
    {
      title: formatMessage({ id: '国家' }),
      dataIndex: 'countryName',
      key: 'countryName',
    },
    {
      title: formatMessage({ id: '验证内容' }),
      dataIndex: 'contentValue',
      key: 'contentValue',
    },
  ];

  let column = [
    {
      title: '国家',
      dataIndex: 'countryName',
      key: 'countryName',
    },
    {
      title: '邮编',
      dataIndex: 'postalName',
      key: 'postalName',
    },
    {
      title: '城市',
      dataIndex: 'cityName',
      key: 'cityName',
    },
    {
      title: '州',
      dataIndex: 'provinces',
      key: 'provinces',
    },
    {
      title: '街道',
      dataIndex: 'street',
      key: 'street',
    },
  ];

  // if (countriesId == '德国') {
  //   column = [
  //     {
  //       title: 'PostCode',
  //       dataIndex: 'zipCode',
  //       key: 'zipCode',
  //     },
  //     {
  //       title: 'address',
  //       dataIndex: 'address',
  //       key: 'address',
  //     },
  //     {
  //       title: 'AddressSort',
  //       dataIndex: 'addressSort',
  //       key: 'addressSort',
  //     },
  //   ];
  // }
  // if (countriesId == '法国') {
  //   column = [
  //     {
  //       title: 'PostCode',
  //       dataIndex: 'zipCode',
  //       key: 'zipCode',
  //     },
  //     {
  //       title: 'City',
  //       dataIndex: 'city',
  //       key: 'city',
  //     },
  //   ];
  // }

  const paginationProps = {
    total: total,
    current: currentPage,
    pageSize: pageSize,
    onChange: changePage,
    showSizeChanger: true,
    pageSizeOptions: ['10', '20', '50', '100', '500'],
    // onShowSizeChange: this.onShowSizeChange,
    showTotal: () => {
      return `${formatMessage({ id: '共' })}- ${total} -${formatMessage({ id: '条记录' })}`;
    },
  };

  return (
    <>
      <div
        style={{
          padding: '20px',
          background: '#fff',
          borderRadius: '10px',
          marginBottom: '20px',
        }}
      >
        <Form labelAlign="left" form={form} onFinish={searchOrder}>
          <Row>
            {/* <Col span={24}>
              <StandardFormRow title="{formatMessage({id: '制单账号'})}" block>
                <Form.Item
                  name="shippers"
                  initialValue={userId}
                  style={{ marginRight: 70 }}
                  className="form_item_bottom"
                  rules={[
                    {
                      required: true,
                      message: '{formatMessage({id: '请至少选择一个制单账号'})}',
                    },
                  ]}
                >
                  <SingleTagSelect className="open" onChange={shipperChange}>
                    {account &&
                      account.map((element, index) => (
                        // @ts-ignore
                        <SingleTagSelect.Option key={index} value={element.accountCode}>
                          {element.accountCode}
                        </SingleTagSelect.Option>
                      ))}
                  </SingleTagSelect>
                </Form.Item>
              </StandardFormRow>
            </Col> */}
            {tabNum == 3 && (
              <Col span={6}>
                <Form.Item
                  label={formatMessage({ id: '产品名称' })}
                  {...formItemLayoutCen}
                  name="channel"
                  style={{ marginBottom: '14px' }}
                  rules={[
                    {
                      required: true,
                      message: formatMessage({ id: '请选择产品' }),
                    },
                  ]}
                >
                  <Select
                    showSearch
                    allowClear
                    placeholder={formatMessage({ id: '请选择产品' })}
                    optionFilterProp="children"
                  >
                    {channels &&
                      channels
                        ?.filter(t => t.status === '1')
                        ?.map((element, index) => (
                          <Select.Option key={index} value={element.id}>
                            {element.nameCh}
                          </Select.Option>
                        ))}
                  </Select>
                </Form.Item>
              </Col>
            )}
            {tabNum == 3 && (
              <Col span={6}>
                <Form.Item
                  label={formatMessage({ id: '国家' })}
                  {...formItemLayoutCen}
                  style={{ marginBottom: '14px' }}
                  name="countries"
                >
                  <Select
                    showSearch
                    allowClear
                    placeholder={formatMessage({ id: '请选择国家' })}
                    optionFilterProp="children"
                    onChange={changeCountries}
                  >
                    {countriesData &&
                      countriesData.map((element, index) => (
                        <Select.Option key={index} value={element.id}>
                          {element.nameCh +
                            '/' +
                            element.countryNamePinYin +
                            '/' +
                            element.nameEn +
                            '/' +
                            element.code}
                        </Select.Option>
                      ))}
                  </Select>
                </Form.Item>
              </Col>
            )}
            {tabNum == 4 && (
              <Col span={6}>
                <Form.Item
                  label={formatMessage({ id: '邮编' })}
                  name="zipCode"
                  {...formItemLayoutCen}
                  rules={[
                    {
                      required: true,
                      message: formatMessage({ id: '请输入邮编' }),
                    },
                  ]}
                  style={{ marginBottom: '14px' }}
                >
                  <Input allowClear placeholder={formatMessage({ id: '请输入邮编' })} />
                </Form.Item>
              </Col>
            )}
            {tabNum == 5 && (
              <Col span={6}>
                <Form.Item
                  label={formatMessage({ id: '国家' })}
                  name="countryName"
                  {...formItemLayoutCen}
                  style={{ marginBottom: '14px' }}
                  rules={[
                    {
                      required: true,
                      message: formatMessage({ id: '请输入国家' }),
                    },
                  ]}
                >
                  <Select
                    showSearch
                    allowClear
                    placeholder={formatMessage({ id: '请选择国家' })}
                    optionFilterProp="children"
                    onChange={changeCountries}
                  >
                    {countriesData &&
                      countriesData.map((element, index) => (
                        <Select.Option key={index} value={element.nameCh}>
                          {element.nameCh}
                        </Select.Option>
                      ))}
                  </Select>
                </Form.Item>
              </Col>
            )}
            {tabNum == 5 && (
              <Col span={6}>
                <Form.Item
                  label={formatMessage({ id: '邮编' })}
                  name="zipCodes"
                  {...formItemLayoutCen}
                  style={{ marginBottom: '14px' }}
                  rules={[
                    {
                      required: true,
                      message: formatMessage({ id: '请输入邮编' }),
                    },
                  ]}
                >
                  <Input allowClear placeholder={formatMessage({ id: '请输入邮编' })} />
                </Form.Item>
              </Col>
            )}
            {tabNum == 4 && (
              <Col span={6}>
                <Form.Item
                  label={formatMessage({ id: '地址' })}
                  name="address"
                  {...formItemLayoutCen}
                  style={{ marginBottom: '14px' }}
                >
                  <Input allowClear placeholder={formatMessage({ id: '请输入地址' })} />
                </Form.Item>
              </Col>
            )}
            {tabNum == 5 && (
              <Col span={6}>
                <Form.Item
                  label={formatMessage({ id: '城市' })}
                  name="city"
                  {...formItemLayoutCen}
                  style={{ marginBottom: '14px' }}
                >
                  <Input allowClear placeholder={formatMessage({ id: '请输入城市' })} />
                </Form.Item>
              </Col>
            )}

            <Col span={6} style={{ lineHeight: '40px' }}>
              <Button
                type="primary"
                htmlType="submit"
                style={{ marginRight: 20 }}
                loading={loadingButton}
              >
                {formatMessage({ id: '查询' })}
              </Button>
              <Button style={{ marginRight: 20 }} onClick={resetSelect}>
                {formatMessage({ id: '重置' })}
              </Button>
            </Col>
          </Row>
        </Form>
      </div>
      <ProTableList
        className={tableCue ? 'tableCue' : ''}
        pagination={paginationProps}
        dataSource={tableData}
        loading={loading}
        columns={tabNum == 3 ? columns : tabNum == 4 ? column : column}
        scroll={{ x: 'max-content' }}
        locale={{
          emptyText:
            !loading && tableCue ? (
              <span className="text-red-500">
                {formatMessage({ id: '邮编不在有效范围哟亲' })}！
              </span>
            ) : (
              formatMessage({ id: '暂无数据' })
            ),
        }}
        search={false}
        toolBarRender={false}
        rowSelection={false}
      />
    </>
  );
};

export default RestrictNameQuery;
