import { <PERSON>umn, Line, Pie } from '@ant-design/plots';
import {
  <PERSON><PERSON>,
  Card,
  Col,
  DatePicker,
  Divider,
  Form,
  Input,
  message,
  Row,
  Select,
  Space,
} from 'antd';
import React, { useEffect, useState } from 'react';
import moment from 'moment';
import PageHeaderBreadcrumb from '@/components/PageHeaderBreadcrumb';
import PageContainerComponent from '@/components/PageContainer';
import style from '@/pages/SmallBag/SmallPacketLine/components/SmallPacketLineCard.less';
import { ProCard } from '@ant-design/pro-components';
import { renderSelectOptionsMulti } from '@/utils/commonConstant';
import { downloadBase64File } from '@/utils/utils';
import { connect } from 'dva';
import { useMount } from 'ahooks';
import SingleTagSelect from '@/components/SingleTagSelect';
import { formatMessage } from 'umi-plugin-react/locale';
import { logSave, LogType } from '@/utils/logSave';

const { RangePicker } = DatePicker;
const { Option } = Select;
const TimeBaseStatistics = ({ dispatch, submitLoading, exportLoading }) => {
  const [day, setDay] = useState(1);
  const [dates, setDates] = useState(undefined);
  const [dateValue, setDateValue] = useState(undefined);
  const [quantity, setQuantity] = useState(0);
  const [selected, setSelected] = useState(undefined);
  const [successQuantity, setSuccessQuantity] = useState(0);
  const [avgDeliveredDays, setAvgDeliveredDays] = useState(0);
  const [unSuccessQuantity, setUnSuccessQuantity] = useState(0);
  const [deliveredProbability, setDeliveredProbability] = useState(0);
  const [successfulDeliveredAnalyse, setSuccessfulDeliveredAnalyse] = useState([]);
  const [productModuleDeliveredProbability, setProductModuleDeliveredProbability] = useState([]);
  const [countryModuleDeliveredProbability, setCountryModuleDeliveredProbability] = useState([]);
  const [sendDateDeliveredProbability, setSendDateDeliveredProbability] = useState([]);
  const [account, setAccount] = useState([]);
  const [country, setCountry] = useState([]);
  const [product, setProduct] = useState([]);
  const [form] = Form.useForm();
  const onReset = () => {
    logSave(LogType.packet31);
    form.resetFields();
  };

  useMount(() => {
    initialFunc();
  });

  const initialFunc = () => {
    getAccount();
    getProduct();
    getCountry();
    // {formatMessage({id: '初始化查询时间'})}
    setDateValue([
      moment()
        .utcOffset('+08:00')
        .subtract(1, 'month'),
      moment().utcOffset('+08:00'),
    ]);
    form.submit();
  };

  /**
   * {formatMessage({id: '动态选择饼图选择项'})}
   */
  useEffect(() => {
    setSelected(
      successfulDeliveredAnalyse.reduce((accumulator, currentValue) => {
        accumulator[currentValue.type] = !!currentValue.value;
        return accumulator;
      }, {})
    );
  }, [successfulDeliveredAnalyse]);

  const getAccount = () => {
    dispatch({
      type: 'smallBag/makeAccountLoad',
      payload: {
        accountType: 0,
        scene: 2,
      },
      callback: response => {
        if (response.success) {
          setAccount(response?.data);
        } else {
          setAccount([]);
        }
      },
    });
  };

  const getCountry = () => {
    dispatch({
      type: 'freight/getCountries',
      callback: response => {
        if (response.success) {
          setCountry(
            renderSelectOptionsMulti(response?.data, {
              label: ['english2bit', 'chinesepinyin', 'chinesename'],
              value: 'id',
            })
          );
        } else {
          setCountry([]);
        }
      },
    });
  };

  const getProduct = () => {
    dispatch({
      type: 'freight/getProducts',
      callback: response => {
        if (response.success) {
          setProduct(
            renderSelectOptionsMulti(
              response?.data,
              {
                label: ['productNumber', 'productCnName'],
                value: 'productNumber',
              },
              '-'
            )
          );
        } else {
          setProduct([]);
        }
      },
    });
  };

  const getDetail = async () => {
    try {
      logSave(LogType.packet32);
      const values = await form.validateFields();
      const params = {
        ...values,
        accountCode: values?.accountCode?.[0],
      };
      dispatch({
        type: 'smallBag/getTimeBaseDetail',
        payload: params,
        callback: response => {
          if (response.success) {
            message.success(response.message);
            // {formatMessage({id: '下载文件'})}
            downloadBase64File(response?.data?.base64, response?.data?.fileName);
          }
        },
      });
    } catch (e) {}
  };

  const getReport = values => {
    const params = {
      ...values,
      accountCode: values?.accountCode?.[0],
    };
    dispatch({
      type: 'smallBag/getTimeBaseReport',
      payload: params,
      callback: response => {
        if (response.success) {
          let data = response?.data;
          // {formatMessage({id: '设置基础数据'})}
          setQuantity(data?.quantity);
          setSuccessQuantity(data?.successQuantity);
          setAvgDeliveredDays(data?.avgDeliveredDays);
          setUnSuccessQuantity(data?.unSuccessQuantity);
          setDeliveredProbability(data?.deliveredProbability);
          let deliveredAnalyse = data?.successfulDeliveredAnalyse;
          // {formatMessage({id: '妥投成功情况表'})}
          setSuccessfulDeliveredAnalyse([
            {
              type: `≤5${formatMessage({ id: '天' })}`,
              value: deliveredAnalyse?.lte5,
            },
            {
              type: `6${formatMessage({ id: '天' })}`,
              value: deliveredAnalyse?.eq6,
            },
            {
              type: `7${formatMessage({ id: '天' })}`,
              value: deliveredAnalyse?.eq7,
            },
            {
              type: `8${formatMessage({ id: '天' })}`,
              value: deliveredAnalyse?.eq8,
            },
            {
              type: `9${formatMessage({ id: '天' })}`,
              value: deliveredAnalyse?.eq9,
            },
            {
              type: `10${formatMessage({ id: '天' })}`,
              value: deliveredAnalyse?.eq10,
            },
            {
              type: `11~15${formatMessage({ id: '天' })}`,
              value: deliveredAnalyse?.gt10Lte15,
            },
            {
              type: `16~20${formatMessage({ id: '天' })}`,
              value: deliveredAnalyse?.gt15Lte20,
            },
            {
              type: `>20${formatMessage({ id: '天' })}`,
              value: deliveredAnalyse?.gt20,
            },
          ]);
          // {formatMessage({id: '产品维度信息'})}
          setProductModuleDeliveredProbability(data?.productModuleDeliveredProbability ?? []);
          // {formatMessage({id: '产品维度信息'})}
          setCountryModuleDeliveredProbability(data?.countryModuleDeliveredProbability ?? []);
          // {formatMessage({id: '时间维度信息'})}
          setSendDateDeliveredProbability(data?.sendDateDeliveredProbability ?? []);
        } else {
          setQuantity(0);
          setSuccessQuantity(0);
          setAvgDeliveredDays(0);
          setUnSuccessQuantity(0);
          setDeliveredProbability(0);
          setSuccessfulDeliveredAnalyse([]);
          setProductModuleDeliveredProbability([]);
          setCountryModuleDeliveredProbability([]);
          setSendDateDeliveredProbability([]);
          setSelected(undefined);
        }
      },
    });
  };

  const disabledDate = current => {
    // {formatMessage({id: '只允许查询最近'})}6{formatMessage({id: '个月的时间'})}
    if (
      current &&
      current <
        moment()
          .utcOffset('+08:00')
          .subtract(1000, 'month')
    ) {
      return true;
    }
    if (!dates) {
      return false;
    }
    const tooLate = dates[0] && current.diff(dates[0], 'days') > 31;
    const tooEarly = dates[1] && dates[1].diff(current, 'days') > 31;
    return !!tooEarly || !!tooLate;
  };
  const onOpenChange = open => {
    if (open) {
      form.setFieldsValue({
        date: [null, null],
      });
    } else {
      form.setFieldsValue({
        date: dateValue,
      });
      setDates(null);
    }
  };

  const onFinish = values => {
    logSave(LogType.packet30);
    getReport(values);
  };

  const onFinishFailed = () => {};

  const sendDateDeliveredProbabilityConfig = {
    data: sendDateDeliveredProbability,
    width: 530,
    xField: 'name',
    yField: 'rate',
    tooltip: {
      formatter: datum => {
        let entries = sendDateDeliveredProbability.filter(
          o => o.type == datum.type && o.name == datum.name
        );
        return {
          name: entries?.[0]?.type.replace(
            formatMessage({ id: '率' }),
            formatMessage({ id: '件数' })
          ),
          value: entries?.[0]?.quantity + `(${formatMessage({ id: '件' })})`,
        };
      },
    },
    yAxis: {
      label: {
        formatter: value => {
          return value + '%';
        },
      },
    },
    seriesField: 'type',
  };
  const countryModuleDeliveredProbabilityConfig = {
    data: countryModuleDeliveredProbability,
    width: 530,
    isGroup: true,
    xField: 'name',
    yField: 'rate',
    seriesField: 'type',
    tooltip: {
      formatter: datum => {
        let entries = countryModuleDeliveredProbability.filter(
          o => o.type == datum.type && o.name == datum.name
        );
        return {
          name: entries?.[0]?.type.replace(
            formatMessage({ id: '率' }),
            formatMessage({ id: '件数' })
          ),
          value: entries?.[0]?.quantity + `(${formatMessage({ id: '件' })})`,
        };
      },
    },
    label: {
      formatter: value => {
        return value?.rate + '%';
      },
      // {formatMessage({id: '可手动配置'})} label {formatMessage({id: '数据标签位置'})}
      position: 'middle',
      // 'top', 'middle', 'bottom'
      // {formatMessage({id: '可配置附加的布局方法'})}
      layout: [
        // {formatMessage({id: '柱形图数据标签位置自动调整'})}
        {
          type: 'interval-adjust-position',
        }, // {formatMessage({id: '数据标签防遮挡'})}
        {
          type: 'interval-hide-overlap',
        }, // {formatMessage({id: '数据标签文颜色自动调整'})}
        {
          type: 'adjust-color',
        },
      ],
    },
  };
  const productModuleDeliveredProbabilityConfig = {
    data: productModuleDeliveredProbability,
    width: 530,
    isGroup: true,
    xField: 'name',
    yField: 'rate',
    seriesField: 'type',
    tooltip: {
      formatter: datum => {
        let entries = productModuleDeliveredProbability.filter(
          o => o.type == datum.type && o.name == datum.name
        );
        return {
          name: entries?.[0]?.type.replace(
            formatMessage({ id: '率' }),
            formatMessage({ id: '件数' })
          ),
          value: entries?.[0]?.quantity + `(${formatMessage({ id: '件' })})`,
        };
      },
    },
    label: {
      formatter: value => {
        return value?.rate + '%';
      },
      // {formatMessage({id: '可手动配置'})} label {formatMessage({id: '数据标签位置'})}
      position: 'middle',
      // {formatMessage({id: '可配置附加的布局方法'})}
      layout: [
        // {formatMessage({id: '柱形图数据标签位置自动调整'})}
        {
          type: 'interval-adjust-position',
        }, // {formatMessage({id: '数据标签防遮挡'})}
        {
          type: 'interval-hide-overlap',
        }, // {formatMessage({id: '数据标签文颜色自动调整'})}
        {
          type: 'adjust-color',
        },
      ],
    },
  };

  const successfulDeliveredAnalyseConfig = {
    data: successfulDeliveredAnalyse,
    angleField: 'value',
    colorField: 'type',
    width: 530,
    radius: 0.8,
    tooltip: {
      formatter: datum => {
        return {
          name: datum.type,
          value: datum.value + `(${formatMessage({ id: '件' })})`,
        };
      },
    },
    legend: {
      offsetX: 25,
      selected: selected,
      itemValue: {
        formatter: (text, item) => {
          let entries = successfulDeliveredAnalyse.filter(o => o.type == text);
          return entries?.[0]?.value + ` ${formatMessage({ id: '件' })}`;
        },
      },
    },
    label: {
      maxItemWidth: 100,
      type: 'inner',
      content: ({ percent }) => `${(percent * 100).toFixed(0)}%`,
    },
    interactions: [
      {
        type: 'element-active',
      },
    ],
  };

  return (
    <PageContainerComponent
      header={{
        title: null,
        breadcrumb: {},
        breadcrumbRender: props => <PageHeaderBreadcrumb {...props} />,
      }}
    >
      <Card>
        <Form
          form={form}
          name="info"
          initialValues={{
            date: [
              moment()
                .utcOffset('+08:00')
                .subtract(1, 'month'),
              moment().utcOffset('+08:00'),
            ],
            dateType: 1,
            accountCode: ['-1'],
          }}
          onFinish={onFinish}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
        >
          <Row>
            <Form.Item
              label={formatMessage({ id: '制单账号' })}
              name="accountCode"
              rules={[
                {
                  required: true,
                  message: formatMessage({ id: '请选择制单账号' }),
                },
              ]}
            >
              <SingleTagSelect onChange={form.submit} style={{ maxHeight: 'none' }}>
                <SingleTagSelect.Option key="-1" value="-1">
                  {formatMessage({ id: '所有' })}
                </SingleTagSelect.Option>
                <SingleTagSelect.Option key="313348" value="313348">
                  313348({formatMessage({ id: '测试数据' })})
                </SingleTagSelect.Option>
                {account.map((element, index) => (
                  <SingleTagSelect.Option key={index} value={element?.accountCode}>
                    {element.warehouseName ?? ''}
                    {element.accountCode}
                  </SingleTagSelect.Option>
                ))}
              </SingleTagSelect>
            </Form.Item>
          </Row>
          <Row gutter={24}>
            <Col span={6}>
              <Form.Item label={formatMessage({ id: '产品名称' })} name="productId">
                <Select
                  showSearch
                  filterOption={(input, option) => (option?.label ?? '').includes(input)}
                  allowClear
                  options={product}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label={formatMessage({ id: '目的地' })} name="countryId">
                <Select
                  showSearch
                  filterOption={(input, option) => (option?.label ?? '').includes(input)}
                  allowClear
                  options={country}
                />
              </Form.Item>
            </Col>
            <Col span={9}>
              <Form.Item label={formatMessage({ id: '查询时间' })} required={true}>
                <Input.Group compact>
                  <Form.Item
                    name={'dateType'}
                    rules={[
                      {
                        required: true,
                        message: formatMessage({ id: '请选择区间' }),
                      },
                    ]}
                    noStyle
                  >
                    <Select onChange={setDay}>
                      <Option value={1}>{formatMessage({ id: '日' })}</Option>
                      <Option value={0}>{formatMessage({ id: '周' })}</Option>
                    </Select>
                  </Form.Item>

                  {day == 1 && (
                    <Form.Item
                      name={'date'}
                      rules={[
                        {
                          required: true,
                          message: formatMessage({ id: '请选择起止时间' }),
                        },
                      ]}
                      noStyle
                    >
                      <RangePicker
                        onOpenChange={onOpenChange}
                        onCalendarChange={val => setDates(val)}
                        // disabledDate={disabledDate}
                        onChange={val => setDateValue(val)}
                        format={'YYYY-MM-DD'}
                      />
                    </Form.Item>
                  )}

                  {day == 0 && (
                    <Form.Item
                      name={'date'}
                      rules={[
                        {
                          required: true,
                          message: formatMessage({ id: '请选择起止周' }),
                        },
                      ]}
                      noStyle
                    >
                      <RangePicker
                        onOpenChange={onOpenChange}
                        onCalendarChange={val => setDates(val)}
                        // disabledDate={disabledDate}
                        onChange={val => setDateValue(val)}
                        picker="week"
                      />
                    </Form.Item>
                  )}
                </Input.Group>
              </Form.Item>
            </Col>
            <Col span={3}>
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" loading={submitLoading}>
                    {formatMessage({ id: '查询' })}
                  </Button>
                  <Button htmlType="button" onClick={onReset}>
                    {formatMessage({ id: '重置' })}
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>
      <Card style={{ marginTop: '10px' }}>
        <Row gutter={24}>
          <Col span={4}>
            <ProCard style={{ flex: '1' }}>
              <div className={style.singleQuantity} style={{ alignItems: 'center' }}>
                <div>
                  <p style={{ color: '#b3b2b2', marginBottom: 0 }}>
                    <span style={{ fontSize: 24, color: '#93c34f', marginRight: 5 }}>
                      {quantity}
                    </span>
                    {formatMessage({ id: '票' })}
                  </p>
                  <p style={{ color: '#505050', textAlign: 'center', marginBottom: 0 }}>
                    {formatMessage({ id: '发出件数' })}
                  </p>
                </div>
              </div>
            </ProCard>
          </Col>
          <Col span={4}>
            <ProCard style={{ flex: '1' }}>
              <div className={style.singleQuantity} style={{ alignItems: 'center' }}>
                <div>
                  <p style={{ color: '#b3b2b2', marginBottom: 0 }}>
                    <span style={{ fontSize: 24, color: '#93c34f', marginRight: 5 }}>
                      {successQuantity}
                    </span>
                    {formatMessage({ id: '票' })}
                  </p>
                  <p style={{ color: '#505050', textAlign: 'center', marginBottom: 0 }}>
                    {formatMessage({ id: '妥投件数' })}
                  </p>
                </div>
              </div>
            </ProCard>
          </Col>
          <Col span={4}>
            <ProCard style={{ flex: '1' }}>
              <div className={style.singleQuantity} style={{ alignItems: 'center' }}>
                <div>
                  <p style={{ color: '#b3b2b2', marginBottom: 0 }}>
                    <span style={{ fontSize: 24, color: '#93c34f', marginRight: 5 }}>
                      {unSuccessQuantity}
                    </span>
                    {formatMessage({ id: '票' })}
                  </p>
                  <p style={{ color: '#505050', textAlign: 'center', marginBottom: 0 }}>
                    {formatMessage({ id: '未妥投件数' })}
                  </p>
                </div>
              </div>
            </ProCard>
          </Col>
          <Col span={4}>
            <ProCard style={{ flex: '1' }}>
              <div className={style.singleQuantity} style={{ alignItems: 'center' }}>
                <div>
                  <p style={{ color: '#b3b2b2', marginBottom: 0 }}>
                    <span style={{ fontSize: 24, color: '#93c34f', marginRight: 5 }}>
                      {deliveredProbability}
                    </span>
                    %
                  </p>
                  <p style={{ color: '#505050', textAlign: 'center', marginBottom: 0 }}>
                    {formatMessage({ id: '当前妥投率' })}
                  </p>
                </div>
              </div>
            </ProCard>
          </Col>
          <Col span={5}>
            <ProCard style={{ flex: '1' }}>
              <div className={style.singleQuantity} style={{ alignItems: 'center' }}>
                <div>
                  <p style={{ color: '#b3b2b2', marginBottom: 0 }}>
                    <span style={{ fontSize: 24, color: '#93c34f', marginRight: 5 }}>
                      {avgDeliveredDays}
                    </span>
                    {formatMessage({ id: '天' })}
                  </p>
                  <p style={{ color: '#505050', textAlign: 'center', marginBottom: 0 }}>
                    {formatMessage({ id: '平均妥投天数' })}
                  </p>
                </div>
              </div>
            </ProCard>
          </Col>
          <Col>
            <Button type="primary" onClick={getDetail} loading={exportLoading}>
              {formatMessage({ id: '明细下载' })}
            </Button>
          </Col>
        </Row>
        <Divider />
        <Row>
          <Col span={12}>
            <Space direction={'vertical'} align={'center'} className="w-full">
              <span>
                <b style={{ color: '#93c34f' }}>{formatMessage({ id: '妥投成功情况分析' })}</b>
              </span>
              <Pie {...successfulDeliveredAnalyseConfig} />
            </Space>
          </Col>
          <Col span={12}>
            <Space direction={'vertical'} align={'center'} className="w-full">
              <span>
                <b style={{ color: '#93c34f' }}>
                  {formatMessage({ id: '产品维度妥投率' })}（{formatMessage({ id: '妥投率百分比' })}
                  /{formatMessage({ id: '产品' })}）
                </b>
              </span>
              <Column {...productModuleDeliveredProbabilityConfig} />
            </Space>
          </Col>
          <Divider />
          <Col span={12}>
            <Space direction={'vertical'} align={'center'} className="w-full">
              <span>
                <b style={{ color: '#93c34f' }}>
                  {formatMessage({ id: '国家妥投率分析' })}（{formatMessage({ id: '妥投率百分比' })}
                  /{formatMessage({ id: '国家' })}）
                </b>
              </span>
              <Column {...countryModuleDeliveredProbabilityConfig} />
            </Space>
          </Col>
          <Col span={12}>
            <Space direction={'vertical'} align={'center'} className="w-full">
              <span>
                <b style={{ color: '#93c34f' }}>
                  {formatMessage({ id: '发货时间妥投率分析' })}（
                  {formatMessage({ id: '妥投比百分比' })}/{formatMessage({ id: '日期' })}）
                </b>
              </span>
              <Line {...sendDateDeliveredProbabilityConfig} />
            </Space>
          </Col>
        </Row>
      </Card>
    </PageContainerComponent>
  );
};

export default connect(({ loading }) => ({
  submitLoading: loading.effects['smallBag/getTimeBaseReport'],
  exportLoading: loading.effects['smallBag/getTimeBaseDetail'],
}))(TimeBaseStatistics);
