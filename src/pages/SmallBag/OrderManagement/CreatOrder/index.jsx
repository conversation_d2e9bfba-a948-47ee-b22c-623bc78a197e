import React, { Component } from 'react';
import { connect } from 'dva';
import moment from 'moment';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {
  Button,
  Col,
  DatePicker,
  Input,
  message,
  Modal,
  notification,
  Popover,
  Radio,
  Row,
  Select,
  Space,
  Tabs,
  Typography,
} from 'antd';
import './createOrder.less';
import { router } from 'umi';
import StandardFormRow from '@/components/StandardFormRow';
import SingleTagSelect from '@/components/SingleTagSelect';
import { downloadBase64File, findMatch, calculateTotalSum } from '@/utils/utils';
import loadPinYinInit from '@/utils/ChineseHelper';
import { downloadFile } from '@/utils/download';
import PageContainerComponent from '@/components/PageContainer';
import PageHeaderBreadcrumb from '@/components/PageHeaderBreadcrumb';
import { Decimal } from 'decimal.js';
import BatchCreation from './components/BatchCreation';
import EjfCommonComponent from './components/EjfCommonComponent';
import BasicInformationOfEJF from './components/BasicInformationOfEJF';
import CommercialExpressBasicInformation from './components/CommercialExpressBasicInformation';
import CommercialExpressCommonComponent from './components/CommercialExpressCommonComponent';
import PopoverTabs from '@/components/PopoverTabs';
import { formatMessage } from 'umi-plugin-react/locale';
import AuthStatusNode from '@/components/AuthStatusNode';
import { calculateFreightAmount, hongKongDHLOpenWindow } from '@/utils/utils';
import { logSave, LogType } from '@/utils/logSave';
import { temuShowList, hongKongDHLProductCode } from '@/utils/commonConstant';

const { Text } = Typography;
const { TextArea } = Input;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

@connect(({ Order, user, loading }) => ({
  Order,
  submitOrderLoading:
    loading.effects['business/getExistBusinessDraftNumber'] ||
    loading.effects['business/createBusinessOrder'],
  submitDraftLoading: loading.effects['business/createBusinessDraft'],
  editDraftAndSubmitDraftLoading:
    loading.effects['business/editBusinessDraft'] ||
    loading.effects['business/businessDraftToOrder'],
  editDraftLoading: loading.effects['business/editBusinessDraft'],
  chargeLoading:
    loading.effects['Order/chargePriceSimple'] || loading.effects['business/manyPiece'],
  printLabelLoading:
    loading.effects['business/businessOrderPrintLabelBatch'] ||
    loading.effects['Order/getPrintLabelData'],
  taxNumberConfirmLoading: loading.effects['taxNumber/taxRecord'],
}))
class Index extends Component {
  constructor(props) {
    super(props);
    this.state = {
      temuShow: false,
      open: false,
      moneyNum: 0, //试算价格
      moneyItem: [],
      tabNumber: '1',
      shipperInitOpt: [],
      deliveryAccount: [], // 制单账号
      value: 0,
      transactionNumber: undefined,
      currencys: [],
      handoverCodes: [],
      currencyName: '', // 单个币种名
      currencyCode: undefined,
      activeCurrency: '', // 所选币种
      countriesData: [], // 所有国家数据
      warehouseList: [], // 交货仓数据
      customsInfo: [
        {
          goodsNameCh: undefined, // 中文名
          goodsNameEn: undefined, // 英文名
          quantity: undefined, // 单品数量
          weight: undefined, // 单品重量
          price: undefined, // 申报价格
          productMaterial: undefined, // 商品材质
          productHsCode: undefined, // 商品海关编码
          productLink: undefined, // 商品链接
          productSku: undefined, // sku
          productImei: undefined, // IMEI
        },
      ], // 报关信息
      channels: [], // 产品数据
      totalNum: 0, // 申报的总数量
      totalWeight: 0, // 申报的总质量
      totalPrice: 0, // 申报的总价值
      heights: undefined, // 高
      widths: undefined, // 宽
      lengths: undefined, // 长
      pickingOrders: undefined, // 拣货单
      formData: {
        userId: undefined, // 选中的制单账号
        channelId: undefined, // 产品编码
        orderNumber: undefined, // 订单号
        dateOfReceipt: undefined, // 收款到账日期
        companyCode: undefined, // 交货仓
      },
      receiverInfo: {
        address: undefined, //收件人地址
        city: undefined, //收件人城市
        company: undefined, // 收件人公司
        countryId: undefined, // 目的国ID
        email: undefined, //邮箱
        houseNumber: undefined, //门牌号
        name: undefined, //姓名
        phone: undefined, //电话
        state: undefined, //州(省)
        receiverTaxNumber: undefined, //税号
        zipCode: undefined, //邮编
        pinflNumber: undefined, //PINFL号码
      },
      isHaveCountry: false, // 是否有这个国家
      senderTaxNumber: undefined, // 发件人税号
      pointId: undefined, //自提点id
      fileList: [],
      uploading: false,
      btnLoading: false,
      EJFtemplates: [], // EJF模板数据
      currentAuthorityValue: null, // 商户状态
      isHaveDeliveryAccount: false, // 制单账号是否只有一个
      fileName: '', // 批量创建订单上传的文件名
      countryDisabled: true,
      IOSS: '', // 税号
      salesPlatform: '', // 销售平台
      handoverCode: undefined, // 海外交货地
      importCustomsInfo: {}, //清关信息
      isModalVisible: false, // 批量上传失败弹框显示状态
      uploadErrorCon: '', // 上传失败提示
      uploadErrorMsg: '', // 上传失败成功与失败条数
      initData: {}, // 一键换单初始化数据
      userIds: '', // 一键换单运单号
      isHaveNumber: false,
      unscriptedEditing: false, // 是否是未制单编辑
      isProduct: false, //是否有这个产品
      isCreatedSuccess: false, // 是否创建成功
      waybillNumber: '', // 制单成功生成的运单号
      isChangeAccount: true,
      isLoadingStart: false,
      selectTypeRadioValue: undefined,
      modalErrorContent: '', // 检测订单号是否已经存在弹窗提示内容
      isModalErrorVisible: false, // 检测订单号是否已经存在弹窗显示状态
      countrySearchValue: null,
      commodityTypeList: [], // 商品类型
      batteryTypeList: [], // 电池类型
      taxTypeList: [], // 税号类型
      packTypeList: [], // 包装类型
      isBusiness: false, // 是否是商业快递
      provinceList: [], // 省
      cityList: [], // 市
      districtList: [], // 区
    };

    this.basicInformationOfEJFRef = React.createRef();
    this.ejfCommonComponentRef = React.createRef();
  }

  componentDidMount() {
    this.validateCustomerPayment();
    const { dispatch } = this.props;
    const {
      userId,
      expressCode,
      id,
      para,
      isFreightTrial,
      isBusiness,
    } = this.props.history.location.query;
    if (isBusiness && isBusiness === '1') {
      // 商业快递
      this.getBusinessDetails();
    } else {
      // 小包专线
      if (para == 'true') {
        this.setState({
          initData: JSON.parse(sessionStorage.getItem('INITDATA')),
          userIds: userId,
        });
      }
      const result = new URLSearchParams(this.props.location.search);
      const type = result.get('type');
      const {
        props: {
          location: {
            query: { number },
          },
        },
      } = this;
      // 获取交货仓
      this.getWarehouseByType();
      let info;
      if (number) {
        this.state.tabNumber = number;
        this.setState({
          tabNumber: number,
          isHaveNumber: true,
        });
      }
      if (userId != undefined && id != undefined) {
        // 获取订单详情
        this.getOrderDetailsById(userId, id);
        this.setState({
          countryDisabled: false,
          unscriptedEditing: true,
          isChangeAccount: false,
          isHaveCountry: true,
        });
      } else if (userId != undefined && expressCode != undefined) {
        // 获取运单详情
        this.getOrderDetails(userId, expressCode);
        this.setState({
          countryDisabled: false,
          isHaveNumber: true,
          isChangeAccount: false,
          isHaveCountry: true,
        });
      } else {
        if (userId) {
          this.setState({
            countryDisabled: false,
            isHaveNumber: true,
            isChangeAccount: false,
            isHaveCountry: true,
          });
          let params = {
            userId: typeof userId == 'string' ? userId : userId[0],
          };
          this.getProductName(params);
        }
        if (type == 'add') {
          this.setState({
            isHaveNumber: true,
            isChangeAccount: false,
            isHaveCountry: true,
          });

          if (JSON.stringify(this.props.Order.copyData) != '{}') {
            info = this.props.Order.copyData;
          } else {
            // 检查本地是否有数据
            info = JSON.parse(sessionStorage.getItem('copys'));
          }
        } else {
          // 检查本地是否有数据
          info = JSON.parse(localStorage.getItem(localStorage.getItem('Merchants')));
        }
        if (info) {
          let forms = {
            userId: info.userId, // 选中的制单账号
            channelId: info.channelId, // 产品编码
            orderNumber: info.orderNumber, // 订单号
            companyCode: info.companyCode, // 交货仓
            dateOfReceipt: info.dateOfReceipt, // 收款到账日期
          };
          this.setState(
            {
              formData: forms,
              transactionNumber: info.transactionNumber, // 平台交易号
              salesPlatform: info.salesPlatform, // 销售平台
              waybillNumber: info.waybillNumber, // 运单号
              handoverCode: info.handoverCode, // 销售平台
              importCustomsInfo: info.importCustomsInfo, // 销售平台
              senderTaxNumber: info.senderInfo?.senderTaxNumber,
              pointId: info.poPStation?.pointId,
              receiverInfo: info.receiverInfo,
              customsInfo: info.parcelInfo.productList,
              totalNum: info.parcelInfo.totalQuantity, // 申报的总数量
              totalWeight: info.parcelInfo.totalWeight, // 申报的总质量
              totalPrice: info.parcelInfo.totalPrice, // 申报的总价值
              heights: info.parcelInfo.height, // 高
              widths: info.parcelInfo.width, // 宽
              lengths: info.parcelInfo.length, // 长
              pickingOrders: info.parcelInfo.pickingOrders, // 拣货单
              currencyCode: info.parcelInfo.currency,
              IOSS: info.parcelInfo.IOSS,
              value: info.parcelInfo.isBattery,
              temuShow: temuShowList.includes(info.channelId),
            },
            () => {
              let params = {
                countryId: info.receiverInfo.countryId,
                warehouseCode: info.companyCode,
                userId: typeof info.userId == 'string' ? info.userId : info.userId[0],
              };
              this.getProductName(params);
            }
          );
        }
        if (
          (this.state.receiverInfo && this.state.receiverInfo.countryId != undefined) ||
          (info && info.channelId)
        ) {
          this.setState({
            countryDisabled: false,
          });
        }
        this.getCountriesName();
      }
      // 获取制单账号
      dispatch({
        type: 'Order/getShippingAccount',
        payload: { scene: 1 },
        callback: response => {
          if (response.success) {
            this.setState(
              {
                deliveryAccount: response.data,
              },
              () => {
                const { formData } = this.state;
                if (
                  (userId != undefined && expressCode == undefined) ||
                  (userId != undefined && id == undefined)
                ) {
                  let formDatas = { ...formData };
                  formDatas.userId = userId;
                  this.setState({
                    formData: formDatas,
                  });
                }
                // 是否是运单试算条跳转而来
                if (isFreightTrial != undefined) {
                  if (response.data?.length == 1) {
                    this.freightTrial([response.data[0].accountCode]);
                  } else {
                    // 弹窗选择
                    this.setState({
                      open: true,
                    });
                  }
                } else {
                  if (response.data.length == 1) {
                    this.setState(
                      {
                        isHaveDeliveryAccount: true,
                        formData: {
                          userId: response.data[0].accountCode, // 选中的制单账号
                          channelId: this.state.formData.channelId
                            ? this.state.formData.channelId
                            : undefined, // 产品编码
                          orderNumber: this.state.formData.orderNumber
                            ? this.state.formData.orderNumber
                            : undefined, // 订单号
                          companyCode: this.state.formData.companyCode
                            ? this.state.formData.companyCode
                            : undefined, // 交货仓
                          dateOfReceipt: this.state.formData.dateOfReceipt
                            ? this.state.formData.dateOfReceipt
                            : undefined, // 收款到账日期
                        },
                        isChangeAccount: false,
                        isHaveCountry: true,
                      },
                      () => {
                        let params = {
                          userId: response.data[0].accountCode,
                        };
                        // this.getProductName(params);
                      }
                    );
                  } else {
                    if (para != undefined) {
                      let arr = response.data.filter(item => {
                        return item.accountCode == userId;
                      });
                      this.setState({
                        deliveryAccount: arr,
                        formData: {
                          userId: userId, // 选中的制单账号
                          channelId: this.state.formData.channelId
                            ? this.state.formData.channelId
                            : undefined, // 产品编码
                          orderNumber: this.state.formData.orderNumber
                            ? this.state.formData.orderNumber
                            : undefined, // 订单号
                          companyCode: this.state.formData.companyCode
                            ? this.state.formData.companyCode
                            : undefined, // 交货仓
                          dateOfReceipt: this.state.formData.dateOfReceipt
                            ? this.state.formData.dateOfReceipt
                            : undefined, // 收款到账日期
                        },
                      });
                    } else {
                      this.setState({
                        isHaveDeliveryAccount: false,
                      });
                    }
                  }
                }
                this.getEJFGenericTemplates();
              }
            );
          }
        },
      });

      // 调用获取所有币种方法
      this.getCurrency();
      this.getHandoverCodes();

      this.getBusinessOrderTypeEnum();
      this.getCountryNameList(1);
    }
  }

  validateCustomerPayment = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'smallBag/validateCustomerPayment',
      callback: result => {
        if (result.success) {
          if (result.data == true) {
            Modal.info({
              title: '温馨提示',
              icon: null,
              content: `财务信息缺失，①需备案(商户主体银行付款账号/法人银行付款账号/股东银行付款账号任意一个；②非商户主体付款账号需签订付款委托书`,
              maskClosable: false,
              onOk() {
                router.push(`/financialManagement/rechargeManagement/offlineTopUp`);
              },
            });
          }
        }
      },
    });
  };

  /**
   * 运价试算
   * @param tags 参数
   */
  freightTrial = tags => {
    let tag = tags[0];
    const { isFreightTrial } = this.props.history.location.query;
    let searchConditions = JSON.parse(isFreightTrial);
    // 如果仅有一组数据满足，渲染数据
    this.setState(
      {
        isBusiness: searchConditions?.isBusiness ?? false,
        formData: {
          userId: tag, // 选中的制单账号
          channelId: searchConditions.productcode, // 产品编码
          orderNumber: this.state.formData.orderNumber
            ? this.state.formData.orderNumber
            : undefined, // 订单号
          companyCode: searchConditions.cityId, // 交货仓
          dateOfReceipt: this.state.formData.dateOfReceipt
            ? this.state.formData.dateOfReceipt
            : undefined, // 收款到账日期
        },
        receiverInfo: {
          address: undefined, //收件人地址
          city: undefined, //收件人城市
          company: undefined, // 收件人公司
          countryId: searchConditions.countryId, // 目的国ID
          email: undefined, //邮箱
          houseNumber: undefined, //门牌号
          name: undefined, //姓名
          phone: undefined, //电话
          state: undefined, //州(省)
          receiverTaxNumber: undefined, //税号
          zipCode: searchConditions.postCode, //邮编
        },
        heights: searchConditions.heigh,
        widths: searchConditions.width,
        lengths: searchConditions.length,
        isChangeAccount: false,
        isHaveCountry: true,
        countryDisabled: false,
        open: false,
      },
      async () => {
        const { form } = this.props;
        let params = {
          countryId: searchConditions.countryId,
          warehouseCode: searchConditions.cityId,
          userId: tag,
        };
        const channels = await this.getProductName(params);
        const isBusiness =
          channels
            ?.flatMap(item => item.value)
            ?.find(item => item.productNumber === searchConditions.productcode)?.primaryClassify ==
          17;
        this.setState(
          {
            isBusiness: isBusiness,
          },
          () => {
            form.setFieldsValue({
              receiverInfo: { zipCode: searchConditions.postCode },
            });
          }
        );
      }
    );
    return true;
  };

  /**
   *@Description: 商业快递获取制单账号
   *@MethodAuthor: dangh
   *@Date: 2023-10-25 10:31:47
   */
  getShipperAccount = () =>
    new Promise((resolve, reject) => {
      const { dispatch } = this.props;
      dispatch({
        type: 'Order/getShippingAccount',
        payload: { scene: 1 },
        callback: response => {
          if (response.success) {
            this.setState(
              {
                deliveryAccount: response.data,
              },
              () => {
                resolve(response.success);
              }
            );
          } else {
            reject(response.success);
          }
        },
      });
    });

  /**
   *@Description: 获取省市区
   *@MethodAuthor: dangh
   *@Date: 2023-10-26 15:20:59
   */

  getCountryNameList = (level, value, initial) =>
    new Promise((resolve, reject) => {
      const { dispatch, form } = this.props;
      dispatch({
        type: 'homePage/getCountryCodeList',
        payload: { level, name: value },
        callback: response => {
          if (response.success) {
            const data =
              response?.data?.map(item => {
                item.value = item.name;
                item.label = item.name;
                return item;
              }) ?? [];
            if (level === 1) {
              form?.resetFields(['senderInfo.state', 'senderInfo.city', 'senderInfo.district']);
              this.setState(
                {
                  provinceList: data,
                  cityList: [],
                  districtList: [],
                },
                () => resolve(response.success)
              );
            } else if (level === 2) {
              if (!initial) form?.resetFields(['senderInfo.city', 'senderInfo.district']);
              this.setState(
                {
                  cityList: data,
                  districtList: [],
                },
                () => resolve(response.success)
              );
            } else {
              if (!initial) form?.resetFields(['senderInfo.district']);
              this.setState(
                {
                  districtList: data,
                },
                () => resolve(response.success)
              );
            }
          } else {
            reject(response.success);
          }
        },
      });
    });

  /**
   *@Description: 获取商业快递
   *@MethodAuthor: dangh
   *@Date: 2023-10-25 10:20:34
   */
  getBusinessDetails = () => {
    const { history, form } = this.props;
    const { userId, expressCode, id, isBusiness, type } = history.location.query;
    Promise.all([
      this.getShipperAccount(),
      this.getWarehouseByType(),
      this.getCountryNameList(1),
      this.getCurrency(),
      this.getHandoverCodes(),
      this.getBusinessOrderTypeEnum(),
      this.getCountriesName(),
    ]).then(res => {
      if (userId !== undefined && id !== undefined) {
        // 获取订单详情
        this.setState(
          {
            countryDisabled: false,
            isChangeAccount: false,
          },
          () => {
            this.getBusinessDraftDetail(userId, id);
          }
        );
      } else if (userId !== undefined && expressCode !== undefined) {
        // 获取运单详情
        this.getBusinessOrderDetail(userId, expressCode);
      } else if (type == 'add') {
        // 从运单编辑一键复制过来的
        const data = JSON.parse(sessionStorage.getItem('copys'));
        form.setFieldsValue({
          ...data,
          userId: [data?.userId],
          countryId: data?.receiverInfo?.countryId,
          parcelInfo:
            data?.parcelInfo?.map(item => ({
              ...item,
              tableKey: (Math.random() * 1000000).toFixed(0),
            })) || [],
          productInfo:
            data?.productInfo?.map(item => ({
              ...item,
              tableKey: (Math.random() * 1000000).toFixed(0),
            })) || [],
          attachmentsInfo:
            data?.attachmentsInfo?.map(item => ({
              ...item,
              tableKey: (Math.random() * 1000000).toFixed(0),
            })) || [],
        });
      }
    });
  };
  /**
   *@Description: 运单详情
   *@MethodAuthor: dangh
   *@Date: 2023-10-25 11:32:07
   */
  getBusinessOrderDetail = (userId, waybillNumber) => {
    const { dispatch, form } = this.props;
    dispatch({
      type: 'business/getBusinessOrderDetail',
      payload: {
        userId,
        waybillNumber,
      },
      callback: response => {
        if (response.success) {
          const params = {
            countryId: response.data?.receiverInfo?.countryId,
            warehouseCode: response.data?.companyCode,
            userId: response?.data?.userId,
          };
          Promise.all([
            this.getProductName(params),
            response.data?.senderInfo?.state
              ? this.getCountryNameList(2, response.data?.senderInfo?.state, true)
              : undefined,
            response.data?.senderInfo?.city
              ? this.getCountryNameList(3, response.data?.senderInfo?.city, true)
              : undefined,
          ]).then(values => {
            let formData = Object.assign({}, this.state.formData, { userId: [userId] });
            this.setState(
              {
                isBusiness: true,
                formData,
              },
              () => {
                setTimeout(() => {
                  form.setFieldsValue({
                    userId: [userId],
                    ...response.data,
                    countryId: response.data?.receiverInfo?.countryId,
                    parcelInfo:
                      response?.data?.parcelInfo?.map(item => ({
                        ...item,
                        tableKey: (Math.random() * 1000000).toFixed(0),
                      })) || [],
                    productInfo:
                      response?.data?.productInfo?.map(item => ({
                        ...item,
                        tableKey: (Math.random() * 1000000).toFixed(0),
                      })) || [],
                    attachmentsInfo:
                      response?.data?.attachmentsInfo?.map(item => ({
                        ...item,
                        tableKey: (Math.random() * 1000000).toFixed(0),
                      })) || [],
                  });
                }, 100);
              }
            );
          });
        }
      },
    });
  };

  /**
   *@Description: 获取订单详情
   *@MethodAuthor: dangh
   *@Date: 2023-10-25 10:50:01
   */
  getBusinessDraftDetail = (userId, id) => {
    const { dispatch, form } = this.props;
    dispatch({
      type: 'business/getBusinessDraftDetail',
      payload: {
        userId,
        id,
      },
      callback: response => {
        if (response.success) {
          const params = {
            countryId: response.data?.receiverInfo?.countryId,
            warehouseCode: response.data?.companyCode,
            userId: response?.data?.userId,
          };
          Promise.all([
            this.getProductName(params),
            response.data?.senderInfo?.state
              ? this.getCountryNameList(2, response.data?.senderInfo?.state, true)
              : undefined,
            response.data?.senderInfo?.city
              ? this.getCountryNameList(3, response.data?.senderInfo?.city, true)
              : undefined,
          ]).then(values => {
            let formData = Object.assign({}, this.state.formData, { userId: [userId] });
            this.setState(
              {
                isBusiness: true,
                formData,
              },
              () => {
                setTimeout(() => {
                  form.setFieldsValue({
                    userId: [userId],
                    ...response.data,
                    countryId: response.data?.receiverInfo?.countryId,
                    parcelInfo:
                      response?.data?.parcelInfo?.map(item => ({
                        ...item,
                        tableKey: (Math.random() * 1000000).toFixed(0),
                      })) || [],
                    productInfo:
                      response?.data?.productInfo?.map(item => ({
                        ...item,
                        tableKey: (Math.random() * 1000000).toFixed(0),
                      })) || [],
                    attachmentsInfo:
                      response?.data?.attachmentsInfo?.map(item => ({
                        ...item,
                        tableKey: (Math.random() * 1000000).toFixed(0),
                      })) || [],
                  });
                }, 100);
              }
            );
          });
        }
      },
    });
  };

  /**
   *@Description: 获取基础枚举属性  需要放到公共页面里，其他组件需要税号里类型
   *@MethodAuthor: dangh
   *@Date: 2023-10-17 10:44:26
   */
  getBusinessOrderTypeEnum = () =>
    new Promise((resolve, reject) => {
      const { dispatch } = this.props;
      dispatch({
        type: 'business/getBusinessOrderTypeEnum',
        callback: response => {
          if (response.success) {
            const {
              commodityType = [],
              batteryType = [],
              taxType = [],
              packType = [],
            } = response.data;
            this.setState(
              {
                commodityTypeList: commodityType,
                batteryTypeList: batteryType,
                taxTypeList: taxType,
                packTypeList: packType,
              },
              () => {
                resolve(response.success);
              }
            );
          } else {
            this.setState(
              {
                commodityTypeList: [],
                batteryTypeList: [],
                taxTypeList: [],
                packTypeList: [],
              },
              () => {
                reject(response.success);
              }
            );
          }
        },
      });
    });

  // 根据制单账号与运单号获取订单详情
  getOrderDetails = (id, code) => {
    const { dispatch } = this.props;
    let params = {
      userId: id,
      waybillNumber: code,
    };

    dispatch({
      type: 'Order/getOrderDetails',
      payload: params,
      callback: result => {
        if (result.success && result.data) {
          let forms = {
            userId: result.data.userId, // 选中的制单账号
            channelId: result.data.channelId, // 产品编码
            orderNumber: result.data.orderNumber, // 订单号
            companyCode: result.data.companyCode, // 交货仓
            dateOfReceipt: result.data.dateOfReceipt, // 收款到账日期
            transactionNumber: result.data.transactionNumber, // 平台交易号
          };
          // 新增3个单独产品的重量提示
          if (result.data.channelId == 1155) {
            notification.warning({
              message: '发货提示',
              description: '毛重和泡重取大后大于等于21kg 才能发货',
              duration: null,
            });
          } else if (result.data.channelId == 1163) {
            notification.warning({
              message: '发货提示',
              description: '毛重大于23kg（不含）泡重大于27kg(不含）才能发货',
              duration: null,
            });
          } else if (result.data.channelId == 1197) {
            notification.warning({
              message: '发货提示',
              description: '毛重大于22kg（不含）泡重大于26Kg（不含）才能发货',
              duration: null,
            });
          }
          this.setState({
            formData: forms,
            senderTaxNumber: result.data.senderInfo.taxNumber,
            pointId: result.data.poPStation?.pointId,
            handoverCode: result.data.handoverCode,
            salesPlatform: result.data.salesPlatform,
            waybillNumber: result.data.waybillNumber,
            importCustomsInfo: result.data.importCustomsInfo,
            receiverInfo: result.data.receiverInfo,
            customsInfo: result.data.parcelInfo.productList,
            totalNum: result.data.parcelInfo.totalQuantity, // 申报的总数量
            totalWeight: result.data.parcelInfo.totalWeight, // 申报的总质量
            totalPrice: result.data.parcelInfo.totalPrice, // 申报的总价值
            heights: result.data.parcelInfo.height ? result.data.parcelInfo.height : undefined, // 高
            widths: result.data.parcelInfo.width ? result.data.parcelInfo.width : undefined, // 宽
            lengths: result.data.parcelInfo.length ? result.data.parcelInfo.length : undefined, // 长
            pickingOrders: result.data.remark, // 拣货单
            currencyName: result.data.parcelInfo.currencyInfo.name,
            currencyCode: result.data.parcelInfo.currencyInfo.code,
            IOSS: result.data.parcelInfo.ioss,
            value: result.data.parcelInfo.hasBattery,
            temuShow: temuShowList.includes(result.data.channelId),
          });
          if (
            result.data.userId &&
            result.data.receiverInfo &&
            result.data.receiverInfo.countryId
          ) {
            let params = {
              countryId: result.data.receiverInfo.countryId,
              warehouseCode: result.data?.companyCode,
              userId:
                typeof result.data.userId == 'string' ? result.data.userId : result.data.userId[0],
            };
            this.getProductName(params);
          }
          // if (result.data.channelId) {
          this.getCountriesName();
          // }
        }
      },
    });
  };

  // 根据制单账号与订单ID获取订单详情
  getOrderDetailsById = (userId, id) => {
    const { dispatch } = this.props;
    let params = {
      id: id, //订单ID
      userId: userId, //制单账号
    };
    dispatch({
      type: 'Order/getOrderDetailById',
      payload: params,
      callback: result => {
        if (result.success) {
          let forms = {
            userId: result.data.userId, // 选中的制单账号
            channelId: result.data.channelId ? result.data.channelId : '', // 产品编码
            orderNumber: result.data.orderNumber, // 订单号
            companyCode: result.data.companyCode, // 交货仓
            dateOfReceipt: result.data.dateOfReceipt ? result.data.dateOfReceipt : '', // 收款到账日期
          };
          // 新增3个单独产品的重量提示
          if (result.data.channelId == 1155) {
            notification.warning({
              message: '发货提示',
              description: '毛重和泡重取大后大于等于21kg 才能发货',
              duration: null,
            });
          } else if (result.data.channelId == 1163) {
            notification.warning({
              message: '发货提示',
              description: '毛重大于23kg（不含）泡重大于27kg(不含）才能发货',
              duration: null,
            });
          } else if (result.data.channelId == 1197) {
            notification.warning({
              message: '发货提示',
              description: '毛重大于22kg（不含）泡重大于26Kg（不含）才能发货',
              duration: null,
            });
          }
          this.setState({
            formData: forms,
            transactionNumber: result.data.transactionNumber ? result.data.transactionNumber : '', // 平台交易号
            senderTaxNumber: result.data.senderInfo.taxNumber
              ? result.data.senderInfo.taxNumber
              : '',
            pointId: result.data.poPStation?.pointId,
            receiverInfo: result.data.receiverInfo,
            salesPlatform: result.data.salesPlatform,
            waybillNumber: result.data.waybillNumber,
            handoverCode: result.data.handoverCode,
            importCustomsInfo: result.data.importCustomsInfo,
            customsInfo: result.data.parcelInfo.productList,
            totalNum: result.data.parcelInfo.totalQuantity
              ? result.data.parcelInfo.totalQuantity
              : 0, // 申报的总数量
            totalWeight: result.data.parcelInfo.totalWeight
              ? result.data.parcelInfo.totalWeight
              : 0, // 申报的总质量
            totalPrice: result.data.parcelInfo.totalPrice ? result.data.parcelInfo.totalPrice : 0, // 申报的总价值
            heights: result.data.parcelInfo.height ? result.data.parcelInfo.height : undefined, // 高
            widths: result.data.parcelInfo.width ? result.data.parcelInfo.width : undefined, // 宽
            lengths: result.data.parcelInfo.length ? result.data.parcelInfo.length : undefined, // 长
            pickingOrders: result.data.remark ? result.data.remark : '', // 拣货单
            currencyName:
              result.data.parcelInfo.currencyInfo && result.data.parcelInfo.currencyInfo.name
                ? result.data.parcelInfo.currencyInfo.name
                : '',
            currencyCode:
              result.data.parcelInfo.currencyInfo && result.data.parcelInfo.currencyInfo.code
                ? result.data.parcelInfo.currencyInfo.code
                : '',
            value: result.data.parcelInfo.hasBattery,
            IOSS: result.data.parcelInfo.ioss,
            temuShow: temuShowList.includes(result.data.channelId),
          });
          if (
            result.data.userId &&
            result.data.receiverInfo &&
            result.data.receiverInfo.countryId
          ) {
            let params = {
              countryId: result.data.receiverInfo.countryId,
              warehouseCode: result.data?.companyCode,
              userId:
                typeof result.data.userId == 'string' ? result.data.userId : result.data.userId[0],
            };
            this.getProductName(params);
          }
          this.getCountriesName();
        }
      },
    });
  };

  // 获取所有币种
  getCurrency = () =>
    new Promise((resolve, reject) => {
      const { dispatch } = this.props;
      dispatch({
        type: 'Order/getCurrency',
        callback: result => {
          if (result.success) {
            if (this.state.currencyCode) {
              result.data.forEach(item => {
                if (item.currencyCode == this.state.currencyCode) {
                  this.setState({
                    currencyCode: item.currencyCode,
                  });
                }
              });
            }
            this.setState(
              {
                currencys: result.data.map(item => {
                  item.label = `${item.name}/${item.code}`;
                  return item;
                }),
              },
              () => {
                resolve(result.success);
              }
            );
          } else {
            reject(result.success);
          }
        },
      });
    });

  // 获取海外交货地
  getHandoverCodes = () =>
    new Promise((resolve, reject) => {
      const { dispatch } = this.props;
      dispatch({
        type: 'Order/getHandoverCodes',
        callback: result => {
          if (result.success) {
            this.setState(
              {
                handoverCodes: result.data
                  .filter(x => x.isEnable)
                  .map(item => {
                    item.label = `${item.countryName}/${item.overseasDeliveryLocation}`;
                    item.code = item.overseasDeliveryLocation;
                    return item;
                  }),
              },
              () => {
                resolve(result.success);
              }
            );
          } else {
            reject(result.success);
          }
        },
      });
    });

  //获取交货仓
  getWarehouseByType = () =>
    new Promise((resolve, reject) => {
      const { dispatch } = this.props;
      dispatch({
        type: 'Order/getWarehouseByType',
        payload: 0,
        callback: res => {
          if (res.success) {
            this.setState(
              {
                warehouseList: res.data,
              },
              () => {
                resolve(res.success);
              }
            );
          } else {
            reject(res.success);
          }
        },
      });
    });

  // 获取产品名称
  getProductName = data =>
    new Promise((resolve, reject) => {
      const { dispatch, form } = this.props;
      const { channelId } = this.state.formData;
      let params = data;
      dispatch({
        type: 'business/getChannel',
        payload: params,
        callback: response => {
          if (response.success) {
            const data = response.data;
            data.forEach(item => {
              const match = item.value.find(product => product.productNumber == channelId);
              if (match) {
                this.setState({
                  isProduct: true,
                });
              }
            });
            this.setState(
              {
                channels: data.reduce((accumulatedData, currentItem) => {
                  // Clone the current item to avoid mutations
                  const newItem = { ...currentItem };
                  newItem.value = newItem.value.map(subItem => ({
                    ...subItem,
                    parentKey: newItem.key,
                  }));
                  accumulatedData.push(newItem);
                  return accumulatedData;
                }, []),
              },
              () => {
                resolve(this.state.channels);
              }
            );
          } else {
            reject(response.success);
          }
        },
      });
      // dispatch({
      //   type: 'Order/getProductName',
      //   payload: params,
      //   callback: result => {
      //     if (result.success) {
      //       let arr = result.data.filter(item => {
      //         return item.status == '1';
      //       });
      //       arr.forEach(item => {
      //         if (item.id == channelId) {
      //           this.setState({
      //             isProduct: true,
      //           });
      //         }
      //       });
      //       this.setState({
      //         channels: arr,
      //       });
      //     }
      //   },
      // });
    });

  // 产品名称获取焦点
  selectOnfocus = () => {
    const { isBusiness } = this.state;
    if (this.state.formData.userId == undefined && !isBusiness) {
      message.warning('请确认制单账号后再制单');
      return;
    }
  };

  // 产品信息变化
  selectChange = (id, record) => {
    const { form } = this.props;
    if (!id && !record) return;
    let ids = Object.assign({}, this.state.formData, { channelId: id });
    this.setState(
      {
        formData: ids,
        isBusiness: record?.primaryClassify == 17,
      },
      () => {
        let parcelInfo = form.getFieldValue('parcelInfo');
        let productInfo = form.getFieldValue('productInfo');
        if (!parcelInfo || parcelInfo?.length === 0) {
          form.setFieldsValue({
            parcelInfo: [{ tableKey: (Math.random() * 1000000).toFixed(0) }],
          });
        }
        if (!productInfo || productInfo?.length === 0) {
          form.setFieldsValue({
            productInfo: [{ tableKey: (Math.random() * 1000000).toFixed(0) }],
          });
        }
      }
    );

    // temu 展示处理
    this.setState({
      temuShow: temuShowList.includes(id),
    });

    if (id == 1155) {
      notification.warning({
        message: '发货提示',
        description: '毛重和泡重取大后大于等于21kg 才能发货',
        duration: null,
      });
    } else if (id == 1163) {
      notification.warning({
        message: '发货提示',
        description: '毛重大于23kg（不含）泡重大于27kg(不含）才能发货',
        duration: null,
      });
    } else if (id == 1197) {
      notification.warning({
        message: '发货提示',
        description: '毛重大于22kg（不含）泡重大于26Kg（不含）才能发货',
        duration: null,
      });
    }

    /**
     * 产品：733-大陆FEDE-IP
     * 客户下单是”约旦“ --- 285或”尼日利亚“ --- 148时
     */
    const result = this.props.form.getFieldsValue();
    if (id == 733 && result?.countryId && (result?.countryId == 285 || result?.countryId == 148)) {
      notification.warning({
        message: '发货提示',
        description: '目的国有16%增值税,收件人不支付将自动转发件人支付',
        duration: null,
      });
    }
    // this.getCountriesName(id);
    // this.props.form.setFieldsValue({
    //   countryId: undefined,
    // });
    // this.setState({
    //   countryId: '',
    //   countryDisabled: false,
    // });
  };

  // 获取全部通达国家
  getCountriesName = () =>
    new Promise((resolve, reject) => {
      const { dispatch } = this.props;
      dispatch({
        type: 'Order/getCountriesName',
        callback: result => {
          if (result.success) {
            result.data.forEach(item => {
              item.countryNamePinYin = loadPinYinInit.ConvertPinyin(item.nameCh);
            });
            this.setState(
              {
                countriesData: result.data,
                // isHaveCountry: true,
              },
              () => {
                resolve(result.success);
              }
            );
          } else {
            reject(result.success);
            // this.setState({
            //   isHaveCountry: false,
            // });
          }
        },
      });
    });

  // 国家选项变化
  contryChange = id => {
    const { userId } = this.state.formData;
    if (!userId) {
      message.warning('请选择制单账号', 2);
      return false;
    } else {
      let countryId = Object.assign({}, this.state.receiverInfo, { countryId: id });
      this.setState(
        {
          countryId: countryId,
          countryDisabled: false,
        },
        () => {
          this.props.form.setFieldsValue({
            channelId: undefined,
          });
          const resultInfo = this.props.form.getFieldsValue();
          let params = {
            countryId: id,
            userId: typeof userId == 'string' ? userId : userId[0],
            warehouseCode: resultInfo?.companyCode,
          };
          this.getProductName(params);
        }
      );
    }
  };

  // 币种类型变化
  currencyChange = id => {
    this.setState({
      currencyCode: id,
    });
  };

  // 海外交货地变化
  handoverChange = id => {
    this.setState({
      handoverCode: id,
    });
  };

  // 到款日期选择
  timeChange = time => {
    let times = moment(time).format('yyyy-MM-DD');
    let date = Object.assign({}, this.state.formData, { dateOfReceipt: times });
    this.setState({
      formData: date,
    });
  };

  // 新增报关信息
  addCustomsInfo = () => {
    const { customsInfo } = this.state;
    if (customsInfo.length == 20) {
      message.error('报关信息仅支持上传20条!');
      return;
    }
    const val = {
      goodsNameCh: undefined,
      goodsNameEn: undefined, // 英文名
      quantity: undefined, // 单品数量
      weight: undefined, // 单品重量
      price: undefined, // 申报价格
      productMaterial: undefined, // 商品材质
      productHsCode: undefined, // 商品海关编码
      productLink: undefined, // 商品链接
      productSku: undefined, // sku
      productImei: undefined, // imei
      currencys: undefined,
    };
    this.setState({
      customsInfo: [...customsInfo, val],
    });
  };

  // 删除报关信息
  delCustomsInfo = i => {
    let TheIndex = i;
    const { form } = this.props;
    const customsInfos = this.state.customsInfo;
    let c = form.getFieldsValue();
    customsInfos.forEach((item, index) => {
      item.goodsNameCh = c[`goodsNameCh${index}`];
      item.goodsNameEn = c[`goodsNameEn${index}`];
      item.price = c[`price${index}`];
      item.productHsCode = c[`productHsCode${index}`];
      item.productLink = c[`productLink${index}`];
      item.productSku = c[`productSku${index}`];
      item.productImei = c[`productImei${index}`];
      item.productMaterial = c[`productMaterial${index}`];
      item.quantity = c[`quantity${index}`];
      item.weight = c[`weight${index}`];
      item.productPrice = c[`productPrice${index}`];
    });
    let a = customsInfos.filter((val, index) => {
      return index != i;
    });
    a.forEach((val, i) => {
      form.setFieldsValue({
        [`goodsNameCh${i}`]: val.goodsNameCh,
        [`goodsNameEn${i}`]: val.goodsNameEn,
        [`price${i}`]: val.price,
        [`productHsCode${i}`]: val.productHsCode,
        [`productLink${i}`]: val.productLink,
        [`productSku${i}`]: val.productSku,
        [`productImei${i}`]: val.productImei,
        [`productMaterial${i}`]: val.productMaterial,
        [`quantity${i}`]: val.quantity,
        [`weight${i}`]: val.weight,
        [`productPrice${i}`]: val.productPrice,
      });
    });
    this.setState(
      {
        customsInfo: [...a],
      },
      () => {
        let Num = 0;
        let price = 0;
        let weight = 0;
        this.state.customsInfo.forEach(value => {
          Num += Number(value.quantity);
          if (value.price) {
            price += Number(value.quantity * value.price);
          }
          if (value.weight) {
            weight += Number(value.quantity * value.weight);
          }
        });
        this.setState({
          totalNum: Num,
          totalWeight: weight,
          totalPrice: price,
        });
      }
    );
  };

  // 事件
  tabOnChange = val => {
    this.state.tabNumber = val;
    this.setState({
      tabNumber: val,
    });
  };
  shipperChange = (tag, index) => {
    const { form } = this.props;
    const { countryId } = this.state.receiverInfo;
    let userID = Object.assign({}, this.state.formData, { userId: tag });
    let finalCountryId = countryId ?? form?.getFieldValue('countryId');
    let warehouseCode = countryId ?? form?.getFieldValue('companyCode');
    this.setState(
      {
        formData: userID,
        isChangeAccount: false,
      },
      () => {
        if (finalCountryId) {
          let params = {
            userId: typeof tag == 'string' ? tag : tag[0],
            countryId: finalCountryId,
            warehouseCode: warehouseCode,
          };
          this.getProductName(params);
        }
      }
    );
    form.setFieldsValue({
      shippers: tag,
      channelId: undefined,
    });
  };

  // 修改单品数量
  changeNum = (val, i) => {
    const arr = this.state.customsInfo;
    arr[i].quantity = val;
    let Num = 0;
    let price = 0;
    let weight = 0;
    arr.forEach(value => {
      Num += Number(value.quantity);
      if (value.price) {
        price += Number(value.quantity * value.price);
      }
      if (value.weight) {
        weight += Number(value.quantity * value.weight);
      }
    });
    const { form } = this.props;
    let onQuantity = val;
    let onPrice = arr[i]?.price;
    if (
      /^\d+$|^\d+[.]?\d+$/.test(onQuantity) &&
      /^([1-9]\d{0,15}\.\d{1,2}|0\.\d{1,2}|[1-9]\d{0,15})$/.test(onPrice)
    ) {
      let productPrice = new Decimal(onPrice).mul(new Decimal(onQuantity));
      form.setFieldsValue({
        [`productPrice${i}`]: productPrice.toNumber(),
      });
    } else {
      form.setFieldsValue({
        [`productPrice${i}`]: undefined,
      });
    }
    this.setState({
      customsInfo: arr,
      totalNum: Num,
      totalWeight: weight,
      totalPrice: price,
    });
  };

  // 修改单品重量
  changeWeight = (val, i) => {
    const arr = this.state.customsInfo;
    arr[i].weight = val;
    let weights = 0;
    arr.forEach(value => {
      if (value.quantity) {
        weights += Number(value.quantity * value.weight);
      } else {
        weights += Number(value.weight);
      }
    });
    this.setState({
      customsInfo: arr,
      totalWeight: weights,
    });
  };

  // 修改单品单价
  changePrice = (val, i) => {
    const arr = this.state.customsInfo;
    arr[i].price = val;
    let prices = 0;
    arr.forEach(value => {
      if (value.quantity) {
        prices += Number(value.quantity * value.price);
      } else {
        prices += Number(value.price);
      }
    });
    const { form } = this.props;
    let onQuantity = arr[i]?.quantity;
    let onPrice = val;
    if (
      /^\d+$|^\d+[.]?\d+$/.test(onQuantity) &&
      /^([1-9]\d{0,15}\.\d{1,2}|0\.\d{1,2}|[1-9]\d{0,15})$/.test(onPrice)
    ) {
      let productPrice = new Decimal(onPrice).mul(new Decimal(onQuantity));
      form.setFieldsValue({
        [`productPrice${i}`]: productPrice.toNumber(),
      });
    } else {
      form.setFieldsValue({
        [`productPrice${i}`]: undefined,
      });
    }
    this.setState({
      customsInfo: arr,
      totalPrice: prices,
    });
  };

  // 是否含电
  isElectricityChange = e => {
    this.setState({
      value: e.target.value,
    });
  };

  // 价格计算
  chargePriceSimple = () => {
    logSave(LogType.packet5);
    const { form, dispatch } = this.props;
    let fieldsValue = form.getFieldsValue();
    const { totalWeight, isBusiness, currencys } = this.state;
    // 获取userId
    let userId = fieldsValue.userId;
    if (!(userId && typeof userId == 'string')) {
      userId = userId ? userId[0] : undefined;
    }
    if (!fieldsValue.countryId) {
      message.error('请选择国家');
      return;
    }
    if (!fieldsValue.channelId) {
      message.error('请选择产品');
      return;
    }
    if (isBusiness) {
      if (!fieldsValue?.parcelInfo?.length) {
        message.error('至少填写一件包裹信息');
        return;
      }
    }
    const businessParams = isBusiness
      ? {
          declaredCurrencyId: currencys?.find(
            item => item.code == fieldsValue?.customsInfo?.currency
          )?.id,
          declaredValue: fieldsValue?.customsInfo?.price,
          companyCode: fieldsValue?.companyCode,
          address: fieldsValue?.receiverInfo?.address1,
          postCode: fieldsValue?.receiverInfo?.zipCode,
          city: fieldsValue?.receiverInfo?.city,
          items: fieldsValue?.parcelInfo,
        }
      : {
          cityId: fieldsValue.companyCode,
          weight: totalWeight,
          high: fieldsValue.height,
          length: fieldsValue.length,
          width: fieldsValue.width,
          postCode: fieldsValue.zipCode,
        };

    // 设置参数
    let params = {
      customerCode: userId,
      productCode: fieldsValue.channelId,
      countryId: fieldsValue.countryId,
      ...businessParams,
    };
    // 进行试算
    dispatch({
      type: isBusiness ? 'business/manyPiece' : 'Order/chargePriceSimple',
      payload: params,
      callback: res => {
        if (res.success) {
          message.success('试算成功');
          let moneyNum = 0;
          let moneyItem = [];
          if (isBusiness) {
            moneyNum = new Decimal(res?.data?.totalMoney);
            moneyItem = calculateFreightAmount(res?.data?.item);
          } else {
            res.data?.expenseItems?.forEach(item => {
              moneyNum = new Decimal(moneyNum).add(new Decimal(item.money));
            });
          }
          this.setState({
            moneyNum: moneyNum.toNumber(),
            moneyItem: moneyItem,
          });
        } else {
          this.setState({
            moneyNum: undefined,
            moneyItem: undefined,
          });
        }
      },
    });
  };

  // 接口暂存 小包专线和商业快递
  clickStagEJFAndBusiness = () => {
    logSave(LogType.packet6);
    const { isBusiness } = this.state;
    if (!isBusiness) {
      this.clickStag();
    } else {
      this.clickStagBusiness();
    }
  };
  // 接口暂存 商业快递
  clickStagBusiness = () => {
    const { form, dispatch } = this.props;
    form.validateFieldsAndScroll(['userId'], err => {
      if (!err) {
        const value = form?.getFieldsValue();
        let userId = typeof value?.userId == 'string' ? value?.userId : value?.userId?.[0];
        const params = {
          ...value,
          userId: userId,
          receiverInfo: {
            ...value?.receiverInfo,
            countryId: value?.countryId,
          },
        };
        this.createBusinessDraft(params, () => {
          localStorage.setItem(
            'businessFormSearch',
            JSON.stringify({ status: '9', userId: [userId] })
          );
          router.push(`/smallBag/orderManagement/orderList?number=3`);
        });
      }
    });
  };

  // 接口暂存
  clickStag = () => {
    const { form, dispatch } = this.props;
    const {
      totalPrice,
      totalWeight,
      totalNum,
      heights,
      widths,
      lengths,
      pickingOrders,
      customsInfo,
    } = this.state;
    let arr = [];
    let fieldsValue = form.getFieldsValue();
    for (let i = 0; i < customsInfo.length; i++) {
      let param = {};
      param.goodsNameCh = fieldsValue[`goodsNameCh${i}`];
      param.goodsNameEn = fieldsValue[`goodsNameEn${i}`];
      param.price = fieldsValue[`price${i}`];
      param.hscode = fieldsValue[`productHsCode${i}`];
      param.material = fieldsValue[`productMaterial${i}`];
      param.quantity = fieldsValue[`quantity${i}`];
      param.weight = fieldsValue[`weight${i}`];
      param.url = fieldsValue[`productLink${i}`];
      param.sku = fieldsValue[`productSku${i}`];
      param.imei = fieldsValue[`productImei${i}`];
      arr.push(param);
      if (param.goodsNameCh == undefined) {
        this.props.form.setFieldsValue({
          [`goodsNameCh${i}`]: undefined,
        });
      }
      if (param.goodsNameEn == undefined) {
        this.props.form.setFieldsValue({
          [`goodsNameEn${i}`]: undefined,
        });
      }
      if (param.price == undefined) {
        this.props.form.setFieldsValue({
          [`price${i}`]: undefined,
        });
      }
      if (param.quantity == undefined) {
        this.props.form.setFieldsValue({
          [`quantity${i}`]: undefined,
        });
      }
      if (param.weight == undefined) {
        this.props.form.setFieldsValue({
          [`weight${i}`]: undefined,
        });
      }
    }
    const IOSSOptionsData = this.basicInformationOfEJFRef.current?.getOptionsData();
    let newArr = arr.splice(0, customsInfo.length);
    let userId = fieldsValue.userId;
    let params = {
      channelId: fieldsValue.channelId,
      orderNumber: fieldsValue.orderNumber,
      transactionNumber: fieldsValue.transactionNumber,
      companyCode: fieldsValue.companyCode,
      salesPlatform: fieldsValue.salesPlatform,
      waybillNumber: fieldsValue.waybillNumber,
      handoverCode: fieldsValue.handoverCode,
      importCustomsInfo: {
        taxPolicyExtends: {
          csp: fieldsValue.importCustomsInfo.taxPolicyExtends.csp,
        },
      },
      dateOfReceipt: fieldsValue.datePicker
        ? moment(fieldsValue.datePicker).format('yyyy-MM-DD')
        : '',
      remark: fieldsValue.pickingOrders,
      poPStation: {
        pointId: fieldsValue.pointId,
      },
      receiverInfo: {
        address: fieldsValue.address,
        city: fieldsValue.city,
        company: fieldsValue.company,
        country: fieldsValue.countryId,
        email: fieldsValue.email,
        houseNumber: fieldsValue.houseNumber,
        name: fieldsValue.name,
        phone: fieldsValue.phone,
        state: fieldsValue.state,
        taxNumber: fieldsValue.receiverTaxNumber,
        zipCode: fieldsValue.zipCode,
        pinflNumber: fieldsValue.pinflNumber,
      },
      parcelInfo: {
        ioss:
          IOSSOptionsData?.find(item => item.value == fieldsValue.IOSS)?.taxId ?? fieldsValue.IOSS,
        currency: fieldsValue.currencys,
        totalPrice: totalPrice,
        totalQuantity: totalNum,
        totalWeight: totalWeight,
        height: fieldsValue.height,
        width: fieldsValue.width,
        length: fieldsValue.length,
        hasBattery: fieldsValue.isBattery,
        productList: newArr,
      },
      senderInfo: {
        taxNumber: fieldsValue.senderTaxNumber,
      },
    };
    if (userId && typeof userId == 'string') {
      params.userId = userId;
    } else {
      params.userId = userId != undefined ? userId[0] : undefined;
    }
    dispatch({
      type: 'Order/temporaryData',
      payload: params,
      callback: res => {
        if (res.success) {
          message.success(res.message, 0.3).then(() => {
            let miscellaneous = JSON.parse(localStorage.getItem('miscellaneous'));
            // if (this.state.isHaveNumber) {
            // 制单成功，跳转订单查询，删除缓存信息
            let deliveryAccount = typeof userId == 'string' ? userId : userId[0];
            if (miscellaneous != null) {
              miscellaneous.isRefresh = true;
              miscellaneous.isRefreshList = false;
              miscellaneous.printingStatus = '';
              miscellaneous.queryState = true;
              miscellaneous.awbNum = [];
              miscellaneous.tabNumber = '9';
              miscellaneous.allTabs = '-2';
              miscellaneous.date = ['', ''];
              localStorage.setItem('miscellaneous', JSON.stringify(miscellaneous));
            }
            localStorage.setItem('deliveryAccount', deliveryAccount);
            localStorage.removeItem('tableData');
            localStorage.removeItem('countriesData');
            localStorage.removeItem('productData');
            localStorage.removeItem('PAGE');
            // }
            router.push('/smallBag/orderManagement/orderList');
          });
        }
        // else {
        //   message.error(res.message);
        // }
      },
    });
  };

  // 提交
  submitAction = text => {
    logSave(LogType.packet7);
    const { isBusiness } = this.state;
    if (!isBusiness) {
      this.submitEjfAction(text);
    } else {
      // 商业快递
      this.submitBusinessAction(text);
    }
  };

  // 商业快递提交
  submitBusinessAction = text => {
    const { form, dispatch } = this.props;
    if (text == 'add' || text == 'submitAdd') {
      this.updateOrderEjfAndBusiness(undefined, text);
    } else {
      form.validateFields((err, values) => {
        if (!err) {
          let result = this.checkPackage(values);
          if (result) {
            return message.error(result);
          }
          const params = {
            ...values,
            userId: typeof values?.userId == 'string' ? values?.userId : values?.userId?.[0],
            receiverInfo: {
              ...values?.receiverInfo,
              countryId: values?.countryId,
            },
          };
          this.checkBusinessNumber(params, () => {
            dispatch({
              type: 'business/createBusinessOrder',
              payload: params,
              callback: response => {
                if (response.success) {
                  let formData = Object.assign({}, this.state.formData, { userId: params.userId });
                  this.setState({
                    isCreatedSuccess: true,
                    waybillNumber: response.data?.waybillNumber,
                    formData,
                  });
                }
              },
            });
          });
        } else {
          message.error('请填写完整所有必填项');
        }
      });
    }
  };

  // EJF提交
  submitEjfAction = text => {
    const { form, dispatch } = this.props;
    const { expressCode, para, status, state } = this.props.history.location.query;
    form
      .validateFields()
      .then(values => {})
      .catch(errorInfo => {
        if (errorInfo.errors.userId) {
          message.error('请选择制单账号！');
          return;
        } else if (errorInfo.errors.orderNumber) {
          message.error('请填写订单号！');
          return;
        } else if (errorInfo.errors.channelId) {
          message.error('请选择产品！');
          return;
        } else if (errorInfo.errors.countryId) {
          message.error('请选择国家！');
          return;
        } else if (errorInfo.errors.currencys) {
          message.error('请选择申报币种！');
          return;
        } else if (errorInfo.errors.name) {
          message.error('请填写姓名！');
          return;
        } else if (errorInfo.errors.state) {
          message.error('请填写省/州！');
          return;
        } else if (errorInfo.errors.city) {
          message.error('请填写城市！');
          return;
        } else if (errorInfo.errors.phone) {
          message.error('请填写电话！');
          return;
        } else if (errorInfo.errors.address) {
          message.error('请填写地址！');
          return;
        } else {
          message.error('请完善报关信息！');
          return;
        }
      });
    const { id } = this.props.history.location.query;
    const {
      totalPrice,
      totalWeight,
      totalNum,
      heights,
      widths,
      lengths,
      pickingOrders,
      customsInfo,
      currencyCode,
      initData,
      userIds,
    } = this.state;
    const { userId } = this.state.formData;
    let arr = [];
    customsInfo.forEach((item, index) => {
      arr.push(
        `goodsNameCh${index}`,
        `goodsNameEn${index}`,
        `price${index}`,
        `productHsCode${index}`,
        `productLink${index}`,
        `productSku${index}`,
        `productImei${index}`,
        `productMaterial${index}`,
        `quantity${index}`,
        `weight${index}`,
        'importCustomsInfo.taxPolicyExtends.csp',
        'currencys',
        'userId',
        'orderNumber',
        'salesPlatform',
        'waybillNumber',
        'handoverCode',
        'transactionNumber',
        'companyCode',
        'channelId',
        'name',
        'company',
        'countryId',
        'phone',
        'state',
        'city',
        'email',
        'zipCode',
        'receiverTaxNumber',
        'address',
        'houseNumber',
        'senderTaxNumber',
        'pointId',
        'pinflNumber',
        'length',
        'pickingOrders',
        'width',
        'height',
        'datePicker',
        'isBattery',
        'IOSS'
      );
    });
    form.validateFieldsAndScroll(arr, (err, values) => {
      if (!err) {
        localStorage.removeItem('PAGE');

        let newArr = [];
        newArr = customsInfo.map((value, i) => {
          value.goodsNameCh = values[`goodsNameCh${i}`];
          value.goodsNameEn = values[`goodsNameEn${i}`];
          value.price = values[`price${i}`];
          value.hscode = values[`productHsCode${i}`];
          value.url = values[`productLink${i}`];
          value.imei = values[`productImei${i}`];
          value.material = values[`productMaterial${i}`];
          value.quantity = values[`quantity${i}`];
          value.weight = values[`weight${i}`];
          return value;
        });
        this.setState({
          btnLoading: true,
        });

        const IOSSOptionsData = this.basicInformationOfEJFRef.current?.getOptionsData();
        const senderTaxNumberOptionsData = this.ejfCommonComponentRef.current?.getOptionsData();
        let params = {
          channelId: values.channelId,
          orderNumber: values.orderNumber,
          transactionNumber: values.transactionNumber,
          salesPlatform: values.salesPlatform,
          waybillNumber: values.waybillNumber,
          handoverCode: values.handoverCode,
          importCustomsInfo: {
            taxPolicyExtends: {
              csp: values.importCustomsInfo.taxPolicyExtends.csp,
            },
          },
          companyCode: values.companyCode,
          remark: values.pickingOrders ? values.pickingOrders : undefined,
          dateOfReceipt: values.datePicker ? moment(values.datePicker).format('yyyy-MM-DD') : '',
          poPStation: {
            pointId: values.pointId,
          },
          receiverInfo: {
            address: values.address,
            city: values.city,
            company: values.company,
            countryId: values.countryId,
            email: values.email,
            houseNumber: values.houseNumber,
            name: values.name,
            phone: values.phone,
            state: values.state,
            taxNumber: values.receiverTaxNumber,
            zipCode: values.zipCode,
            pinflNumber: values.pinflNumber,
          }, // 收件人信息
          parcelInfo: {
            currency: currencyCode,
            totalPrice: totalPrice,
            totalQuantity: totalNum,
            totalWeight: totalWeight,
            height: values.height,
            width: values.width,
            length: values.length,
            productList: newArr,
            hasBattery: values.isBattery,
            ioss: IOSSOptionsData?.find(item => item.value == values.IOSS)?.taxId ?? values.IOSS,
          }, //货物信息
          senderInfo: {
            taxNumber:
              senderTaxNumberOptionsData?.find(item => item.value == values.senderTaxNumber)
                ?.taxId ?? values.senderTaxNumber,
          },
        };
        if (typeof userId == 'string') {
          params.userId = userId;
        } else {
          params.userId = userId[0];
        }
        if (userId != undefined && id != undefined && (text == 'add' || text == 'submitAdd')) {
          params.id = id;
          this.updateOrderEjfAndBusiness(params, text);
        } else {
          this.checkOrderNumber(params, () => {
            dispatch({
              type: 'Order/createOrders',
              payload: params,
              callback: res => {
                this.setState({
                  btnLoading: false,
                });
                if (res.success) {
                  message.success(res.message);
                  if (para == undefined) {
                    let result = new URLSearchParams(this.props.location.search);
                    let type = result.get('type');
                    if (type == 'add') {
                      sessionStorage.removeItem('copys');
                    }
                    this.setState({
                      isCreatedSuccess: true,
                      waybillNumber: res.data.waybillNumber,
                    });
                    // localStorage.removeItem(localStorage.getItem('Merchants'));
                    // localStorage.setItem('NODATA', 1);
                    // // 制单成功，跳转订单查询，删除缓存信息
                    // let miscellaneous = JSON.parse(localStorage.getItem('miscellaneous'));
                    // if (miscellaneous != null) {
                    //   miscellaneous.isRefreshList = true;
                    //   miscellaneous.tabNumber = '0';
                    //   localStorage.setItem('miscellaneous', JSON.stringify(miscellaneous));
                    // }
                    // let deliveryAccount = typeof userId == 'string' ? userId : userId[0];
                    // localStorage.setItem('deliveryAccount', deliveryAccount);
                    // // }
                    // this.props.history.push('/smallBag/orderManagement/orderList');
                  } else {
                    let paras = JSON.stringify({ ...initData, status });
                    sessionStorage.setItem('createOrderData', paras);
                    sessionStorage.setItem('newAwb', res.data.waybillNumber);
                    router.push(`/smallBag/anomalyOrder?showDispose=1`);
                  }
                }
                // else {
                //   message.error(res.message);
                // }
              },
            });
          });
        }
      } else {
        let newArr = [];
        let params = {};
        newArr = customsInfo.map((value, i) => {
          value.goodsNameCh = values[`goodsNameCh${i}`];
          value.goodsNameEn = values[`goodsNameEn${i}`];
          value.price = values[`price${i}`];
          value.productHsCode = values[`productHsCode${i}`];
          value.productLink = values[`productLink${i}`];
          value.productSku = values[`productSku${i}`];
          value.productImei = values[`productImei${i}`];
          value.productMaterial = values[`productMaterial${i}`];
          value.quantity = values[`quantity${i}`];
          value.weight = values[`weight${i}`];
          return value;
        });
      }
    });
  };

  // 提交商业制单前请求检测订单号是否已经存在
  checkBusinessNumber = (params, callback) => {
    const { dispatch } = this.props;
    dispatch({
      type: 'business/getExistBusinessDraftNumber',
      payload: { userId: params?.userId, listOrderNumber: [params?.orderNumber] },
      callback: response => {
        this.setState({
          btnLoading: false,
        });
        if (response.success) {
          if (callback) callback();
        } else {
          this.setState(
            {
              modalErrorContent: response?.message,
            },
            () => {
              this.checkOrderNumberCallback(
                {
                  file: response.data?.file,
                  base64: response.data?.base64,
                  fileName: response.data?.fileName,
                },
                callback
              );
            }
          );
        }
      },
    });
  };

  // 提交制单前请求检测订单号是否已经存在
  checkOrderNumber = (params, callback) => {
    const { dispatch } = this.props;
    dispatch({
      type: 'Order/whetherTheOrderNumberExists',
      payload: { userId: params?.userId, orderNumber: [params?.orderNumber] },
      callback: res => {
        this.setState({
          btnLoading: false,
        });
        if (res.success) {
          if (callback) callback();
        } else {
          this.setState(
            {
              modalErrorContent: res?.message,
            },
            () => {
              this.checkOrderNumberCallback(
                { file: res.data?.file, base64: res.data?.base64, fileName: res.data?.fileName },
                callback
              );
            }
          );
        }
      },
    });
  };

  // 处理选择完是或否后的操作
  checkOrderNumberCallback = (params, callback) => {
    const { dispatch } = this.props;
    const { selectTypeRadioValue } = this.state;
    // 1为是  0 为否
    if (selectTypeRadioValue !== undefined && selectTypeRadioValue == 1) {
      this.setState({
        selectTypeRadioValue: undefined,
      });
      // 执行回调函数里的方法
      if (callback) callback();
    } else if (selectTypeRadioValue !== undefined && selectTypeRadioValue == 0) {
      this.setState({
        selectTypeRadioValue: undefined,
      });
    } else {
      // 打开弹窗去选择是或否
      this.setState({
        isModalErrorVisible: true,
      });
      if (params?.file) {
        this.dataURLtoDownload(params?.base64, params?.fileName);
      }
    }
  };

  // 同一封装的修改订单方法
  editBusinessDraft = (params, callback) => {
    const { dispatch } = this.props;
    dispatch({
      type: 'business/editBusinessDraft',
      payload: params,
      callback: response => {
        if (response.success) {
          if (callback) callback();
        }
      },
    });
  };

  // 同一封装的创建订单方法
  createBusinessDraft = (params, callback) => {
    const { dispatch } = this.props;
    dispatch({
      type: 'business/createBusinessDraft',
      payload: params,
      callback: response => {
        if (response.success) {
          message.success(response.message, 0.3).then(() => {
            if (callback) callback();
          });
        }
      },
    });
  };

  // 用于小包专线和商业快递保存更新订单信息
  updateOrderEjfAndBusiness = (data, text) => {
    const { isBusiness } = this.state;
    if (!isBusiness) {
      this.updateOrder(data, text);
    } else {
      this.updateBusiness(text);
    }
  };

  // 保存更新草稿信息
  updateBusiness = text => {
    const { dispatch, form } = this.props;
    const { id } = this.props.history.location.query;
    form.validateFields(['userId'], err => {
      if (!err) {
        const value = form?.getFieldsValue();
        const params = {
          ...value,
          id,
          ids: [id],
          userId: typeof value?.userId === 'string' ? value?.userId : value?.userId?.[0],
          receiverInfo: {
            ...value?.receiverInfo,
            countryId: value?.countryId,
          },
        };
        if (text == 'add') {
          this.editBusinessDraft(params, () => {
            router.push('/smallBag/orderManagement/orderList?number=3');
          });
        } else {
          this.editBusinessDraft(params, () => {
            form.validateFields((err, values) => {
              if (err) return message.error('请填写完整所有必填项');
              // 调用订单生成运单号的接口
              let result = this.checkPackage(values);
              if (result) {
                return message.error(result);
              }
              this.checkBusinessNumber(params, () => {
                this.businessToWaybill(params);
              });
            });
          });
        }
      }
    });
  };

  checkPackage = values => {
    const { parcelInfo = [], productInfo = [] } = values;
    const parcelIndex = parcelInfo?.findIndex(
      item => !item?.length || !item?.height || !item?.width || !item?.weight
    );
    const produceIndex = productInfo?.findIndex(
      item => !item?.goodsNameCh || !item?.goodsNameEn || !item?.price || !item?.quantity
    );
    if (parcelIndex !== -1) {
      return `请填写包裹信息中第${parcelIndex + 1}行必填项！`;
    }
    if (produceIndex !== -1) {
      return `请填写寄运商品信息中第${produceIndex + 1}行必填项！`;
    }
    return null;
  };

  // 保存更新订单信息
  updateOrder = (data, text) => {
    const { dispatch } = this.props;
    dispatch({
      type: 'Order/updateOrder',
      payload: data,
      callback: result => {
        if (result.success) {
          if (text == 'add') {
            this.setState({
              btnLoading: false,
            });
            message.success(result.message, 0.3).then(() => {
              if (this.state.unscriptedEditing) {
                // 如果是未制单编辑点击保存
                let miscellaneous = JSON.parse(localStorage.getItem('miscellaneous'));
                if (miscellaneous != null) {
                  miscellaneous.tabNumber = '9';
                  miscellaneous.isRefreshList = true;
                  localStorage.setItem('miscellaneous', JSON.stringify(miscellaneous));
                }
                let deliveryAccount = typeof data.userId == 'string' ? data.userId : data.userId[0];
                localStorage.setItem('deliveryAccount', deliveryAccount);
              }
              this.props.history.push('/smallBag/orderManagement/orderList');
            });
          } else {
            this.checkOrderNumber(data, () => {
              let param = {
                userId: data.userId,
                ids: [data.id],
              };
              this.orderToWaybill(param);
            });
          }
        } else {
          // message.error(result.message);
          this.setState({
            btnLoading: false,
          });
        }
      },
    });
  };

  // 商业快递订单生成运单号
  businessToWaybill = data => {
    const { dispatch } = this.props;
    dispatch({
      type: 'business/businessDraftToOrder',
      payload: data,
      callback: response => {
        if (response.success) {
          this.setState({
            btnLoading: false,
            isCreatedSuccess: true,
            waybillNumber: response?.data[0]?.data?.waybillNumber,
          });
        }
      },
    });
  };

  // 订单生成运单号
  orderToWaybill = data => {
    const { dispatch } = this.props;
    dispatch({
      type: 'Order/submitVoucher',
      payload: data,
      callback: result => {
        if (result.success) {
          this.setState({
            btnLoading: false,
            isCreatedSuccess: true,
            waybillNumber: result.data[0].waybillNumber,
          });
          // message.success(result.message).then(() => {
          //   let miscellaneous = JSON.parse(localStorage.getItem('miscellaneous'));
          //   if (this.state.unscriptedEditing && miscellaneous != null) {
          //     miscellaneous.isRefreshList = true;
          //     miscellaneous.tabNumber = '0';
          //     localStorage.setItem('miscellaneous', JSON.stringify(miscellaneous));
          //   }
          //   this.props.history.push('/smallBag/orderManagement/orderList');
          // });
        } else {
          message.error(result.message);
          // .then(() => {
          //   let miscellaneous = JSON.parse(localStorage.getItem('miscellaneous'));
          //   if (this.state.unscriptedEditing && miscellaneous != null) {
          //     let miscellaneous = JSON.parse(localStorage.getItem('miscellaneous'));
          //     miscellaneous.isRefreshList = true;
          //     localStorage.setItem('miscellaneous', JSON.stringify(miscellaneous));
          //   }
          // });
          this.setState({
            btnLoading: false,
          });
        }
      },
    });
  };

  // 获取EJF导入模板
  getEJFGenericTemplates = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'Order/getEJFGenericTemplate',
      callback: result => {
        if (result.success) {
          this.setState({
            EJFtemplates: result.data,
          });
        }
      },
    });
  };

  // 点击模板进行下载
  clickDownload = id => {
    downloadFile({
      url: `/csc/ejf/draft/downloadEJFGenericTemplate/${id}`,
      method: 'POST',
    });
  };

  // 上传文档
  handleUpload = () => {
    const { userId } = this.state.formData;
    if (!userId) {
      message.error('请选择制单账号！');
      return;
    }
    const { fileList } = this.state;
    const formData = new FormData();
    fileList.forEach(file => {
      formData.append('files', file);
    });
    this.setState({
      uploading: true,
      isLoadingStart: true,
    });
    fetch(`/csc/ejf/draft/createOrder/${userId}`, {
      method: 'POST',
      body: formData,
    })
      .then(res => res.json())
      .then(res => {
        if (res.success) {
          this.setState({
            fileList: [],
          });
          message.success('导入成功');
          // .then(() => {
          //   // 制单成功，跳转订单查询，删除缓存信息
          //   let miscellaneous = JSON.parse(localStorage.getItem('miscellaneous'));
          //   let deliveryAccount = typeof userId == 'string' ? userId : userId[0];
          //   if (miscellaneous != null) {
          //     miscellaneous.isRefresh = true;
          //     miscellaneous.isRefreshList = false;
          //     miscellaneous.printingStatus = '';
          //     miscellaneous.tabNumber = '9';
          //     miscellaneous.date = ['', ''];
          //     localStorage.setItem('miscellaneous', JSON.stringify(miscellaneous));
          //   }
          //   localStorage.setItem('deliveryAccount', deliveryAccount);
          //   localStorage.removeItem('tableData');
          //   localStorage.removeItem('countriesData');
          //   localStorage.removeItem('productData');
          // });
          this.setState({
            isModalVisible: true,
            uploadErrorMsg: res.message,
            uploadErrorCon: '',
          });
        } else {
          if (res.code == 501) {
            message.error({
              content: res.message,
              style: {
                whiteSpace: 'pre-wrap',
              },
            });
          } else if (res.code == 502) {
            this.setState({
              isModalVisible: true,
              uploadErrorMsg:
                '成功条数：' +
                (res.successCount ?? '--') +
                ',失败条数：' +
                (res.failuresCount ?? '--'),
              uploadErrorCon: res.message,
            });
            this.dataURLtoDownload(res.errorMsg, res.fileName);
          } else {
            this.setState({
              isModalVisible: true,
              uploadErrorMsg:
                '成功条数：' +
                (res.successCount ?? '--') +
                ',失败条数：' +
                (res.failuresCount ?? '--'),
              uploadErrorCon: res.message,
            });
          }
        }
      })
      .catch(res => {
        message.error({
          content: res.message,
          style: {
            whiteSpace: 'pre-wrap',
          },
        });
      })
      .finally(() => {
        this.setState({
          uploading: false,
          isLoadingStart: false,
        });
      });
  };

  //base64解析下载 dataurl是后端返回的base64字符串，name是文件名
  dataURLtoDownload = (dataurl, name) => {
    let bstr = atob(dataurl), //解析 base-64 编码的字符串
      n = bstr.length,
      u8arr = new Uint8Array(n); //创建初始化为0的，包含length个元素的无符号整型数组
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n); //返回字符串第一个字符的 Unicode 编码
    }
    let blob = new Blob([u8arr]); //转化成blob
    let url = URL.createObjectURL(blob); //这个新的URL 对象表示指定的 File 对象或 Blob 对象
    let a = document.createElement('a'); //创建一个a标签
    a.href = url;
    a.download = name;
    a.click();
    URL.revokeObjectURL(a.href); //释放之前创建的url对象
  };

  // 打印标签
  clickPrintLabels = () => {
    const { dispatch } = this.props;
    const { userId } = this.state.formData;
    const { waybillNumber, isBusiness } = this.state;
    let params = {
      waybillNumbers: [waybillNumber],
      userId: typeof userId == 'string' ? userId : userId[0],
      printRemark: '0',
    };
    isBusiness
      ? dispatch({
          type: 'business/businessOrderPrintLabelBatch',
          payload: params,
          callback: res => {
            if (res.success) {
              message.success({
                content: res.message,
                style: {
                  whiteSpace: 'pre-wrap',
                },
              });
              downloadBase64File(res.data?.base64, res.data?.fileName);
            } else {
              if (res.data) {
                downloadBase64File(res.data?.base64, res.data?.fileName);
              }
            }
          },
        })
      : dispatch({
          type: 'Order/getPrintLabelData',
          payload: params,
          callback: res => {
            if (res.success) {
              message.success({
                content: res.message,
                style: {
                  whiteSpace: 'pre-wrap',
                },
              });
              this.dataURLtoDownload(res.data, res.fileName);
            } else {
              message.error({
                content: res.message,
                style: {
                  whiteSpace: 'pre-wrap',
                },
              });
            }
          },
        });
  };

  // 重置form表单值
  resetFieldsForm = () => {
    const { form } = this.props;
    const { isBusiness } = this.state;
    let objData = {
      userId: undefined,
      orderNumber: undefined,
      companyCode: undefined,
      waybillNumber: undefined,
      salesPlatform: undefined,
      handoverCode: undefined,
      importCustomsInfo: undefined,
      channelId: undefined,
      countryId: undefined,
      currencys: undefined,
      transactionNumber: undefined,
      IOSS: undefined,
      name: undefined,
      company: undefined,
      state: undefined,
      zipCode: undefined,
      city: undefined,
      houseNumber: undefined,
      phone: undefined,
      email: undefined,
      receiverTaxNumber: undefined,
      address: undefined,
      senderTaxNumber: undefined,
      pointId: undefined,
      pinflNumber: undefined,
      length: undefined,
      width: undefined,
      height: undefined,
      datePicker: undefined,
      pickingOrders: undefined,
    };
    form.setFieldsValue(objData);
    if (isBusiness) {
      form.resetFields();
    }
    this.setState(
      {
        customsInfo: [
          {
            goodsNameCh: undefined, // 中文名
            goodsNameEn: undefined, // 英文名
            quantity: undefined, // 单品数量
            weight: undefined, // 单品重量
            price: undefined, // 申报价格
            productMaterial: undefined, // 商品材质
            productHsCode: undefined, // 商品海关编码
            productLink: undefined, // 商品链接
            productSku: undefined, // sku
            productImei: undefined, // IMEI
          },
        ],
        totalNum: 0, // 申报的总数量
        totalWeight: 0, // 申报的总质量
        totalPrice: 0, // 申报的总价值
        isChangeAccount: true,
        countryDisabled: true,
      },
      () => {
        this.state.customsInfo.forEach((val, i) => {
          form.setFieldsValue({
            [`goodsNameCh${i}`]: val.goodsNameCh,
            [`goodsNameEn${i}`]: val.goodsNameEn,
            [`price${i}`]: val.price,
            [`productHsCode${i}`]: val.productHsCode,
            [`productLink${i}`]: val.productLink,
            [`productSku${i}`]: val.productSku,
            [`productImei${i}`]: val.productImei,
            [`productMaterial${i}`]: val.productMaterial,
            [`quantity${i}`]: val.quantity,
            [`weight${i}`]: val.weight,
          });
        });
      }
    );
  };

  handleChangeWarehouse = () => {
    this.setState(
      {
        channels: [],
      },
      () => {
        const { form } = this.props;
        form.setFieldsValue({
          channelId: undefined,
          countryId: undefined,
        });
      }
    );
  };

  render() {
    const {
      temuShow,
      open,
      tabNumber,
      value,
      currencys,
      transactionNumber,
      currencyCode,
      customsInfo,
      deliveryAccount,
      channels,
      totalNum,
      totalWeight,
      totalPrice,
      lengths,
      widths,
      heights,
      countriesData,
      senderTaxNumber,
      pointId,
      pinflNumber,
      receiverInfo,
      EJFtemplates,
      isHaveCountry,
      btnLoading,
      currentAuthorityValue,
      isHaveDeliveryAccount,
      pickingOrders,
      fileName,
      countryDisabled,
      IOSS,
      salesPlatform,
      handoverCode,
      handoverCodes,
      handoverChange,
      importCustomsInfo,
      isModalVisible,
      uploadErrorCon,
      uploadErrorMsg,
      userIds,
      isHaveNumber,
      isProduct,
      unscriptedEditing,
      isCreatedSuccess,
      waybillNumber,
      isChangeAccount,
      isLoadingStart,
      modalErrorContent,
      isModalErrorVisible,
      selectTypeRadioValue,
      warehouseList,
      moneyNum,
      moneyItem,
      countrySearchValue,
      commodityTypeList,
      packTypeList,
      batteryTypeList,
      taxTypeList,
      isBusiness,
      provinceList,
      cityList,
      districtList,
    } = this.state;

    const { orderNumber, channelId, userId, dateOfReceipt, companyCode } = this.state.formData;
    const { id } = this.props.history.location.query;
    // let dochannels = channels.filter(item => item.channelNameCh.indexOf('燕文') == -1); //indexOf('xxx')==-1 是不存在，不等于-1 是存在
    const {
      form: { getFieldDecorator },
      shipperInitOpt,
      chargeLoading,
      submitOrderLoading,
      submitDraftLoading,
      editDraftAndSubmitDraftLoading,
      editDraftLoading,
      printLabelLoading,
    } = this.props;
    doorplateLayout;
    const formItemLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
        md: { span: 6 },
        xxl: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
        md: { span: 16 },
        xxl: { span: 16 },
      },
    };
    const formItemLayoutes = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 3 },
        md: { span: 3 },
        xxl: { span: 2 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
        md: { span: 20 },
        xxl: { span: 20 },
      },
    };
    const formItemLayouts = {
      labelCol: { xs: { span: 24 }, sm: { span: 2 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 20 } },
    };
    const doorplateLayout = {
      labelCol: { xs: { span: 24 }, sm: { span: 4 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
    };
    const exportInfoLayout = {
      labelCol: { xs: { span: 24 }, sm: { span: 8 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 14 } },
    };
    const exportInfoLongLayout = {
      labelCol: { xs: { span: 24 }, sm: { span: 4 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
    };
    const packagingLayout = {
      wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
    };

    const actionsTextMap = {
      expandText: '展开',
      collapseText: '收起',
    };

    const account = deliveryAccount;
    let shipperOpen = account.length > 7 ? true : false;
    const { uploading, fileList } = this.state;
    const props = {
      onRemove: file => {
        this.setState(state => {
          const index = state.fileList.indexOf(file);
          const newFileList = state.fileList.slice();
          newFileList.splice(index, 1);
          return {
            fileList: newFileList,
          };
        });
      },
      beforeUpload: file => {
        // const type = file.type;
        let fileName = file.name.split('.');
        let fileTypeName = fileName[fileName.length - 1];
        // if (type != 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
        //   message.error('只能上传.xlsx格式文件');
        //   return;
        // }
        if (fileTypeName != 'xlsx') {
          message.error('只能上传.xlsx格式文件');
          return;
        }
        if (file.size > ********) {
          message.error('导入文件不能大于10M');
          return;
        }
        this.setState(state => ({
          fileList: [file],
          fileName: file.name,
        }));
        return false;
      },
      fileList,
    };

    return (
      <PageContainerComponent
        header={{
          title: null,
          breadcrumb: {},
          breadcrumbRender: props => (
            <PageHeaderBreadcrumb {...props} col>
              <>
                <h2
                  style={{
                    display: id == undefined ? '' : 'none',
                    fontSize: '20px',
                    marginBottom: 0,
                  }}
                >
                  <Button
                    style={{
                      float: 'right',
                      display: userIds || isHaveNumber || unscriptedEditing ? '' : 'none',
                    }}
                    onClick={() =>
                      isBusiness
                        ? router.push('/smallBag/orderManagement/orderList?number=3')
                        : router.push('/smallBag/orderManagement/orderList')
                    }
                  >
                    {formatMessage({ id: '返回' })}
                  </Button>
                </h2>
                <h2
                  style={{
                    display: id != undefined ? 'flex' : 'none',
                    fontSize: '20px',
                    justifyContent: 'flex-end',
                    marginBottom: 5,
                  }}
                >
                  <Button
                    style={{
                      display: userIds || isHaveNumber || unscriptedEditing ? '' : 'none',
                    }}
                    onClick={() =>
                      isBusiness
                        ? router.push('/smallBag/orderManagement/orderList?number=3')
                        : router.push('/smallBag/orderManagement/orderList')
                    }
                  >
                    {formatMessage({ id: '返回' })}
                  </Button>
                </h2>
                <Tabs
                  activeKey={tabNumber}
                  onChange={this.tabOnChange}
                  style={{
                    display: id != undefined ? 'none' : '',
                    borderRadius: '10px 10px 0 0',
                    marginBottom: '-24px',
                  }}
                >
                  <TabPane tab={`${formatMessage({ id: '单票制单' })}`} key={'1'}></TabPane>
                  <TabPane tab={`${formatMessage({ id: '批量制单' })}`} key={'2'}></TabPane>
                  {/* <TabPane tab={`API制单`} key={'3'}></TabPane> */}
                </Tabs>
              </>
            </PageHeaderBreadcrumb>
          ),
        }}
      >
        <div>
          <div style={{ padding: '1px 0 30px' }}>
            {/*单票创建*/}
            <div style={{ display: tabNumber === '1' ? '' : 'none' }} className="conten_singleDiv">
              <Form {...formItemLayout} className="ant-advanced-search-form">
                {/*  制单账号*/}
                <div
                  className="deliveryAccount"
                  style={{
                    padding: '10px 20px 0',
                    backgroundColor: '#fff',
                    borderRadius: '10px',
                    marginBottom: '10px',
                    display: 'flex',
                  }}
                >
                  <Row>
                    <StandardFormRow title={formatMessage({ id: '制单账号' })} block>
                      {/* <Col span={20}> */}
                      <Form.Item
                        style={{ marginRight: 70 }}
                        className="form_item_tagSelect"
                        labelCol={{ xs: { span: 24 }, sm: { span: 8 } }}
                        wrapperCol={{
                          xs: { span: 24 },
                          sm: { span: 24 },
                          ms: { span: 24 },
                          xxl: { span: 24 },
                        }}
                      >
                        {getFieldDecorator('userId', {
                          initialValue: userId,
                          rules: [
                            {
                              required: true,
                              message: formatMessage({ id: '请至少选择一个制单账号' }),
                            },
                          ],
                        })(
                          <SingleTagSelect
                            // actionsText={actionsTextMap}
                            className="open"
                            // expandable={shipperOpen}
                            onChange={this.shipperChange}
                            checked={isHaveDeliveryAccount}
                          >
                            {account &&
                              account.map((element, index) => (
                                <SingleTagSelect.Option key={index} value={element.accountCode}>
                                  {element.warehouseName ?? ''}
                                  {element.accountCode}
                                </SingleTagSelect.Option>
                              ))}
                          </SingleTagSelect>
                        )}
                      </Form.Item>
                      {/* </Col> */}
                    </StandardFormRow>
                  </Row>
                </div>
                {/*    运单信息*/}
                <div
                  style={{
                    padding: '10px 20px 0',
                    backgroundColor: '#fff',
                    borderRadius: '10px',
                    marginBottom: '10px',
                  }}
                >
                  <span
                    // onClick={e => e.preventDefault()}
                    style={{
                      fontSize: '16px',
                      display: 'block',
                      color: '#52C41A',
                      marginBottom: '10px',
                    }}
                  >
                    {formatMessage({ id: '基础信息' })}
                  </span>

                  <Row>
                    <Col span={8}>
                      <Form.Item label={formatMessage({ id: '交货仓' })} labelAlign="right">
                        {getFieldDecorator(`companyCode`, {
                          initialValue: companyCode,
                          rules: [
                            {
                              required: false,
                              message: formatMessage({ id: '请选择交货仓' }),
                            },
                          ],
                        })(
                          <Select
                            showSearch
                            allowClear
                            placeholder={formatMessage({ id: '请选择交货仓' })}
                            optionFilterProp="children"
                            onChange={this.handleChangeWarehouse}
                            // onChange={this.currencyChange}
                          >
                            {warehouseList &&
                              warehouseList.map((element, index) => (
                                <Select.Option key={index} value={element.code}>
                                  {element.name}
                                </Select.Option>
                              ))}
                          </Select>
                        )}
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label={formatMessage({ id: '订单号' })} labelAlign="right">
                        {getFieldDecorator('orderNumber', {
                          initialValue: orderNumber,
                          rules: [
                            {
                              required: true,
                              message: formatMessage({ id: '请输入订单号' }),
                            },
                            {
                              max: 50,
                              message: formatMessage({ id: '请输入50位以下的订单号' }),
                            },
                          ],
                        })(
                          <Input allowClear placeholder={formatMessage({ id: '请输入订单号' })} />
                        )}
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label={formatMessage({ id: '目的地' })} labelAlign="right">
                        {getFieldDecorator('countryId', {
                          initialValue: isHaveCountry ? receiverInfo.countryId : '',
                          rules: [
                            {
                              required: true,
                              message: formatMessage({ id: '请选择国家' }),
                            },
                          ],
                        })(
                          <Select
                            showSearch
                            placeholder={formatMessage({ id: '请选择国家' })}
                            onChange={this.contryChange}
                            optionFilterProp="children"
                            // filterOption={(input, option) =>
                            //   (option?.childre n ?? '').includes(input)
                            // }
                            filterSort={(optionA, optionB) => {
                              if (countrySearchValue) {
                                const a = optionA?.children?.split('/');
                                const b = optionB?.children?.split('/');
                                const aResult = findMatch(a, countrySearchValue);
                                const bResult = findMatch(b, countrySearchValue);
                                return bResult - aResult;
                              }
                            }}
                            onSearch={value => {
                              this.setState({
                                countrySearchValue: value,
                              });
                            }}
                          >
                            {countriesData &&
                              countriesData.map((element, index) => (
                                <Select.Option key={index} value={element.id}>
                                  {element.nameCh +
                                    '/' +
                                    element.code +
                                    '/' +
                                    element.nameEn +
                                    '/' +
                                    element.countryNamePinYin}
                                </Select.Option>
                              ))}
                          </Select>
                        )}
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label={formatMessage({ id: '产品名称' })} labelAlign="right">
                        {getFieldDecorator('channelId', {
                          initialValue: isProduct ? channelId : '',
                          rules: [
                            {
                              required: true,
                              message: formatMessage({ id: '请选择产品' }),
                            },
                          ],
                        })(
                          <PopoverTabs
                            form={this.props.form}
                            data={channels}
                            onSelectOnChange={this.selectChange}
                            selectOnFocus={this.selectOnfocus}
                          />
                        )}
                      </Form.Item>
                    </Col>
                    {/*{temuShow && (*/}
                    {/*  <>*/}
                    {/*    <Col span={8}>*/}
                    {/*      <Form.Item label={formatMessage({ id: '运单号' })} labelAlign="right">*/}
                    {/*        {getFieldDecorator('waybillNumber', {*/}
                    {/*          initialValue: waybillNumber,*/}
                    {/*          rules: [*/}
                    {/*            {*/}
                    {/*              max: 50,*/}
                    {/*              message: formatMessage({ id: '请输入50位以下的运单号' }),*/}
                    {/*            },*/}
                    {/*          ],*/}
                    {/*        })(*/}
                    {/*          <Input*/}
                    {/*            allowClear*/}
                    {/*            placeholder={formatMessage({ id: '请输入运单号' })}*/}
                    {/*          />*/}
                    {/*        )}*/}
                    {/*      </Form.Item>*/}
                    {/*    </Col>*/}
                    {/*    <Col span={8}>*/}
                    {/*      <Form.Item label={formatMessage({ id: '海外交货地' })} labelAlign="right">*/}
                    {/*        {getFieldDecorator('handoverCode', {*/}
                    {/*          initialValue: handoverCode,*/}
                    {/*        })(*/}
                    {/*          <Select*/}
                    {/*            showSearch*/}
                    {/*            allowClear*/}
                    {/*            placeholder={formatMessage({ id: '请选择海外交货地' })}*/}
                    {/*            optionFilterProp="children"*/}
                    {/*            onChange={handoverChange}*/}
                    {/*          >*/}
                    {/*            {handoverCodes &&*/}
                    {/*              handoverCodes.map((element, index) => (*/}
                    {/*                <Select.Option key={index} value={element.code}>*/}
                    {/*                  {element.label}*/}
                    {/*                </Select.Option>*/}
                    {/*              ))}*/}
                    {/*          </Select>*/}
                    {/*        )}*/}
                    {/*      </Form.Item>*/}
                    {/*    </Col>*/}
                    {/*  </>*/}
                    {/*)}*/}
                    {isBusiness ? (
                      <CommercialExpressBasicInformation
                        {...this.props}
                        commodityTypeList={commodityTypeList}
                        packTypeList={packTypeList}
                        batteryTypeList={batteryTypeList}
                        taxTypeList={taxTypeList}
                        currencys={currencys}
                      />
                    ) : (
                      <BasicInformationOfEJF
                        {...this.props}
                        basicInformationOfEJFRef={this.basicInformationOfEJFRef}
                        orderNumber={orderNumber}
                        currencyCode={currencyCode}
                        currencys={currencys}
                        transactionNumber={transactionNumber}
                        value={value}
                        IOSS={IOSS}
                        salesPlatform={salesPlatform}
                        currencyChange={this.currencyChange}
                        isElectricityChange={this.isElectricityChange}
                      />
                    )}
                  </Row>
                </div>
                {isBusiness ? (
                  <CommercialExpressCommonComponent
                    {...this.props}
                    commodityTypeList={commodityTypeList}
                    packTypeList={packTypeList}
                    batteryTypeList={batteryTypeList}
                    taxTypeList={taxTypeList}
                    provinceList={provinceList}
                    cityList={cityList}
                    districtList={districtList}
                    getCountryNameList={this.getCountryNameList}
                  />
                ) : (
                  <EjfCommonComponent
                    {...this.props}
                    ejfCommonComponentRef={this.ejfCommonComponentRef}
                    receiverInfo={receiverInfo}
                    senderTaxNumber={senderTaxNumber}
                    pointId={pointId}
                    customsInfo={customsInfo}
                    totalNum={totalNum}
                    totalWeight={totalWeight}
                    currencyCode={currencyCode}
                    totalPrice={totalPrice}
                    channelId={channelId}
                    lengths={lengths}
                    widths={widths}
                    heights={heights}
                    importCustomsInfo={importCustomsInfo}
                    dateOfReceipt={dateOfReceipt}
                    pickingOrders={pickingOrders}
                    timeChange={this.timeChange}
                    addCustomsInfo={this.addCustomsInfo}
                    changeNum={this.changeNum}
                    changeWeight={this.changeWeight}
                    changePrice={this.changePrice}
                    delCustomsInfo={this.delCustomsInfo}
                  />
                )}

                <div className="w-full text-center bg-white rounded-xl px-3 py-5">
                  <Space>
                    {id == undefined && (
                      <Button
                        type="primary"
                        disabled={btnLoading}
                        loading={submitOrderLoading}
                        onClick={this.submitAction}
                        htmlType="submit"
                      >
                        {formatMessage({ id: '提交制单' })}
                      </Button>
                    )}
                    {id != undefined && (
                      <Button
                        type="primary"
                        disabled={btnLoading}
                        loading={editDraftAndSubmitDraftLoading}
                        onClick={() => this.submitAction('submitAdd')}
                        htmlType="submit"
                      >
                        {formatMessage({ id: '提交制单' })}
                      </Button>
                    )}

                    {id != undefined && (
                      <Button
                        type="primary"
                        disabled={btnLoading}
                        loading={editDraftLoading}
                        onClick={() => this.submitAction('add')}
                      >
                        {formatMessage({ id: '保存' })}
                      </Button>
                    )}
                    {id == undefined && (
                      <Button
                        type="primary"
                        disabled={btnLoading}
                        loading={submitDraftLoading}
                        onClick={this.clickStagEJFAndBusiness}
                      >
                        {formatMessage({ id: '暂存' })}
                      </Button>
                    )}
                    {hongKongDHLProductCode?.includes(channelId) && (
                      <Button type="primary" onClick={hongKongDHLOpenWindow}>
                        下载发票
                      </Button>
                    )}
                    <AuthStatusNode authKey="bill:freightTrial">
                      <Button
                        disabled={btnLoading}
                        loading={chargeLoading}
                        onClick={this.chargePriceSimple}
                      >
                        {formatMessage({ id: '金额试算' })}
                      </Button>
                    </AuthStatusNode>
                    {id != undefined && (
                      <Button
                        onClick={() =>
                          isBusiness
                            ? router.push('/smallBag/orderManagement/orderList?number=3')
                            : router.push('/smallBag/orderManagement/orderList')
                        }
                      >
                        {formatMessage({ id: '返回' })}
                      </Button>
                    )}
                    <AuthStatusNode authKey="bill:freightTrial">
                      <span>
                        {formatMessage({ id: '预估费用总额' })}
                        {isBusiness && (
                          <Popover
                            content={
                              <div style={{ whiteSpace: 'pre-wrap' }}>
                                {moneyItem
                                  ?.map(x => x.itemName + ': ' + x.cnyMoney + ' RMB')
                                  .join('\n')}
                              </div>
                            }
                          >
                            <span
                              style={{ color: '#52c41a', fontSize: '25px', paddingInline: '8px' }}
                            >
                              {moneyNum ? moneyNum : '--'}
                            </span>
                          </Popover>
                        )}
                        {!isBusiness && (
                          <span
                            style={{ color: '#52c41a', fontSize: '25px', paddingInline: '8px' }}
                          >
                            {moneyNum ? moneyNum : '--'}
                          </span>
                        )}
                        RMB
                      </span>
                    </AuthStatusNode>
                  </Space>
                </div>
              </Form>
            </div>
            {/*批量创建*/}
            <div style={{ display: tabNumber === '2' ? '' : 'none' }}>
              <BatchCreation
                {...this.props}
                tabNumber={tabNumber}
                countryDisabled={countryDisabled}
                userId={userId}
                isHaveDeliveryAccount={isHaveDeliveryAccount}
                account={account}
                shipperOpen={shipperOpen}
                EJFtemplates={EJFtemplates}
                shipperChange={this.shipperChange}
                dataURLtoDownload={this.dataURLtoDownload}
              />
            </div>

            {/* 单票创建成功弹框 */}
            <Modal
              title={formatMessage({ id: '制单成功' })}
              visible={isCreatedSuccess}
              // onOk={() => {
              //   router.push('/smallBag/orderManagement/orderList');
              // }}
              onCancel={() => {
                router.push(`/smallBag/orderManagement/creatOrder`);
                window.location.reload();
                // this.setState({ isCreatedSuccess: false });
                // this.resetFieldsForm()
              }}
              footer={[
                <Button
                  key="submit"
                  type="primary"
                  loading={printLabelLoading}
                  onClick={() => this.clickPrintLabels()}
                >
                  {formatMessage({ id: '打印标签' })}
                </Button>,
                <Button
                  key="submit"
                  onClick={() => {
                    router.push(
                      isBusiness
                        ? `/smallBag/orderManagement/businessDetails?userId=${userId}&expressCode=${waybillNumber}`
                        : `/smallBag/orderManagement/orderDetails?userId=${userId}&expressCode=${waybillNumber}`
                    );
                  }}
                >
                  {formatMessage({ id: '查看详情' })}
                </Button>,
                <Button
                  key="back"
                  onClick={() => {
                    router.push(`/smallBag/orderManagement/creatOrder`);
                    window.location.reload();
                    // this.setState({ isCreatedSuccess: false });
                    // this.resetFieldsForm()
                  }}
                >
                  {formatMessage({ id: '继续下单' })}
                </Button>,
              ]}
            >
              <p>
                {formatMessage({ id: '您的订单提交成功' })}，
                {formatMessage({ id: '您可在订单查询页面查看详情并打印标签' })}，
                {formatMessage({ id: '打印标签后即可发货至燕文' })}
              </p>
            </Modal>

            {/* 单票创建具体产品提示 */}
            {/* 检测订单号是否已经存在 */}
            <Modal
              title={formatMessage({ id: '提示' })}
              visible={isModalErrorVisible}
              closable={false}
              footer={[
                <Button
                  key="submit"
                  type="primary"
                  onClick={() => {
                    if (selectTypeRadioValue === undefined)
                      return message.error('请选择是否继续制单！');
                    this.setState(
                      {
                        isModalErrorVisible: false,
                      },
                      () => {
                        if (selectTypeRadioValue == 1) {
                          this.submitAction(id == undefined ? undefined : 'submitAdd');
                        } else {
                          this.setState({
                            selectTypeRadioValue: undefined,
                          });
                        }
                      }
                    );
                  }}
                >
                  {formatMessage({ id: '确定' })}
                </Button>,
              ]}
            >
              <p>{modalErrorContent}</p>
              <Radio.Group
                value={selectTypeRadioValue}
                onChange={e => {
                  this.setState({
                    selectTypeRadioValue: e.target.value,
                  });
                }}
              >
                <Radio value={1}>{formatMessage({ id: '是' })}</Radio>
                <Radio value={2}>{formatMessage({ id: '否' })}</Radio>
              </Radio.Group>
            </Modal>
            {/* 检测订单号是否已经存在 */}
          </div>
        </div>
        <Modal
          title={formatMessage({ id: '请选择制单账号' })}
          open={open}
          footer={null}
          className="conten_batchDiv"
          onCancel={() => this.setState({ open: false })}
        >
          <SingleTagSelect onChange={this.freightTrial}>
            {account &&
              account.map((element, index) => (
                <SingleTagSelect.Option key={index} value={element.accountCode}>
                  {element.accountCode}
                </SingleTagSelect.Option>
              ))}
          </SingleTagSelect>
        </Modal>
      </PageContainerComponent>
    );
  }
}

const mapStateToProps = ({ exportNamespace, resource, loading, state }) => ({
  state,
  resource,
  loading,
  exportNamespace,
});
export default Form.create({
  onValuesChange: (props, changeValues, allValues) => {
    const {
      form: { setFieldsValue, setFields },
    } = props;
    // 从 allValues 中提取 customsInfo
    const customsInfo = allValues.customsInfo;
    // 定义需要相加的字段
    const fieldsToSum = ['totalProductPrice', 'insurance', 'otherFee', 'packFee'];

    // 调用计算总和的函数，传入字段数组
    const totalSumNumber = calculateTotalSum(customsInfo, fieldsToSum);
    if (totalSumNumber != 0) {
      setFields({
        'customsInfo.price': {
          value: totalSumNumber,
          // errors: [],
        },
      });
    } else {
      setFields({
        'customsInfo.price': {
          value: 0,
          // errors: [],
        },
      });
    }
    // 你可以在这里继续使用 totalSumNumber
  },
})(connect(mapStateToProps)(Index));
