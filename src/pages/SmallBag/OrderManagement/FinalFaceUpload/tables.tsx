import '@/pages/index.less';
import { ProCard, ProColumnType, ProFormInstance, ProTable } from '@ant-design/pro-components';
import { useUpdateEffect } from 'ahooks';
import { Button, Space, TablePaginationConfig, Tabs, message, Popconfirm, Modal } from 'antd';
import { useState, useRef } from 'react';
import React from 'react';
import Detail from './detail';
import SingleUpload from './singleUpload';
import { downloadBase64File, openPreviewModal } from '@/utils/utils';
import BatchUpload from './batchUpload';
import moment from 'moment';

interface IProps {
  pagination: TablePaginationConfig;
  data: any[];
  tabActiveKey: string;
  setTabActiveKey: (key: string) => void;
  resetPageSize: () => void;
  dispatch: any;
  formRef: React.MutableRefObject<ProFormInstance | undefined>;
  [key: string]: any;
}

const TablesComponents = ({
  pagination,
  data,
  tabActiveKey,
  setTabActiveKey,
  resetPageSize,
  dispatch,
  formRef,
  tableLoading,
  deleteLoading,
  fetchData,
}: IProps) => {
  const modalRef = useRef<{ open: (record) => void }>();
  const singleUploadRef = useRef<{ openModal: (record) => void }>();
  const batchUploadRef = useRef<{ openModal: () => void }>();
  const [dataSource, setDataSource] = useState<Array<any>>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  useUpdateEffect(() => {
    setDataSource(data);
  }, [data]);

  const deleteLabel = nums => {
    const customerCode = formRef.current?.getFieldValue('customerCode');
    dispatch({
      type: 'smallBag/deleteLabel',
      payload: { list: nums, customerCode: customerCode },
      callback: result => {
        if (result.success) {
          message.success('操作成功');
          setSelectedRowKeys([]);
          fetchData();
        }
      },
    });
  };

  const handlePreview = record => {
    const customerCode = formRef.current?.getFieldValue('customerCode');
    dispatch({
      type: 'smallBag/getOrderLabelDetail',
      payload: {
        customerCode,
        waybillNumber: record?.waybillNumber,
      },
      callback: response => {
        if (response.success) {
          openPreviewModal({
            url: response?.data,
          });
        }
      },
    });
    // openPreviewModal({
    //   url: record?.previewUrl,
    //   contractName: record?.contractName,
    // })
  };

  const columns: ProColumnType<any>[] = [
    {
      title: '订单号',
      dataIndex: 'orderNumber',
    },
    {
      title: '运单号',
      dataIndex: 'waybillNumber',
      // render: (text, record) => {
      //   return (
      //     <a
      //       onClick={() => {
      //         const customerCode = formRef.current?.getFieldValue('customerCode');
      //         modalRef.current?.open({ ...record, customerCode });
      //       }}
      //     >
      //       {text}
      //     </a>
      //   );
      // },
    },
    {
      title: '尾程单号',
      dataIndex: 'trackingNumber',
    },
    {
      title: '产品名称',
      dataIndex: 'channelName',
    },
    {
      title: '目的国家',
      dataIndex: 'receiverCountryName',
    },
    {
      title: '收件人姓名',
      dataIndex: 'receiverName',
    },
    {
      title: '制单时间',
      dataIndex: 'createTime',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      render: (text, record) => {
        return (
          <Space>
            {tabActiveKey === '1' && (
              <a onClick={() => singleUploadRef.current?.openModal(record)}>上传</a>
            )}
            {tabActiveKey === '2' && (
              <Space>
                <a onClick={() => handlePreview(record)}>预览</a>
                {/*<a onClick={() => deleteLabel()}>删除</a>*/}
                <Popconfirm
                  title="确定删除?"
                  onConfirm={() => deleteLabel([record.waybillNumber])}
                  okText="是"
                  cancelText="否"
                >
                  {<a>删除</a>}
                </Popconfirm>
              </Space>
            )}
          </Space>
        );
      }, // 根据要求，暂时返回 null
    },
  ];

  const tabItem = [
    {
      key: '1',
      label: '待上传',
      // children: 'Content of Tab Pane 1',
    },
    {
      key: '2',
      label: '已上传',
      // children: 'Content of Tab Pane 2',
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(selectedRowKeys);
    },
  };

  const tableAlertRender = ({
    selectedRowKeys,
    selectedRows,
    onCleanSelected,
  }: {
    selectedRowKeys: React.Key[];
    selectedRows: typeof data | Array<any>;
    onCleanSelected: () => void;
  }) => {
    // console.log(selectedRowKeys, selectedRows);
    return (
      <Space size={24}>
        <span>已选 {selectedRowKeys.length} 项</span>
        <span>{`容器数量: ${selectedRows.reduce(
          (pre, item) => pre + item.containers,
          0
        )} 个`}</span>
        <span>{`调用量: ${selectedRows.reduce((pre, item) => pre + item.callNumber, 0)} 次`}</span>
        <a className="mt-2" onClick={onCleanSelected}>
          清空
        </a>
      </Space>
    );
  };

  const handleOptions = () => {
    if (selectedRowKeys.length === 0) {
      message.error('请至少选择一个单号');
      return;
    }
    const customerCode = formRef.current?.getFieldValue('customerCode');
    const dateTime = formRef.current?.getFieldValue('dateTime');
    dispatch({
      type: 'smallBag/exportLabelTemplate',
      payload: {
        customerCode,
        waybillNumberArray: selectedRowKeys,
        startTime:
          typeof dateTime?.[0] === 'string'
            ? dateTime?.[0]
            : dateTime?.[0]?.format('YYYY-MM-DD 00:00:00') ?? undefined,
        endTime:
          typeof dateTime?.[1] === 'string'
            ? dateTime?.[1]
            : dateTime?.[1]?.format('YYYY-MM-DD 23:59:59') ?? undefined,
      },
      callback: response => {
        if (response.success) {
          downloadBase64File(response?.data?.url, response?.data?.fileName);
          message.success('导出成功');
        }
        setSelectedRowKeys([]);
      },
    });
  };

  const handleDeleteOptions = () => {
    if (selectedRowKeys.length === 0) {
      message.error('请至少选择一个单号');
      return;
    }
    if (selectedRowKeys.length > 200) {
      message.error('单次删除最大支持200条');
      return;
    }
    // 确认对话框
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除吗？',
      okText: '确定',
      cancelText: '取消',
      onOk() {
        deleteLabel(selectedRowKeys);
      },
      onCancel() {},
    });
  };

  return (
    <ProCard ghost gutter={[0, 10]}>
      <ProCard
        bordered
        headStyle={{
          paddingTop: 6,
        }}
        bodyStyle={{
          paddingTop: 0,
        }}
        title={
          <Tabs
            className="ant-tab-no-bottom-border"
            activeKey={tabActiveKey}
            items={tabItem}
            onChange={key => {
              setTabActiveKey(key);
              resetPageSize();
            }}
          />
        }
        extra={
          tabActiveKey === '1' ? (
            <Space>
              <Button onClick={() => handleOptions()} type="primary">
                导出上传单号模版
              </Button>
              <Button onClick={() => batchUploadRef.current?.openModal()} type="primary">
                批量上传
              </Button>
            </Space>
          ) : (
            <Button onClick={() => handleDeleteOptions()} type="primary" loading={deleteLoading}>
              批量删除
            </Button>
          )
        }
      >
        <ProTable
          rowKey="waybillNumber"
          search={false}
          rowSelection={rowSelection}
          toolBarRender={false}
          dataSource={dataSource}
          columns={columns}
          pagination={pagination}
          loading={tableLoading}
          rowClassName={record =>
            moment(record?.createTime)?.add(6, 'days') < moment() && tabActiveKey === '1'
              ? 'text-red-500'
              : ''
          }
          // tableAlertRender={tableAlertRender}
          // tableAlertOptionRender={() => {
          //   return (
          //     <Space size={16}>
          //       <a onClick={handleOptions}>批量取消</a>
          //     </Space>
          //   );
          // }}
        />
      </ProCard>
      <Detail modalRef={modalRef} dispatch={dispatch} />
      <SingleUpload modalRef={singleUploadRef} dispatch={dispatch} fetchData={fetchData} />
      <BatchUpload modalRef={batchUploadRef} dispatch={dispatch} fetchData={fetchData} />
    </ProCard>
  );
};

export default TablesComponents;
