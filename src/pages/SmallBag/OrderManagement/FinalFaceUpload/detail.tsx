import React, { useState, useImperativeHandle } from 'react';
import { Drawer, Card, Descriptions, Divider, Button } from 'antd';
import { DetailInfo } from './types';
import ProTableList from '@/components/ProTable';
import { CloseOutlined } from '@ant-design/icons';

const Detail = ({ dispatch, modalRef }) => {
  const [open, setOpen] = useState(false);
  const [params, setParams] = useState<Partial<DetailInfo>>({});

  useImperativeHandle(modalRef, () => ({
    open: record => {
      fetchData(record);
    },
  }));

  const fetchData = record => {
    dispatch({
      type: 'overseas/getOverseaDetail',
      payload: {
        waybillNumber: record?.waybillNumber,
        userId: record?.userId,
      },
      callback: response => {
        if (response.success) {
          setParams({
            ...params,
            ...record,
            ...response?.data,
          });
          setOpen(true);
        }
      },
    });
  };

  const reset = () => {
    setOpen(false);
    setParams({});
  };

  const columns = [
    {
      title: '中文品名',
      dataIndex: 'goodsNameCh',
      key: 'goodsNameCh',
    },
    {
      title: '英文品名',
      dataIndex: 'goodsNameEn',
      key: 'goodsNameEn',
    },
    {
      title: '商品sku',
      dataIndex: 'sku',
      key: 'sku',
    },
    {
      title: '申报单价',
      dataIndex: 'price',
      key: 'price',
    },
    {
      title: '申报数量',
      dataIndex: 'quantity',
      key: 'quantity',
    },
    {
      title: '单票重量(G)',
      dataIndex: 'weight',
      key: 'weight',
    },
    {
      title: '商品海关编码',
      dataIndex: 'hsCode',
      key: 'hsCode',
    },
    {
      title: '商品链接',
      dataIndex: 'productUrl',
      key: 'productUrl',
    },
    {
      title: '商品材质',
      dataIndex: 'material',
      key: 'material',
    },
  ];

  const statusMap = {
    0: '已制单',
    1: '已确认发货',
    2: '已收货',
    3: '已发货',
    4: '已妥投',
    5: '已取消',
    6: '已截留',
    7: '派送异常',
    8: '退件/异常',
  };

  return (
    <Drawer
      title="详情"
      width="50%"
      open={open}
      onClose={reset}
      closable={false}
      extra={<Button type="text" icon={<CloseOutlined />} onClick={reset} />}
      footer={null}
    >
      <Card>
        <Descriptions
          title={<span style={{ fontSize: '16px', color: '#52C41A' }}>基础信息</span>}
          column={3}
        >
          <Descriptions.Item label="业务账号">{params?.userId}</Descriptions.Item>
          <Descriptions.Item label="运单号">{params?.waybillNumber}</Descriptions.Item>
          <Descriptions.Item label="订单号">{params?.orderNumber}</Descriptions.Item>
          <Descriptions.Item label="产品名称">{params?.channelName}</Descriptions.Item>
          <Descriptions.Item label="包裹送到carrier的仓库名">
            {params?.inductionFacility}
          </Descriptions.Item>
          <Descriptions.Item label="订单来源">{params?.orderSource}</Descriptions.Item>
          <Descriptions.Item label="运单状态">{statusMap[params?.status] ?? ''}</Descriptions.Item>
          <Descriptions.Item label="是否打印">
            {params?.isPrint == 1 ? '是' : params?.isPrint == 0 ? '否' : ''}
          </Descriptions.Item>
          <Descriptions.Item label="下单时间">{params?.createTime}</Descriptions.Item>
        </Descriptions>
        <Divider />
        <Descriptions
          title={<span style={{ fontSize: '16px', color: '#52C41A' }}>收件人信息</span>}
          column={3}
        >
          <Descriptions.Item label="收件人姓名">{params?.receiverInfo?.name}</Descriptions.Item>
          <Descriptions.Item label="收件人公司">{params?.receiverInfo?.company}</Descriptions.Item>
          <Descriptions.Item label="收件人电话">{params?.receiverInfo?.phone}</Descriptions.Item>
          <Descriptions.Item label="收件人手机">{params?.receiverInfo?.mobile}</Descriptions.Item>
          <Descriptions.Item label="收件人邮箱">{params?.receiverInfo?.email}</Descriptions.Item>
          <Descriptions.Item label="收件人国家">
            {params?.receiverInfo?.countryName}
          </Descriptions.Item>
          <Descriptions.Item label="收件人州(省)">{params?.receiverInfo?.state}</Descriptions.Item>
          <Descriptions.Item label="收件人城市">{params?.receiverInfo?.city}</Descriptions.Item>
          <Descriptions.Item label="邮编">{params?.receiverInfo?.zipCode}</Descriptions.Item>
          <Descriptions.Item label="收件人区">{params?.receiverInfo?.district}</Descriptions.Item>
          <Descriptions.Item label="收件人税号">
            {params?.receiverInfo?.taxNumber}
          </Descriptions.Item>
          <Descriptions.Item label="收件人地址1">
            {params?.receiverInfo?.address1}
          </Descriptions.Item>
          <Descriptions.Item label="收件人地址2">
            {params?.receiverInfo?.address2}
          </Descriptions.Item>
        </Descriptions>
        <Divider />
        <Descriptions
          title={<span style={{ fontSize: '16px', color: '#52C41A' }}>发件人信息</span>}
          column={3}
        >
          <Descriptions.Item label="发件人姓名">{params?.senderInfo?.name}</Descriptions.Item>
          <Descriptions.Item label="发件人电话">{params?.senderInfo?.phone}</Descriptions.Item>
          <Descriptions.Item label="发件人手机">{params?.senderInfo?.mobile}</Descriptions.Item>
          <Descriptions.Item label="发件人公司">{params?.senderInfo?.company}</Descriptions.Item>
          <Descriptions.Item label="发件人邮箱">{params?.senderInfo?.email}</Descriptions.Item>
          <Descriptions.Item label="发件人国家">
            {params?.senderInfo?.countryName}
          </Descriptions.Item>
          <Descriptions.Item label="发件人州(省)">{params?.senderInfo?.state}</Descriptions.Item>
          <Descriptions.Item label="发件人城市">{params?.senderInfo?.city}</Descriptions.Item>
          <Descriptions.Item label="发件人区">{params?.senderInfo?.district}</Descriptions.Item>
          <Descriptions.Item label="发件人门牌号">
            {params?.senderInfo?.houseNumber}
          </Descriptions.Item>
          <Descriptions.Item label="发件人邮编">{params?.senderInfo?.zipCode}</Descriptions.Item>
          <Descriptions.Item label="发件人地址1">{params?.senderInfo?.address1}</Descriptions.Item>
          <Descriptions.Item label="发件人地址2">{params?.senderInfo?.address2}</Descriptions.Item>
        </Descriptions>
        <Divider />
        {/*<Descriptions*/}
        {/*  title={<span style={{ fontSize: '16px', color: '#52C41A' }}>退件人信息</span>}*/}
        {/*  column={3}*/}
        {/*>*/}
        {/*  <Descriptions.Item label="退件人姓名">{params?.returnInfo?.name}</Descriptions.Item>*/}
        {/*  <Descriptions.Item label="退件人电话">{params?.returnInfo?.phone}</Descriptions.Item>*/}
        {/*  <Descriptions.Item label="退件人手机">{params?.returnInfo?.mobile}</Descriptions.Item>*/}
        {/*  <Descriptions.Item label="退件人公司">{params?.returnInfo?.company}</Descriptions.Item>*/}
        {/*  <Descriptions.Item label="退件人邮箱">{params?.returnInfo?.email}</Descriptions.Item>*/}
        {/*  <Descriptions.Item label="退件人国家">*/}
        {/*    {params?.returnInfo?.countryName}*/}
        {/*  </Descriptions.Item>*/}
        {/*  <Descriptions.Item label="退件人州(省)">{params?.returnInfo?.state}</Descriptions.Item>*/}
        {/*  <Descriptions.Item label="退件人城市">{params?.returnInfo?.city}</Descriptions.Item>*/}
        {/*  <Descriptions.Item label="退件人区">{params?.returnInfo?.district}</Descriptions.Item>*/}
        {/*  <Descriptions.Item label="退件人门牌号">*/}
        {/*    {params?.returnInfo?.houseNumber}*/}
        {/*  </Descriptions.Item>*/}
        {/*  <Descriptions.Item label="退件人邮编">{params?.returnInfo?.zipCode}</Descriptions.Item>*/}
        {/*  <Descriptions.Item label="退件人地址1">{params?.returnInfo?.address1}</Descriptions.Item>*/}
        {/*  <Descriptions.Item label="退件人地址2">{params?.returnInfo?.address2}</Descriptions.Item>*/}
        {/*</Descriptions>*/}
        <Divider />
        <Descriptions
          title={<span style={{ fontSize: '16px', color: '#52C41A' }}>包裹信息</span>}
          column={3}
        >
          <Descriptions.Item label="包裹总重量">
            {params?.parcelInfo?.totalWeight}
            {params?.parcelInfo?.weightUnit}
          </Descriptions.Item>
          <Descriptions.Item label="总申报价值">
            {params?.parcelInfo?.totalDeclareValue}
          </Descriptions.Item>
          <Descriptions.Item label="申报币种">
            {params?.parcelInfo?.declareCurrency}
          </Descriptions.Item>
          <Descriptions.Item label="包裹高度">{params?.parcelInfo?.height}</Descriptions.Item>
          <Descriptions.Item label="包裹宽度">{params?.parcelInfo?.width}</Descriptions.Item>
          <Descriptions.Item label="包裹长度">{params?.parcelInfo?.length}</Descriptions.Item>
          <Descriptions.Item label="是否含电">
            {params?.parcelInfo?.hasBattery === '1' ? '是' : '否'}
          </Descriptions.Item>
          <Descriptions.Item label="商品数量总和">
            {params?.parcelInfo?.totalQuantity}
          </Descriptions.Item>
        </Descriptions>
        <ProTableList
          columns={columns}
          dataSource={params?.parcelInfo?.productList}
          pagination={false}
          rowSelection={false}
          rowKey={(record, index) => index}
          search={false}
          toolBarRender={false}
        />
      </Card>
    </Drawer>
  );
};

export default Detail;
