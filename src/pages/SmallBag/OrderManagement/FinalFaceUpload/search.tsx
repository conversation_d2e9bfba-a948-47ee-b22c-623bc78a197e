import '@/pages/index.less';
import {
  ProCard,
  ProForm,
  ProFormDateRangePicker,
  ProFormRadio,
  ProFormSelect,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Button, Col, Row, Space } from 'antd';
import React from 'react';
import moment from 'moment';
const SearchComponent = ({
  formRef,
  accountData,
  onSearch,
  buttonLoading,
  dateTime,
  productList,
}: Common.SearchIProps<any>) => {
  const handleReset = () => {
    formRef.current?.resetFields();
  };

  const handleSearch = async () => {
    try {
      const values = await formRef.current?.validateFields();
      onSearch(values);
    } catch (error) {
      console.error(error);
    }
  };

  const disabledDate = current => {
    return current && current > moment();
  };

  return (
    <ProCard ghost>
      <ProCard bordered>
        <ProForm
          formRef={formRef}
          labelAlign="left"
          layout="horizontal"
          labelCol={{ flex: '70px' }}
          initialValues={{
            dateTime,
            customerCode: accountData[0]?.accountCode,
          }}
          submitter={{
            render: () => {
              return [];
            },
          }}
        >
          <Row gutter={40}>
            <Col span={24}>
              <div className="ant-radio-button-part-no-border">
                <ProFormRadio.Group
                  name="customerCode"
                  label="业务账号"
                  radioType="button"
                  options={accountData}
                  fieldProps={{
                    buttonStyle: 'solid',
                  }}
                />
              </div>
            </Col>
          </Row>
          <Row gutter={40}>
            <Col span={6}>
              <ProFormSelect name="channelId" label="产品名称" options={productList} />
            </Col>
            <Col span={6}>
              <ProFormDateRangePicker
                fieldProps={{
                  style: { width: '100%' },
                  disabledDate,
                }}
                name={'dateTime'}
                label="制单时间"
                placeholder={['请选择开始时间', '请选择结束时间']}
              />
            </Col>
          </Row>
          <Row gutter={40}>
            <Col span={6}>
              <ProFormTextArea
                name="waybillNumber"
                label="单号查询"
                placeholder="请输入查询运单号，最多支持500条"
              />
            </Col>

            <Col span={6}>
              <Space className="pl-2" direction="vertical">
                <Button type="primary" onClick={handleSearch} loading={buttonLoading}>
                  查询
                </Button>
                <Button onClick={handleReset}>重置</Button>
              </Space>
            </Col>
          </Row>
        </ProForm>
      </ProCard>
    </ProCard>
  );
};

export default SearchComponent;
