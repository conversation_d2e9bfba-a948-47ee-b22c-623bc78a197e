import { ProFormInstance } from '@ant-design/pro-components';
import { useMount, useUpdateEffect } from 'ahooks';
import { useRef, useState } from 'react';
import SearchComponent from './search';
import TablesComponents from './tables';
import moment from 'moment';
import React from 'react';
import { connect } from 'dva';
import PageContainerComponent from '@/components/PageContainer';
import PageHeaderBreadcrumb from '@/components/PageHeaderBreadcrumb';
import { textWaybill } from '@/utils/utils';

type TAccountDataInfo = USER_API.getOverseaAccountsData & {
  label?: string;
  value?: string;
};

type TAccountData = Array<TAccountDataInfo>;

const startTimeDate = moment()
  .subtract(6, 'months')
  .format('YYYY-MM-DD 00:00:00');
const endTimeDate = moment().format('YYYY-MM-DD 23:59:59');

const Index = (props: any) => {
  const { dispatch, tableLoading } = props;
  const formRef = useRef<ProFormInstance<any>>();
  const [pageSize, setPageSize] = useState(10);
  const [current, setCurrent] = useState(1);
  const [total, setTotal] = useState(0);
  const [dataList, setDataList] = useState<any>([]);
  const [tabActiveKey, setTabActiveKey] = useState('1');
  const [accountData, setAccountData] = useState<TAccountData>([]);
  const [productList, setProductList] = useState<any>([]);
  useUpdateEffect(() => {
    getList();
  }, [pageSize, current, tabActiveKey]);

  useMount(() => {
    getInit();
  });

  const fetchShippingAccounts = async () =>
    new Promise((resolve, reject) => {
      dispatch({
        type: 'Order/getShippingAccount',
        payload: { scene: 2 },
        callback: response => {
          if (response.success) {
            const data = response.data?.map(item => ({
              ...item,
              label: `${item?.warehouseName}${item?.accountCode}`,
              value: item?.accountCode,
            }));
            setAccountData(data || []);
            resolve(data || []);
          } else {
            reject([]);
          }
        },
      });
    });

  const fetchProductList = async () =>
    new Promise((resolve, reject) => {
      dispatch({
        type: 'smallBag/getProductList',
        payload: {},
        callback: response => {
          if (response.success) {
            setProductList(
              response.data?.map(v => ({
                ...v,
                label: v?.productCnName,
                value: v?.productNumber,
              }))
            );
            resolve(response.data);
          } else {
            reject([]);
          }
        },
      });
    });

  const getInit = async () => {
    Promise.all([fetchShippingAccounts(), fetchProductList()]).then(res => {
      formRef.current?.setFieldsValue({
        customerCode: res[0]?.[0]?.accountCode,
        dateTime: [startTimeDate, endTimeDate],
      });
      getList();
    });
  };

  const getList = async () => {
    try {
      const values = await formRef.current?.validateFields();
      const params: Partial<typeof values> = {
        ...values,
        pageSize,
        pageNum: current,
        state: tabActiveKey !== '1',
        waybillNumberArray: values?.waybillNumber
          ? textWaybill(values?.waybillNumber, 500, true)
          : undefined,
        startTime:
          typeof values?.dateTime?.[0] === 'string'
            ? values?.dateTime?.[0]
            : values?.dateTime?.[0]?.format('YYYY-MM-DD 00:00:00') ?? undefined,
        endTime:
          typeof values?.dateTime?.[1] === 'string'
            ? values?.dateTime?.[1]
            : values?.dateTime?.[1]?.format('YYYY-MM-DD 23:59:59') ?? undefined,
      };
      dispatch({
        type: 'smallBag/getOrderLabelList',
        payload: params,
        callback: response => {
          if (response.success) {
            setDataList(response.data?.records ?? []);
            setTotal(response.data?.totalRecord ?? 0);
          } else {
            setDataList([]);
            setTotal(0);
          }
        },
      });
    } catch (error) {
      console.error(error);
    }
  };

  const resetPageSize = () => {
    setCurrent(1);
    setPageSize(10);
  };

  const pagination = {
    pageSize,
    current,
    total,
    showSizeChanger: true,
    pageSizeOptions: ['5', '10', '20', '50', '100', '500', '1000'],
    onChange: (page: number, pageSize: number) => {
      setCurrent(page);
      setPageSize(pageSize);
    },
    showTotal: () => {
      return `共- ${total} -条记录`;
    },
  };

  const handleSearch = (values?: any) => {
    if (current === 1 && pageSize === 10) {
      getList();
    } else {
      setCurrent(1);
      setPageSize(10);
    }
  };

  return (
    <PageContainerComponent
      {...props}
      header={{
        title: null,
        breadcrumb: {},
        breadcrumbRender: props => <PageHeaderBreadcrumb {...props} />,
      }}
    >
      <SearchComponent
        formRef={formRef}
        {...props}
        accountData={accountData}
        onSearch={handleSearch}
        buttonLoading={tableLoading}
        dateTime={[startTimeDate, endTimeDate]}
        productList={productList}
      />
      <TablesComponents
        {...props}
        tabActiveKey={tabActiveKey}
        setTabActiveKey={setTabActiveKey}
        pagination={pagination}
        data={dataList}
        resetPageSize={resetPageSize}
        formRef={formRef}
        fetchData={handleSearch}
      />
    </PageContainerComponent>
  );
};

export default connect(({ loading }) => ({
  tableLoading: loading.effects['smallBag/getOrderLabelList'],
  singleUploadLoading: loading.effects['smallBag/importLabel'],
  batchUploadLoading: loading.effects['smallBag/importLabel'],
  deleteLoading: loading.effects['smallBag/deleteLabel'],
}))(Index);
