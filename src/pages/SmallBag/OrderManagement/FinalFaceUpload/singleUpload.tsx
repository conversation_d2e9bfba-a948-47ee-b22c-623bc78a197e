import React, { useImperativeHandle, useRef, useState } from 'react';
import { Modal, Space, Row, Button, message, Upload, Form, UploadProps } from 'antd';
import { ProForm, ProFormText } from '@ant-design/pro-components';
import { ProFormInstance } from '@ant-design/pro-components';
import { changeBeforeUpload } from '@/utils/utils';
import { InboxOutlined } from '@ant-design/icons';
import { useUpdateEffect } from 'ahooks';
const { Dragger } = Upload;

const SingleUpload = props => {
  const { onCancel, modalRef, dispatch, singleUploadLoading, fetchData } = props;

  const formRef = useRef<ProFormInstance<{}>>();

  const [open, setOpen] = useState(false);
  const [record, setRecord] = useState<undefined | { [key: string]: any }>();
  const [check, setCheck] = useState<undefined | boolean>();
  const [pdfFileList, setPdfFileList] = useState<any[]>([]);
  const [pdfUploadedFile, setPdfUploadedFile] = useState<any | null>(null);

  useUpdateEffect(() => {
    if (record) {
      formRef.current?.setFieldsValue({
        waybillNumber: record?.waybillNumber,
      });
    }
  }, [record]);

  useImperativeHandle(modalRef, () => ({
    // 打开弹窗
    openModal: (record?: any) => {
      setOpen(true);
      setRecord(record);
    },
  }));

  const uploadProps: UploadProps = {
    name: 'file',
    action: '/csc/temu/analyzePdfLabelFile?type=0',
    multiple: false, // 确保 multiple 为 false
    accept: '.pdf, application/pdf',
    fileList: pdfFileList, // 受控
    beforeUpload: (file: File) => {
      const isPdf = file.type === 'application/pdf' || file.name.endsWith('.pdf');
      if (!isPdf) {
        message.error(`${file.name} 不是 PDF 文件`);
      }
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        message.error('单个文件必须小于 10MB!');
      }
      return isPdf && isLt10M ? true : Upload.LIST_IGNORE;
    },
    onChange(info: any) {
      // maxCount: 1 确保 info.fileList 最多只有一个文件, 或者在移除后为空数组
      // 如果需要严格控制只显示一个文件（即使上传组件内部可能暂时有两个），可以取最后一个
      const currentFileList = info.fileList.slice(-1);
      setPdfFileList(currentFileList);
      if (info.file.status === 'done') {
        if (info.file.response.success) {
          const data = info.file.response.data;
          if (data.length > 1) {
            message.error('当前仅支持单个运单号上传');
            setPdfUploadedFile(null);
            setPdfFileList([]);
            formRef?.current?.setFieldsValue({ labelBase64: [] });
            formRef?.current?.validateFields(['labelBase64']);
            return;
          }
          message.success(`${info.file.name} 文件添加成功`);
          setPdfUploadedFile(info.file); // 直接设置单个文件
          formRef?.current?.setFieldsValue({
            labelBase64: currentFileList?.map(v => ({ ...v, url: data[0].labelBase64 })),
          }); // 表单值也应该是当前文件列表
          formRef?.current?.validateFields(['labelBase64']);
        } else {
          message.error(info.file.response.message);
          setPdfUploadedFile(null);
          setPdfFileList([]);
          formRef?.current?.setFieldsValue({ labelBase64: [] });
          formRef?.current?.validateFields(['labelBase64']);
        }
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 文件添加失败.`);
        if (pdfUploadedFile && pdfUploadedFile.uid === info.file.uid) {
          setPdfUploadedFile(null);
        }
        formRef?.current?.setFieldsValue({ labelBase64: [] }); // 上传失败，清空表单中的文件
        formRef?.current?.validateFields(['labelBase64']);
      } else if (info.file.status === 'removed') {
        // 当文件从Upload组件中移除时（例如通过Upload组件的移除按钮，虽然我们隐藏了它）
        // 或者beforeUpload返回 Upload.LIST_IGNORE 时，也可能触发
        setPdfUploadedFile(null);
        setPdfFileList([]); // 确保文件列表也清空
        formRef?.current?.setFieldsValue({ labelBase64: [] });
        formRef?.current?.validateFields(['labelBase64']);
      }
    },
    maxCount: 1, // maxCount: 1 确保 info.fileList 最多只有一个文件
  };

  // 关闭弹窗
  const handleCancel = () => {
    formRef.current?.resetFields();
    setOpen(false);
    setRecord(undefined);
    setCheck(undefined);
  };

  // 提交表单
  const handleSubmit = async (values: any) => {
    console.log(values);
    console.log(record);

    dispatch({
      type: 'smallBag/importLabel',
      payload: [
        {
          customerCode: record?.userId,
          waybillNumber: values?.waybillNumber,
          trackingNumber: values?.trackingNumber,
          labelBase64: values?.labelBase64 ? values?.labelBase64[0]?.url : undefined,
        },
      ],
      callback: response => {
        if (response.success) {
          message.success(response.message);
          handleCancel();
          fetchData();
        }
      },
    });
  };

  return (
    <Modal title={`单票上传`} open={open} onCancel={() => handleCancel()} footer={null}>
      <ProForm
        formRef={formRef}
        layout="horizontal"
        labelAlign="right"
        labelCol={{ flex: '110px' }}
        onFinish={handleSubmit}
        submitter={{
          render: (_, dom) => null,
        }}
      >
        <>
          <ProFormText readonly name="waybillNumber" label="运单号" placeholder="请输入运单号" />
          <ProFormText name="trackingNumber" label="尾程单号" placeholder="请输入尾程单号" />
          <Form.Item
            label="上传文件"
            name="labelBase64"
            rules={[{ required: true, message: '请上传文件' }]}
          >
            <Dragger {...uploadProps}>
              <Space direction="vertical">
                <p className="ant-upload-drag-icon">
                  <InboxOutlined />
                </p>
                <p>拖拽PDF文件到此处，或点击下方按钮选择文件</p>
                <Button type="primary">选择文件</Button>
                <p>支持PDF文件、不超过10M大小的文件，保证清晰</p>
              </Space>
            </Dragger>
          </Form.Item>

          <Row justify="center">
            <Space>
              <Button onClick={() => handleCancel()}>取消</Button>
              <Button type="primary" htmlType="submit" loading={singleUploadLoading}>
                确定
              </Button>
            </Space>
          </Row>
        </>
      </ProForm>
    </Modal>
  );
};

export default SingleUpload;
