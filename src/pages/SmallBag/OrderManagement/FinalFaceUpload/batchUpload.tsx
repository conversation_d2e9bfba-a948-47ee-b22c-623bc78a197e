import React, { useState, useImperativeHandle } from 'react';
import { Modal, Form, Button, Upload, Typography, Card, message, Space, Input } from 'antd';
import {
  FileExcelOutlined,
  FilePdfOutlined,
  CheckCircleOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import { EditableProTable } from '@ant-design/pro-components';
import { downloadBase64File, openPreviewModal } from '@/utils/utils';
import { useUpdateEffect } from 'ahooks';

const { Title, Text } = Typography;

/**
 * 格式化文件大小
 * @param bytes 文件大小（字节）
 * @returns 格式化后的文件大小字符串
 */
const formatFileSize = (bytes: number): string => {
  if (!bytes || bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

interface UploadedFileDisplayProps {
  /**
   * 已上传文件显示组件
   * @param file antd UploadFile 实例
   * @param onRemove 移除文件回调
   * @param fileTypeIcon 文件类型图标
   */
  file: any; // antd UploadFile instance
  onRemove: () => void;
  fileTypeIcon: React.ReactNode;
}

const UploadedFileDisplay: React.FC<UploadedFileDisplayProps> = ({
  file,
  onRemove,
  fileTypeIcon,
}) => {
  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <span>已上传文件</span>
      <div
        style={{
          border: '1px solid #d9d9d9',
          padding: '10px',
          borderRadius: '2px',
          backgroundColor: '#fafafa',
        }}
      >
        <Space align="center" style={{ width: '100%', justifyContent: 'space-between' }}>
          <Space align="center">
            {fileTypeIcon}
            <div style={{ marginLeft: 8 }}>
              <Text strong>{file.name}</Text>
              <br />
              <Text type="secondary">{formatFileSize(file.size)}</Text>
            </div>
          </Space>
          <Space align="center">
            <CheckCircleOutlined style={{ color: 'green', fontSize: '16px', marginRight: '4px' }} />
            <Text style={{ color: 'green', marginRight: '8px' }}>已读取</Text>
            <Button
              type="text"
              icon={<DeleteOutlined style={{ color: 'red' }} />}
              onClick={onRemove}
              style={{ padding: '0 4px' }}
            />
          </Space>
        </Space>
      </div>
    </Space>
  );
};

interface BatchUploadProps {
  modalRef: any;
  onCancel?: () => void;
  /**
   * 处理移除 Excel 文件
   */
  onSubmit?: (values: any) => void; // 表单提交回调
  dispatch: any;
  fetchData: (values?: any) => void;
  [key: string]: any;
}

const BatchUpload: React.FC<BatchUploadProps> = ({
  modalRef,
  onCancel,
  onSubmit,
  dispatch,
  fetchData,
  batchUploadLoading,
}) => {
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [excelFileList, setExcelFileList] = useState<any[]>([]);
  const [pdfFileList, setPdfFileList] = useState<any[]>([]);
  const [excelUploadedFile, setExcelUploadedFile] = useState<any | null>(null);
  const [excelUploadData, setExcelUploadData] = useState<any>([]);
  const [pdfUploadData, setPdfUploadData] = useState<any>([]);
  const [pdfUploadedFile, setPdfUploadedFile] = useState<any | null>(null);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);

  useUpdateEffect(() => {
    if (
      excelUploadData &&
      excelUploadData.length > 0 &&
      pdfUploadData &&
      pdfUploadData.length > 0
    ) {
      const pdfDataMap = new Map();
      pdfUploadData.forEach(pdfItem => {
        pdfDataMap.set(pdfItem.trackingNumber, pdfItem);
      });

      const newProcessedData = excelUploadData.map(excelItem => {
        const pdfMatch = pdfDataMap.get(excelItem.trackingNumber);
        if (pdfMatch) {
          return {
            ...excelItem,
            pdfTrackingNumber: pdfMatch.trackingNumber,
            labelBase64: pdfMatch.labelBase64,
            matchStatus: '已匹配', // 已匹配状态
          };
        } else {
          return {
            ...excelItem,
            pdfTrackingNumber: null,
            matchStatus: '未匹配', // 未匹配状态
          };
        }
      });
      form.setFieldsValue({ detailsInfo: newProcessedData });
      const filterData = newProcessedData.filter(item => item.matchStatus === '未匹配');
      const keysForEditing = filterData.map(item => item.waybillNumber);
      setEditableRowKeys(keysForEditing);
    } else {
      form.setFieldsValue({ detailsInfo: [] });
      setEditableRowKeys([]);
    }
  }, [excelUploadData, pdfUploadData, form]);

  const handleRemoveExcel = () => {
    setExcelUploadedFile(null);
    setExcelFileList([]);
    setExcelUploadData([]);
    form.setFieldsValue({ excelFile: [] });
    form.validateFields(['excelFile', 'detailsInfo']);
  };

  const handleRemovePdf = () => {
    setPdfUploadedFile(null);
    setPdfFileList([]);
    setPdfUploadData([]);
    form.setFieldsValue({ pdfFiles: [] });
    form.validateFields(['pdfFiles', 'detailsInfo']);
  };

  const dashedBoxStyle: React.CSSProperties = {
    border: '1px dashed #d9d9d9',
    padding: '20px',
    textAlign: 'center',
    borderRadius: '2px',
    // backgroundColor: '#fafafa', // 可选
  };
  /**
   * 打开 Modal
   */
  useImperativeHandle(modalRef, () => ({
    openModal: () => {
      setVisible(true);
    },
  }));

  const handleDownloadTemplate = () => {
    dispatch({
      type: 'smallBag/exportLabelTemplate',
      payload: {
        customerCode: '',
      },
      callback: response => {
        if (response.success) {
          downloadBase64File(response?.data?.url, response?.data?.fileName);
          message.success('模板下载成功');
        }
      },
    });
  };

  const handleCancel = () => {
    form.resetFields();
    setVisible(false);
    setExcelUploadedFile(null);
    setPdfUploadedFile(null);
    setExcelFileList([]);
    setEditableRowKeys([]);
    setPdfFileList([]);
    setExcelUploadData([]);
    setPdfUploadData([]);
  };

  const handlePreview = record => {
    openPreviewModal({
      url: record?.labelBase64,
    });
    // dispatch({
    //   type: 'smallBag/getOrderLabelDetail',
    //   payload: {
    //     customerCode: record?.customerCode,
    //     waybillNumber: record?.waybillNumber,
    //   },
    //   callback: response => {
    //     if (response.success) {
    //       openPreviewModal({
    //         url: response?.data,
    //       });
    //     }
    //   },
    // });
  };

  /**
   * 处理表单提交
   */
  const handleFormSubmit = async () => {
    try {
      const values = await form.validateFields();
      const params = values?.detailsInfo?.map(v => ({
        waybillNumber: v?.waybillNumber,
        trackingNumber: v?.trackingNumber,
        customerCode: v?.customerCode,
        labelBase64: v?.labelBase64,
      }));
      dispatch({
        type: 'smallBag/importLabel',
        payload: params,
        callback: response => {
          if (response.success) {
            message.success(response?.message);
            handleCancel();
            fetchData();
          }
        },
      });
    } catch (error) {
      console.log('Validate Failed:', error);
      message.error('表单校验失败, 请检查输入项!');
    }
  };

  const excelUploadProps = {
    name: 'file',
    multiple: false,
    action: '/csc/temu/importLabelFile',
    accept: '.xlsx, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    fileList: excelFileList, // 受控
    beforeUpload: (file: File) => {
      const isXlsx =
        file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        file.name.endsWith('.xlsx');
      if (!isXlsx) {
        message.error(`${file.name} 不是 .xlsx 文件`);
      }
      const isLt5M = file.size / 1024 / 1024 < 200;
      if (!isLt5M) {
        message.error('文件必须小于 200MB!');
      }
      return isXlsx && isLt5M ? true : Upload.LIST_IGNORE;
    },
    onChange(info: any) {
      setExcelFileList(info.fileList);
      if (info.file.status === 'done') {
        if (info.file.response.success) {
          message.success(`${info.file.name} 文件添加成功`);
          setExcelUploadedFile(info.file);
          setExcelUploadData(info.file.response.data);
          form.setFieldsValue({ excelFile: info.fileList });
          form.validateFields(['excelFile']);
        } else {
          message.error(info.file.response.message);
          setExcelUploadedFile(null);
          setExcelFileList([]);
          setExcelUploadData([]);
          form.setFieldsValue({ excelFile: [] });
          form.validateFields(['excelFile']);
        }
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 文件添加失败.`);
        if (excelUploadedFile && excelUploadedFile.uid === info.file.uid) {
          setExcelUploadedFile(null);
        }
        setExcelUploadData([]);
        form.setFieldsValue({ excelFile: [] });
        form.validateFields(['excelFile']);
      } else if (info.file.status === 'removed') {
        setExcelUploadedFile(null);
        setExcelUploadData([]);
        form.setFieldsValue({ excelFile: [] });
        form.validateFields(['excelFile']);
      }
    },
    maxCount: 1, // maxCount: 1 确保 info.fileList 最多只有一个文件
    // customRequest: ({ onSuccess }) => setTimeout(() => onSuccess('ok'), 0) // 模拟上传成功
  };

  const pdfUploadProps = {
    name: 'file',
    action: '/csc/temu/analyzePdfLabelFile',
    multiple: false, // 确保 multiple 为 false
    accept: '.pdf, application/pdf',
    fileList: pdfFileList, // 受控
    beforeUpload: (file: File) => {
      const isPdf = file.type === 'application/pdf' || file.name.endsWith('.pdf');
      if (!isPdf) {
        message.error(`${file.name} 不是 PDF 文件`);
      }
      const isLt10M = true;
      if (!isLt10M) {
        message.error('单个文件必须小于 10MB!');
      }
      return isPdf && isLt10M ? true : Upload.LIST_IGNORE;
    },
    onChange(info: any) {
      // maxCount: 1 确保 info.fileList 最多只有一个文件, 或者在移除后为空数组
      // 如果需要严格控制只显示一个文件（即使上传组件内部可能暂时有两个），可以取最后一个
      const currentFileList = info.fileList.slice(-1);
      if (info.file.status === 'done') {
        if (info.file.response.success) {
          setPdfFileList(currentFileList);
          message.success(`${info.file.name} 文件添加成功`);
          setPdfUploadedFile(info.file); // 直接设置单个文件
          setPdfUploadData(info.file.response.data);
          form.setFieldsValue({ pdfFiles: currentFileList }); // 表单值也应该是当前文件列表
          form.validateFields(['pdfFiles']);
        } else {
          message.error(info.file.response.message);
          setPdfUploadedFile(null);
          setPdfFileList([]);
          setPdfUploadData([]);
          form.setFieldsValue({ pdfFiles: [] });
          form.validateFields(['pdfFiles']);
        }
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 文件添加失败.`);
        if (pdfUploadedFile && pdfUploadedFile.uid === info.file.uid) {
          setPdfUploadedFile(null);
        }
        setPdfUploadData([]);
        form.setFieldsValue({ pdfFiles: [] }); // 上传失败，清空表单中的文件
        form.validateFields(['pdfFiles']);
      } else if (info.file.status === 'removed') {
        // 当文件从Upload组件中移除时（例如通过Upload组件的移除按钮，虽然我们隐藏了它）
        // 或者beforeUpload返回 Upload.LIST_IGNORE 时，也可能触发
        setPdfUploadedFile(null);
        setPdfFileList([]); // 确保文件列表也清空
        setPdfUploadData([]);
        form.setFieldsValue({ pdfFiles: [] });
        form.validateFields(['pdfFiles']);
      }
    },
    maxCount: 1, // maxCount: 1 确保 info.fileList 最多只有一个文件
    // customRequest: ({ onSuccess }) => setTimeout(() => onSuccess('ok'), 0) // 模拟上传成功
  };

  const columns = [
    {
      title: <>运单号</>,
      dataIndex: 'waybillNumber',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项是必填项',
          },
        ],
      },
    },
    {
      title: <>订单号</>,
      dataIndex: 'orderNumber',
      readonly: true,
    },
    {
      title: <>尾程单号</>,
      dataIndex: 'trackingNumber',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项是必填项',
          },
        ],
      },
    },
    {
      title: <>PDF文件上的尾程单号</>,
      dataIndex: 'pdfTrackingNumber',
      readonly: true,
    },
    {
      title: <>匹配状态</>,
      dataIndex: 'matchStatus', // '2': 未匹配, '1': 已匹配
      readonly: true,
      render: (text, record) => {
        return record?.matchStatus === '已匹配' ? (
          '已匹配'
        ) : (
          <span className="text-red-500">未匹配</span>
        );
      },
    },
    {
      title: '操作',
      width: '120px',
      valueType: 'option',
      render: (text, record, _, action) => [
        record?.matchStatus === '已匹配' ? (
          <Button
            type="link"
            onClick={() => {
              handlePreview(record);
            }}
          >
            预览
          </Button>
        ) : null,
      ],
    },
  ];

  return (
    <Modal
      title={
        <Space>
          <Title level={5} style={{ margin: 0 }}>
            批量导入数据
          </Title>
          <Text className="text-sm" type="secondary">
            请按照步骤上传文件，系统将自动匹配单号信息
          </Text>
        </Space>
      }
      open={visible} // Antd v4 使用 visible, v5+ 推荐 open 但 visible 通常兼容
      onCancel={handleCancel}
      destroyOnClose // 关闭时销毁 Modal 里的子元素，包括表单状态
      width="85%"
      style={{ top: 20 }}
      bodyStyle={{ maxHeight: 'calc(100vh - 210px)', overflowY: 'auto' }} // 108px = 55px header + 53px footer
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" onClick={handleFormSubmit} loading={batchUploadLoading}>
          提交
        </Button>,
      ]}
    >
      <Form form={form} layout="vertical" name="batchUploadForm">
        <Card
          title={
            <Title level={5} style={{ margin: 0 }}>
              第一步：上传尾程单号对应关系
            </Title>
          }
          bodyStyle={{
            padding: '16px',
          }}
          style={{ marginBottom: 16 }}
        >
          {excelUploadedFile ? (
            <UploadedFileDisplay
              file={excelUploadedFile}
              onRemove={handleRemoveExcel}
              fileTypeIcon={<FileExcelOutlined style={{ fontSize: '32px', color: '#1890ff' }} />}
            />
          ) : (
            <div style={dashedBoxStyle}>
              <FileExcelOutlined style={{ fontSize: '36px', color: '#1890ff' }} />
              <Text style={{ display: 'block', marginBottom: 16 }}>
                请上传包含尾程单号与运单号对应关系的 Excel 文件
              </Text>
              <Form.Item
                name="excelFile"
                rules={[{ required: true, message: '请上传对应关系Excel文件!' }]}
                valuePropName="fileList"
                getValueFromEvent={e => {
                  if (Array.isArray(e)) {
                    return e;
                  }
                  return e && e.fileList;
                }}
                style={{ marginBottom: 8 }}
              >
                <Upload {...excelUploadProps} showUploadList={false}>
                  <Space>
                    <Button type="primary">选择文件</Button>
                    {/* <Button
                      type="default"
                      onClick={e => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleDownloadTemplate();
                      }}
                    >
                      下载模板
                    </Button> */}
                  </Space>
                </Upload>
              </Form.Item>
              <Text type="secondary" style={{ display: 'block' }}>
                支持 .xlsx 格式，文件大小不超过 5MB
              </Text>
            </div>
          )}
        </Card>

        <Card
          title={
            <Space>
              <Title level={5} style={{ margin: 0 }}>
                第二步：请上传尾程标签PDF文件
              </Title>
              <Text className="text-sm" type="secondary">
                （多个尾程标签请合并一个PDF文件上传）
              </Text>
            </Space>
          }
          bodyStyle={{
            padding: '16px',
          }}
          style={{ marginBottom: 16 }}
        >
          {pdfUploadedFile ? (
            <>
              <UploadedFileDisplay
                file={pdfUploadedFile}
                onRemove={handleRemovePdf} // 直接调用，不需要参数
                fileTypeIcon={<FilePdfOutlined style={{ fontSize: '32px', color: '#f5222d' }} />}
              />
              {/* Hidden Form.Item to keep form state, if UploadedFileDisplay is shown, form should have value */}
              <Form.Item name="pdfFiles" style={{ display: 'none' }} initialValue={pdfFileList}>
                <Input />
              </Form.Item>
            </>
          ) : (
            <div style={dashedBoxStyle}>
              <FilePdfOutlined style={{ fontSize: '36px', color: '#1890ff' }} />
              <Text style={{ display: 'block', marginBottom: 16 }}>
                请点击下方按钮选择 PDF 文件
              </Text>
              <Form.Item
                name="pdfFiles"
                rules={[{ required: true, message: '请上传PDF文件!' }]}
                valuePropName="fileList"
                getValueFromEvent={e => {
                  if (Array.isArray(e)) {
                    return e;
                  }
                  return e && e.fileList;
                }}
                style={{ marginBottom: 8 }}
              >
                <Upload {...pdfUploadProps} showUploadList={false}>
                  <Button type="primary">选择文件</Button>
                </Upload>
              </Form.Item>
              <Text type="secondary" style={{ display: 'block' }}>
                支持单个 PDF 文件，文件大小不超过 200MB
              </Text>
            </div>
          )}
        </Card>

        <Card
          title={
            <Space>
              <Title level={5} style={{ margin: 0 }}>
                数据预览
              </Title>
              <Text className="text-sm" type="secondary">
                上传文件后，此处将显示预览数据。
              </Text>
            </Space>
          }
          bodyStyle={{
            padding: '16px',
          }}
        >
          {/* 后续可添加表格等预览组件 */}
          <EditableProTable
            rowKey="waybillNumber"
            loading={false}
            columns={columns as any}
            name="detailsInfo"
            toolBarRender={false}
            recordCreatorProps={false}
            editable={{
              type: 'multiple',
              editableKeys,
              actionRender: (row, config, defaultDoms) => {
                return [defaultDoms.delete];
              },
              // onValuesChange: (record, recordList) => {},
              onChange: setEditableRowKeys,
            }}
          />
        </Card>
      </Form>
    </Modal>
  );
};

export default BatchUpload;
