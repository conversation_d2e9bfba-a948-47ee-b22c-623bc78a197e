import React, { useState, useEffect, useRef, useImperativeHandle, forwardRef } from 'react';
import {
  Card,
  Button,
  Table,
  Form,
  Row,
  Col,
  Input,
  Space,
  DatePicker,
  message,
  Upload,
  Popconfirm,
} from 'antd';
import { downloadFile, downLoadBase64File } from '@/utils/download';
import moment from 'moment';
import EditGoodsTable from './EditGoodsTable';
import ImportTable from './importTable';
import { logSave, LogType } from '@/utils/logSave';
import { formatMessage } from 'umi-plugin-react/locale';

const goodsRecordInformation = (props, ref) => {
  const { form, dispatch, handleEdit } = props;
  const editTableRef = useRef();
  const importTableRef = useRef();

  const [currencyList, setCurrencyList] = useState([]);
  const [editCurrenyList, setEditCurrenyList] = useState([]);
  const [dataList, setDataList] = useState([]);
  const [total, setTotal] = useState(0);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [tabKey, setTabKey] = useState('0');
  const [loading, setLoading] = useState(false);
  const [uploadLoading, setUploadLoading] = useState(false);
  const [count, setCount] = useState({
    waitingCount: 0, // 待审核
    passCount: 0, // 已审核
    refuseCount: 0, // 已拒绝
    allCount: 0, // 全部
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);

  const [downLoading, setDownLoading] = useState(false);

  const startTime = moment()
    .startOf('month')
    .format('YYYY-MM-DD');
  const endTime = moment().format('YYYY-MM-DD');
  const tabList = [
    {
      key: '0',
      tab: `待审核（${count.waitingCount}）`,
    },
    {
      key: '1',
      tab: `已审核（${count.passCount}）`,
    },
    {
      key: '2',
      tab: `已拒绝（${count.refuseCount}）`,
    },
    {
      key: '3',
      tab: `全部（${count.allCount}）`,
    },
  ];

  const columns = [
    {
      title: '中文品名',
      key: 'cnProductName',
      dataIndex: 'cnProductName',
      width: 150,
    },
    {
      title: '英文品名',
      key: 'enProductName',
      dataIndex: 'enProductName',
      width: 150,
      render: text => <span className="whitespace-pre-wrap">{text}</span>,
    },
    {
      title: 'SKU',
      key: 'skuId',
      dataIndex: 'skuId',
      width: 150,
    },
    {
      title: '海关编码',
      key: 'hsCode',
      dataIndex: 'hsCode',
      width: 150,
    },
    {
      title: '申报价值',
      key: 'declaredValue',
      dataIndex: 'declaredValue',
      width: 150,
    },
    {
      title: '申报币种',
      key: 'currencyId',
      dataIndex: 'currencyId',
      width: 150,
      render: text => currencyList.find(item => item.id == text)?.label ?? '',
    },
    {
      title: '规格型号',
      key: 'specification',
      dataIndex: 'specification',
      width: 150,
    },
    {
      title: '商品净重(kg)',
      key: 'netWeight',
      dataIndex: 'netWeight',
      width: 150,
    },
    {
      title: '商品货号',
      key: 'cargoNumber',
      dataIndex: 'cargoNumber',
      width: 150,
    },
    {
      title: '法定第一数量',
      key: 'legalFirstQuantity',
      dataIndex: 'legalFirstQuantity',
      width: 150,
    },
    {
      title: '法定计量单位',
      key: 'measuringUnit',
      dataIndex: 'measuringUnit',
      width: 150,
    },
    {
      title: '法定第二数量',
      key: 'legalSecondQuantity',
      dataIndex: 'legalSecondQuantity',
      width: 150,
    },
    {
      title: '第二数量单位',
      key: 'secondNumberUnit',
      dataIndex: 'secondNumberUnit',
      width: 150,
    },
    {
      title: '计量单位',
      key: 'units',
      dataIndex: 'units',
      width: 150,
    },
    {
      title: '商品链接',
      key: 'imgUrl',
      dataIndex: 'imgUrl',
      width: 180,
      ellipsis: true,
    },
    {
      title: '商品销售链接',
      key: 'commodityUrl',
      dataIndex: 'commodityUrl',
      width: 180,
      ellipsis: true,
    },
    {
      title: '状态',
      key: 'auditStatus',
      dataIndex: 'auditStatus',
      width: 150,
      render: text => (
        <span style={{ color: '#52c41a' }}>
          {text == 0 ? '待审核' : text == 1 ? '已审核' : '已拒绝'}
        </span>
      ),
    },
    {
      title: '拒绝原因',
      key: 'refusalCause',
      dataIndex: 'refusalCause',
      width: 150,
      ellipsis: true,
      filterType: tabKey != 2 ? true : undefined,
      render: text => <span style={{ color: 'red' }}>{text}</span>,
    },
    {
      title: '申请时间',
      key: 'createTime',
      dataIndex: 'createTime',
      width: 150,
    },
    {
      title: '操作',
      key: 'option',
      dataIndex: 'option',
      width: 150,
      fixed: 'right',
      filterType: tabKey != 2 ? true : undefined,
      render: (text, record) => {
        return (
          <Space>
            <Button type="link" onClick={() => handleEdit(record)}>
              编辑
            </Button>
            <Popconfirm
              title="是否确认删除该条数据"
              onConfirm={() => handleOneDelete(record)}
              okText="确认"
              cancelText="取消"
            >
              <Button type="link" danger>
                删除
              </Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  const pagination = {
    total: total,
    current: current,
    pageSize: pageSize,
    pageSizeOptions: ['10', '20', '50', '100', '500'],
    showSizeChanger: true,
    onChange: (page, pageSize) => {
      setCurrent(page);
      setPageSize(pageSize);
    },
    showTotal: () => {
      return `共- ${total} -条记录`;
    },
  };

  const selectChange = (newSelectedRowKeys, newSelectedRows) => {
    setSelectedRowKeys(newSelectedRowKeys);
    setSelectedRows(newSelectedRows);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: selectChange,
  };

  function usePrevious(value) {
    const ref = useRef();
    useEffect(() => {
      ref.current = value;
    });
    return ref.current;
  }

  const previous = usePrevious({ tabKey, current, pageSize });

  useImperativeHandle(ref, () => ({
    refresh: () => {
      initialize();
    },
  }));

  useEffect(() => {
    initialize();
  }, []);

  useEffect(() => {
    if (!previous) return;
    if (
      previous?.tabKey != tabKey ||
      previous?.current != current ||
      previous?.pageSize != pageSize
    ) {
      getDataList();
    }
  }, [tabKey, current, pageSize]);

  const initialize = () => {
    reset();
    setSelectedRowKeys([]);
    setSelectedRows([]);
    setDataList([]);
    getDataList();
    getCurrencyList();
  };

  // 查询币种
  const getCurrencyList = () => {
    dispatch({
      type: 'complianceMerchant/complianceGetCurrency',
      callback: response => {
        if (response.success) {
          const data = response.data.map(item => {
            item.label = `${item.name}/${item.description}`;
            item.editValue = `${item.name}/${item.description}`;
            item.value = item.id;
            return item;
          });
          setCurrencyList(data);
          setEditCurrenyList(data);
        } else {
          setCurrencyList([]);
          setEditCurrenyList([]);
          message.error(response.message);
        }
      },
    });
  };

  // 查询商品备案信息状态数量
  const getCommodityNum = params => {
    dispatch({
      type: 'complianceMerchant/commodityNum',
      payload: params,
      callback: response => {
        if (response.success) {
          const info = response.data;
          setCount({
            ...count,
            waitingCount: info.waitingCount ?? 0,
            passCount: info.passCount ?? 0,
            refuseCount: info.refuseCount ?? 0,
            allCount: info.allCount ?? 0,
          });
        } else {
          message.error(response.message);
        }
      },
    });
  };

  const handleOneDelete = record => {
    handleDelete([record.id]);
  };

  // 批量和单个删除共用
  const handleDelete = params => {
    dispatch({
      type: 'complianceMerchant/complianceBatchDelete',
      payload: { ids: params },
      callback: response => {
        if (response.success) {
          message.success('删除成功！');
          setSelectedRowKeys([]);
          setSelectedRows([]);
          setDataList([]);
          getDataList();
        } else {
          message.error(response.message);
        }
      },
    });
  };

  const handleTabChange = key => {
    setTabKey(key);
  };

  // 重置
  const reset = () => {
    form.resetFields([
      'cnProductNameForm',
      'enProductNameForm',
      'hsCodeForm',
      'specificationForm',
      'sku',
    ]);
    form.setFieldsValue({
      createTimeBegin: moment(startTime, 'YYYY-MM-DD'),
      createTimeEnd: moment(endTime, 'YYYY-MM-DD'),
    });
  };

  // 处理不同的按钮   导入 下载 删除 导出
  const handleChangeStatus = async status => {
    switch (status) {
      case 'download':
        setDownLoading(true);
        const result = await downLoadBase64File({
          url: `/csc/compliance/templateDownload`,
        });
        if (result === 1) {
          setDownLoading(false);
        } else {
          setDownLoading(false);
        }
        break;
      case 'delete':
        if (selectedRows.length === 0) {
          return message.warning('请选择要删除的商品！');
        }
        const data = selectedRows.map(item => item.id);
        handleDelete(data);
        break;
      case 'edit':
        if (selectedRows.length === 0) {
          return message.warning('请选择要编辑的商品！');
        }
        const list = selectedRows.map((item, index) => {
          item.id = item.id ? item.id : `${(Math.random() * 1000000).toFixed(0)}${index}`;
          return item;
        });
        editTableRef.current.open(list);
        break;
    }
  };

  // 上传
  const batchUpload = (file, fileList) => {
    logSave(LogType.compliance5);
    const fileExtension = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase();
    const isXls = fileExtension === 'xlsx' || fileExtension === 'xls' || fileExtension === 'zip';
    if (!isXls) {
      message.error('请按照模板文件上传');
      return;
    }
    let formData = new FormData();
    formData.append('file', file);
    setUploadLoading(true);
    dispatch({
      type: 'complianceMerchant/complianceUploading',
      payload: formData,
      callback: async response => {
        setUploadLoading(false);
        if (response.success) {
          const values = await form.validateFields();
          importTableRef.current.open({
            ...values,
            createTimeBegin: `${values.createTimeBegin.format('YYYY-MM-DD')} 00:00:00`,
            createTimeEnd: `${values.createTimeEnd.format('YYYY-MM-DD')} 23:59:59`,
          });
        } else {
          message.error(response.message);
        }
      },
    });
  };

  // 不同状态展示不同按钮
  const changeTabBarButton = () => {
    return tabKey == 0 ? (
      <>
        <Button
          onClick={() => {
            logSave(LogType.compliance4);
            handleChangeStatus('download');
          }}
          loading={downLoading}
        >
          模板下载
        </Button>
        <Upload showUploadList={false} beforeUpload={batchUpload} accept=".xls,.xlsx">
          <Button type="primary" loading={uploadLoading}>
            模板导入
          </Button>
        </Upload>
      </>
    ) : tabKey == 1 ? null : tabKey == 2 ? (
      <>
        <Button onClick={() => handleChangeStatus('edit')} type="primary">
          编辑
        </Button>
        <Popconfirm
          title="是否确认删除"
          onConfirm={() => handleChangeStatus('delete')}
          okText="确认"
          cancelText="取消"
        >
          <Button danger>删除</Button>
        </Popconfirm>
      </>
    ) : null;
  };

  /**
   * 导入记录
   */
  const importRecord = async () => {
    logSave(LogType.compliance3);
    const values = await form.validateFields();
    importTableRef.current?.open({
      ...values,
      createTimeBegin: `${values.createTimeBegin.format('YYYY-MM-DD')} 00:00:00`,
      createTimeEnd: `${values.createTimeEnd.format('YYYY-MM-DD')} 23:59:59`,
    });
  };

  const getDataList = async () => {
    logSave(LogType.compliance2);
    const values = await form.validateFields();
    setLoading(true);
    if (!values.createTimeBegin) {
      message.warning('请选择开始时间！');
      return;
    } else if (!values.createTimeEnd) {
      message.warning('请选择结束时间！');
      return;
    } else if (values.createTimeEnd.diff(values.createTimeBegin, 'days') < 0) {
      message.warning('结束时间不能小于开始时间');
      return;
    }
    const params = {
      ...values,
      cnProductName: values.cnProductNameForm,
      enProductName: values.enProductNameForm,
      hsCode: values.hsCodeForm,
      specification: values.specificationForm,
      createTimeBegin: `${values.createTimeBegin.format('YYYY-MM-DD')} 00:00:00`,
      createTimeEnd: `${values.createTimeEnd.format('YYYY-MM-DD')} 23:59:59`,
      auditStatus: tabKey == 3 ? undefined : tabKey,
      current,
      size: pageSize,
    };
    if (values.sku) {
      params.skuId = values.sku.split('\n');
    }
    getCommodityNum(params);
    dispatch({
      type: 'complianceMerchant/commodityRecordPage',
      payload: params,
      callback: response => {
        setLoading(false);
        if (response.success) {
          setDataList(response.data.list);
          setTotal(response.data.total);
        } else {
          message.error(response.message);
          setDataList([]);
          setTotal(0);
        }
      },
    });
  };

  return (
    <>
      <Card>
        <Form
          form={form}
          layout="horizontal"
          initialValues={{
            createTimeBegin: moment(startTime, 'YYYY-MM-DD'),
            createTimeEnd: moment(endTime, 'YYYY-MM-DD'),
          }}
          labelCol={{ flex: '80px' }}
        >
          <Row gutter={15}>
            <Col span={8}>
              <Form.Item label="中文品名" name="cnProductNameForm">
                <Input placeholder="请输入中文品名" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="英文品名" name="enProductNameForm">
                <Input placeholder="请输入英文品名" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="海关编码" name="hsCodeForm">
                <Input placeholder="请输入海关编码" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={15}>
            <Col span={8}>
              <Form.Item
                label="SKU"
                name="sku"
                rules={[
                  {
                    validator: (_, value) => {
                      if (value) {
                        const lines = value.split('\n').filter(line => line.trim() !== '');
                        if (lines.length > 30) {
                          return Promise.reject(new Error('最多只能输入30个SKU'));
                        }
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <Input.TextArea
                  placeholder="请输入SKU，最多输入30个，每个一行"
                  style={{ width: '100%', height: '90px' }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Row gutter={0}>
                <Col span={24}>
                  <Form.Item label="规格型号" name="specificationForm">
                    <Input placeholder="请输入规格型号" />
                  </Form.Item>
                </Col>
                <Col span={24}>
                  <Form.Item label="申请时间">
                    <Space>
                      <Form.Item name="createTimeBegin" noStyle>
                        <DatePicker style={{ width: '210px' }} /> {/* 调整宽度 */}
                      </Form.Item>
                      <span>~</span>
                      <Form.Item name="createTimeEnd" noStyle>
                        <DatePicker style={{ width: '210px' }} /> {/* 调整宽度 */}
                      </Form.Item>
                    </Space>
                  </Form.Item>
                </Col>
              </Row>
            </Col>
            <Col span={8}>
              <Space>
                <Button type="primary" onClick={getDataList}>
                  查询
                </Button>
                <Button onClick={reset}>重置</Button>
              </Space>
            </Col>
          </Row>
        </Form>
      </Card>
      <Card
        tabList={tabList}
        activeTabKey={tabKey}
        onTabChange={handleTabChange}
        tabBarExtraContent={
          <Space>
            <Button onClick={() => importRecord()}>导入记录</Button>
            {changeTabBarButton()}
          </Space>
        }
        style={{ marginTop: 24 }}
        bordered={false}
      >
        <Table
          rowKey="id"
          rowSelection={tabKey == 0 || tabKey == 1 || tabKey == 3 ? null : rowSelection}
          pagination={pagination}
          loading={loading}
          scroll={{ x: 1500 }}
          columns={columns.filter(item => item.filterType === undefined)}
          dataSource={dataList}
        />
      </Card>
      <EditGoodsTable
        ref={editTableRef}
        editCurrenyList={editCurrenyList}
        onClose={initialize}
        {...props}
      />
      <ImportTable ref={importTableRef} {...props} />
    </>
  );
};

export default forwardRef(goodsRecordInformation);
