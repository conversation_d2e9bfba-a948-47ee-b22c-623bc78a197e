import React, { useState, useImperativeHandle, useRef } from 'react';
import { Modal, Space, Form, List, Button, message, Typography } from 'antd';
import { ProForm, ProFormInstance, ProFormSelect, ProFormRadio } from '@ant-design/pro-components';
import { useUpdateEffect } from 'ahooks';

function ProgramList(props: any) {
  const { programList, value, onChange } = props;

  return (
    <List
      dataSource={programList}
      renderItem={(item: any) => (
        <List.Item
          style={{ justifyContent: 'start' }}
          className="cursor-pointer"
          onClick={() => onChange?.(item.id)}
        >
          <div className="relative flex items-center justify-center w-5 h-5 mr-2">
            <div
              className={`w-5 h-5 border-2 rounded-full ${
                value === item.id ? 'border-green-500' : 'border-gray-300'
              }`}
            ></div>
            {value === item.id ? (
              <div className="absolute flex items-center justify-center w-5 h-5 bg-green-500 rounded-full">
                <svg
                  className="w-4 h-4 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={3}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
            ) : (
              <div className="absolute flex items-center justify-center w-5 h-5 bg-white rounded-full border border-gray-400 border-solid"></div>
            )}
          </div>
          <span>{item.programName}</span>
        </List.Item>
      )}
    />
  );
}

const tempProgramList = ['13', '4', '9', '8', '3'];

const TrackingNumberSubscriptionModal = props => {
  const { modalRef, dispatch, loading, productData, accountData, onRefresh } = props;
  const formRef = useRef<ProFormInstance>();
  const [open, setOpen] = useState(false);
  const [programList, setProgramList] = useState([]);
  const [paramsInfo, setParamsInfo] = useState<any>({});
  const [title, setTitle] = useState('新增');
  const [countriesData, setCountriesData] = useState([]);

  useImperativeHandle(modalRef, () => ({
    show,
  }));

  useUpdateEffect(() => {
    if (open) {
      if (paramsInfo?.id) {
        Promise.all([getCountryByProductCode(paramsInfo.productCode)]).then(([response]) => {
          const countryCodes = paramsInfo?.countryCodeList?.split(',') || [];
          const allCountryCodes = response.data?.map(item => item.countryId);
          // 判断是否包含所有国家
          const isAllCountries =
            allCountryCodes.length > 0 &&
            allCountryCodes.every(code => countryCodes.includes(code));
          formRef.current?.setFieldsValue({
            ...paramsInfo,
            countryCodeList: isAllCountries ? ['ALL', ...countryCodes] : countryCodes,
            accountCodeList: paramsInfo?.accountCode,
          });
        });
      }
    }
  }, [open, paramsInfo?.id]);

  const initialFunc = () =>
    new Promise((resolve, reject) => {
      dispatch({
        type: 'trajectorySubscription/queryProgramList',
        callback: response => {
          if (response.success) {
            setProgramList(response?.data?.filter(item => tempProgramList.includes(item.id)));
            resolve(response);
          } else {
            reject(response);
          }
        },
      });
    });

  const handleProductChange = value => {
    formRef.current.setFieldsValue({ ['countryCodeList']: undefined });
    getCountryByProductCode(value);
  };

  const getCountryByProductCode = channelId =>
    new Promise((resolve, reject) => {
      dispatch({
        type: 'business/getCountryListByProductCode',
        payload: { channelId: channelId },
        callback: response => {
          if (response.success) {
            setCountriesData(
              response.data?.map(item => ({
                ...item,
                label: item?.countryName,
                value: item?.countryId,
              }))
            );
            resolve(response);
          } else {
            reject(response);
          }
        },
      });
    });

  const show = (record?: any) => {
    Promise.all([initialFunc()]).then(() => {
      setOpen(true);
      if (record) {
        setTitle('编辑');
        setParamsInfo({ ...paramsInfo, ...record });
      }
    });
  };

  const reset = () => {
    formRef.current?.resetFields();
    setProgramList([]);
    setParamsInfo({});
    setTitle('新增');
  };

  const onCancel = () => {
    reset();
    setOpen(false);
  };

  const onFinish = (values: any) => {
    const params = {
      ...values,
      countryCodeList: values.countryCodeList?.filter(item => item !== 'ALL') || [],
    };
    if (title === '新增') {
      dispatch({
        type: 'trajectorySubscription/submitProductTrackSub',
        payload: params,
        callback: response => {
          if (response.success) {
            message.success(response.message);
            onRefresh();
            onCancel();
          }
        },
      });
    } else {
      const updateParams = {
        programId: values?.programId,
        id: paramsInfo?.id,
        countryCodeList: values.countryCodeList,
      };
      dispatch({
        type: 'trajectorySubscription/updateProductTrackSub',
        payload: updateParams,
        callback: response => {
          if (response.success) {
            message.success(response.message);
            onRefresh();
            onCancel();
          }
        },
      });
    }
  };

  return (
    <Modal title={`${title}产品订阅`} open={open} onCancel={onCancel} footer={null} width={500}>
      <ProForm
        formRef={formRef}
        labelAlign="left"
        layout="vertical"
        initialValues={{
          businessLine: '0',
          subscribeNode: '0',
        }}
        onFinish={onFinish}
        submitter={{
          render: () => {
            return [];
          },
        }}
      >
        <ProFormSelect
          name="businessLine"
          label="业务线 :"
          options={[{ label: '小包专线', value: '0' }]}
          disabled={true}
          fieldProps={{
            getPopupContainer: triggerNode => triggerNode.parentNode,
          }}
        />
        <ProFormSelect
          name="accountCodeList"
          label="制单账号 :"
          rules={[{ required: true, message: '请选择制单账号' }]}
          options={accountData?.map(item => ({
            label: item?.accountCode,
            value: item?.accountCode,
          }))}
          fieldProps={{
            mode: 'multiple',
            maxTagCount: 3,
            getPopupContainer: triggerNode => triggerNode.parentNode,
          }}
          disabled={title === '编辑'}
        />
        <ProFormSelect
          name="productCode"
          rules={[{ required: true, message: '请选择产品名称' }]}
          label="产品名称 :"
          options={productData}
          showSearch
          disabled={title === '编辑'}
          onChange={handleProductChange}
          fieldProps={{
            getPopupContainer: triggerNode => triggerNode.parentNode,
          }}
        />
        <ProFormSelect
          name="countryCodeList"
          label="目的国 :"
          rules={[{ required: true, message: '请选择目的国' }]}
          options={[
            {
              label: '全部国家',
              value: 'ALL',
              disabled:
                formRef.current?.getFieldValue('countryCodeList')?.length === countriesData.length,
            },
            ...countriesData,
          ]}
          fieldProps={{
            mode: 'multiple',
            maxTagCount: 3,
            getPopupContainer: triggerNode => triggerNode.parentNode,
            onChange: value => {
              const allValues = countriesData.map(item => item.value);
              // 如果选择了"全部"或者已经选择了所有国家
              // @ts-ignore
              if (value.includes('ALL') || value.length === allValues.length) {
                formRef.current?.setFieldsValue({
                  countryCodeList: ['ALL', ...allValues],
                });
              }
              // 如果取消选择了部分国家
              // @ts-ignore
              else if (value.length < allValues.length && value.includes('ALL')) {
                formRef.current?.setFieldsValue({
                  // @ts-ignore
                  countryCodeList: value.filter(v => v !== 'ALL'),
                });
              }
            },
          }}
          showSearch
        />
        <div className="ant-portal-no-form-bottom">
          <Typography.Paragraph>订阅节点 :</Typography.Paragraph>
        </div>
        <Space direction="vertical" size={2} style={{ color: 'red' }} className="mt-2">
          <span>温馨提示:</span>
          <span>1. 订阅完成后，将仅展示您订阅的节点，其他未订阅的节点将不展示。</span>
          <span>2. 订阅信息保存成功，即时生效。</span>
        </Space>
        {/* <ProFormText name="trackingNumber" label="TrackingNumber" /> */}
        <Form.Item name="programId" rules={[{ required: true, message: '请选择订阅节点' }]}>
          <ProgramList programList={programList} />
        </Form.Item>
        <div className="flex justify-center">
          <Space>
            <Button onClick={onCancel}>取消</Button>
            <Button type="primary" htmlType="submit" loading={loading}>
              保存
            </Button>
          </Space>
        </div>
      </ProForm>
    </Modal>
  );
};

export default TrackingNumberSubscriptionModal;
