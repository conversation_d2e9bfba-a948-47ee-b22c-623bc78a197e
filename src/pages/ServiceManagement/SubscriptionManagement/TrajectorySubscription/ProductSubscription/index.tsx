import { ProFormInstance } from '@ant-design/pro-components';
import { useMount, useUpdateEffect } from 'ahooks';
import { useRef, useState } from 'react';
import SearchComponent from './search';
import TablesComponents from './tables';
import moment from 'moment';
import React from 'react';

type TAccountDataInfo = USER_API.getOverseaAccountsData & {
  label?: string;
  value?: string;
};

type TAccountData = Array<TAccountDataInfo>;

const startTimeDate = moment()
  .subtract(6, 'months')
  .format('YYYY-MM-DD');
const endTimeDate = moment().format('YYYY-MM-DD');

const Index = (props: any) => {
  const { dispatch, productTableLoading } = props;
  const formRef = useRef<
    ProFormInstance<TRAJECTORY_SUBSCRIPTION.getProductTrackSubscriptionData>
  >();
  const [pageSize, setPageSize] = useState(10);
  const [current, setCurrent] = useState(1);
  const [total, setTotal] = useState(0);
  const [dataList, setDataList] = useState<
    TRAJECTORY_SUBSCRIPTION.getProductTrackSubscriptionResponse['list']
  >([]);
  const [tabActiveKey, setTabActiveKey] = useState('1');
  const [accountData, setAccountData] = useState<TAccountData>([]);
  const [productData, setProductData] = useState<TAccountData>([]);
  const [countriesData, setCountriesData] = useState<TAccountData>([]);
  useUpdateEffect(() => {
    getList();
  }, [pageSize, current, tabActiveKey]);

  useMount(() => {
    getInit();
  });

  const getInitFunc = async () =>
    new Promise((resolve, reject) => {
      dispatch({
        type: 'Order/getShippingAccount',
        payload: { scene: 2 },
        callback: response => {
          if (response.success) {
            const data = response.data?.map(item => ({
              ...item,
              label: `${item?.warehouseName}${item?.accountCode}`,
              value: item?.accountCode,
            }));
            setAccountData(data || []);
            resolve(data || []);
          } else {
            reject([]);
          }
        },
      });
    });

  const getCountriesName = () => {
    dispatch({
      type: 'Order/getCountriesName',
      callback: result => {
        if (result.success) {
          setCountriesData(
            result.data?.map(item => ({
              ...item,
              label: item?.nameCh,
              value: item?.id,
            }))
          );
        }
      },
    });
  };

  const getProductName = () => {
    let params = {
      userId: '',
      countryId: '',
    };
    dispatch({
      type: 'trajectorySubscription/getEjfProducts',
      payload: params,
      callback: result => {
        if (result.success) {
          const chinaPost = ['18', '25', '26'];
          setProductData(
            result.data
              ?.filter(
                x =>
                  chinaPost.indexOf(x.ywProductGenealogy) == -1 &&
                  !(x.ywProductGenealogy == '3' && x.ywProductGenealogyDetail == '8') &&
                  x.ywProductGenealogyDetail != '21' &&
                  x.isEjfEnabled == '1' &&
                  x.marketingMethod == '2'
              )
              .map(item => ({
                ...item,
                label: item?.productNumber + '-' + item?.productCnName,
                value: item?.productNumber,
              }))
          );
        }
      },
    });
  };

  const getInit = async () => {
    Promise.all([getInitFunc(), getCountriesName(), getProductName()]).then((res: any) => {
      formRef.current?.setFieldsValue({
        accountCodeList: res?.[0]?.map(item => item.accountCode),
        dateTime: [startTimeDate, endTimeDate],
      });
      getList();
    });
  };

  const getList = async () => {
    try {
      const values = await formRef.current?.validateFields();
      const params: Partial<typeof values> = {
        ...values,
        size: pageSize,
        current,
        // listNumber: values?.listNumber ? textWaybill(values?.listNumber, 500, true) : undefined,
        createTimeBegin:
          typeof values?.dateTime?.[0] === 'string'
            ? `${values?.dateTime?.[0]} 00:00:00`
            : values?.dateTime?.[0]?.format('YYYY-MM-DD 00:00:00') ?? undefined,
        createTimeEnd:
          typeof values?.dateTime?.[1] === 'string'
            ? `${values?.dateTime?.[1]} 23:59:59`
            : values?.dateTime?.[1]?.format('YYYY-MM-DD 23:59:59') ?? undefined,
      };
      dispatch({
        type: 'trajectorySubscription/listProductTrackSub',
        payload: params,
        callback: response => {
          if (response.success) {
            setDataList(response.data?.list ?? []);
            setTotal(response.data?.total ?? 0);
          } else {
            setDataList([]);
            setTotal(0);
          }
        },
      });
    } catch (error) {
      console.error(error);
    }
  };

  const resetPageSize = () => {
    if (current === 1 && pageSize === 10) {
      getList();
    } else {
      setCurrent(1);
      setPageSize(10);
    }
  };

  const pagination = {
    pageSize,
    current,
    total,
    showSizeChanger: true,
    pageSizeOptions: ['5', '10', '20', '50', '100'],
    onChange: (page: number, pageSize: number) => {
      setCurrent(page);
      setPageSize(pageSize);
    },
    showTotal: () => {
      return `共- ${total} -条记录`;
    },
  };

  const handleSearch = (values: TRAJECTORY_SUBSCRIPTION.getTrajectorySubscriptionData) => {
    if (current === 1 && pageSize === 10) {
      getList();
    } else {
      setCurrent(1);
      setPageSize(10);
    }
  };

  return (
    <>
      <SearchComponent
        formRef={formRef}
        {...props}
        accountData={accountData}
        productData={productData}
        onSearch={handleSearch}
        buttonLoading={productTableLoading}
        dateTime={[startTimeDate, endTimeDate]}
      />
      <TablesComponents
        {...props}
        tabActiveKey={tabActiveKey}
        setTabActiveKey={setTabActiveKey}
        pagination={pagination}
        data={dataList}
        resetPageSize={resetPageSize}
        tableLoading={productTableLoading}
        formRef={formRef}
        accountData={accountData}
        productData={productData}
        countriesData={countriesData}
      />
    </>
  );
};

export default Index;
