import React, { useState, useRef, useEffect } from 'react';
import PageContainerComponent from '@/components/PageContainer';
import PageHeaderBreadcrumb from '@/components/PageHeaderBreadcrumb';
import { Button, Input, message, Form } from 'antd';
import { connect } from 'dva';
import FormSearch from './components/FormSearch';
import Table from './components/Table';
import AddModal from './components/AddModal';
import _ from 'lodash';
import usePrevious from '@/hooks/usePrevious';
import { useMount } from 'ahooks';
import { formatMessage } from 'umi-plugin-react/locale';

const Index = props => {
  const { dispatch } = props;
  const [form] = Form.useForm();

  const tableRef = useRef<any>();
  const modalRef = useRef<any>();
  const [dataSource, setDataSource] = useState([]);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [pendingCount, setPendingCount] = useState(0); // 待处理
  const [processCount, setProcessCount] = useState(0); // 处理中
  const [endCount, setEndCount] = useState(0); // 已完结
  const [evaluatedCount, setEvaluatedCount] = useState(0); // 已评价
  const [cancelledCount, setCancelledCount] = useState(0); // 已撤销

  const [orderId, setOrderId] = useState(''); // 工单编号
  const [params, setParams] = useState({
    inquiryStatus: 0,
    beginTime: undefined,
    endTime: undefined,
  }); // 搜索条件

  const previous = usePrevious({ current, pageSize, params, orderId });

  useMount(() => {
    getList();
  });

  useEffect(() => {
    if (!previous) return;
    if (
      previous.current !== current ||
      previous.pageSize !== pageSize ||
      !_.isEqual(previous.params, params)
    ) {
      getList();
    }
  }, [params, current, pageSize, orderId]);

  // 新建申请
  const toAdd = () => {
    dispatch({
      type: 'user/getAuthorityStatus',
      payload: { type: 0 },
      callback: response => {
        if (response.success) {
          const key = `${response.data}`;
          if (key === '6') {
            // 先请求是否可以新增
            dispatch({
              type: 'feedback/getNumberId',
              payload: '10',
              callback: response => {
                if (response.success) {
                  modalRef.current?.showModal(response.data);
                }
              },
            });
          } else if (key === '1') {
            // 未实名认证
            message.error('请先实名认证');
          } else if (key === '5') {
            // 未实名认证
            message.error('此功能暂无法使用，请前往首页签署合同');
          } else {
            // 未开通小包服务
            message.error('您尚未开通小包业务线，请开通成功后请再进行此操作！');
          }
        }
      },
    });
  };

  // 搜索
  const handleFormSubmit = value => {
    if (value) {
      let word;
      word = value.replace(/(^\s*)|(\s*$)/g, '');
      word = value.replace(/[\s+|\t+|,]/g, '，');
      const reg = /[`~!@#$%^&*()_+=<>?:"{}|.\/;'\\[\]·~！@#￥%……&*（）——+={}|《》？：“”【】、；‘'。、]/im;
      const chineseReg = /^[^\u4E00-\u9FA5]+$/g;
      if (!chineseReg.test(word)) {
        message.error('运单号不能输入汉字');
        return;
      }
      if (reg.test(word)) {
        message.error('运单号不能输入特殊字符');
        return;
      }

      setOrderId(value);
      getList();
    } else {
      message.error('请填写运单编号');
    }
  };

  const mainSearch = (
    <Form.Item name="orderId">
      <Input.Search
        allowClear
        placeholder={formatMessage({ id: '请输入运单编号' })}
        enterButton={formatMessage({ id: '搜索' })}
        size="large"
        onSearch={handleFormSubmit}
        style={{ width: 522 }}
      />
    </Form.Item>
  );

  const paginationProps = {
    pageSize: pageSize,
    current: current,
    total: total,
    showSizeChanger: true,
    pageSizeOptions: ['10', '20', '50', '100', '500'],
    showTotal: () => {
      return `共- ${total} -条记录`;
    },
    style: { textAlign: 'center', display: 'block', width: '100%' },
    onChange: (page, pageSize) => {
      setCurrent(page);
      setPageSize(pageSize);
      //   this.setState({ PageIndex: page, pageSize }, () => this.gettableList(page, 'pages'));
    },
  };

  const handleChangeTab = value => {
    setCurrent(1);
    setPageSize(10);
    tableRef.current?.changeTabs(value);
  };

  const handleCancel = value => {
    if (value) {
      getList();
    }
  };

  // 重新发起或继续询问
  const handleRelaunch = (record, type) => {
    const params = {
      ...record,
      type,
    };

    modalRef.current?.showModal(params);
  };

  const getList = (values?: any) => {
    const valuesInfo = form.getFieldsValue();
    const paramsValue = values
      ? {
          ...values,
          current: current,
          size: pageSize,
          waybillNumber: valuesInfo?.orderId ?? '',
          sort: '3', // 投诉是1，建议是2，查件是3
        }
      : {
          ...params,
          current: current,
          size: pageSize,
          waybillNumber: valuesInfo?.orderId ?? '',
          sort: '3',
        };
    if (values !== undefined) {
      setParams({ ...values });
    } else {
      dispatch({
        type: 'feedback/search',
        payload: paramsValue,
        callback: response => {
          if (response.success) {
            setDataSource(response.data.list);
            setTotal(response.data.total);
            setPendingCount(response.data.processSaveCount);
            setProcessCount(response.data.processIngCount);
            setEndCount(response.data.processSuccessCount);
            setCancelledCount(response.data.clientCancelCount);
          } else {
            setDataSource([]);
            setTotal(0);
            setPendingCount(0);
            setProcessCount(0);
            setEndCount(0);
            setCancelledCount(0);
          }
        },
      });
    }
  };

  return (
    <PageContainerComponent
      header={{
        title: null,
        breadcrumb: {},
        breadcrumbRender: props => (
          <PageHeaderBreadcrumb {...props} col>
            <h2>
              <Button type="primary" style={{ float: 'right' }} onClick={() => toAdd()}>
                {formatMessage({ id: '新建查询' })}
              </Button>
            </h2>
            <Form form={form}>
              <div className="text-center mb-4">{mainSearch}</div>
            </Form>
          </PageHeaderBreadcrumb>
        ),
      }}
    >
      <FormSearch
        changeParamsFunc={getList}
        changeTab={handleChangeTab}
        pendingCount={pendingCount}
        processCount={processCount}
        endCount={endCount}
      />
      <Table
        {...props}
        tableRef={tableRef}
        paginationProps={paginationProps}
        dataSource={dataSource}
        getList={getList}
        relaunch={handleRelaunch}
      />
      <AddModal {...props} modalRef={modalRef} onCancel={handleCancel} />
    </PageContainerComponent>
  );
};

export default connect(({ loading }) => ({
  queryLoading: loading.effects['feedback/search'],
  saveLoading:
    loading.effects['feedback/create'] ||
    loading.effects['feedback/activate'] ||
    loading.effects['feedback/communication'],
}))(Index);
