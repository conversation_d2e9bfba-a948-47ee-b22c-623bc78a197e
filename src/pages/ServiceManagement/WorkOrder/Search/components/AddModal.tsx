import React, { useImperativeHandle, useState, useRef, useEffect } from 'react';
import { Button, Modal, Row, Space, message } from 'antd';
import {
  ProForm,
  ProFormTextArea,
  ProFormInstance,
  ProFormSelect,
} from '@ant-design/pro-components';
import { onFormErrorMessage } from '@/utils/utils';
import { queryReasonList } from '@/utils/commonConstant';

const AddModal = props => {
  const { modalRef, onCancel, dispatch, saveLoading } = props;

  const formRef = useRef<ProFormInstance<any>>();
  const [open, setOpen] = useState(false);
  // const [orderId, setOrderId] = useState<any>();
  const [record, setRecord] = useState<any>({
    orderId: undefined,
  });
  const [selectOther, setSelectOther] = useState(false);
  const [continueResend, setContinueResend] = useState(false);

  useImperativeHandle(modalRef, () => ({
    showModal: info => {
      setOpen(true);
      // setOrderId(orderId);
      setRecord({
        ...record,
        ...info,
      });
    },
  }));

  useEffect(() => {
    if (record.hasOwnProperty('type')) {
      setContinueResend(true);
      setTimeout(() => {
        formRef.current?.setFieldsValue({
          ...record,
          wayBillNo: record.waybillNo,
          queryReason: +record.queryReason,
        });
      }, 300);
    }
  }, [record]);

  const handleCancel = (value?: any) => {
    formRef.current?.resetFields();
    setOpen(false);
    onCancel(value);
  };

  const validateToNextPassword = (rule, value, callback) => {
    if (value === undefined) return callback();
    const str = value.replace(/[\r\n]/g, '');
    var counts = value.split('\n').length;
    let valrlurs = new RegExp('^[0-9a-zA-Z -]+$');
    if (counts > 30) {
      callback('最多输入30单');
    } else if (value && !valrlurs.test(str)) {
      callback('不能出现数字及字母外的字符');
    } else if (new Set(value.split('\n')).size !== counts) {
      callback('不能出现重复单号');
    } else {
      callback();
    }
  };

  const handleSubmit = async values => {
    if (record.hasOwnProperty('type')) {
      let para = {
        orderId: record.orderId,
        wayBillNo: values.wayBillNo,
        queryReason: values.queryReason,
        remark: values.remark,
      };
      if (record.type === 'keep') {
        // 继续询问
        dispatch({
          type: 'feedback/communication',
          payload: para,
          callback: result => {
            if (result.success) {
              message.success(result.message);
              handleCancel(result.success);
            }
          },
        });
      } else {
        //重新发起
        dispatch({
          type: 'feedback/activate',
          payload: para,
          callback: result => {
            if (result.success) {
              message.success(result.message);
              handleCancel(result.success);
            }
          },
        });
      }
    } else {
      // 新增查件
      let para = {
        orderId: record.orderId,
        wayBillNoList: Array.from(new Set(values.wayBillNo.split(/[\r\n]/g))).filter(
          item => item !== ''
        ),
        queryReason: values.queryReason,
        remark: values.remark,
      };
      dispatch({
        type: 'feedback/create',
        payload: para,
        callback: result => {
          if (result.success) {
            message.success(result.message);
            handleCancel(result.success);
          } else {
            message.error(
              result.message.includes('以下运单未结案')
                ? `${result.message.split(';')?.[0]},若有疑问请联系客服处理`
                : result.message
            );
          }
        },
      });
    }
  };

  return (
    <Modal
      title={continueResend ? '修改查件' : '新建查件'}
      open={open}
      onCancel={() => handleCancel()}
      footer={null}
    >
      <ProForm
        formRef={formRef}
        layout="horizontal"
        labelAlign="right"
        labelCol={{ flex: '100px' }}
        onFinish={handleSubmit}
        onFinishFailed={onFormErrorMessage}
        submitter={{
          render: (_, dom) => null,
        }}
      >
        <ProFormTextArea
          label="运单号"
          name="wayBillNo"
          placeholder={'最多30单,每单一行'}
          disabled={continueResend}
          rules={[
            {
              required: true,
              message: '运单号不能为空',
            },
            {
              validator: validateToNextPassword,
            },
          ]}
          fieldProps={{
            style: { width: '260px', display: 'inline-block', height: '120px' },
          }}
        />
        <ProFormSelect
          label="查件原因"
          name="queryReason"
          disabled={continueResend}
          rules={[{ required: true, message: '请选择原因' }]}
          placeholder={'请选择原因'}
          options={queryReasonList}
          fieldProps={{
            onChange: value => {
              setSelectOther(value === 11);
            },
          }}
        />
        <ProFormTextArea
          label="备注"
          name="remark"
          rules={[{ required: selectOther, message: '投诉内容不能为空' }]}
          placeholder={'请补充您的查件原因'}
          fieldProps={{
            rows: 4,
          }}
        />
        <Row justify="center">
          <Space>
            <Button onClick={() => handleCancel()}>取消</Button>
            <Button type="primary" htmlType="submit" loading={saveLoading}>
              提交
            </Button>
          </Space>
        </Row>
      </ProForm>
    </Modal>
  );
};

export default AddModal;
