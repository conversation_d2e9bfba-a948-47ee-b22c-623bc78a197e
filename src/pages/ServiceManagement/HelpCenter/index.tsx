import React, { useState } from 'react';
import { connect } from 'dva';
import { useMount } from 'ahooks';
import ProSkeleton from '@ant-design/pro-skeleton';

const Index = props => {
  const { dispatch, pageLoading } = props;
  const [url, setUrl] = useState(null);

  useMount(() => {
    initialFunc();
  });

  const initialFunc = () => {
    const formData = new FormData();
    handUrlParam(formData);
    window.onresize = () => {
      changeFrameHeight();
    };
    dispatch({
      type: 'user/getHelpUrl',
      payload: formData,
      callback: response => {
        if (response.success) {
          setUrl(response.data);
        }
      },
    });
  };

  const handUrlParam = formData => {
    let params = new URLSearchParams(props.location.search);
    if (params.toString() !== '') {
      const paramKeys = [];
      params.forEach((value, key) => {
        formData.append(key, value);
      });
      // 删除所有参数
      paramKeys.forEach(key => {
        params.delete(key);
      });

      // 更新 URL，但不刷新页面
      props.history.replace({
        pathname: props.location.pathname,
      });
    }
  };

  const changeFrameHeight = () => {
    let iframe = window.document.getElementById('myiframe') as any;
    if (iframe) {
      iframe.height = `${window.document.documentElement.clientHeight - 250}px`;
    }
  };

  return (
    <>
      {pageLoading ? (
        <ProSkeleton type="descriptions" />
      ) : (
        <iframe
          id="myiframe"
          width="100%"
          height="700px"
          scrolling="auto"
          src={url}
          frameBorder="0"
          onLoad={changeFrameHeight}
        />
      )}
    </>
  );
};

export default connect(({ loading }) => ({
  pageLoading: loading.effects['user/getHelpUrl'],
}))(Index);
