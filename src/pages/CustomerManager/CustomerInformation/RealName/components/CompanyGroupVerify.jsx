import ModalImage from '@/components/ModalImage';
import { dateLongCheck } from '@/utils/commonConstant';
import {
  changeBeforeUpload,
  disabledDate,
  getBase64,
  onFormErrorMessage,
  replaceBase64Str,
} from '@/utils/utils';
import {
  ProForm,
  ProFormCheckbox,
  ProFormDatePicker,
  ProFormGroup,
  ProFormRadio,
  ProFormText,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import { Alert, Button, Col, Row, Space, Typography, message } from 'antd';
import moment from 'moment';
import React, { useEffect, useRef, useState } from 'react';
import { router } from 'umi';
import { useMount } from 'ahooks';
import { formatMessage } from 'umi-plugin-react/locale';

const setUploadImage = url => {
  return url
    ? [
        {
          uid: '-1',
          name: 'image.png',
          status: 'done',
          url: url,
        },
      ]
    : undefined;
};

/*
 *@Description: 组织信息校验
 *@MethodAuthor: dangh
 *@Date: 2023-03-02 17:05:33
 */
const CompanyGroupVerify = props => {
  const {
    customerInfo,
    dispatch,
    saveCompanyApplyLoading,
    onOk,
    handleGoBack,
    handleReAuth,
  } = props;
  const formRef = useRef();
  const [checkDate, setCheckDate] = useState(true); // 有效期是否必填
  const [previewOpen, setPreviewOpen] = useState(false); // 是否显示打开链接
  const [previewImage, setPreviewImage] = useState(); // 打开链接
  const [agentIsLegal, setAgentIsLegal] = useState(); // 办理人是否为法人

  useMount(() => {
    getCustomerInfoFunc();
  });

  const getCustomerInfoFunc = () => {
    dispatch({
      type: 'realNameAuth/getCustomerAuthInfo',
      callback: response => {
        if (response.success) {
          const customerInfo = response.data;
          formRef.current?.setFieldsValue({
            cardAttach: setUploadImage(customerInfo?.cardAttach),
            companyName: customerInfo?.companyName,
            registerNumber: customerInfo?.registerNumber,
            agentIsLegal: customerInfo?.agentIsLegal,
            legalName: customerInfo?.corporateName,
            corporateCard: customerInfo?.corporateCard,
            corporatePhone: customerInfo?.corporatePhone,
            certificateValidity:
              customerInfo?.cardValidityPeriod && customerInfo?.cardValidityPeriod !== dateLongCheck
                ? moment(customerInfo?.cardValidityPeriod, 'YYYY-MM-DD')
                : undefined,
            checkbox: customerInfo?.cardValidityPeriod === dateLongCheck ? ['长期'] : undefined,
          });
          setCheckDate(!(customerInfo?.cardValidityPeriod === dateLongCheck));
        }
      },
    });
  };

  // 获取图片orc识别
  const ocrPicInfo = (value, type) =>
    new Promise(async (resolve, reject) => {
      const picInfo = { companyPic: await replaceBase64Str(value) };

      dispatch({
        type: 'realNameAuth/attachOcr',
        payload: {
          type: '1',
          ...picInfo,
        },
        callback: response => {
          if (response.success) {
            const data = response.data || {};
            if (data?.hasOwnProperty('validityPeriod')) {
              let params = {};
              // response.data?.validityPeriod?.split('-')?.[1]
              const certificateValidity = data?.validityPeriod?.includes('-')
                ? data?.validityPeriod?.split('-')?.[1]
                : data?.validityPeriod?.split('至')?.[1];

              if (customerInfo?.applyType == 3 || customerInfo?.applyType == 4) {
                params = {
                  checkbox: certificateValidity === '长期' ? ['长期'] : [],
                  certificateValidity:
                    response.data?.validityPeriod && certificateValidity !== '长期'
                      ? moment(certificateValidity, 'YYYY-MM-DD')
                      : undefined,
                  legalName: response.data?.personName,
                  registerNumber: response.data?.cardNumber,
                };
              } else {
                params = {
                  checkbox: certificateValidity === '长期' ? ['长期'] : [],
                  certificateValidity:
                    response.data?.validityPeriod && certificateValidity !== '长期'
                      ? moment(certificateValidity, 'YYYY-MM-DD')
                      : undefined,
                  legalName: response.data?.personName,
                  registerNumber: response.data?.cardNumber,
                  companyName: response.data?.companyName,
                };
              }
              formRef.current?.setFieldsValue({
                ...params,
              });
              formRef.current?.resetFields(['corporateCard']);
              setCheckDate(!(certificateValidity === '长期'));
            }
            resolve(true);
          } else {
            resolve(false);
            message.error('营业执照识别失败，请检查重新上传');
          }
        },
      });
    });

  // 上传图片后回调处理
  const handleOnChange = (info, type) => {
    if (info.file.status === 'uploading') {
      return;
    }
    if (info.file.status === 'done') {
      // Get this url from response in real world.
      if (info.file.response.success) {
        const uploadList = formRef.current?.getFieldValue([type]);
        const url = info.file.response.data;
        const data = uploadList.map(item => {
          item.url = url;
          return item;
        });
        formRef.current?.setFieldsValue({
          [type]: data,
        });
      } else {
        formRef.current?.resetFields([type]);
        message.error(info.file.response.message);
      }
    }
  };

  // 如果props没有办理人信息时，需要重新调接口
  const getAgentInfo = () => {
    dispatch({
      type: 'realNameAuth/getCustomerAuthInfo',
      callback: response => {
        if (response.success) {
          formRef.current?.setFieldsValue({
            legalName: response.data?.personName,
            corporateCard: response.data?.personCard,
          });
        }
      },
    });
  };

  const handleSubmit = values => {
    const params = {
      ...values,
      cardAttach: values?.cardAttach ? values?.cardAttach[0]?.url?.split('?')[0] : undefined,
      certificateValidity: !checkDate
        ? moment(dateLongCheck).format('YYYY-MM-DD 00:00:00')
        : moment(values?.certificateValidity).format('YYYY-MM-DD 00:00:00'),
      audit: false,
      // agentIsLegal: false,
    };
    if (values?.certificateValidity && moment(values?.certificateValidity).isBefore(moment()))
      return message.error('证件有效截止日期不能小于当前日期');
    dispatch({
      type: 'realNameAuth/saveCompanyApply',
      payload: params,
      callback: response => {
        if (response.success) {
          message.success(response.message);
          if (response?.data === 3) {
            router.push({
              pathname: '/homePageList',
              state: {
                auth: true,
              },
            });
          } else {
            onOk(2);
          }
        }
      },
    });
  };

  const flexPx = '200px';

  return (
    <>
      <Row justify="center" className="mt-10">
        <Col xxl={12} xl={20}>
          <Alert
            message={`${formatMessage({ id: '企业认证仅用于进行实名认证' })}，${formatMessage({
              id: '不会泄露您的信息',
            })}，${formatMessage({ id: '认证过程中企业名称' })}、${formatMessage({
              id: '营业执照号不允许修改',
            })}，${formatMessage({ id: '如需修改请联系销售经理' })}/${formatMessage({
              id: '客服处理',
            })}`}
            type="success"
          />
        </Col>
      </Row>
      <Row justify="center" className="mt-10">
        <Col xxl={10} xl={18}>
          <ProForm
            formRef={formRef}
            layout="horizontal"
            labelAlign="right"
            labelCol={{ flex: flexPx }}
            onFinish={handleSubmit}
            onFinishFailed={onFormErrorMessage}
            submitter={{
              render: (_, dom) => null,
            }}
          >
            <ProFormUploadButton
              name="cardAttach"
              max={1}
              fieldProps={{
                name: 'attach',
                listType: 'picture-card',
                data: {
                  objName: 'auditList',
                  singleFile: true,
                  fileType: '4',
                },
                beforeUpload: file =>
                  changeBeforeUpload(
                    file,
                    5,
                    ['image/jpeg', 'image/png'],
                    '请使用jpg/jpeg/png格式、不超过5M大小的图片，保证清晰',
                    undefined,
                    false,
                    file =>
                      new Promise(async (resolve, reject) => {
                        const result = await ocrPicInfo(file, 'cardAttach');
                        resolve(result);
                      })
                  ),
                onChange: info => handleOnChange(info, 'cardAttach'),
                onPreview: async file => {
                  if (!file.url && !file.preview) {
                    file.preview = await getBase64(file.originFileObj);
                  }
                  setPreviewImage(file.url || file.preview);
                  setPreviewOpen(true);
                },
              }}
              label={formatMessage({ id: '营业执照证件照' })}
              title="上传照片"
              action="/csc/file/upload/attachment"
              rules={[
                {
                  required: true,
                  message: formatMessage({ id: '请上传证件正面照' }),
                },
              ]}
              extra={
                <Typography.Text className="pl-4">
                  {formatMessage({ id: '证件正面照' })}
                </Typography.Text>
              }
            />
            <ProFormText
              name="companyName"
              label={formatMessage({ id: '企业名称' })}
              placeholder={`${formatMessage({
                id: '需要与营业执照企业名称保持一致',
              })}，${formatMessage({ id: '注意半全角括号区分' })}`}
              rules={[
                {
                  required: true,
                  message: formatMessage({ id: '请输入企业名称' }),
                },
                {
                  pattern: /^(?:[\u4e00-\u9fa5a-zA-Z]+(?:（[\u4e00-\u9fa5]+）)?)+$/,
                  message: formatMessage({ id: '企业名称格式不正确' }),
                },
              ]}
              width="md"
              disabled={customerInfo?.applyType === 3}
            />
            <ProFormText
              name="registerNumber"
              label={formatMessage({ id: '营业执照' })}
              width="md"
              placeholder={`${formatMessage({ id: '请输入' })}91${formatMessage({
                id: '开头的',
              })}18${formatMessage({ id: '位统一社会信用代码或工商注册号' })}`}
              rules={[
                {
                  required: true,
                  message: formatMessage({ id: '请输入营业执照' }),
                },
              ]}
            />
            <ProFormGroup>
              <ProFormDatePicker
                width="md"
                name="certificateValidity"
                label={formatMessage({ id: '营业执照证件有效截止日期' })}
                disabled={!checkDate}
                fieldProps={{
                  disabledDate: disabledDate,
                }}
                rules={[
                  {
                    required: checkDate,
                    message: formatMessage({ id: '请选择营业执照证件有效截止日期' }),
                  },
                ]}
              />
              <ProFormCheckbox.Group
                name="checkbox"
                layout="horizontal"
                options={[formatMessage({ id: '长期' })]}
                fieldProps={{
                  onChange: e => {
                    setCheckDate(!e[0]);
                    formRef.current?.resetFields(['certificateValidity']);
                  },
                }}
              />
            </ProFormGroup>
            <div
              style={{
                paddingLeft: flexPx,
                marginTop: '-4px',
                marginBottom: '24px',
              }}
            >
              <Typography.Text
                style={{
                  padding: '8px 89px 8px 8px',
                  background: '#e7f4ff',
                  border: '1px solid #c0e1ff',
                  borderRadius: '5px',
                }}
              >
                {formatMessage({ id: '温馨提示' })}：
                {formatMessage({ id: '有效截止日期需与营业执照证件一致' })}
              </Typography.Text>
            </div>
            <ProFormRadio.Group
              name="agentIsLegal"
              label={formatMessage({ id: '办理人是否为法人' })}
              options={[
                { label: formatMessage({ id: '是' }), value: true },
                { label: formatMessage({ id: '否' }), value: false },
              ]}
              rules={[
                {
                  required: true,
                  message: formatMessage({ id: '请选择办理人是否为法人' }),
                },
              ]}
              fieldProps={{
                onChange: e => {
                  setAgentIsLegal(e.target.value);
                  if (e.target.value) {
                    getAgentInfo();
                  } else {
                    formRef.current?.resetFields(['legalName', 'corporateCard']);
                  }
                },
              }}
            />
            <ProFormText
              name="legalName"
              label={formatMessage({ id: '法人姓名' })}
              placeholder={formatMessage({ id: '请输入法定代表人姓名' })}
              width="md"
              rules={[
                {
                  required: true,
                  message: formatMessage({ id: '请输入法定代表人姓名' }),
                },
                {
                  whitespace: true,
                  message: formatMessage({ id: '不能输入空格' }),
                },
                // {
                //   pattern: /^[\u4e00-\u9fa5]{2,15}$/,
                //   message: '仅支持填写2~15位的的汉字填写',
                // },
              ]}
              disabled={agentIsLegal}
            />
            <ProFormText
              name="corporateCard"
              label={formatMessage({ id: '法人证件号' })}
              placeholder={formatMessage({ id: '请输入法人证件号' })}
              width="md"
              rules={[
                {
                  required: true,
                  message: formatMessage({ id: '请输入法人证件号' }),
                },
                {
                  whitespace: true,
                  message: formatMessage({ id: '不能输入空格' }),
                },
                {
                  pattern: /^[A-Za-z0-9]{0,18}$/,
                  message: formatMessage({ id: '仅支持数字和字母填写小于等于18位' }),
                },
              ]}
              disabled={agentIsLegal}
            />
            <ProFormText
              name="corporatePhone"
              label={formatMessage({ id: '法人手机号' })}
              placeholder={formatMessage({ id: '请输入法人手机号' })}
              width="md"
              rules={[
                {
                  required: true,
                  message: formatMessage({ id: '请输入法人手机号' }),
                },
                {
                  whitespace: true,
                  message: formatMessage({ id: '不能输入空格' }),
                },
                {
                  pattern: /^1\d{10}$/,
                  message: formatMessage({ id: '请输入正确手机号' }),
                },
              ]}
              // disabled={agentIsLegal}
            />

            <Row justify="center">
              <Space>
                {customerInfo?.applyType !== 3 && (
                  <Button onClick={handleReAuth}>
                    {formatMessage({ id: '重新选择用户类型' })}
                  </Button>
                )}
                <Button onClick={() => handleGoBack(0)}>上一步</Button>
                <Button type="primary" htmlType="submit" loading={saveCompanyApplyLoading}>
                  {formatMessage({ id: '提交认证' })}
                </Button>
              </Space>
            </Row>
          </ProForm>
        </Col>
      </Row>
      {previewOpen && (
        <ModalImage open={previewOpen} url={previewImage} onCancel={() => setPreviewOpen(false)} />
      )}
    </>
  );
};

export default CompanyGroupVerify;
