import React, { useState, useRef, useEffect } from 'react';
import { Col, Row, Button, Modal } from 'antd';
import { PhoneOutlined } from '@ant-design/icons';
import HomePageCard from './components/HomePageCard';
import HomePageNotice from './components/HomePageNotice';
import HomePageFreightTrack from './components/HomePageFreightTrack';
import HomePageModal from './components/HomePageModal';
import HomePageStartService from './components/HomePageStartService';
import HomePageSmallPacket from './components/HomePageSmallPacket';
import HomePageFBAOpen from './components/HomePageFBAOpen';
import HomePageCNWarehouse from './components/HomePageCNWarehouse';
import HomePageOverseasWare from './components/HomePageOverseasWare';
import HomePageMessageModal from './components/HomePageMessageModal';
import HomePageUserCard from './components/HomePageUserCard';
import HomePageQuickEntrance from './components/HomePageQuickEntrance';
import HomePageFareCalculation from './components/HomePageFareCalculation';
import QRcode from '@/assets/QRcode.png';
import './homePage.less';
import { router } from 'umi';
import { useMount, useUpdateEffect } from 'ahooks';
import { connect } from 'dva';
import PageLoading from '@/components/PageLoading';
import useSingleSignOn from '@/hooks/useSingleSignOn';
import useOpenBusinessStatusDispatch from '@/hooks/useOpenBusinessStatusDispatch';
import { initializeUdeskChat } from '@/utils/robot';

/**
 * 说明：首页
 * 创建人：Lsy
 * 创建时间：2023-02-20
 */
const HomePage = props => {
  const { location, dispatch, pageLoading, homePage, isTourOpen, isTourBillOpen } = props;

  const messageModalRef = useRef();
  const cardRef = useRef();
  const [isModalOpen, setIsModalOpen] = useState(false); // 提醒认证框开关状态
  const [state, setState] = useState(0); // 判定modal-footer展示和标题/内容
  const [startServiceOpen, setStartServiceOpen] = useState(false); // 开通业务弹窗开关状态
  const [smallPacketOpen, setSmallPacketOpen] = useState(false); // 小包专线弹窗开关状态
  const [fbaOpen, setFBAOpen] = useState(false); // FBA专线开通弹框
  const [CNwarehouseOpen, setCNwarehouseOpen] = useState(false); // 中国仓开通弹框
  const [overseasWareOpen, setOverseasWareOpen] = useState(false); // F海外仓开通弹框
  const [completeAttachMessage, setCompleteAttachMessage] = useState('');

  useMount(() => {
    init();
  });

  useEffect(() => {
    if (homePage.smallBagStatus) {
      openService('0');
    } else if (homePage.fbaStatus) {
      openService('1');
    } else if (homePage.overseasStatus) {
      openService('2');
    }
  }, [homePage]);

  useUpdateEffect(() => {
    if (!isTourOpen) {
      init();
    }
  }, [isTourOpen]);

  useUpdateEffect(() => {
    if (!isTourBillOpen) {
      if (localStorage.getItem('openMessageModal') === null) {
        messageModalRef.current?.showModal();
      }
    }
  }, [isTourBillOpen]);

  // 初始化
  const init = () => {
    if (location?.query?.type == '5') {
      Modal.success({
        content: '已签署完成，请等待销售进一步处理。',
      });
    }
    if (!isTourOpen) {
      getIMParam();
      // 1.获取是否有小包账号
      // 2.如果有就去请求是否点击了账单余额
      // 3.如果查看过了就弹出消息弹窗
      getCustomerAuthStatus(async () => {
        const smallBag = await useOpenBusinessStatusDispatch(dispatch, 0);
        const overseas = await useOpenBusinessStatusDispatch(dispatch, 2);
        const typeStatus = JSON.parse(sessionStorage.getItem('USER_ACTION_BUSINESS'));
        const PATHNAME = sessionStorage.getItem('PATHNAME');
        // 海外派处理未开通的情况，开通的情况直接跳转页面
        if (!overseas?.status) {
          if (PATHNAME?.includes('overseas')) {
            if (overseas?.key === '5') {
              cardRef.current?.checkIsNeedRealName('2');
            } else {
              changeOpenService('2');
            }
          }
        }

        if (smallBag?.status) {
          doYouNeedToShow(() => {
            if (localStorage.getItem('openMessageModal') === null) {
              messageModalRef.current?.showModal();
            }
            /**
             * 判断是否是从小包专线跳转过来的
             */
            if (typeStatus?.smallBag) {
              changeOpenService('0');
            } else if (typeStatus?.fba) {
              changeOpenService('1');
            }
          });
        }
      });
    }
  };

  const doYouNeedToShow = callback => {
    dispatch({
      type: 'user/doYouNeedToShow',
      payload: { type: 'bill' },
      callback: response => {
        if (response.success) {
          if (!response?.data) {
            if (callback) callback();
          }
        }
      },
    });
  };

  // 判断页面是否是从认证成功后跳转过来
  const isAuth = () => {
    if (location.state?.auth) {
      // setStartServiceOpen(true);
    } else {
      setStartServiceOpen(false);
    }
  };

  // 点击开通业务
  const getCustomerStartService = callback => {
    dispatch({
      type: 'realNameAuth/getCustomerAuthStatus',
      callback: response => {
        if (response.success) {
          if (response.data === '0') {
            // 认证成功
            setIsModalOpen(false);
            isAuth();
            if (callback) callback();
          } else if (response.data === '11') {
            setState(11);
            setIsModalOpen(true);
          } else {
            setIsModalOpen(true);
          }
        }
      },
    });
  };

  const getCustomerAuthStatus = callback => {
    dispatch({
      type: 'homePage/getCustomerIndexAuthState',
      callback: response => {
        if (response.success) {
          const dataType = response.data?.authState;
          const unLimitTime = response.data?.unLimitTime;
          const changTypeName = response.data?.changTypeName;
          if (dataType === 1) {
            // 认证成功
            setIsModalOpen(false);
            isAuth();
            dispatch({
              type: 'homePage/checkUnLimitNotice',
              callback: result => {
                if (result.success && result.data.ifNotice && result.data.unLimitDate) {
                  Modal.info({
                    title: '温馨提示',
                    icon: null,
                    content: `您的小包业务处于【补签合同】流程中，请在${result.data.unLimitDate}前完成合同签署，否则将影响您的制单！`,
                    maskClosable: true,
                  });
                }
              },
            });
            if (callback) callback();
          } else if (dataType === 0) {
            // 新商户
            setIsModalOpen(true);
            setState(0);
          } else if (dataType === 2) {
            if (response.data?.completeAttachMessage) {
              setCompleteAttachMessage(response.data?.completeAttachMessage);
            }
            //补齐证件照
            setState(11);
            setIsModalOpen(true);
          } else if (dataType === 3) {
            //补齐资料 限制流程
            Modal.confirm({
              title: '温馨提示',
              icon: null,
              content: `您当前正处于【${
                changTypeName ? changTypeName : '补齐资料'
              }】流程中，如需使用更多功能，请在完成认证后操作！`,
              okText: '立即前往',
              cancelText: '暂不处理',
              onOk: () => {
                router.push('/customerManager/customerInformation/realName');
              },
            });
          } else if (dataType === 4) {
            // 补齐资料 不限制
            Modal.confirm({
              title: '温馨提示',
              icon: null,
              content: `您当前的认证信息不完善，请在${unLimitTime}前完成【${
                changTypeName ? changTypeName : '补齐资料'
              }】，否则将影响您的制单！`,
              okText: '立即前往',
              cancelText: '暂不处理',
              onOk: () => {
                router.push('/customerManager/customerInformation/realName');
              },
            });
          }
        }
      },
    });
  };

  // 获取im客户数据
  const getIMParam = () => {
    dispatch({
      type: 'homePage/getIMParam',
      callback: result => {
        if (result.success) {
          let Merchants = localStorage.getItem('Merchants');
          // 商户信息不一致，清除EJF订单查询缓存所需的数据
          if (Merchants && Merchants != result.data.c_cf_商户号) {
            localStorage.removeItem('productData');
            localStorage.removeItem('countriesData');
            localStorage.removeItem('tableData');
            localStorage.removeItem('deliveryAccount');
            localStorage.removeItem('miscellaneous');
            localStorage.removeItem('number');
          }
          localStorage.setItem('Merchants', result.data.c_cf_商户号);
        }
      },
    });
  };

  // modal控制显示/不显示
  const handleOk = state => {
    setIsModalOpen(false);
    router.push('/customerManager/customerInformation/realName');
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    if (localStorage.getItem('openMessageModal') === null) {
      messageModalRef.current?.showModal();
    }
  };

  // 开通业务弹窗关闭
  const handleCancelStartService = type => {
    switch (type) {
      case 0: //小包专线
        setSmallPacketOpen(true);
        break;
      case 1: // FBA业务
        setFBAOpen(true);
        break;
      case 2: //海外仓
        setOverseasWareOpen(true);
        break;
      case 3: // 中国仓
        setCNwarehouseOpen(true);
        break;
    }
    setStartServiceOpen(false);
  };

  const changeOpenService = value => {
    switch (value) {
      case '0': //小包专线
        setSmallPacketOpen(true);
        break;
      case '1': // FBA业务
        setFBAOpen(true);
        break;
      case '2': //海外派
        setOverseasWareOpen(true);
        break;
      case '3': // 中国仓
        // setCNwarehouseOpen(true);
        useSingleSignOn(dispatch);
        break;
    }
  };

  // 组件点击开通业务
  const openService = value => {
    getCustomerStartService(() => changeOpenService(value));
  };

  // Card组件已认证点击查看进度
  const onClickMakOrOpen = status => {
    // 查看进度
    if (status === 2) {
      setState(3);
      setIsModalOpen(true);
    }
  };

  const handleCancelStatusOpen = () => {
    dispatch({
      type: 'homePage/handleOpenStatus',
      payload: {
        smallBagStatus: false, // 开通小包专线状态
        fbaStatus: false, // 开通FBA专线状态
        overseasStatus: false, // 开通海外派业务状态
      },
    });
  };

  return (
    <>
      <div className="2xl:px-6 py-4 xl:py-5 px-4">
        <Row gutter={0}>
          <Col span={20}>
            <HomePageCard
              modalRef={cardRef}
              onOpenService={openService}
              onClickMakOrOpen={onClickMakOrOpen}
              {...props}
            />
            <HomePageFareCalculation {...props} />
            {/* 通知公告 Start */}
            <Col
              style={{
                borderRadius: 6,
                marginTop: '13px',
                paddingInline: '10px',
              }}
            >
              <HomePageNotice {...props} />
            </Col>
            {/* 通知公告 end */}
          </Col>
          <Col span={4}>
            <HomePageUserCard {...props} />
            <Col style={{ marginTop: '20px', paddingInline: 0 }}>
              <HomePageQuickEntrance {...props} />
            </Col>
          </Col>
        </Row>

        {/* Card End */}
        {false && (
          <Row style={{ marginTop: '20px' }}>
            {/* 通知公告 Start */}
            <Col span={12} style={{ padding: '0 10px' }}>
              <Col style={{ height: 390, background: '#fff', borderRadius: 6 }}>
                <HomePageNotice {...props} />
              </Col>
            </Col>
            {/* 通知公告 End */}
            <Col span={12} style={{ padding: '0 10px' }}>
              {/* 运费试算/轨迹查询 Start */}
              <Col
                span={24}
                style={{ height: 264, background: '#fff', borderRadius: 6, marginBottom: 22 }}
              >
                <HomePageFreightTrack />
              </Col>
              {/* 运费试算/轨迹查询 End */}
              {/* 服务热线/公众号 Start */}
              <Col style={{ display: 'flex' }}>
                <Col span={12} style={{ paddingRight: 10 }}>
                  <Col
                    span={24}
                    style={{
                      display: 'flex',
                      background: '#fff',
                      height: 104,
                      borderRadius: 6,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <PhoneOutlined
                      style={{ fontSize: 34, color: '#b0b0b0', marginRight: 10 }}
                      rotate={90}
                    />
                    <div className="hotlileService">
                      <div className="hotlileService-title">服务热线</div>
                      <div
                        className="hotlileService-phone"
                        style={{ fontSize: 20, fontWeight: 600, color: '#707070' }}
                      >
                        ************
                      </div>
                    </div>
                  </Col>
                </Col>
                <Col span={12} style={{ paddingLeft: 10 }}>
                  <Col
                    span={24}
                    style={{
                      display: 'flex',
                      background: '#fff',
                      height: 104,
                      borderRadius: 6,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <div style={{ textAlign: 'center', marginRight: 10 }}>
                      <p style={{ margin: 0 }}>关注</p>
                      <p style={{ margin: 0 }}>"燕文物流"</p>
                      <p style={{ margin: 0 }}>服务号</p>
                    </div>
                    <img src={QRcode}></img>
                  </Col>
                </Col>
              </Col>
              {/* 服务热线/公众号 End */}
            </Col>
          </Row>
        )}
        {/* 认证弹框 Start */}
        {isModalOpen && (
          <HomePageModal
            isModalOpen={isModalOpen}
            handleOk={handleOk}
            handleCancel={handleCancel}
            state={state}
            completeAttachMessage={completeAttachMessage}
          />
        )}
        {/* 认证弹框 End */}
        {/* 开通业务弹框 Start */}
        {startServiceOpen && (
          <HomePageStartService open={startServiceOpen} onCancel={handleCancelStartService} />
        )}
        {/* 开通业务弹框 End */}
        {/* 小包专线弹框 Start */}
        {smallPacketOpen && (
          <HomePageSmallPacket
            {...props}
            open={smallPacketOpen}
            onCancel={value => {
              setSmallPacketOpen(false);
              handleCancelStatusOpen();
              if (value) {
                window.location.reload();
              }
            }}
          />
        )}
        {/* 小包专线弹框 End */}
        {/* FBA专线开通弹框 Start */}
        {fbaOpen && (
          <HomePageFBAOpen
            {...props}
            open={fbaOpen}
            onCancel={value => {
              setFBAOpen(false);
              handleCancelStatusOpen();
              if (value) {
                window.location.reload();
              }
            }}
          />
        )}
        {/* FBA专线开通弹框 End */}
        {/* 海外仓开通弹框 Start */}
        {overseasWareOpen && (
          <HomePageOverseasWare
            {...props}
            open={overseasWareOpen}
            onCancel={value => {
              setOverseasWareOpen(false);
              handleCancelStatusOpen();
              if (value) {
                window.location.reload();
              }
            }}
          />
        )}
        {/* 海外仓开通弹框 End */}
        {/* 中国仓开通弹框 Start */}
        {CNwarehouseOpen && (
          <HomePageCNWarehouse open={CNwarehouseOpen} onCancel={() => setCNwarehouseOpen(false)} />
        )}
        {/* 中国仓开通弹框 End */}
        {/* 消息弹框 Start */}
        <HomePageMessageModal modalRef={messageModalRef} {...props} />
        {/* 消息弹框 End */}
      </div>
      {/* {pageLoading ? (
        <>
          <PageLoading />
        </>
      ) : (

      )} */}
    </>
  );
};

export default connect(({ homePage, loading, user }) => ({
  userInfo: user.userInfo,
  isTourOpen: user.isTourOpen,
  isTourBillOpen: user.isTourBillOpen,
  homePage,
  pageLoading: loading.effects['homePage/getCustomerIndexAuthState'], // 查询商户认证状态
  saveSmallPacketLoading: loading.effects['homePage/saveApply'],
  saveFbaLoading: loading.effects['homePage/fbaApply'],
  saveOverseasWareLoading: loading.effects['overseas/saveOverseaApply'],
  getBusinessApplyStatusLoading: loading.effects['homePage/queryAuthAndBusinessState'],
  selectFreezeReasonsLoading: loading.effects['homePage/selectFreezeReasons'],
  getPersonRecordSignLoading: loading.effects['homePage/getPersonRecordSign'],
}))(HomePage);
