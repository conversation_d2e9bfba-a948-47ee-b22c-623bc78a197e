import React, { useState, useEffect, useRef, useImperativeHandle } from 'react';
import { ProCard } from '@ant-design/pro-components';
import {
  ExclamationCircleFilled,
  LoadingOutlined,
  CheckCircleFilled,
  SolutionOutlined,
} from '@ant-design/icons';
import IconFont from '@/components/IconFont';
import { Steps, Tooltip, Modal, Row, Space, Popover, Button, Spin, Select } from 'antd';
import { Redirect, router } from 'umi';
import styles from './HomePageCard.less';
import iconLoading from '@/assets/loading.png';
import account_icon3 from '@/assets/account_icon3.png';
import account_icon2 from '@/assets/account_icon2.png';
import { businessIntroduction, renderSelectOptions } from '@/utils/commonConstant';
import MerchantStatusModal from './MerchantStatusModal';
// @ts-ignore
import smallBagBg from '../../../assets/smallBagBg.png';
// @ts-ignore
import fbaBg from '../../../assets/fbaBg.png';
import BasicLayoutStyles from '../../../layouts/BasicLayout.less';
import HomePageUserBankModal from './HomePageUserBankModal';
import SignModal from './SignModal';
import { formatMessage } from 'umi-plugin-react/locale';

const { confirm } = Modal;

const HomePageCard = props => {
  const { onOpenService, onClickMakOrOpen, dispatch, isTourBillOpen, modalRef } = props;
  const userBankRef = useRef();
  const signModalRef = useRef();
  const [loading, setLoading] = useState(false);
  const [merchantStatusOpen, setMerchantStatusOpen] = useState(false); // 冻结弹窗
  const [bankOptions, setBankOptions] = useState([]); // 付款银行列表
  const [overseasBusinessValue, setOverseasBusinessValue] = useState(); // 海外派业务账号
  // status 0:未开通业务,1:未认证,2:已认证，3:待审核,4:审核失败，5:待签约，6:完成
  const [certifiedData, setCertifiedData] = useState([
    {
      title: formatMessage({ id: '小包专线' }),
      extra: '',
      key: '0',
      status: undefined,
      bg: smallBagBg,
      billInfo: {
        unSettledBalance: '', //未出账单金额
        settledBalance: '***', //可用余额
        changedBalance: '***', //账户余额
        frozenBalance: '', //制单预扣款金额
        frozenCustomer: false, //是否为预扣款客户
        merchantCode: '', // 业务账号
      },
      buttonLoading: false, // 控制按钮loading
    },
    {
      title: `FBA${formatMessage({ id: '专线' })}`,
      extra: '',
      key: '1',
      status: undefined,
      bg: fbaBg,
      billInfo: {
        unSettledBalance: '', //未出账单金额
        settledBalance: '***', //可用余额
        changedBalance: '***', //账户余额
        frozenBalance: '', //制单预扣款金额
        frozenCustomer: false, //是否为预扣款客户
        merchantCode: '', // 业务账号
      },
      buttonLoading: false, // 控制按钮loading
    },
    // {
    //   title: '中国仓',
    //   extra: '',
    //   key: '3',
    //   status: undefined,
    //   billInfo: {
    //     unSettledBalance: '', //未出账单金额
    //     settledBalance: '***', //可用余额
    //     changedBalance: '***', //账户余额
    //     frozenBalance: '', //制单预扣款金额
    //     frozenCustomer: false, //是否为预扣款客户
    //   },
    // },
    {
      title: '海外派',
      extra: '',
      key: '2',
      status: undefined,
      billInfo: {
        unSettledBalance: '', //未出账单金额
        settledBalance: '***', //可用余额
        changedBalance: '***', //账户余额
        frozenBalance: '', //制单预扣款金额
        frozenCustomer: false, //是否为预扣款客户
      },
    },
  ]);

  const steps = [
    {
      selector: '[data-tut=bill-step]',
      content: () => (
        <>
          <div
            style={{
              background: 'rgba(255, 255, 255, 0.9)',
              padding: '10px',
              borderRadius: '5px',
              color: '#000',
            }}
          >
            <h3 style={{ fontSize: '14px' }}>{formatMessage({ id: '账户余额' })}</h3>
            <p style={{ fontSize: '12px' }}>{formatMessage({ id: '点击查看费用明细' })}</p>
          </div>
        </>
      ),
      style: {
        boxShadow: 'none',
        width: '285px',
        textAlign: 'left',
        borderRadius: '30px',
      },
    },
  ];

  const [open, setOpen] = useState(false);

  useImperativeHandle(modalRef, () => ({
    checkIsNeedRealName,
  }));

  const accountAmountIcon = (url, borderColor, bcgColor) => (
    <div
      className="text-2xl border-8 border-solid w-20 h-20 font-black text-center flex justify-center items-center"
      style={{
        borderRadius: '100px',
        borderColor,
        backgroundColor: bcgColor,
        width: '65px',
        height: '65px',
        marginRight: '10px',
      }}
    >
      <img src={url} style={{ maxWidth: '100%', height: 'auto' }} />
    </div>
  );

  const getBillBalance = (type, customerCode) =>
    new Promise(resolve => {
      dispatch({
        type: 'smallBag/getBillBalance',
        payload: { businessType: type, customerCode },
        callback: response => {
          if (response.success) {
            resolve(response.data);
            // setBillInfo(response.data);
          } else {
            resolve(null);
          }
        },
      });
    });

  useEffect(() => {
    init();
  }, []);

  const init = async () => {
    setLoading(true);
    Promise.allSettled([
      getBankList(),
      getBusinessApplyStatus(0),
      getBusinessApplyStatus(1),
      getBusinessApplyStatus(2),
      // getBusinessApplyStatus(3),
    ]).then(results => {
      const newCertifiedData = certifiedData.map(item => {
        const result = results.find(result => result?.value?.type + '' === item.key);
        // 暂时未处理请求错误后的处理
        if (result?.status === 'fulfilled') {
          item.status = result.value?.value;
          item.extra = result.value?.frozenStatusName;
        }
        // 处理获取账单信息
        if (result?.value?.billInfoResult) {
          if (Array.isArray(result?.value?.billInfoResult)) {
            item.billInfo = result?.value?.billInfoResult;
          } else {
            item.billInfo = { ...result?.value?.billInfoResult };
          }
        }
        return item;
      });
      setLoading(false);
      setCertifiedData(newCertifiedData);
      setOverseasBusinessValue(
        newCertifiedData?.find(v => v.key === '2')?.billInfo?.[0]?.accountCode
      );
    });
  };

  //  银行信息列表
  const getBankList = () =>
    new Promise((resolve, reject) => {
      dispatch({
        type: 'paymentAccount/bankList',
        callback: response => {
          if (response.success) {
            setBankOptions(
              renderSelectOptions(response.data, { label: 'paymentModeName', value: 'code' })
            );
            resolve();
          } else {
            setBankOptions([]);
            reject();
          }
        },
      });
    });

  //根据业务类型与业务类型对应状态查询对应数据
  const getBusinessApplyData = (type, customerCode) => {
    if (type === 0) {
      return getBillBalance(type);
    } else if (type === 1) {
      return getBillBalance(type);
    } else if (type === 2) {
      return getBillBalance(type, customerCode);
    }
  };

  // 获取海外派业务账号
  const getOverseasBusinessAccount = () =>
    new Promise((resolve, reject) => {
      dispatch({
        type: 'overseas/getOverseaAccounts',
        payload: 1,
        callback: response => {
          if (response.success) {
            resolve(response.data);
          } else {
            reject(false);
          }
        },
      });
    });

  const createOrder = type => {
    switch (type) {
      case '0':
        router.push('/smallBag/orderManagement/creatOrder');
        break;
      case '1':
        router.push('/fba/fbaOrder');
        break;
      case '2':
        router.push('/overseas/orderManagement');
        break;
      default:
        break;
    }
  };

  const merchantStatus = ejfStatus => {
    let str = '不活动'; // 0
    if (ejfStatus === 1) {
      str = '活动';
    } else if (ejfStatus === 2) {
      str = '冻结';
    } else if (ejfStatus === 3) {
      str = '其他';
    }
    return str;
  };

  const getBusinessApplyStatus = type =>
    new Promise((resolve, reject) => {
      dispatch({
        type: 'homePage/queryAuthAndBusinessState',
        payload: { type },
        callback: response => {
          if (response.success) {
            let billInfoResult;
            if (response?.data?.businessProcessState === 4) {
              if (type === 2) {
                // 海外派需要先获取到制单账号的数据
                getOverseasBusinessAccount().then(async results => {
                  if (results) {
                    const promiseBillData = await Promise.allSettled(
                      results?.map(v => getBusinessApplyData(type, v.accountCode))
                    );
                    const data = results.map(v => {
                      const result = promiseBillData.find(
                        val => val?.value?.merchantCode + '' === v.accountCode
                      );
                      if (result?.status === 'fulfilled') {
                        return { ...v, ...result.value };
                      }
                    });
                    billInfoResult = data;
                  } else {
                    billInfoResult = {
                      unSettledBalance: '', //未出账单金额
                      settledBalance: '***', //可用余额
                      changedBalance: '***', //账户余额
                      frozenBalance: '', //制单预扣款金额
                      frozenCustomer: false, //是否为预扣款客户
                      merchantCode: '', // 业务账号
                      accountCode: '', // 业务账号
                    };
                  }
                  resolve({
                    type,
                    value: response?.data?.businessProcessState,
                    billInfoResult,
                    frozenStatusName: response?.data?.frozenStatusName,
                  });
                });
              } else {
                getBusinessApplyData(type).then(result => {
                  if (result) {
                    billInfoResult = result;
                  } else {
                    billInfoResult = {
                      unSettledBalance: '', //未出账单金额
                      settledBalance: '***', //可用余额
                      changedBalance: '***', //账户余额
                      frozenBalance: '', //制单预扣款金额
                      frozenCustomer: false, //是否为预扣款客户
                      merchantCode: '', // 业务账号
                      accountCode: '', // 业务账号
                    };
                  }
                  resolve({
                    type,
                    value: response?.data?.businessProcessState,
                    billInfoResult,
                    frozenStatusName: response?.data?.frozenStatusName,
                  });
                });
              }
            } else {
              resolve({
                type,
                value: response?.data?.businessProcessState,
                frozenStatusName: response?.data?.frozenStatusName,
              });
            }
          } else {
            reject({ type, value: response.success });
          }
        },
      });
    });
  // 控制进度条的状态
  const transformStatusUIStep = status => {
    // switch (status) {
    //   case 0: //'未开通业务'
    //     return;
    //   case 1: //'未认证'
    //     return 0;
    //   case 2: //'已认证'
    //     return 1;
    //   case 3: // '待审核'
    //     return 1;
    //   case 4: //'审核失败'
    //     return 1;
    //   case 5: //'待签约'
    //     return 2;
    //   case 6: //'完成'
    //     return 3;
    //   default:
    //     //'未开通业务'
    //     return;
    // }
    switch (status) {
      case 0: //'未开通业务'
        return;
      case 1: //'待审核'
        return 0;
      case 2: //'审核失败'
        return 0;
      case 3: // '待签约'
        return 1;
      case 4: //'完成'
        return 2;
      default:
        //'未开通业务'
        return;
    }
  };

  // 控制进度条的提示
  const transformStatusUITitle = status => {
    switch (status) {
      case 0: //'未开通业务'
        return formatMessage({ id: '尚未开通' });
      case 1: //'待审核'
        return formatMessage({ id: '审核中' });
      case 2: //'审核失败'
        return formatMessage({ id: '审核失败' });
      case 3: // '待签约'
        return formatMessage({ id: '待签署' });
      case 4: //'完成'
        return 3;
      default:
        //'未开通业务'
        return formatMessage({ id: '尚未开通' });
    }
  };

  // 未开通业务的dom
  const notFoundDom = key => (
    <>
      <p style={{ fontSize: '16px' }}>{formatMessage({ id: '尚未开通' })}</p>
      <p
        style={{
          color: '#059b42',
          cursor: 'pointer',
          textAlign: 'center',
        }}
        onClick={() => onOpenService(key)}
      >
        {formatMessage({ id: '立即开通' })}
      </p>
    </>
  );

  const handleFrozen = () => {
    router.push('/smallBag/smallPacketLine?openFreeze=1');
  };

  // 完成后的dom
  const finishDom = (billInfo, key, record) => {
    const data = Array.isArray(billInfo)
      ? billInfo?.find(v => v?.accountCode === overseasBusinessValue)
      : billInfo;
    return (
      <Row justify="space-around" className="w-full">
        {finishBillDom({
          key,
          record,
          title: formatMessage({ id: '未出账单金额' }),
          subTitle: tipTitle(formatMessage({ id: '未出账单金额' })),
          value: data?.unSettledBalance,
          icon: accountAmountIcon(account_icon2, '#fff5d5', '#ffcc2e'),
        })}
        {finishBillDom({
          key,
          record,
          title: formatMessage({ id: '可用余额' }),
          subTitle: tipTitle(formatMessage({ id: '可用余额' }), data?.frozenCustomer),
          value: data?.settledBalance,
          icon: accountAmountIcon(account_icon3, '#dbecff', '#4da1ff'),
        })}
      </Row>
    );
  };

  // 提示语
  const tipTitle = (title, weekPay) => {
    if (title === formatMessage({ id: '账户余额' })) {
      return `${formatMessage({
        id: '代表已扣除已出账单部分的余款',
      })}，${formatMessage({ id: '正数代表有余款' })}，${formatMessage({
        id: '负数代表已发生欠款',
      })}`;
    } else if (title === formatMessage({ id: '未出账单金额' })) {
      return `${formatMessage({ id: '代表当周所发货物' })}，${formatMessage({
        id: '处理后产生的金额',
      })}`;
    } else if (title === formatMessage({ id: '制单预扣金额' })) {
      return formatMessage({ id: '代表在制单系统成功下单时预扣的金额' });
    } else if (title === formatMessage({ id: '可用余额' }) && weekPay) {
      return `${formatMessage({
        id: '代表账户的当前可用余款',
      })}，${formatMessage({
        id: '已经扣除未出账单金额和制单预扣金额',
      })}，${formatMessage({ id: '正数代表有余款' })}，${formatMessage({
        id: '负数代表已经发生欠款',
      })}`;
    } else if (title === formatMessage({ id: '可用余额' })) {
      return `${formatMessage({
        id: '代表账户的当前可用余款',
      })}，${formatMessage({ id: '已经扣除未出账单金额' })}，${formatMessage({
        id: '正数代表有余款',
      })}，${formatMessage({ id: '负数代表已经发生欠款' })}`;
    }
  };

  const finishBillDom = ({ key, record, title, subTitle, value, icon }) => (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      {icon}
      <div className="wallet">
        <div
          style={{
            color: '#898989',
            fontSize: 12,
            marginBottom: 5,
          }}
          className="wallet-title"
        >
          {title}
          <Tooltip placement="top" title={subTitle}>
            <ExclamationCircleFilled style={{ color: '#C0CCDA', marginLeft: 3 }} />
          </Tooltip>
          {title === formatMessage({ id: '可用余额' }) && (
            <span
              style={{
                color: '#93c350',
                marginLeft: 8,
                cursor: 'pointer',
              }}
              onClick={() =>
                router.push(
                  `/financialManagement/rechargeManagement/offlineTopUp${key == 1 ? '?type=1' : ''}`
                )
              }
            >
              {formatMessage({ id: '充值' })}
            </span>
          )}
        </div>
        {title === formatMessage({ id: '可用余额' }) && key == 0 ? (
          <RenderTour>
            <div
              style={{ fontWeight: '500', fontSize: 16, cursor: 'pointer' }}
              className="wallet-content"
              onClick={() => {
                router.push(`/financialManagement/myBill${key == 1 ? '?activeKey=1' : ''}`);
              }}
            >
              {value}
            </div>
          </RenderTour>
        ) : (
          <div
            style={{ fontWeight: '500', fontSize: 16, cursor: 'pointer' }}
            className="wallet-content"
            onClick={() => {
              router.push(`/financialManagement/myBill${key == 1 ? '?activeKey=1' : ''}`);
            }}
          >
            {value}
          </div>
        )}

        {record?.extra === '冻结' && title === formatMessage({ id: '可用余额' }) && key == 0 && (
          <div style={{ fontSize: 12 }}>
            <a onClick={handleFrozen}>{formatMessage({ id: '申请解冻' })}</a>
          </div>
        )}
      </div>
    </div>
  );

  const renderStyleTitle = status => ({
    textAlign: status == 1 || status == 2 ? 'left' : 'right',
    paddingRight: status == 1 || status == 2 ? 0 : '18%',
    paddingLeft: status == 1 || status == 2 ? '26%' : 0,
  });

  // 已认证的dom
  const certifiedDom = (status, key) => (
    <div className="w-full">
      <p
        style={{
          color: status === 2 ? 'red' : '#059b42',
          fontSize: 14,
          cursor: 'pointer',
          marginBottom: -10,
          ...renderStyleTitle(status),
        }}
        className="progress-title"
        onClick={() => (status === 3 ? onClickRightService(key, status) : onClickMakOrOpen(status))}
      >
        {transformStatusUITitle(status)}
      </p>
      <Steps
        current={transformStatusUIStep(status)}
        className="STEPS"
        size="small"
        labelPlacement="vertical"
        items={[
          {
            // title: '实名认证',
            title: formatMessage({ id: '信息提交' }),
          },
          {
            // title: '信息审核',
            title: formatMessage({ id: '合同生成' }),
          },
          // {
          //   title: '合同签署',
          //   // status: 'process',
          //   // icon: (
          //   // 	<img style={{wigth: 24}} src={iconLoading}></img>
          //   // ),
          //   // icon: <LoadingOutlined />,
          // },
          {
            title: formatMessage({ id: '完成' }),
          },
        ]}
      />
    </div>
  );

  // 业务筹备的dom
  const businessPreparationDom = (status, key) => (
    <>
      <p style={{ fontSize: '16px' }}>{formatMessage({ id: '业务筹备中' })}</p>
      <p
        style={{
          color: '#059b42',
          cursor: 'pointer',
          textAlign: 'center',
        }}
      >
        {formatMessage({ id: '敬请期待' })}
      </p>
    </>
  );

  const AlreadyOpenedDom = (status, key, item) => {
    if (status === undefined) {
      return notFoundDom(key);
    } else {
      switch (status) {
        case -1: // 业务筹备
          return businessPreparationDom(status, key);
        case 0: //'未开通业务'
          return notFoundDom(key);
        case 1: //'待审核'
          return certifiedDom(status, key);
        case 2: //'审核失败'
          return certifiedDom(status, key);
        case 3: // '待签约'
          return certifiedDom(status, key);
        case 4: //'完成'
          return finishDom(
            item?.billInfo ?? {
              unSettledBalance: '', //未出账单金额
              settledBalance: '***', //可用余额
              changedBalance: '***', //账户余额
              frozenBalance: '', //制单预扣款金额
              frozenCustomer: false, //是否为预扣款客户
              merchantCode: '', // 业务账号
            },
            key,
            item
          );
        default:
          //'未开通业务'
          return notFoundDom(key);
      }
    }
  };

  // 校验是否客户需要进行实名认证
  const checkIsNeedRealName = key => {
    handleButtonLoading(key);
    dispatch({
      type: 'realNameAuth/checkCustomerRealAuth',
      callback: response => {
        if (response.success) {
          if (response.data == 0) {
            // 不需要实名认证
            getSignUrl(key);
            // router.push('/userManagement/realName');
          } else if (response.data == 1) {
            handleButtonLoading(key);
            // 需要实名认证
            confirm({
              title: formatMessage({ id: '您的商户信息还未完成实名认证' }),
              content: `${formatMessage({ id: '请完成' })}【${formatMessage({
                id: '实名认证',
              })}】${formatMessage({ id: '后再签署合同' })}。`,
              okText: formatMessage({ id: '去认证' }),
              onOk() {
                return completeData().catch(error => {
                  console.error(error);
                });
              },
            });
          } else if (response.data == 2) {
            handleButtonLoading(key);
            // 补齐证件照弹框
            confirm({
              title: formatMessage({ id: '温馨提示' }),
              content: (
                <span style={{ fontWeight: 'bold', color: '#f50b0b' }}>
                  {formatMessage({ id: '您提交的证件照信息有误' })}，
                  {formatMessage({ id: '请重新提交' })}
                </span>
              ),
              okText: formatMessage({ id: '去补齐' }),
              onOk() {
                return router.push('/customerManager/customerInformation/realName');
              },
            });
          }
        }
      },
    });
  };

  // 主动发起补齐资料
  const completeData = () =>
    new Promise((resolve, reject) => {
      dispatch({
        type: 'realNameAuth/completeData',
        callback: response => {
          if (response.success) {
            resolve(response);
            router.push('/customerManager/customerInformation/realName');
          } else {
            reject(response);
          }
        },
      });
    });
  const ChildComponent = () => {
    return <div>This is the child component</div>;
  };

  const handleButtonLoading = key => {
    const data = [...certifiedData];
    setCertifiedData(
      data?.map(v => {
        if (v.key === key) {
          v.buttonLoading = !v.buttonLoading;
        }
        return v;
      })
    );
  };

  // 获取合同签署地址
  const getSignUrl = contractType => {
    dispatch({
      type: 'homePage/contractAndRecord',
      payload: { businessType: +contractType },
      callback: response => {
        handleButtonLoading(contractType);
        if (response.success) {
          if (response.data.status == 0) {
            signModalRef.current?.open({
              url: response.data?.signUrl,
              businessType: +contractType,
              contractName: response.data.contractName,
            });
          } else if (response?.data?.status == 1 || response?.data?.status == 3) {
            //需要个人银行卡备案
            userBankRef.current?.open({ businessType: +contractType, ...response.data });
          } else if (response?.data?.status == 2) {
            // 补齐资料弹框
            confirm({
              title: formatMessage({ id: '温馨提示' }),
              content: `${formatMessage({
                id: '当前签署合同缺少商户主体银行卡信息',
              })}，${formatMessage({ id: '需重新认证添加' })}`,
              okText: formatMessage({ id: '立即添加' }),
              cancelText: formatMessage({ id: '暂不添加' }),
              onOk() {
                return completeData().catch(error => {
                  console.error(error);
                });
              },
            });
          }
        }
      },
    });
  };

  // 改变右侧业务状态文字
  const changeRightServiceStatusTitle = status => {
    switch (status) {
      case 0: //'未开通业务'
        return formatMessage({ id: '开通业务' });
      case 1: //'待审核'
        return formatMessage({ id: '查看进度' });
      case 2: //'审核失败'
        return formatMessage({ id: '修改信息' });
      case 3: // '待签约'
        return formatMessage({ id: '立即签署' });
      case 4: //'完成'
        return;
      default:
        //'未开通业务'
        return formatMessage({ id: '开通业务' });
    }
  };

  // 点击左侧业务按钮
  const onClickLeftService = (key, status) => {};

  // 点击右侧业务按钮
  const onClickRightService = (key, status) => {
    if (status === undefined) {
      onOpenService(key);
    } else {
      switch (status) {
        case 0: //'未开通业务'
          onOpenService(key);
          break;
        case 1: //'待审核'
          onOpenService(key);
          break;
        case 2: //'审核失败'
          onOpenService(key);
          break;
        case 3: // '待签约'
          checkIsNeedRealName(key);
          break;
        case 4: //'完成'
          createOrder(key);
          break;
        default:
          //'未开通业务'
          onOpenService(key);
          break;
      }
    }
  };

  // 点击左侧业务按钮
  const handleActionsLeft = item => {
    switch (item?.status) {
      case 4: //'报价单下载'
        const url = {
          '0': '/financialManagement/priceInquiry/quotationDownload',
          '1': '/fba/priceSheet',
          '2': '/financialManagement/priceInquiry/freightTrial?type=4',
        };
        router.push(url[item.key]);
        break;
      default:
        const businessObject = {
          '0': {
            title: formatMessage({ id: '小包专线业务介绍' }),
            // url: ''
            okText: formatMessage({ id: '去开通' }),
            onOk: () => {
              onOpenService('0');
            },
          },
          '1': {
            title: `FBA${formatMessage({ id: '专线业务介绍' })}`,
            okText: formatMessage({ id: '去开通' }),
            onOk: () => {
              onOpenService('1');
            },
          },
          '2': {
            title: formatMessage({ id: '海外仓业务介绍' }),
            okText: formatMessage({ id: '知道了' }),
            onOk: () => {},
          },
          '3': {
            title: formatMessage({ id: '中国仓业务介绍' }),
            okText: formatMessage({ id: '知道了' }),
            onOk: () => {},
          },
        };

        // 业务介绍
        Modal.confirm({
          title: businessObject[item?.key]?.title ?? '',
          content: businessIntroduction[item?.key],
          icon: null,
          okText: businessObject[item?.key]?.okText ?? formatMessage({ id: '去开通' }),
          cancelText: item?.key == 2 || item?.key == 3 ? null : formatMessage({ id: '暂不开通' }),
          onOk: businessObject[item?.key]?.onOk ?? (() => {}),
        });
        break;
    }
  };

  const closeTour = () => {
    dispatch({
      type: 'user/viewTheEnd',
      payload: { type: 'bill' },
    });
  };

  const RenderTour = ({ children }) => (
    <Popover
      content={
        <>
          <p style={{ fontSize: '12px', width: 309 }}>
            {formatMessage({ id: '点击查看费用明细' })}
          </p>
          <Row justify="center">
            <Button type="primary" size="small" onClick={closeTour}>
              {formatMessage({ id: '知道了' })}
            </Button>
          </Row>
        </>
      }
      placement="bottom"
      title={formatMessage({ id: '账户余额' })}
      trigger="click"
      open={isTourBillOpen}
    >
      {children}
    </Popover>
  );

  const renderExtra = item => {
    const overseasExtra = Array.isArray(item?.billInfo)
      ? item?.billInfo?.find(v => v?.accountCode === overseasBusinessValue)?.ejfStatusName
      : '';

    return item?.status == 4 ? (
      <div
        className="extra"
        style={{
          color:
            (item?.extra === '活动' && item?.key == 0) ||
            (item?.extra === '正常' && item?.key == 1) ||
            (item?.key === '2' && overseasExtra === '活动')
              ? '#52c41a'
              : 'red',
        }}
      >
        {item?.key === '2' ? overseasExtra : item.extra}
      </div>
    ) : (
      <div
        onClick={() => (item?.status === 3 ? onClickRightService(item?.key, item?.status) : null)}
        className="extra"
        style={{ color: item?.status == 2 ? 'red' : '#52c41a' }}
      >
        <Space>
          {item?.status != 0 ? (
            item?.status == 1 || item?.status == 2 ? (
              <IconFont type="icon-daishenhe" style={{ fontSize: '14px' }} />
            ) : (
              <IconFont type="icon-dhy" style={{ fontSize: '14px' }} />
            )
          ) : null}
          {item?.status == 1
            ? formatMessage({ id: '信息审核中' })
            : item?.status == 2
            ? formatMessage({ id: '审核失败' })
            : item?.status == 3
            ? formatMessage({ id: '待签署合同' })
            : ''}
        </Space>
      </div>
    );
  };

  const renderCardChildren = item => (
    <ProCard
      // data-tut={item?.key == 0 ? 'bill-step' : null}
      className="uncertifiedCardList"
      title={
        <Space>
          {item.title}
          {item?.billInfo && Array.isArray(item?.billInfo) ? (
            <Select
              value={overseasBusinessValue}
              options={item?.billInfo?.map(v => ({
                label: `(${v?.accountCode})`,
                value: v?.accountCode,
              }))}
              size="small"
              bordered={false}
              style={{ width: 125, fontSize: '12px', color: '#52c41a' }}
              onChange={value => {
                setOverseasBusinessValue(value);
              }}
            />
          ) : (
            item?.billInfo?.merchantCode && (
              <span style={{ color: '#52c41a', fontSize: '12px' }}>
                ({item?.billInfo?.merchantCode})
              </span>
            )
          )}
        </Space>
      }
      extra={renderExtra(item)}
      colSpan={{ xs: 8, sm: 8, md: 8, lg: 8, xl: 8 }}
      layout="center"
      bordered
      bodyStyle={{
        backgroundImage: `url(${item.bg})`,
        backgroundRepeat: 'no-repeat',
        backgroundSize: '100% 100%',
      }}
      actions={
        item.status == -1
          ? [
              item.key !== '2' && (
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    // padding: 12,
                    flex: 1,
                    gap: 8,
                    borderRight: '1px solid #e5e5e5',
                  }}
                  onClick={() => {
                    handleActionsLeft(item);
                  }}
                >
                  {formatMessage({ id: '业务介绍' })}
                </div>
              ),
            ]
          : [
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  // padding: 12,
                  flex: 1,
                  gap: 8,
                  borderRight: '1px solid #e5e5e5',
                }}
                onClick={() => {
                  handleActionsLeft(item);
                }}
              >
                {item.status === 4
                  ? item?.key === '2'
                    ? '运价试算'
                    : formatMessage({ id: '报价单下载' })
                  : formatMessage({ id: '业务介绍' })}
              </div>,
              item?.buttonLoading ? (
                <Spin />
              ) : (
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    // padding: 12,
                    flex: 1,
                    gap: 8,
                  }}
                  onClick={() => onClickRightService(item.key, item.status)}
                >
                  {item.status === 4
                    ? item?.key === '2'
                      ? '查询订单'
                      : formatMessage({ id: '去制单' })
                    : changeRightServiceStatusTitle(item.status)}
                </div>
              ),
            ]
      }
    >
      {AlreadyOpenedDom(item.status, item.key, item)}
    </ProCard>
  );

  return (
    <ProCard className="uncertifiedCard" ghost gutter={24} loading={loading}>
      {/* 已认证Start */}
      {certifiedData && certifiedData.map(item => renderCardChildren(item))}
      {/* 已认证End */}
      <MerchantStatusModal
        {...props}
        visible={merchantStatusOpen}
        onCancel={() => {
          setMerchantStatusOpen(false);
        }}
      />
      <HomePageUserBankModal {...props} modalRef={userBankRef} bankOptions={bankOptions} />
      <SignModal {...props} modalRef={signModalRef} />
    </ProCard>
  );
};

export default HomePageCard;
