import React, { useState, MutableRefObject, useImperativeHandle } from 'react';
import { Modal, Button } from 'antd';
import { router } from 'umi';
import { logSave, LogType } from '@/utils/logSave';

type ModalRefType = {
  showModal: () => void;
};

type Props = {
  modalRef: MutableRefObject<ModalRefType>;
  dispatch: Function;
};

/**
 *
 * @returns 首页消息弹窗
 */
const HomePageMessageModal = ({ modalRef, dispatch }: Props) => {
  const [open, setOpen] = useState(false);
  const [allMessageArray, setAllMessageArray] = useState([]);

  useImperativeHandle(modalRef, () => ({
    showModal: () => {
      initialFunc();
    },
  }));

  // 判断弹窗信息包含的内容
  const handleOrderPopupMessage = (data: any) => {
    return (
      data.hasOwnProperty('problemNumber') ||
      data.hasOwnProperty('warehouseNumber') ||
      data.hasOwnProperty('overseasNumber') ||
      data.hasOwnProperty('returnNumber')
    );
  };

  // 到达专线代取提醒
  const handleContentShow = (data: any) =>
    data.hasOwnProperty('pendingForPickUp') ||
    data.hasOwnProperty('deliveryDelay') ||
    data.hasOwnProperty('deliveryFailed') ||
    data.hasOwnProperty('unableToDeliver');
  // 通知公告
  const handleAnnouncement = data => data.hasOwnProperty('announcement');
  // 消息订阅
  const handleMessageShow = (data: any) => data.hasOwnProperty('subscription');

  // 联系方式变更
  const handleContactChange = (data: any) =>
    data?.hasOwnProperty('enterpriseWeChat') && data?.enterpriseWeChat;

  // 密码修改
  const handleChangePassword = (data: any) => data?.hasOwnProperty('isNeedModifyPwd');

  // menu更新
  const handlePopupMenuUpdate = (data: any) => data?.hasOwnProperty('popupMenuUpdate');

  const initialFunc = () => {
    dispatch({
      type: 'homePage/popupMessage',
      callback: response => {
        const data = response.data;
        const allMessageData = data.map((item: any) => {
          if (handleAnnouncement(item)) {
            // 当前为公告
            item = { ...item, ...item.announcement };
            item.titleHeader = '公告通知';
            item.buttonTitle = '查看详情';
            item.text = `更新时间: ${item.updatedate}`;
            item.description = item.title;
          } else if (handleOrderPopupMessage(item)) {
            // 当前为异常件
            item.titleHeader = '异常件提醒';
            item.buttonTitle = '现在处理';

            const texts = [
              {
                text: `待处理-问题件: ${item?.problemNumber ?? 0}`,
                hasValue: item?.problemNumber,
              },
              {
                text: `待处理-仓外异常件：${item?.warehouseNumber ?? 0}`,
                hasValue: item?.warehouseNumber,
              },
              {
                text: `待处理-海外重派：${item?.overseasNumber ?? 0}`,
                hasValue: item?.overseasNumber,
              },
              {
                text: `待处理-退件：${item?.returnNumber ?? 0}`,
                hasValue: item?.returnNumber,
              },
            ];

            item.text = texts
              .filter(text => text.hasValue)
              .map(text => text.text)
              .join('、');
            item.description = '您好，请及时处理异常件，以免影响货物整体时效。';
          } else if (handleContentShow(item)) {
            // 到达专线代取提醒
            item.titleHeader = '待领取件提醒';
            item.buttonTitle = '现在处理';
            const properties = [
              { key: 'pendingForPickUp', label: '到达待取' },
              { key: 'deliveryDelay', label: '派送延迟' },
              { key: 'deliveryFailed', label: '派送失败' },
              { key: 'unableToDeliver', label: '无法交付' },
            ];

            item.text = properties
              .filter(({ key }) => item[key])
              .map(({ key, label }) => `${label}：${item[key]}`)
              .join('、');
            item.description = '您好，请及时查看国外投递异常运单并通知收件人，避免退件产生损失。';
          } else if (handleMessageShow(item)) {
            // 消息订阅提示
            item.titleHeader = '订阅功能上线';
            item.buttonTitle = '去订阅';
            item.text = `您好，订阅功能支持自定义短信、公众号、邮件等方式接收信息，如有需求，请点击 “去订阅”`;
          } else if (handleContactChange(item)) {
            // 联系方式变更
            item.titleHeader = '联系方式变更提醒';
            item.buttonTitle = '立即添加';
            item.text = '您的销售或客服的沟通工具已变更成企业微信';
            item.description = '';
          } else if (handleChangePassword(item)) {
            // 密码修改
            item.titleHeader = '密码修改提醒';
            item.buttonTitle = '修改密码';
            item.text =
              '系统检测到您已长时间未修改密码，为了您的使用安全，建议您点击【修改密码】，重新设置一个9-20位的密码（含字母、数字或符号，符号可以有_.!@#?）';
            item.description = '';
          } else if (handlePopupMenuUpdate(item)) {
            item.titleHeader = '菜单调整提醒';
            item.buttonTitle = '前往查看';
            item.text =
              '您好，因业务架构调整，小包专线的轨迹查询与时效统计以及FBA专线的轨迹查询等菜单已调整至服务管理';
            item.description = '';
          }
          item.showUpdateModal = false;
          return item;
        });
        setOpen(
          allMessageData.length > 0 &&
            allMessageData.findIndex(item => item.text !== undefined) > -1
        );
        setAllMessageArray(allMessageData);
      },
    });
  };

  const wechatRequest = () => {
    dispatch({
      type: 'homePage/enterpriseWeChat',
      callback: response => {
        if (response.success) {
          router.push('/customerManager/customerInformation/myProfile');
        }
      },
    });
  };

  const clickItem = (item: any) => {
    localStorage.setItem('openMessageModal', 'false');
    switch (item.titleHeader) {
      case '公告通知': // 查看公告
        logSave(LogType.home19);
        dispatch({
          type: 'homePage/readAnnouncement',
          payload: {
            contentId: item.contentId,
          },
          callback: result => {
            if (result.success) {
              let param = {
                order: 'asc',
                limit: 10,
                currentPage: 1,
                context: '',
                categoryId: '196',
                top: null,
              };
              // 进入详情
              router.push({
                pathname: '/serviceManagement/content/notificationMessage',
                state: { contentId: item.contentId, conList: [{}], param: param },
              });
            }
          },
        });
        break;
      case '异常件提醒': // 异常件
        logSave(LogType.home1);
        if (item?.problemNumber) {
          // 问题件
          router.push('/smallBag/anomalyOrder?type=0');
        } else if (item?.warehouseNumber) {
          // 仓外异常件
          router.push(`/smallBag/anomalyOrder?type=2`);
        } else if (item?.overseasNumber) {
          // 海外重派
          router.push(`/smallBag/anomalyOrder?type=3`);
        } else if (item?.returnNumber) {
          // 退件
          router.push(`/smallBag/anomalyOrder?type=4`);
        }
        handleCancel();
        break;
      case '待领取件提醒':
        logSave(LogType.home4);
        router.push(`/smallBag/anomalyOrder?lineType=3`);
        break;
      case '订阅功能上线':
        router.push('/serviceManagement/messageCenter/messageSubscription');
        break;
      case '联系方式变更提醒':
        wechatRequest();
        break;
      case '密码修改提醒':
        logSave(LogType.home3);
        router.push('/customerManager/customerInformation/changePassword');
        break;
      case '菜单调整提醒':
        dispatch({
          type: 'taxNumber/readAction',
          payload: {
            type: '4',
            typeId: '4',
          },
          callback: result => {
            if (result.success) {
              router.push('/serviceManagement');
            }
          },
        });
        break;
    }
  };

  const handleCancel = () => {
    localStorage.setItem('openMessageModal', 'false');
    setOpen(false);
  };

  return (
    <Modal open={open} footer={null} maskClosable={false} onCancel={handleCancel}>
      <div
        style={{
          padding: '10px',
          textAlign: 'center',
        }}
      >
        <h3>燕文重要提示</h3>
        <ul
          style={{
            listStyle: 'decimal',
          }}
        >
          {allMessageArray.map((item, index) => {
            return (
              <li key={index}>
                {/* {item.code}、  */}
                <div className="w-full text-left mb-1">
                  <span>
                    <span
                      style={{
                        fontWeight: 'bold',
                      }}
                    >
                      【{item.titleHeader}】
                    </span>
                    &nbsp;&nbsp;&nbsp;
                    {item.text}
                  </span>
                  <div className="flex items-center justify-between">
                    <span
                      style={{
                        // @ts-ignore
                        webkitBoxOrient: 'vertical',
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                      }}
                      className="w-4/5 overflow-hidden overflow-ellipsis"
                    >
                      {item.description}
                    </span>

                    <Button type="link" onClick={() => clickItem(item)}>
                      {item.buttonTitle}
                    </Button>
                  </div>
                </div>
              </li>
            );
          })}
        </ul>
      </div>
    </Modal>
  );
};

export default HomePageMessageModal;
