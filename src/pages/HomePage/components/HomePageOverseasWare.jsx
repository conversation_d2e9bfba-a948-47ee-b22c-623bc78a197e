/*
 * @Description:
 * @Author:
 * @Date: 2023-03-09 17:10:40
 * @LastEditTime: 2023-03-10 15:07:10
 * @LastEditors:
 */
import { useState, useRef, useEffect } from 'react';
import { Modal, FormItem, Row, Button, Space, message, Divider, Input, Col } from 'antd';
import { CloseOutlined, PlusOutlined } from '@ant-design/icons';
import {
  ProForm,
  ProFormText,
  ProFormCheckbox,
  ProFormGroup,
  ProFormSelect,
} from '@ant-design/pro-components';
import {
  positionList,
  relationShipList,
  customerPlatformSources,
  returnWayList,
  officeAddressRegex,
} from '@/utils/commonConstant';
import PageLoading from '@/components/PageLoading';
import { APILoader } from '@uiw/react-amap';
import BindAddress from '@/pages/SmallBag/CollectManagement/AddressMaintenance/components/BindAddress';
import { validatorEmail } from '@/utils/utils';

const HomePageOverseasWare = props => {
  const { open, onCancel, saveOverseasWareLoading, dispatch } = props;
  const formRef = useRef();
  const inputRef = useRef();
  const [saleEdit, setSaleEdit] = useState(true);
  const [warehouseList, setWarehouseList] = useState([]); // 交互仓库列表
  const [provinceList, setProvinceList] = useState([]); // 省份列表
  const [cityList, setCityList] = useState([]); // 城市列表
  const [districtList, setDistrictList] = useState([]); // 区县列表
  const [returnProvinceList, setReturnProvinceList] = useState([]); // 退件地址省份列表
  const [returnCityList, setReturnCityList] = useState([]); // 退件地址城市列表
  const [returnDistrictList, setReturnDistrictList] = useState([]); // 退件地址区县列表
  const [authStatus, setAuthStatus] = useState(0); // 认证状态 // 0 未认证 1 待审核 2 待签约 3 签约完成  4 审核失败
  const [noEdit, setNoEdit] = useState(false); // 是否不可编辑
  const [refuseReason, setRefuseReason] = useState(); // 拒绝原因
  const [loading, setLoading] = useState(false);
  const [ticketQuantityList, setTicketQuantityList] = useState([]);
  const [averageDailyRequired, setAverageDailyRequired] = useState(false); // 日均需求是否必填
  const [introducerSaleId, setIntroducerSaleId] = useState();
  const [dataInfo, setDataInfo] = useState({
    introducerSaleId: undefined,
  }); // 用于销售手机号传参的数据
  const [showSalePhone, setShowSalePhone] = useState(false); // 是否显示销售手机号
  const [isFirst, setIsFirst] = useState(false); // 是否第一次进入
  const [managementPlatformItem, setManagementPlatformItem] = useState(''); // 经营平台
  const [paramsMap, setParamsMap] = useState(); // 高德弹窗所需要的参数
  const [returnVisible, setReturnVisible] = useState(false); // 退件地址弹窗
  const [optWay, setOptWay] = useState(); // 选择退件方式用于更新页面
  const [collectingPoint, setCollectingPoint] = useState({
    collectingPointUuid: undefined,
    collectingPointName: undefined,
  }); // 揽收点内容

  useEffect(() => {
    if (open) {
      setLoading(true);
      Promise.all([
        getCountryCodeList({ level: 1 }),
        getWarehouseByType(),
        getAverageDailyRequired(),
      ]).then(() => {
        getPacketApplyInfo();
      });
    }
  }, [open]);

  // 查询是否有默认销售
  const checkMainSaleByType = async () => {
    dispatch({
      type: 'homePage/checkMainSaleByType',
      payload: {
        type: '0',
      },
      callback: response => {
        if (response.success) {
          setIntroducerSaleId(response?.data ?? undefined);
          setShowSalePhone(response?.data ? false : true);
        }
      },
    });
  };

  // 校验客户输入的销售手机号是否正确
  const validateSale = async () => {
    try {
      const response = await formRef.current?.validateFields(['introducerSaleMobile']);
      dispatch({
        type: 'homePage/validateSale',
        payload: {
          type: '0',
          salePhone: response?.introducerSaleMobile,
        },
        callback: response => {
          if (response.success) {
            setIntroducerSaleId(response.data);
          } else {
            formRef.current?.resetFields(['introducerSaleMobile']);
          }
        },
      });
    } catch (error) {
      console.error(error);
    }
  };

  // 用于提交校验客户输入的销售手机号是否正确
  const validateSaleSubmit = () =>
    new Promise(async resolve => {
      const response = await formRef.current?.validateFields(['introducerSaleMobile']);
      if (response?.introducerSaleMobile && showSalePhone) {
        // 可能为不展示销售
        dispatch({
          type: 'homePage/validateSale',
          payload: {
            type: '0',
            salePhone: response?.introducerSaleMobile,
            noMessage: true,
          },
          callback: response => {
            if (response.success) {
              setIntroducerSaleId(response.data);
              resolve({
                type: true,
                introducerSaleId: response.data,
              });
            } else {
              formRef.current?.resetFields(['introducerSaleMobile']);
              resolve(false);
            }
          },
        });
      } else {
        // 可能为选择无推荐销售
        resolve({
          type: true,
          introducerSaleId: introducerSaleId ?? undefined,
        });
      }
    });

  // 获取日均票件量
  const getAverageDailyRequired = async () => {
    dispatch({
      type: 'Register/shipmentOfDay',
      callback: response => {
        if (response.success) {
          const data = response.data.map(item => ({
            value: item.code,
            label: item.name,
          }));
          // setAverageDailyRequired(response.data);
          setTicketQuantityList(data);
        }
      },
    });
  };

  // 获取认证状态
  const getPacketApplyInfo = () => {
    dispatch({
      type: 'overseas/getOverseaApplyInfo',
      callback: async response => {
        if (response.success) {
          const data = response?.data;
          const status = changeStatus(+data?.auditStatus);
          setIsFirst(status === undefined);
          if (status === 0) {
            formRef.current?.resetFields();
          } else if (status === 1) {
            setSaleEdit(data?.introducerSaleMobile ? true : false);
            setShowSalePhone(data?.introducerSaleId ? false : true);
            setOptWay(data?.returnType);
            setCollectingPoint({
              ...collectingPoint,
              collectingPointName: data?.collectingPointName,
              collectingPointUuid: data?.collectingPointUuid,
            });
            setParamsMap({
              ...paramsMap,
              cityCode: data?.warehouseCode,
              cityName: data?.warehouseName?.replace('燕文', '')?.replace('虚拟仓', ''),
            });
            const officeIsLive =
              data?.officeProvinceCode && data?.officeCityCode && data?.officeAreaCode;
            const returnIsLive =
              data?.returnProvinceCode && data?.returnCityCode && data?.returnAreaCode;
            if (returnIsLive || officeIsLive) {
              const allPromiseData =
                officeIsLive && returnIsLive
                  ? [
                      await getCountryCodeList({
                        level: 2,
                        value: data?.officeProvinceCode,
                        initial: true,
                      }),
                      await getCountryCodeList({
                        level: 3,
                        value: data?.officeCityCode,
                        initial: true,
                      }),
                      await getCountryCodeList({
                        level: 2,
                        value: data?.returnProvinceCode,
                        initial: true,
                        type: 'return',
                      }),
                      await getCountryCodeList({
                        level: 3,
                        value: data?.returnCityCode,
                        initial: true,
                        type: 'return',
                      }),
                    ]
                  : officeIsLive
                  ? [
                      await getCountryCodeList({
                        level: 2,
                        value: data?.officeProvinceCode,
                        initial: true,
                      }),
                      await getCountryCodeList({
                        level: 3,
                        value: data?.officeCityCode,
                        initial: true,
                      }),
                    ]
                  : returnIsLive
                  ? [
                      await getCountryCodeList({
                        level: 2,
                        value: data?.returnProvinceCode,
                        initial: true,
                        type: 'return',
                      }),
                      await getCountryCodeList({
                        level: 3,
                        value: data?.returnCityCode,
                        initial: true,
                        type: 'return',
                      }),
                    ]
                  : [];
              Promise.all(allPromiseData).then(() => {
                setLoading(false);
                setTimeout(() => {
                  const isReturnTypeValid = data?.returnType === 2 || data?.returnType === 3;
                  const isAddressSame =
                    data?.officeProvinceName === data?.returnProvinceName &&
                    data?.officeCityName === data?.returnCityName &&
                    data?.officeAreaName === data?.returnAreaName;
                  formRef.current?.setFieldsValue({
                    ...data,
                    checkbox: data?.introducerSaleMobile ? [] : ['无推荐销售'],
                    managementPlatform: data?.managementPlatform?.split(','),
                    returnAddressToOffice:
                      isReturnTypeValid && isAddressSame ? ['退件地址同办公地址'] : [],
                  });
                }, 300);
              });
            } else {
              setLoading(false);
              formRef.current?.setFieldsValue({
                ...data,
                checkbox: data?.introducerSaleMobile ? [] : ['无推荐销售'],
                managementPlatform: data?.managementPlatform?.split(','),
              });
            }
          } else if (status === 4) {
            // 审核失败
            Promise.all([checkMainSaleByType()]).then(async () => {
              setSaleEdit(true);
              setRefuseReason(data?.refuseReason);
              setDataInfo({
                ...dataInfo,
                ...data,
              });
              setOptWay(data?.returnType);
              setCollectingPoint({
                ...collectingPoint,
                collectingPointName: data?.collectingPointName,
                collectingPointUuid: data?.collectingPointUuid,
              });
              setParamsMap({
                ...paramsMap,
                cityCode: data?.warehouseCode,
                cityName: data?.warehouseName?.replace('燕文', '')?.replace('虚拟仓', ''),
              });
              const officeIsLive =
                data?.officeProvinceCode && data?.officeCityCode && data?.officeAreaCode;
              const returnIsLive =
                data?.returnProvinceCode && data?.returnCityCode && data?.returnAreaCode;
              if (officeIsLive || returnIsLive) {
                const allPromiseData =
                  officeIsLive && returnIsLive
                    ? [
                        await getCountryCodeList({
                          level: 2,
                          value: data?.officeProvinceCode,
                          initial: true,
                        }),
                        await getCountryCodeList({
                          level: 3,
                          value: data?.officeCityCode,
                          initial: true,
                        }),
                        await getCountryCodeList({
                          level: 2,
                          value: data?.returnProvinceCode,
                          initial: true,
                          type: 'return',
                        }),
                        await getCountryCodeList({
                          level: 3,
                          value: data?.returnCityCode,
                          initial: true,
                          type: 'return',
                        }),
                      ]
                    : officeIsLive
                    ? [
                        await getCountryCodeList({
                          level: 2,
                          value: data?.officeProvinceCode,
                          initial: true,
                        }),
                        await getCountryCodeList({
                          level: 3,
                          value: data?.officeCityCode,
                          initial: true,
                        }),
                      ]
                    : returnIsLive
                    ? [
                        await getCountryCodeList({
                          level: 2,
                          value: data?.returnProvinceCode,
                          initial: true,
                          type: 'return',
                        }),
                        await getCountryCodeList({
                          level: 3,
                          value: data?.returnCityCode,
                          initial: true,
                          type: 'return',
                        }),
                      ]
                    : [];
                Promise.all(allPromiseData).then(() => {
                  setLoading(false);
                  setTimeout(() => {
                    const isReturnTypeValid = data?.returnType === 2 || data?.returnType === 3;
                    const isAddressSame =
                      data?.officeProvinceName === data?.returnProvinceName &&
                      data?.officeCityName === data?.returnCityName &&
                      data?.officeAreaName === data?.returnAreaName;
                    formRef.current?.setFieldsValue({
                      ...data,
                      introducerSaleMobile: undefined,
                      managementPlatform: data?.managementPlatform?.split(','),
                      returnAddressToOffice:
                        isReturnTypeValid && isAddressSame ? ['退件地址同办公地址'] : [],
                      checkbox: [],
                    });
                  }, 300);
                });
              } else {
                setLoading(false);
                setTimeout(() => {
                  formRef.current?.setFieldsValue({
                    ...data,
                    introducerSaleMobile: undefined,
                    managementPlatform: data?.managementPlatform?.split(','),
                    // checkbox: data?.introducerSaleMobile ? [] : ['无推荐销售'],
                    checkbox: [],
                  });
                }, 300);
              }
            });
          } else if (status === 2 || status === 3) {
            // 待签约 也等于审核通过 刷新页面变为签署合同
            setLoading(false);
            // handleOnCancel(true);
          } else if (status === undefined) {
            // 第一次进入
            Promise.all([checkMainSaleByType()]).then(() => {
              setLoading(false);
              if (!renderSessionData()) {
                formRef.current?.setFieldsValue({
                  ...data,
                });
              }
            });
          }
        } else {
          setLoading(false);
          // handleOnCancel();
        }
      },
    });
  };

  const renderSessionData = async () => {
    const sessionData = JSON.parse(sessionStorage.getItem('overseasWare'));
    if (sessionData) {
      const { collectingPoint: newCollectingPoint, paramsMap: newParamsMap, ...data } = sessionData;
      setSaleEdit(!data?.checkbox?.[0]);
      setAverageDailyRequired(data?.checkbox?.[0]);
      setOptWay(data?.returnType);
      setCollectingPoint({
        ...collectingPoint,
        ...newCollectingPoint,
      });
      setParamsMap({
        ...paramsMap,
        ...newParamsMap,
        cityCode: data?.warehouseCode,
        cityName: newParamsMap?.warehouseName?.replace('燕文', '')?.replace('虚拟仓', ''),
      });
      const officeIsLive = data?.officeProvinceCode && data?.officeCityCode && data?.officeAreaCode;
      const returnIsLive = data?.returnProvinceCode && data?.returnCityCode && data?.returnAreaCode;
      if (returnIsLive || officeIsLive) {
        const allPromiseData =
          officeIsLive && returnIsLive
            ? [
                await getCountryCodeList({
                  level: 2,
                  value: data?.officeProvinceCode,
                  initial: true,
                }),
                await getCountryCodeList({
                  level: 3,
                  value: data?.officeCityCode,
                  initial: true,
                }),
                await getCountryCodeList({
                  level: 2,
                  value: data?.returnProvinceCode,
                  initial: true,
                  type: 'return',
                }),
                await getCountryCodeList({
                  level: 3,
                  value: data?.returnCityCode,
                  initial: true,
                  type: 'return',
                }),
              ]
            : officeIsLive
            ? [
                await getCountryCodeList({
                  level: 2,
                  value: data?.officeProvinceCode,
                  initial: true,
                }),
                await getCountryCodeList({
                  level: 3,
                  value: data?.officeCityCode,
                  initial: true,
                }),
              ]
            : returnIsLive
            ? [
                await getCountryCodeList({
                  level: 2,
                  value: data?.returnProvinceCode,
                  initial: true,
                  type: 'return',
                }),
                await getCountryCodeList({
                  level: 3,
                  value: data?.returnCityCode,
                  initial: true,
                  type: 'return',
                }),
              ]
            : [];
        Promise.all(allPromiseData).then(() => {
          setLoading(false);
          setTimeout(() => {
            const isReturnTypeValid = data?.returnType === 2 || data?.returnType === 3;
            const isAddressSame =
              data?.officeProvinceName === data?.returnProvinceName &&
              data?.officeCityName === data?.returnCityName &&
              data?.officeAreaName === data?.returnAreaName;
            formRef.current?.setFieldsValue({
              ...data,
              checkbox: data?.checkbox?.[0] ? ['无推荐销售'] : [],
              managementPlatform:
                typeof data?.managementPlatform == 'string'
                  ? data?.managementPlatform?.split(',')
                  : data?.managementPlatform,
              returnAddressToOffice:
                isReturnTypeValid && isAddressSame ? ['退件地址同办公地址'] : [],
            });
          }, 300);
        });
      } else {
        setLoading(false);
        formRef.current?.setFieldsValue({
          ...data,
          checkbox: data?.checkbox?.[0] ? ['无推荐销售'] : [],
          managementPlatform:
            typeof data?.managementPlatform == 'string'
              ? data?.managementPlatform?.split(',')
              : data?.managementPlatform,
        });
      }
      return true;
    }
    return false;
  };

  const changeStatus = status => {
    let result;
    switch (+status) {
      case 0:
      case 1: // 0 ,1 未认证
        result = 0;
        setAuthStatus(0);
        setNoEdit(false);
        break;
      case 2: // 待审核
        result = 1;
        setAuthStatus(1);
        setNoEdit(true);
        break;
      case 3:
      case 4:
      case 5:
      case 6:
      case 7: // 待签约
        result = 2;
        setAuthStatus(2);
        setNoEdit(true);
        break;
      case 8: // 签约完成
        result = 3;
        setAuthStatus(3);
        break;
      case -1: // 审核失败
        result = 4;
        setAuthStatus(4);
        break;
    }
    return result;
  };

  const getWarehouseByType = async () => {
    dispatch({
      type: 'homePage/getAllWarehouseByType',
      payload: 4,
      callback: response => {
        if (response.success) {
          const data = response.data.map(item => {
            item.value = item.code;
            item.label = item.name;
            return item;
          });
          setWarehouseList(data);
        } else {
          setWarehouseList([]);
        }
      },
    });
  };

  const getCountryCodeList = async ({ level, value, initial, type }) => {
    return new Promise((resolve, reject) => {
      dispatch({
        type: 'homePage/getCountryCodeList',
        payload: {
          level,
          code: value,
        },
        callback: response => {
          if (response.success) {
            const data = response.data.map(item => ({
              ...item,
              value: item.code,
              label: item.name,
            }));
            const returnData = response.data.map(item => ({
              ...item,
              value: item.name,
              label: item.name,
            }));
            if (level === 1) {
              formRef.current?.resetFields([
                'officeProvinceCode',
                'officeCityCode',
                'officeAreaCode',
                'returnProvinceName',
                'returnCityName',
                'returnAreaName',
              ]);

              setReturnProvinceList(returnData);
              setReturnCityList([]);
              setReturnDistrictList([]);
              setProvinceList(data);
              setCityList([]);
              setDistrictList([]);
              resolve();
            } else if (level === 2) {
              if (!initial)
                formRef.current?.resetFields(
                  type === 'return'
                    ? ['returnCityName', 'returnAreaName']
                    : ['officeCityCode', 'officeAreaCode']
                );
              if (type === 'return') {
                setReturnCityList(returnData);
                setReturnDistrictList([]);
              } else {
                setCityList(data);
                setDistrictList([]);
              }
              resolve();
            } else {
              if (!initial)
                formRef.current?.resetFields(
                  type === 'return' ? ['returnAreaName'] : ['officeAreaCode']
                );
              if (type === 'return') {
                setReturnDistrictList(returnData);
              } else {
                setDistrictList(data);
              }
              resolve();
            }
          } else {
            if (level === '1') {
              setProvinceList([]);
              setReturnProvinceList([]);
            } else if (level === '2') {
              setCityList([]);
              setReturnCityList([]);
            } else {
              setDistrictList([]);
              setReturnDistrictList([]);
            }
            reject();
          }
        },
      });
    });
  };
  // 保存第一次进入的数据
  const saveFirstSessionData = () => {
    const firstData = formRef.current?.getFieldsValue();
    const sessionData = { ...firstData, collectingPoint, paramsMap };
    // 第一次进入并关闭弹窗后存储数据
    sessionStorage.setItem('overseasWare', JSON.stringify(sessionData));
  };

  const handleOnCancel = value => {
    if (isFirst) {
      saveFirstSessionData();
    }
    formRef.current?.resetFields();
    setShowSalePhone(false);
    setAverageDailyRequired(false);
    setIntroducerSaleId(undefined);
    setParamsMap(undefined);
    setReturnVisible(false);
    setOptWay(undefined);
    setIsFirst(false);
    setCollectingPoint({
      collectingPointName: undefined,
      collectingPointUuid: undefined,
    });
    setDataInfo({
      introducerSaleId: undefined,
    });
    onCancel(value);
  };

  const validatorFormValue = (rule, value, callback, compareField, message) => {
    const compareValue = formRef.current?.getFieldValue(compareField);
    if (value && compareValue && value === compareValue) {
      // formRef.current?.setFieldsValue({
      //   [targetField]: undefined,
      // });
      callback(message);
    }
    callback();
  };
  // 经营平台自定义输入
  const addItem = e => {
    e.preventDefault();
    let data = formRef.current?.getFieldsValue();
    if (managementPlatformItem !== '') {
      formRef.current?.setFieldsValue({
        managementPlatform: [...(data?.managementPlatform ?? []), managementPlatformItem],
      });
      setManagementPlatformItem('');
      setTimeout(() => {
        inputRef.current?.focus();
      }, 0);
    }
  };
  // 退件地址弹窗回调
  const handleReturnSubmit = data => {
    const { params } = data;
    formRef.current?.setFieldsValue({
      returnAddress: params?.address,
      returnProvinceName: params?.provinceName,
      returnCityName: params?.cityName,
      returnAreaName: params?.areaName,
    });
    setCollectingPoint({
      ...collectingPoint,
      ...params,
    });
  };

  const handleSubmit = async values => {
    const result = await validateSaleSubmit();
    if (!result) return;
    const officeProvinceName = provinceList.find(item => item.code === values?.officeProvinceCode)
      ?.name;
    const officeCityName = cityList.find(item => item.code === values?.officeCityCode)?.name;
    const officeAreaName = districtList.find(item => item.code === values?.officeAreaCode)?.name;
    const officeAddress = values?.officeAddress
      ?.replaceAll(officeProvinceName, '')
      ?.replaceAll(officeCityName, '')
      ?.replaceAll(officeAreaName, '');
    const params = {
      ...values,
      ...collectingPoint,
      officeProvinceName: officeProvinceName,
      officeCityName: officeCityName,
      officeAreaName: officeAreaName,
      officeAddress: officeAddress,
      managementPlatform: values?.managementPlatform?.join(','),
      // introducerSaleId: introducerSaleId ? introducerSaleId : values?.introducerSaleMobile,
      introducerSaleId:
        !saleEdit && dataInfo?.introducerSaleId
          ? dataInfo?.introducerSaleId
          : result?.type
          ? result?.introducerSaleId
          : introducerSaleId
          ? introducerSaleId
          : values?.introducerSaleMobile,
    };
    // delete params.checkbox;
    dispatch({
      type: 'overseas/saveOverseaApply',
      payload: params,
      callback: response => {
        if (response.success) {
          sessionStorage.removeItem('overseasWare');
          message.success(response.message);
          handleOnCancel(response.success);
        } else {
          saveFirstSessionData();
        }
      },
    });
  };

  const displayAndSelect = optWay === undefined || optWay == 0;

  return (
    <Modal
      title={
        <>
          海外派业务开通{' '}
          {noEdit && (
            <span
              style={{
                color: 'red',
              }}
              className="float-right mr-20"
            >
              审核中
            </span>
          )}
          {refuseReason && (
            <span
              style={{
                color: 'red',
              }}
              className="float-right mr-20"
            >
              {refuseReason}
            </span>
          )}
        </>
      }
      width="55%"
      open={open}
      maskClosable={false}
      footer={null}
      onCancel={() => handleOnCancel()}
      destroyOnClose
    >
      {loading ? (
        <PageLoading paddingTop={0} />
      ) : (
        <>
          <ProForm
            formRef={formRef}
            layout="horizontal"
            labelAlign="left"
            labelCol={{
              flex: '95px',
            }}
            onFinish={handleSubmit}
            submitter={{
              render: (_, dom) => null,
            }}
          >
            {showSalePhone && (
              <ProFormGroup>
                <ProFormText
                  name="introducerSaleMobile"
                  label="推荐销售"
                  width="sm"
                  placeholder="请输入推荐销售员手机号"
                  rules={[
                    {
                      required: saleEdit,
                      message: '请输入推荐销售员手机号',
                    },
                    {
                      pattern: /^1\d{10}$/,
                      message: '手机号格式错误',
                    },
                  ]}
                  fieldProps={{
                    onBlur: () => {
                      validateSale();
                    },
                    disabled: !saleEdit,
                  }}
                />
                <ProFormCheckbox.Group
                  name="checkbox"
                  layout="horizontal"
                  options={['无推荐销售']}
                  disabled={noEdit}
                  fieldProps={{
                    onChange: e => {
                      setSaleEdit(!e[0]);
                      setAverageDailyRequired(e[0]);
                      formRef.current?.resetFields(['introducerSaleMobile', 'dayShipCode']);
                    },
                  }}
                />
              </ProFormGroup>
            )}
            {averageDailyRequired && !refuseReason && (
              <ProFormSelect
                label="日均票件量"
                width="sm"
                name="dayShipCode"
                rules={[
                  {
                    required: averageDailyRequired && !refuseReason,
                    message: '请选择日均票件量',
                  },
                ]}
                fieldProps={{
                  options: ticketQuantityList,
                }}
              />
            )}
            <ProFormSelect
              width="sm"
              label="注入仓"
              name="warehouseCode"
              options={warehouseList}
              placeholder="请选择注入仓"
              rules={[
                {
                  required: true,
                  message: '请选择注入仓',
                },
              ]}
              fieldProps={{
                showSearch: true,
                disabled: noEdit,
              }}
            />
            <div className="flex flex-col justify-start items-start ">
              <div className="ant-col ant-form-item-label mb-3">
                <label
                  htmlFor="upload"
                  className="ant-form-item-required"
                  title="业务联系人及办公地址"
                >
                  业务联系人及办公地址
                </label>
              </div>
              <ProFormGroup>
                <Space align="start">
                  <div className="ant-col ant-form-item-label mb-3">
                    <label htmlFor="upload" title="姓名">
                      姓名
                    </label>
                  </div>
                  <ProFormText
                    name="contactName"
                    width={194}
                    labelCol={{
                      flex: '50px',
                    }}
                    placeholder="请输入业务负责人姓名"
                    rules={[
                      {
                        required: true,
                        message: '请输入业务负责人姓名',
                      },

                      {
                        validator: (rule, value, callback) => {
                          validatorFormValue(
                            rule,
                            value,
                            callback,
                            'emergencyContactName',
                            '不能与紧急联系人姓名相同!'
                          );
                        },
                      },
                    ]}
                    fieldProps={{
                      disabled: noEdit,
                    }}
                  />
                </Space>
                <Space align="start">
                  <div className="ant-col ant-form-item-label mb-3">
                    <label htmlFor="upload" title="手机号">
                      手机号
                    </label>
                  </div>
                  <ProFormText
                    name="contactPhone"
                    width={194}
                    labelCol={{
                      flex: '65px',
                    }}
                    placeholder="请输入业务负责人手机号"
                    rules={[
                      {
                        required: true,
                        message: '请输入业务负责人手机号',
                      },

                      {
                        validator: (rule, value, callback) => {
                          validatorFormValue(
                            rule,
                            value,
                            callback,
                            'emergencyContactPhone',
                            '不能与紧急联系人手机号相同!'
                          );
                        },
                      },
                    ]}
                    fieldProps={{
                      disabled: noEdit,
                    }}
                  />
                </Space>

                <Space align="start">
                  <div className="ant-col ant-form-item-label mb-3">
                    <label htmlFor="upload" title="岗位">
                      岗位
                    </label>
                  </div>
                  <ProFormSelect
                    labelCol={{
                      flex: '50px',
                    }}
                    name="contactPost"
                    width={160}
                    options={positionList}
                    placeholder="请选择岗位"
                    rules={[
                      {
                        required: true,
                        message: '请选择岗位',
                      },
                    ]}
                    fieldProps={{
                      showSearch: true,
                      disabled: noEdit,
                    }}
                  />
                </Space>
              </ProFormGroup>
              <Space align="start">
                <div className="ant-col ant-form-item-label">
                  <label htmlFor="upload" title="办公地址">
                    办公地址
                  </label>
                </div>
                <Row gutter={8}>
                  <Col>
                    <ProFormSelect
                      name="officeProvinceCode"
                      width={160}
                      rules={[
                        {
                          required: true,
                          message: '请选择省',
                        },
                      ]}
                      fieldProps={{
                        showSearch: true,
                        onChange: (value, option) => {
                          setOptWay(undefined);
                          formRef.current?.resetFields([
                            'returnAddress',
                            'returnProvinceName',
                            'returnCityName',
                            'returnAreaName',
                            'returnAddressToOffice',
                            'returnType',
                          ]);
                          getCountryCodeList({ level: 2, value });
                        },
                        disabled: noEdit,
                      }}
                      options={provinceList}
                    />
                  </Col>
                  <Col>
                    <ProFormSelect
                      name="officeCityCode"
                      width={160}
                      rules={[
                        {
                          required: true,
                          message: '请选择市',
                        },
                      ]}
                      fieldProps={{
                        showSearch: true,
                        onChange: value => {
                          setOptWay(undefined);
                          formRef.current?.resetFields([
                            'returnAddress',
                            'returnProvinceName',
                            'returnCityName',
                            'returnAreaName',
                            'returnAddressToOffice',
                            'returnType',
                          ]);
                          getCountryCodeList({ level: 3, value });
                        },
                        disabled: noEdit,
                      }}
                      options={cityList}
                    />
                  </Col>
                  <Col>
                    <ProFormSelect
                      name="officeAreaCode"
                      width={160}
                      rules={[
                        {
                          required: true,
                          message: '请选择区',
                        },
                      ]}
                      options={districtList}
                      fieldProps={{
                        showSearch: true,
                        disabled: noEdit,
                        onChange: () => {
                          setOptWay(undefined);
                          formRef.current?.resetFields([
                            'returnAddress',
                            'returnProvinceName',
                            'returnCityName',
                            'returnAreaName',
                            'returnAddressToOffice',
                            'returnType',
                          ]);
                        },
                      }}
                    />
                  </Col>
                  <Col>
                    <ProFormText
                      name="officeAddress"
                      width={400}
                      rules={[
                        {
                          required: true,
                          message: '请输入办公地址',
                        },
                        {
                          max: 50,
                          message: '办公地址最多50个字符',
                        },
                      ]}
                      placeholder="请输入办公地址需具体到门牌号，例如xx栋xx号xx室"
                      fieldProps={{
                        disabled: noEdit,
                        onChange: () => {
                          setOptWay(undefined);
                          formRef.current?.resetFields([
                            'returnAddress',
                            'returnProvinceName',
                            'returnCityName',
                            'returnAreaName',
                            'returnAddressToOffice',
                            'returnType',
                          ]);
                        },
                      }}
                    />
                  </Col>
                </Row>
              </Space>
            </div>
            <div className="flex flex-col justify-start items-start ">
              <div className="ant-col ant-form-item-label mb-3">
                <label htmlFor="upload" className="ant-form-item-required" title="退件地址及联系人">
                  退件地址及联系人
                </label>
              </div>
              <Row gutter={[8, 0]} className="w-full">
                <Col span={8}>
                  <ProFormText
                    labelCol={{
                      flex: '70px',
                    }}
                    label="省/州"
                    name="returnProvinceName"
                    placeholder="请输入省/州"
                    rules={[
                      {
                        required: true,
                        message: '请输入省/州',
                      },
                    ]}
                    fieldProps={{
                      disabled: noEdit,
                    }}
                  />
                </Col>
                <Col span={8}>
                  <ProFormText
                    labelCol={{
                      flex: '70px',
                    }}
                    label="城市"
                    name="returnCityName"
                    placeholder="请输入城市"
                    rules={[
                      {
                        required: true,
                        message: '请输入城市',
                      },
                      {
                        max: 50,
                        message: '城市名称最多50个字符',
                      },
                    ]}
                    fieldProps={{
                      disabled: noEdit,
                    }}
                  />
                </Col>
                <Col span={8}>
                  <ProFormText
                    labelCol={{
                      flex: '70px',
                    }}
                    label="邮编"
                    name="returnContactZipCode"
                    placeholder="请输入邮编"
                    rules={[
                      {
                        required: true,
                        message: '请输入邮编',
                      },
                    ]}
                    fieldProps={{
                      disabled: noEdit,
                    }}
                  />
                </Col>
                <Col span={16}>
                  <ProFormText
                    labelCol={{
                      flex: '50px',
                    }}
                    label="地址"
                    name="returnAddress"
                    placeholder="请输入详细地址"
                    rules={[
                      {
                        required: true,
                        message: '请输入详细地址',
                      },
                      {
                        max: 100,
                        message: '详细地址最多100个字符',
                      },
                      {
                        pattern: /^[\u4e00-\u9fa5a-zA-Z0-9\-\.\(\)\（\）\x20]+$/,
                        message: '包含非法字符',
                      },
                    ]}
                    fieldProps={{
                      disabled: noEdit,
                    }}
                  />
                </Col>
              </Row>

              <ProFormGroup>
                <Space align="start">
                  <div className="ant-col ant-form-item-label ">
                    <label htmlFor="upload" title="联系人姓名">
                      联系人姓名
                    </label>
                  </div>
                  <ProFormText
                    name="returnContactName"
                    width={194}
                    labelCol={{
                      flex: '50px',
                    }}
                    placeholder="请输入负责人姓名"
                    rules={[
                      {
                        required: true,
                        message: '请输入退件负责人姓名',
                      },
                    ]}
                    fieldProps={{
                      disabled: noEdit,
                    }}
                  />
                </Space>
                <Space align="start">
                  <div className="ant-col ant-form-item-label ">
                    <label htmlFor="upload" title="联系人手机号">
                      联系人手机号
                    </label>
                  </div>
                  <ProFormText
                    name="returnContactPhone"
                    width={194}
                    labelCol={{
                      flex: '65px',
                    }}
                    placeholder="请输入负责人手机号"
                    rules={[
                      {
                        required: true,
                        message: '请输入退件负责人手机号',
                      },
                    ]}
                    fieldProps={{
                      disabled: noEdit,
                    }}
                  />
                </Space>

                <Space align="start">
                  <div className="ant-col ant-form-item-label ">
                    <label htmlFor="upload" title="联系人邮箱">
                      联系人邮箱
                    </label>
                  </div>
                  <ProFormText
                    labelCol={{
                      flex: '50px',
                    }}
                    name="returnContactEmail" // 需要确保后端字段名称对应
                    width={160}
                    placeholder="请输入邮箱"
                    rules={[
                      { required: true, message: '请输入邮箱' },
                      {
                        validator: (_, value) =>
                          validatorEmail(value, formRef, 'returnContactEmail'),
                      },
                    ]}
                    fieldProps={{
                      disabled: noEdit,
                      maxLength: 50,
                    }}
                  />
                </Space>
              </ProFormGroup>
            </div>
            <div className="flex flex-col justify-start items-start">
              <div className="ant-col ant-form-item-label mb-3">
                <label htmlFor="upload" className="ant-form-item-required" title="紧急联系人">
                  紧急联系人
                </label>
              </div>
              <ProFormGroup>
                <Space align="start">
                  <div className="ant-col ant-form-item-label mb-3">
                    <label htmlFor="upload" title="姓名">
                      姓名
                    </label>
                  </div>
                  <ProFormText
                    name="emergencyContactName"
                    width={194}
                    labelCol={{
                      flex: '50px',
                    }}
                    placeholder="请输入紧急联系人姓名"
                    rules={[
                      {
                        required: true,
                        message: '请输入紧急联系人姓名',
                      },
                      {
                        validator: (rule, value, callback) => {
                          validatorFormValue(
                            rule,
                            value,
                            callback,
                            'contactName',
                            '不能与业务负责人姓名相同!'
                          );
                        },
                      },
                    ]}
                    fieldProps={{
                      disabled: noEdit,
                    }}
                  />
                </Space>
                <Space align="start">
                  <div className="ant-col ant-form-item-label mb-3">
                    <label htmlFor="upload" title="手机号">
                      手机号
                    </label>
                  </div>
                  <ProFormText
                    name="emergencyContactPhone"
                    width={194}
                    labelCol={{
                      flex: '65px',
                    }}
                    placeholder="请输入紧急联系人手机号"
                    rules={[
                      {
                        required: true,
                        message: '请输入紧急联系人手机号',
                      },

                      {
                        validator: (rule, value, callback) => {
                          validatorFormValue(
                            rule,
                            value,
                            callback,
                            'contactPhone',
                            '不能与业务负责人手机号相同!'
                          );
                        },
                      },
                    ]}
                    fieldProps={{
                      disabled: noEdit,
                    }}
                  />
                </Space>
                <Space align="start">
                  <div className="ant-col ant-form-item-label mb-3">
                    <label htmlFor="upload" title="与商户关系">
                      与商户关系
                    </label>
                  </div>
                  <ProFormSelect
                    labelCol={{
                      flex: '90px',
                    }}
                    name="relationShip"
                    width={160}
                    options={relationShipList}
                    placeholder="请选择与商户关系"
                    rules={[
                      {
                        required: true,
                        message: '请选择与商户关系',
                      },
                    ]}
                    fieldProps={{
                      disabled: noEdit,
                    }}
                  />
                </Space>
              </ProFormGroup>
            </div>
            <div className="flex flex-col justify-start items-start">
              <div className="ant-col ant-form-item-label mb-3">
                <label htmlFor="upload" className="ant-form-item-required" title="账单接收邮箱">
                  账单接收邮箱
                </label>
              </div>
              <ProFormText
                name="billMail"
                width="sm"
                placeholder="请输入邮箱"
                rules={[
                  {
                    validator: (_, value) => validatorEmail(value, formRef, 'billMail'),
                  },
                ]}
                fieldProps={{
                  disabled: noEdit,
                }}
              />
            </div>
            <Row justify="center">
              <Space>
                <Button className="w-32" onClick={() => handleOnCancel()}>
                  关闭
                </Button>
                <Button
                  type="primary"
                  htmlType="submit"
                  className="w-32"
                  loading={saveOverseasWareLoading}
                  disabled={noEdit}
                >
                  提交
                </Button>
              </Space>
            </Row>
          </ProForm>
        </>
      )}
      <APILoader version="2.0.5" akay="afedb3fbce242c06f1af43158b67eb07">
        <BindAddress
          {...props}
          onClose={() => {
            setReturnVisible(false);
            formRef.current?.resetFields([
              'returnAddress',
              'returnProvinceName',
              'returnCityName',
              'returnAreaName',
            ]);
          }}
          title="新增退件地址"
          visible={returnVisible}
          handleReturnSubmit={handleReturnSubmit}
          paramsMap={paramsMap}
          provinceList={returnProvinceList}
          checkAddressUrl={'addressMaintenance/verifySite'}
          getCoordsUrl={'addressMaintenance/verifyFence'}
          mapListUrl={'addressMaintenance/mapList'}
          mapTagUrl={'addressMaintenance/mapTagKey'}
          dropOffPointUrl={'addressMaintenance/dropOffPoint'}
        />
      </APILoader>
    </Modal>
  );
};

export default HomePageOverseasWare;
