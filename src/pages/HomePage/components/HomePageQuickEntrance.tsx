import React, { useState } from 'react';
import { ProCard } from '@ant-design/pro-components';
import { Row, Col, message, Modal } from 'antd';
import './HomePageCard.less';
// @ts-ignore
import abnormalOrder from '../../../assets/abnormalOrder.png';
// @ts-ignore
import helpCenter from '../../../assets/helpCenter.png';
// @ts-ignore
import myBill from '../../../assets/myBill.png';
// @ts-ignore
import openPlatform from '../../../assets/openPlatform.png';
// @ts-ignore
import orderMakingAssistant from '../../../assets/orderMakingAssistant.png';
// @ts-ignore
import trackQuery from '../../../assets/trackQuery.png';
// @ts-ignore
import offlineRecharge from '../../../assets/offlineRecharge.png';
// @ts-ignore
import complaintsAndSuggestions from '../../../assets/complaintsAndSuggestions.png';
// @ts-ignore
import messageCenter from '../../../assets/messageCenter.png';
import { router } from 'umi';
import { isRouteAuth } from '@/utils/utils';
import { businessIntroduction } from '@/utils/commonConstant';
import useOpenBusinessStatus from '@/hooks/useOpenBusinessStatus';
import { useMount } from 'ahooks';
import { formatMessage } from 'umi-plugin-react/locale';
import { logSave, LogType } from '@/utils/logSave';

/**
 * 快捷入口
 * @returns
 */
const HomePageQuickEntrance = ({ dispatch }) => {
  const [smallBagStatus, setSmallBagStatus] = useState(false);

  const data = [
    {
      title: formatMessage({ id: '轨迹查询' }),
      icon: trackQuery,
      url: 'https://track.yw56.com.cn/',
      logType: 'home6',
    },
    {
      title: formatMessage({ id: '我的账单' }),
      icon: myBill,
      url: '/financialManagement/myBill',
      logType: 'home7',
    },
    {
      title: formatMessage({ id: '线下充值' }),
      icon: offlineRecharge,
      url: '/financialManagement/rechargeManagement/offlineTopUp',
      logType: 'home8',
    },
    {
      title: formatMessage({ id: '异常订单' }),
      icon: abnormalOrder,
      url: '/smallBag/anomalyOrder',
      logType: 'home9',
    },
    {
      title: formatMessage({ id: '消息中心' }),
      icon: messageCenter,
      url: '/serviceManagement/messageCenter/messageNotification',
      logType: 'home10',
    },
    {
      title: formatMessage({ id: '制单助手' }),
      icon: orderMakingAssistant,
      url: '/smallBag/orderManagement/makeAssistant',
      logType: 'home11',
    },
    {
      title: formatMessage({ id: '投诉建议' }),
      icon: complaintsAndSuggestions,
      url: '/serviceManagement/workOrder/complaints',
      logType: 'home12',
    },
    {
      title: formatMessage({ id: '帮助中心' }),
      icon: helpCenter,
      url: '/serviceManagement/helpCenter',
      logType: 'home13',
    },
    {
      title: formatMessage({ id: '开放平台' }),
      icon: openPlatform,
      url: 'https://www.yw56.com.cn/webfile/API%E6%8E%A5%E5%8F%A3/',
      logType: 'home14',
    },
  ];

  useMount(() => {
    initialFn();
  });

  const initialFn = async () => {
    const result = await Promise.allSettled([useOpenBusinessStatus(dispatch, 0)]);
    result.forEach(item => {
      if (item.status === 'fulfilled') {
        const { type, status } = item.value;
        if (type === 0) {
          setSmallBagStatus(status);
        }
      }
    });
  };

  const handleRouter = item => {
    const typeKeyOne = formatMessage({ id: '制单助手' });
    const typeKeyTwo = formatMessage({ id: '异常订单' });
    const urlTypeObj = {
      [typeKeyOne]: '0',
      [typeKeyTwo]: '0',
    };
    const businessObject = {
      '0': {
        title: '小包专线业务介绍',
        // url: ''
        okText: '去开通',
        onOk: () => {
          dispatch({
            type: 'homePage/handleOpenStatus',
            payload: {
              smallBagStatus: true, // 开通小包专线状态
              fbaStatus: false, // 开通FBA专线状态
            },
          });
          router.push('/homePageList');
        },
      },
      '1': {
        title: 'FBA专线业务介绍',
        okText: '去开通',
        onOk: () => {
          dispatch({
            type: 'homePage/handleOpenStatus',
            payload: {
              smallBagStatus: false, // 开通小包专线状态
              fbaStatus: true, // 开通FBA专线状态
            },
          });
          router.push('/homePageList');
        },
      },
    };
    if (!isRouteAuth(item?.url)) {
      return message.error(formatMessage({ id: '没有权限访问' }));
    } else if (Object.keys(urlTypeObj).includes(item?.title) && !smallBagStatus) {
      const key = urlTypeObj[item?.title];
      return Modal.confirm({
        title: businessObject[key]?.title ?? '',
        content: businessIntroduction[key],
        icon: null,
        okText: businessObject[key]?.okText ?? '去开通',
        cancelText: '暂不开通',
        onOk: businessObject[key]?.onOk ?? (() => {}),
      });
    }
    router.push(item.url);
  };

  const gotoTrack = (url: string) => {
    dispatch({
      type: 'homePage/getEncodeMerchant',
      callback: response => {
        if (response.success) {
          window.open(`${url}?target=${response?.data?.target}&time=${response?.data?.time}`);
        }
      },
    });
  };

  const handleHttpsRouter = (item: typeof data[number]) => {
    if (item?.title === formatMessage({ id: '轨迹查询' })) {
      gotoTrack(item?.url);
    } else {
      window.open(item.url);
    }
  };

  return (
    <ProCard
      title={formatMessage({ id: '快捷入口' })}
      bordered
      style={{ boxShadow: '0 4px 4px 0 rgba(190, 190, 190, 0.5)' }}
      headerBordered
    >
      <Row gutter={[20, 34]} style={{ marginBlockStart: '8px', marginBlockEnd: '32px' }}>
        {data &&
          data.map(item => (
            <Col span={8}>
              <div
                className="flex flex-col items-center justify-center cursor-pointer"
                onClick={() => {
                  logSave(LogType[item.logType]);
                  item?.url?.startsWith('https') ? handleHttpsRouter(item) : handleRouter(item);
                }}
              >
                <div className="w-14 h-14 text-center">
                  <img src={item.icon} />
                </div>
                <span>{item.title}</span>
              </div>
            </Col>
          ))}
      </Row>
    </ProCard>
  );
};
export default HomePageQuickEntrance;
