export default {
  请先开通小包或: '请先开通小包或',
  业务再进行试算: '业务再进行试算',
  运价试算: '运价试算',
  小包运价试算: '小包运价试算',
  美国加拿大澳大利亚只允许选择包税: '美国加拿大澳大利亚只允许选择包税',
  燕文: '燕文',
  根据国家: '根据国家',
  获取亚马逊揽收仓: '获取亚马逊揽收仓',
  清空仓库: '清空仓库',
  请选择国家: '请选择国家',
  列: '列',
  产品名称: '产品名称',
  产品说明: '产品说明',
  产品时效: '产品时效',
  预计运费: '预计运费',
  操作: '操作',
  去下单: '去下单',
  请输入出发地: '请输入出发地',
  出发地: '出发地',
  请输入目的国: '请输入目的国',
  目的国: '目的国',
  查询: '查询',
  单箱尺寸: '单箱尺寸',
  请输入正确的长: '请输入正确的长',
  长: '长',
  请输入正确的宽: '请输入正确的宽',
  宽: '宽',
  请输入正确的高: '请输入正确的高',
  高: '高',
  单箱重量: '单箱重量',
  请输入正确的重量: '请输入正确的重量',
  请输入正确的箱数: '请输入正确的箱数',
  箱数: '箱数',
  重置: '重置',
  地址类型: '地址类型',
  请选择类型: '请选择类型',
  仓库: '仓库',
  私人地址: '私人地址',
  请选择亚马逊仓库: '请选择亚马逊仓库',
  邮编: '邮编',
  请输入邮编: '请输入邮编',
  交税模式: '交税模式',
  请选择交税模式: '请选择交税模式',
  包税: '包税',
  自税: '自税',
  递延: '递延',
  新添加: '新添加',
  默认值: '默认值',
  数据: '数据',
  显示: '显示',
  数据数组: '数据数组',
  展开列: '展开列',
  商户状态: '商户状态',
  查询条件: '查询条件',
  是否展示目的地提示: '是否展示目的地提示',
  展示目的地提示内容: '展示目的地提示内容',
  去重函数: '去重函数',
  改变数据格式: '改变数据格式',
  目的国数据筛选: '目的国数据筛选',
  首先按照中文: '首先按照中文',
  二字码: '二字码',
  英文: '英文',
  拼音: '拼音',
  来精确匹配: '来精确匹配',
  匹配上放入数组: '匹配上放入数组',
  匹配不上则按照中文: '匹配不上则按照中文',
  拼音来进行模糊匹配: '拼音来进行模糊匹配',
  每次放入之前: '每次放入之前',
  都判断当前数组中是否存在此条记录信息: '都判断当前数组中是否存在此条记录信息',
  每次文本变化后都要按照此规则进行匹配: '每次文本变化后都要按照此规则进行匹配',
  日本: '日本',
  澳大利亚提示: '澳大利亚提示',
  燕文专线发往日本冲绳县地区有偏远附加费: '燕文专线发往日本冲绳县地区有偏远附加费',
  请输入邮编以便试算出更准确的费用: '请输入邮编以便试算出更准确的费用',
  澳大利亚: '澳大利亚',
  燕文专线发往澳大利亚按邮编分区计费: '燕文专线发往澳大利亚按邮编分区计费',
  需输入邮编进行试算: '需输入邮编进行试算',
  获取表格数据: '获取表格数据',
  金额: '金额',
  追踪等级: '追踪等级',
  超长费用: '超长费用',
  基础资费: '基础资费',
  干线费用: '干线费用',
  燃油费: '燃油费',
  附加费: '附加费',
  参考时效: '参考时效',
  产品类型: '产品类型',
  货品属性: '货品属性',
  是否计泡: '是否计泡',
  顺序: '顺序',
  计泡系数: '计泡系数',
  重量: '重量',
  资费: '资费',
  元: '元',
  折后价: '折后价',
  折扣值: '折扣值',
  燃油费率: '燃油费率',
  长宽高请同时存在: '长宽高请同时存在',
  表格张开渲染: '表格张开渲染',
  价格构成: '价格构成',
  计费方式: '计费方式',
  走货属性: '走货属性',
  申报价值: '申报价值',
  重量要求: '重量要求',
  包装尺寸: '包装尺寸',
  派送地址要求: '派送地址要求',
  退件重派: '退件重派',
  保险服务: '保险服务',
  赔偿标准: '赔偿标准',
  查询网址: '查询网址',
  其他要求: '其他要求',
  暂无: '暂无',
  国家: '国家',
  自然日: '自然日',
  特殊要求: '特殊要求',
  税费征收说明: '税费征收说明',
  点击查看详情: '点击查看详情',
  推荐: '推荐',
  跟踪类型: '跟踪类型',
  天: '天',
  计费重量: '计费重量',
  克: '克',
  返回响应: '返回响应',
  基础费: '基础费',
  干线费: '干线费',
  折扣: '折扣',
  创建订单: '创建订单',
  分页: '分页',
  限制只能输入数字: '限制只能输入数字',
  点击创建订单: '点击创建订单',
  北京: '北京',
  分页详细: '分页详细',
  共: '共',
  条: '条',
  请选择出发地: '请选择出发地',
  请选择: '请选择',
  普货: '普货',
  特货: '特货',
  敏感货: '敏感货',
  特品: '特品',
  实际重量: '实际重量',
  请填写重量: '请填写重量',
  请输入: '请输入',
  目的地: '目的地',
  请填写目的地: '请填写目的地',
  燕文专线: '燕文专线',
  燕文挂号: '燕文挂号',
  燕文经济: '燕文经济',
  中邮挂号: '中邮挂号',
  外邮产品: '外邮产品',
  商业快递: '商业快递',
  尺寸: '尺寸',
  隐藏: '隐藏',
  控制展开列: '控制展开列',
  自定义展开内容: '自定义展开内容',
  温馨提示: '温馨提示',
  此处所查结果仅做参考: '此处所查结果仅做参考',
  且所有产品不包含税费: '且所有产品不包含税费',
  最终运费以实际发生费用为准: '最终运费以实际发生费用为准',
  为保障试算结果更准确: '为保障试算结果更准确',
  请结合如下信息进行试算: '请结合如下信息进行试算',
  专线: '专线',
  快递类产品建议输入邮编查询: '快递类产品建议输入邮编查询',
  若包裹体积超大请输入尺寸查询: '若包裹体积超大请输入尺寸查询',
  计费重量标红则表示已计泡: '计费重量标红则表示已计泡',
  仅商业快递产品可通达: '仅商业快递产品可通达',
  海外仓: '海外仓',
  仓库地址: '仓库地址',
};
