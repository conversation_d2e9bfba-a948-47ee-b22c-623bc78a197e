import request from '@/utils/request';

const order = {
  getOrderLists: '/csc/ejf/current/getOrder', // 获取订单列表POST
  getProductName: '/csc/ejf/base/getChannel', // 获取仓号获取产品名称POST
  getCountriesName: '/csc/ejf/base/getCountry', // 获取产品编号获得国家POST
  getCurrency: '/csc/ejf/base/getCurrency', // 获取所有币种POST
  getOrderDetails: '/csc/ejf/order/getOrderById', // 根据订单号获得订单详情POST
  createOrders: '/csc/ejf/order/createOrder', // 创建订单POST
  confirmDelivery: '/csc/ejf/order/orderConfirmationDelivery', // 订单列表确认发货POST
  cancelWaybill: '/csc/ejf/order/cancelOrder', // 取消运单POST
  updateOrder: '/csc/ejf/draft/updateOrder', // 更新订单POST
  importOrderList: '/csc/ejf/draft/listOrder', // 获取导入订单的订单列表POST
  deleteOrder: '/csc/ejf/draft/deleteOrder', // 删除订单POST
  CliskApplyWithholding: '/csc/intercept/createInterceptByWayBillNo', // 申请截留POST
  submitVoucher: '/csc/ejf/draft/generateExpressCode', // 提交制单POST
  getOrderDetailById: '/csc/ejf/draft/orderDetails', // 导入订单重新制单获取详情POST
  interceptCancel: '/csc/intercept/cancelInterceptEJF', // 取消截留POST
  getEJFGenericTemplate: '/csc/ejf/draft/getEJFGenericTemplate', // 获取EJF导入模板POST
  exportData: '/csc/ejf/order/printInfo', // 导出数据POST
  getShippingAccount: '/csc/ejf/order/getShippingAccount', // 获取制单账号POST
  clickEJFGenericTemplate: '/csc/ejf/draft/downloadEJFGenericTemplate', // 点击模板下载POST  params is id
  getPrintLabelData: '/csc/ejf/order/printLabels', // 美国地址校验POST
  HKPrintLabelData: '/csc/ejf/order/HKPrintLabelData', // 香港标签打印
  temporaryData: '/csc/ejf/draft/createOrderSimple', // 暂存数据POST
  chargePriceSimple: '/csc/web/chargePriceSimple', // 价格试算
  getVoucherIsEditData: '/csc/ejf/current/isEdit', // 查看订单查询列表已制单状态数据是否可以编辑POST
  changeWaybill: '/csc/ejf/order/editOrder', // EJF已制单编辑修改运单POST
  cancelConfirmDelivery: '/csc/ejf/order/cancelConfirmationDelivery', // 取消确认发货POST
  getSignature: '/csc/ejf/order/getSignature', // 获取签收图片POST
  downloadSignature: '/csc/ejf/order/downloadSignature', // 获取签收图片POST
  submitVoucherAll: '/csc/ejf/current/generateExpressCodeAll', // POST
  whetherTheOrderNumberExists: '/csc/ejf/current/whetherTheOrderNumberExists', // 检测订单号是否已经存在POST
  whetherTheOrderNumberExistsList: '/csc/ejf/current/whetherTheOrderNumberExistsList', // 检测订单号是否已经存在-提交制单POST
  getCheckPoint: '/csc/track/getCheckPoint', // 获取节点 POST
  getHandoverCodes: '/csc/ejf/order/getOverseasDeliveryLocation', // 获取海外交货地 POST
};

export async function getCheckPoint(params: any) {
  return request(order.getCheckPoint, {
    method: 'POST',
    body: params,
  });
}

export async function getHandoverCodes(params: any) {
  return request(order.getHandoverCodes, {
    method: 'POST',
    body: params,
  });
}

export async function clickEJFGenericTemplate(id: any) {
  return request(`${order.clickEJFGenericTemplate}/${id}`, {
    method: 'POST',
  });
}

export async function getOrderLists(params: any) {
  return request(order.getOrderLists, {
    method: 'POST',
    body: params,
    noMessage: true,
  });
}

export async function getProductName(params: any) {
  return request(order.getProductName, {
    method: 'POST',
    body: params,
  });
}

export async function getCountriesName(params: any) {
  return request(
    params != undefined ? `${order.getCountriesName}/${params}` : order.getCountriesName,
    {
      method: 'POST',
      body: params,
    }
  );
}

export async function getCurrency(params: any) {
  return request(order.getCurrency, {
    method: 'POST',
    body: params,
  });
}

export async function getOrderDetails(params: any) {
  return request(order.getOrderDetails, {
    method: 'POST',
    body: params,
  });
}

export async function createOrders(params: any) {
  return request(order.createOrders, {
    method: 'POST',
    body: params,
  });
}

export async function confirmDelivery(params: any) {
  return request(order.confirmDelivery, {
    method: 'POST',
    body: params,
    noMessage: true,
  });
}

export async function cancelWaybill(params: any) {
  return request(order.cancelWaybill, {
    method: 'POST',
    body: params,
    noMessage: true,
  });
}

export async function updateOrder(params: any) {
  return request(order.updateOrder, {
    method: 'POST',
    body: params,
  });
}

export async function importOrderList(params: any) {
  return request(order.importOrderList, {
    method: 'POST',
    body: params,
  });
}

export async function deleteOrder(params: any) {
  return request(order.deleteOrder, {
    method: 'POST',
    body: params,
    noMessage: true,
  });
}

export async function CliskApplyWithholding(params: any) {
  return request(order.CliskApplyWithholding, {
    method: 'POST',
    body: params,
  });
}

export async function submitVoucher(params: any) {
  return request(order.submitVoucher, {
    method: 'POST',
    body: params,
    noMessage: true,
  });
}

export async function getOrderDetailById(params: any) {
  return request(order.getOrderDetailById, {
    method: 'POST',
    body: params,
  });
}

export async function interceptCancel(params: any) {
  return request(order.interceptCancel, {
    method: 'POST',
    body: params,
  });
}

export async function getEJFGenericTemplate(params: any) {
  return request(order.getEJFGenericTemplate, {
    method: 'POST',
    body: params,
  });
}

export async function exportData(params: any) {
  return request(order.exportData, {
    method: 'POST',
    body: params,
  });
}

export async function getShippingAccount(params: any) {
  return request(order.getShippingAccount, {
    method: 'POST',
    body: params,
  });
}

export async function getPrintLabelData(params: any) {
  return request(order.getPrintLabelData, {
    method: 'POST',
    body: params,
    noMessage: true,
  });
}

export async function HKPrintLabelData(params: any) {
  return request(order.HKPrintLabelData, {
    method: 'POST',
    body: params,
    noMessage: true,
  });
}

export async function temporaryData(params: any) {
  return request(order.temporaryData, {
    method: 'POST',
    body: params,
  });
}

export async function chargePriceSimple(params: any) {
  return request(order.chargePriceSimple, {
    method: 'POST',
    body: params,
  });
}

export async function getVoucherIsEditData(params: any) {
  return request(order.getVoucherIsEditData, {
    method: 'POST',
    body: params,
    noMessage: true,
  });
}

export async function changeWaybill(params: any) {
  return request(order.changeWaybill, {
    method: 'POST',
    body: params,
  });
}

export async function cancelConfirmDelivery(params: any) {
  return request(order.cancelConfirmDelivery, {
    method: 'POST',
    body: params,
  });
}

export async function getSignature(params: any) {
  return request(order.getSignature, {
    method: 'POST',
    body: params,
  });
}

export async function downloadSignature(params: any) {
  return request(order.downloadSignature, {
    method: 'POST',
    body: params,
  });
}

export async function submitVoucherAll(params: any) {
  return request(order.submitVoucherAll, {
    method: 'POST',
    body: params,
    noMessage: true,
  });
}

export async function whetherTheOrderNumberExists(params: any) {
  return request(order.whetherTheOrderNumberExists, {
    method: 'POST',
    body: params,
    noMessage: true,
  });
}

export async function whetherTheOrderNumberExistsList(params: any) {
  return request(order.whetherTheOrderNumberExistsList, {
    method: 'POST',
    body: params,
    noMessage: true,
  });
}
