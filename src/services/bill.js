import request from '@/utils/request';

// 我的账单接口
const myBill = {
  getBillBalance: '/csc/bill/getBillBalance', // 查询账单余额
  getNumberList: '/csc/bill/getNumberList', // 查询运单明细
  getAccountPeriodList: '/csc/bill/getAccountPeriodList', // 查询账期列表
  typeList: '/csc/bill/historicalBill/list', // 查询类型清单
  getHistoryListByType: '/csc/bill/getHistoryListByType', // 查询精确查询类型清单
  detail: '/csc/bill/detail/list', // 查询类型清单详情
  getBankAccountList: '/csc/settleBankAccount/getBankAccountList', // 查询银行信息
  unBilledBillList: '/csc/bill/unBilledBillList/list', // 未出账单列表
  validateHwpMerchant: '/csc/index/validateHwpMerchant', // 判断是不是海外派客户
  withoutList: '/csc/bill/without/list', // 制单豫扣款明细
  sendWinglist: '/csc/bill/without/sendWinglist', // 海外派（制单预扣金额）
  getChargeRecordList: '/csc/bill/getChargeRecordList', // 充值记录
  getFbaBankAccountList: '/csc/settleBankAccount/getFbaBankAccountList', // FBA查询账单
  cancelOrderWithDebitPage: '/csc/ejf/order/cancelOrderWithDebitPage', // 小包取消订单
  export: '/csc/bill/detail/export', // 账单详情下载
  exportCheck: '/csc/bill/detail/exportCheck', // 判断下载是否完成,同时后台发起下载
  getEBillList: '/csc/bill/getEBillList', //POST 查询电子账单列表
  getOverseaRecord: '/csc/oversea/getOverseaRecord', //POST 查询电子账单列表
  createAlipayScanOrder: '/csc/bill/createAlipayScanOrder', // 创建二维码支付订单
  queryOrderState: '/csc/bill/queryOrderState', // 查询二维码订单支付状态
  generateExampleData: '/csc/bill/generateExampleData', // 生成测试账单数据
  addReceiverConfig: '/csc/bill/addReceiverConfig', // 修改或添加配置
  queryReceiverConfig: '/csc/bill/queryReceiverConfig', // 查询配置
  unBilledTaxBill: '/csc/bill/unBilledTaxBill/list', // 查询未出账单税金
};

export async function unBilledTaxBill(params) {
  return request(myBill.unBilledTaxBill, {
    method: 'POST',
    body: params,
  });
}

export async function queryReceiverConfig(params) {
  return request(myBill.queryReceiverConfig, {
    method: 'POST',
    body: params,
  });
}

export async function addReceiverConfig(params) {
  return request(myBill.addReceiverConfig, {
    method: 'POST',
    body: params,
  });
}

export async function generateExampleData(params) {
  return request(myBill.generateExampleData, {
    method: 'POST',
    body: params,
  });
}

export async function queryOrderState(params) {
  return request(myBill.queryOrderState, {
    method: 'POST',
    body: params,
  });
}

export async function createAlipayScanOrder(params) {
  return request(myBill.createAlipayScanOrder, {
    method: 'POST',
    body: params,
  });
}

export async function getOverseaRecord(params) {
  return request(myBill.getOverseaRecord, {
    method: 'POST',
    body: params,
  });
}

export async function getEBillList(params) {
  return request(myBill.getEBillList, {
    method: 'POST',
    body: params,
  });
}

export async function exportCheck(params) {
  const commonParams = `transType=${params.transType}&mainNo=${params.mainNo}&currentPage=${params.currentPage}&pageSize=${params.pageSize}&waybillNumber=${params.waybillNumber}`;
  const url = params?.startDate
    ? `${myBill.exportCheck}?startCalcTime=${params.startDate}&endCalcTime=${params.endDate}&${commonParams}`
    : `${myBill.exportCheck}?${commonParams}`;
  return request(url, {
    method: 'GET',
  });
}

export async function exportFile(params) {
  const commonParams = `transType=${params.transType}&mainNo=${params.mainNo}&currentPage=${params.currentPage}&pageSize=${params.pageSize}&waybillNumber=${params.waybillNumber}`;
  const url = params?.startDate
    ? `${myBill.export}?startCalcTime=${params.startDate}&endCalcTime=${params.endDate}&${commonParams}`
    : `${myBill.export}?${commonParams}`;
  return request(url, {
    method: 'GET',
  });
}

export async function cancelOrderWithDebitPage(params) {
  return request(myBill.cancelOrderWithDebitPage, {
    method: 'POST',
    body: params,
  });
}

export async function getBillBalance(params) {
  return request(myBill.getBillBalance, {
    method: 'POST',
    body: params,
  });
}
export async function getNumberList(params) {
  return request(myBill.getNumberList, {
    method: 'POST',
    body: params,
  });
}
export async function getAccountPeriodList(params) {
  return request(myBill.getAccountPeriodList, {
    method: 'POST',
    body: params,
  });
}
export async function typeList(params) {
  return request(myBill.typeList, {
    method: 'POST',
    body: params,
  });
}
export async function getHistoryListByType(params) {
  return request(myBill.getHistoryListByType, {
    method: 'POST',
    body: params,
  });
}
export async function detail(params) {
  return request(myBill.detail, {
    method: 'POST',
    body: params,
  });
}

export async function getBankAccountList() {
  return request(myBill.getBankAccountList, {
    method: 'GET',
  });
}

export async function unBilledBillList(params) {
  return request(myBill.unBilledBillList, {
    method: 'POST',
    body: params,
  });
}

export async function validateHwpMerchant() {
  return request(myBill.validateHwpMerchant, {
    method: 'GET',
  });
}

export async function withoutList(params) {
  return request(myBill.withoutList, {
    method: 'POST',
    body: params,
  });
}

export async function sendWinglist(params) {
  return request(myBill.sendWinglist, {
    method: 'POST',
    body: params,
  });
}

export async function getChargeRecordList(params) {
  return request(myBill.getChargeRecordList, {
    method: 'POST',
    body: params,
  });
}

export async function getFbaBankAccountList() {
  return request(myBill.getFbaBankAccountList, {
    method: 'GET',
  });
}
