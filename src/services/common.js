import request from '@/utils/request';

const common = {
  getSliderCaptcha: '/csc/api/sys/auth/getSliderCapatcha', // 获取滑块验证码
  getNowYear: '/csc/api/sys/nowYear', // 获取当前年份
  getWarehouseByType: '/csc/common/getWarehouseByType', // 根据类型查询仓库列表
  getCountryCodeList: '/csc/common/getCountryCodeList', // 查询省市区
  loadAccounts: '/csc/account/loadAccounts', // 查询制单账号列表
  getBusinessStatusByTypes: '/csc/index/getBusinessStatusByTypes', // 查询多个业务线是否开通情况
  createTask: '/csc/downloadTask/createTask', // 创建下载任务
  deleteTask: '/csc/downloadTask/deleteTask', // 删除下载任务
  download: '/csc/downloadTask/download', // 下载任务文件
  queryTaskState: '/csc/downloadTask/queryTaskState', // 查询任务的状态
  list: '/csc/downloadTask/list', // 查询下载任务列表
  getPacketWarehouse: '/csc/packet/getPacketWarehouse', // 根据类型查询仓库列表
  getCityPostCodeList: '/csc/common/getCityPostCodeList', // 查询城市邮编列表
  getCurrencyRateList: '/csc/common/getCurrencyRateList', // 查询汇率
  getAbnormalTypeList: '/csc/common/getAbnormalTypeList', // 查询异常类型
};

export async function getAbnormalTypeList(params) {
  return request(common.getAbnormalTypeList, {
    method: 'GET',
  });
}

export async function getCurrencyRateList(params) {
  return request(common.getCurrencyRateList, {
    method: 'POST',
    body: params,
  });
}

export async function getCityPostCodeList(params) {
  return request(common.getCityPostCodeList, {
    method: 'POST',
    body: params,
  });
}

export async function getAllWarehouseByType(params) {
  return request(`${common.getWarehouseByType}/${params}`, {
    method: 'GET',
  });
}

export async function createTask(params) {
  return request(common.createTask, {
    method: 'POST',
    body: params,
  });
}

export async function deleteTask(params) {
  return request(`${common.deleteTask}?taskId=${params.taskId}`, {
    method: 'GET',
  });
}

export async function download(params) {
  return request(`${common.download}?taskId=${params}`, {
    method: 'GET',
  });
}

export async function queryTaskState(params) {
  return request(`${common.queryTaskState}?taskId=${params}`, {
    method: 'GET',
  });
}

export async function list(params) {
  return request(common.list, {
    method: 'POST',
    body: params,
  });
}

export async function getSliderCaptcha(params) {
  return request(common.getSliderCaptcha, {
    method: 'GET',
    params,
  });
}

export async function getNowYear(params) {
  return request(common.getNowYear, {
    method: 'GET',
    params,
  });
}

export async function getWarehouseByType(params) {
  return request(`${common.getPacketWarehouse}`, {
    method: 'GET',
  });
}

export async function getCountryCodeList(params) {
  return request(common.getCountryCodeList, {
    method: 'POST',
    body: params,
  });
}

export async function loadAccounts(params) {
  return request(common.loadAccounts, {
    method: 'POST',
    body: params,
  });
}

export async function getBusinessStatusByTypes(params) {
  return request(common.getBusinessStatusByTypes, {
    method: 'POST',
    body: params,
  });
}
