/*
 * @Description:
 * @Author:
 * @Date: 2023-03-16 14:09:54
 * @LastEditTime: 2023-03-16 14:26:55
 * @LastEditors:
 */
import request from '@/utils/request';

// #region  订单管理-时效统计
const timeBaseStatistics = {
  getPrescriptionList: '/csc/track/trackStatistics', // 获取时效列表数据
  getReport: '/csc/timebase/getReport', // 时效报表
  getDetail: '/csc/timebase/getDetail', // 时效报表
  getCustomerData: '/csc/packet/getCustomerData', // 查询资料完整度信息
  unFreezeMerchant: '/csc/packet/unFreezeMerchant', // 解冻申请
  contractSign: '/csc/packet/contractSign', // 发起补签合同
  updateContact: '/csc/packet/updateContact', // 修改小包联系人和紧急联系人
  unFreezeNotice: '/csc/packet/unFreezeNotice', // 解冻通知
  validateCustomerPayment: '/csc/packet/validateCustomerPayment', // 校验法人付款账号是否缺失
};

export async function validateCustomerPayment() {
  return request(timeBaseStatistics.validateCustomerPayment, {
    method: 'POST',
    noMessage: true,
  });
}

export async function unFreezeNotice() {
  return request(timeBaseStatistics.unFreezeNotice, {
    method: 'POST',
    noMessage: true,
  });
}

export async function updateContact(params) {
  return request(timeBaseStatistics.updateContact, {
    method: 'POST',
    body: params,
  });
}

export async function contractSign() {
  return request(timeBaseStatistics.contractSign, {
    method: 'GET',
  });
}

export async function unFreezeMerchant(params) {
  return request(timeBaseStatistics.unFreezeMerchant, {
    method: 'POST',
    body: params,
  });
}

export async function getCustomerData() {
  return request(timeBaseStatistics.getCustomerData, {
    method: 'GET',
  });
}

export async function getPrescriptionList(params) {
  return request(timeBaseStatistics.getPrescriptionList, {
    method: 'POST',
    body: params,
  });
}

export async function getTimeBaseReport(params) {
  return request(timeBaseStatistics.getReport, {
    method: 'POST',
    body: params,
  });
}

export async function getTimeBaseDetail(params) {
  return request(timeBaseStatistics.getDetail, {
    method: 'POST',
    body: params,
  });
}
// #endregion 订单管理-时效统计

// #region 订单管理-待领取运单
const waybillCollected = {
  getWaybillCollectedList: '/csc/track/notTake', // 获取待领取运单列表
  notTakeByGroup: '/csc/track/notTakeByGroup', // 待领取运单数据汇总
  recordTheWaybill: '/csc/track/recordTheWaybill', // 待领取运单已读信息保存
  trackingCheckPoint: '/csc/track/checkPoint', //查询追踪详情
  toTakeDetail: '/csc/track/export/toTakeDetail', //导出数据
};

export async function getWaybillCollectedList(params) {
  return request(waybillCollected.getWaybillCollectedList, {
    method: 'POST',
    body: params,
  });
}

export async function notTakeByGroup(params) {
  return request(waybillCollected.notTakeByGroup, {
    method: 'POST',
    body: params,
    noMessage: true,
  });
}

export async function recordTheWaybill(params) {
  return request(waybillCollected.recordTheWaybill, {
    method: 'POST',
    body: params,
  });
}

export async function trackingCheckPoint(params) {
  return request(`${waybillCollected.trackingCheckPoint}/${params.number}`, {
    method: 'GET',
  });
}

export async function toTakeDetail(params) {
  return request(`${waybillCollected.toTakeDetail}`, {
    method: 'POST',
    body: params,
  });
}

//  #endregion 订单管理-待领取运单

// #region 账号管理-平台账号管理
const platformAccount = {
  getPlatformAccounts: '/csc/platform/getPlatformAccounts', // 获取平台账号列表
  addPlatformAccount: '/csc/platform/addPlatformAccount', // 新增平台账号
  addPlatformAccountPage: '/csc/platform/addPlatformAccountPage', // 获取新增平台账号需要的参数
  updatePlatformAccount: '/csc/platform/updatePlatformAccount', // 编辑平台账号
};

export async function updatePlatformAccount(params) {
  return request(platformAccount.updatePlatformAccount, {
    method: 'POST',
    body: params,
  });
}

export async function getPlatformAccounts(params) {
  return request(platformAccount.getPlatformAccounts, {
    method: 'POST',
    body: params,
  });
}

export async function addPlatformAccount(params) {
  return request(platformAccount.addPlatformAccount, {
    method: 'POST',
    body: params,
  });
}

export async function addPlatformAccountPage(params) {
  return request(platformAccount.addPlatformAccountPage, {
    method: 'POST',
    body: params,
  });
}

// #endregion 账号管理-平台账号管理

// #region 账号管理-制单账号管理
const makeAccount = {
  saveAccount: '/csc/account/saveAccount', // 保存制单账号
  loadAccounts: '/csc/account/loadAccounts', // 查询制单账号列表
  editAccount: '/csc/account/editAccount', // 编辑制单账号
};

export async function makeAccountSave(params) {
  return request(makeAccount.saveAccount, {
    method: 'POST',
    body: params,
  });
}

export async function makeAccountLoad(params) {
  return request(makeAccount.loadAccounts, {
    method: 'POST',
    body: params,
  });
}

export async function makeAccountEdit(params) {
  return request(makeAccount.editAccount, {
    method: 'POST',
    body: params,
  });
}

// #endregion 账号管理-制单账号管理

// #region 小包专线
const smallBag = {
  getBankAccountList: '/csc/settleBankAccount/getBankAccountList', // 查询结算银行信息
  getExpresses: '/csc/ejf/current/getExpresses', // 小包首页数量统计
  getPacketBaseInfo: '/csc/packet/getBasePacketInfo', // 查询小包业务线基础信息
  getBillBalance: '/csc/bill/getBillBalance', // 查询业务线下商户账单信息
  getPacketApplyStatus: '/csc/packet/getPacketApplyStatus', // 查询小包业务状态
  getPacketMerchantCode: '/csc/packet/getPacketMerchantCode', // 查询小包业务账号
  getPacketMessageNumber: '/csc/index/reminder/message', // 异常包裹数量
  getPayableAmount: '/csc/bill/getPayableAmount', // 查询小包业务线账单周期
};

export async function getPacketBaseInfo() {
  return request(smallBag.getPacketBaseInfo, {
    method: 'GET',
  });
}

export async function getBankAccountList() {
  return request(smallBag.getBankAccountList, {
    method: 'GET',
  });
}

export async function getExpresses(params) {
  return request(smallBag.getExpresses, {
    method: 'POST',
    body: params,
    noMessage: true,
  });
}

export async function getBillBalance(params) {
  return request(smallBag.getBillBalance, {
    method: 'POST',
    body: params,
  });
}

export async function getPacketApplyStatus() {
  return request(smallBag.getPacketApplyStatus, {
    method: 'GET',
    noMessage: true,
  });
}

export async function getPacketMerchantCode() {
  return request(smallBag.getPacketMerchantCode, {
    method: 'GET',
  });
}

export async function getPacketMessageNumber(params) {
  return request(smallBag.getPacketMessageNumber, {
    method: 'POST',
    body: params,
  });
}

export async function getPayableAmount(params) {
  return request(smallBag.getPayableAmount, {
    method: 'POST',
    body: params,
  });
}

// #endregion 小包专线

// #region 小包专线-Temu尾程面单上传
const temu = {
  getProductList: '/csc/temu/getProductList', // 查询产品接口
  getOrderLabelList: '/csc/temu/getOrderLabelList', // 查询运单列表接口
  getOrderLabelDetail: '/csc/temu/getOrderLabelDetail', // 预览面单接口
  importLabel: '/csc/temu/importLabel', // 上传接口
  exportLabelTemplate: '/csc/temu/exportLabelTemplate', // 下载模版接口
  deleteLabel: '/csc/temu/deleteLabel', // 删除面单接口
};

export async function deleteLabel(params) {
  return request(temu.deleteLabel, {
    method: 'POST',
    body: params,
  });
}

export async function exportLabelTemplate(params) {
  return request(temu.exportLabelTemplate, {
    method: 'POST',
    body: params,
  });
}

export async function importLabel(params) {
  return request(temu.importLabel, {
    method: 'POST',
    body: params,
  });
}

export async function getOrderLabelDetail(params) {
  return request(temu.getOrderLabelDetail, {
    method: 'POST',
    body: params,
  });
}

export async function getOrderLabelList(params) {
  return request(temu.getOrderLabelList, {
    method: 'POST',
    body: params,
  });
}

export async function getProductList() {
  return request(temu.getProductList, {
    method: 'POST',
  });
}

// #endregion 小包专线-Temu尾程面单上传
