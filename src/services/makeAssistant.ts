import request from '@/utils/request';

const makeAssistant = {
  uploadFiles: '/csc/ejf/kyc/addKYC', // 制单助手上传图片
  getKYCList: '/csc/ejf/kyc/listKYC', // 查询本地KYC记录
  updateFiles: '/csc/ejf/kyc/updateKYC', // 修改上传失败的图片
  delKYCRecord: '/csc/ejf/kyc/deleteKYC', // 删除kyc记录
  recordUpload: '/csc/ejf/kyc/uploadDataForRecord', // kyc备案上传
  getRecordedList: '/csc/ejf/kyc/queryUploadedList', // 获取已备案列表数据
  USAddressCheck: '/csc/ejf/kyc/USAddressDataVerification', // 美国地址校验
  exportUSAAddressData: '/csc/ejf/kyc/exportUSAAddressData', // 美国地址校验数据导出
  getShippingAccount: '/csc/ejf/order/getShippingAccount', // 获取制单账号
  getCountriesName: '/csc/ejf/base/getCountry', // 获取产品编号获得国家
  getProductName: '/csc/ejf/base/getChannel', // 获取仓号获取产品名称
  restrictNameQuery: '/csc/ejf/limit/nameRestriction', // 制单助手限制品名查询
  germanPostcodeValidAddressQuery: '/csc/ejf/limit/germanPostcodeValidAddressQuery', // 德邮邮编有效地址查询
  frenchPostcodeValidForCityEnquiries: '/csc/ejf/limit/frenchPostcodeValidForCityEnquiries', // 法邮邮编有效城市查询
  getKYCDerive: '/csc/ejf/kyc/exportUploadedData', // 印度kyc已上传导出
  getUSATaxRateList: '/csc/ejf/kyc/getUSATaxRateList', // 查询美国税率
  getUSAHTSRateList: '/csc/ejf/kyc/getUSAHTSRateList', // 查询美国海关编码税率
  getUSAProductList: '/csc/ejf/kyc/getUSAProductList', // 查询美国产品
  getUSACalculateRate: '/csc/ejf/kyc/getUSACalculateRate', // 美国试算汇率
};

export async function getUSACalculateRate(params: any) {
  return request(makeAssistant.getUSACalculateRate, {
    method: 'POST',
    body: params,
  });
}

export async function getUSAProductList(params: any) {
  return request(makeAssistant.getUSAProductList, {
    method: 'POST',
    body: params,
  });
}

export async function getUSAHTSRateList(params: any) {
  return request(makeAssistant.getUSAHTSRateList, {
    method: 'POST',
    body: params,
  });
}

export async function getUSATaxRateList(params: any) {
  return request(makeAssistant.getUSATaxRateList, {
    method: 'POST',
    body: params,
  });
}

export async function exportUSAAddressData(params: any) {
  return request(makeAssistant.exportUSAAddressData, {
    method: 'POST',
    body: params,
  });
}

export async function uploadFiles(params: any) {
  return request(makeAssistant.uploadFiles, {
    method: 'POST',
    body: params,
    noMessage: true,
  });
}

export async function getKYCList(params: any) {
  return request(makeAssistant.getKYCList, {
    method: 'POST',
    body: params,
  });
}

export async function updateFiles(params: any) {
  return request(`${makeAssistant.updateFiles}/${params.id}/${params.type}`, {
    method: 'POST',
    body: params.formData,
  });
}

export async function delKYCRecord(params: any) {
  return request(makeAssistant.delKYCRecord, {
    method: 'POST',
    body: params,
  });
}

export async function recordUpload(params: any) {
  return request(makeAssistant.recordUpload, {
    method: 'POST',
    body: params,
  });
}

export async function getRecordedList(params: any) {
  return request(makeAssistant.getRecordedList, {
    method: 'POST',
    body: params,
  });
}

export async function USAddressCheck(params: any) {
  return request(makeAssistant.USAddressCheck, {
    method: 'POST',
    body: params,
  });
}

export async function getShippingAccount(params: any) {
  return request(makeAssistant.getShippingAccount, {
    method: 'POST',
    body: params,
  });
}

export async function getCountriesName(params: any) {
  return request(
    params ? `${makeAssistant.getCountriesName}/${params}` : makeAssistant.getCountriesName,
    {
      method: 'POST',
      body: params,
    }
  );
}

export async function getProductName(params: any) {
  return request(makeAssistant.getProductName, {
    method: 'POST',
    body: params,
  });
}

export async function restrictNameQuery(params: any) {
  return request(makeAssistant.restrictNameQuery, {
    method: 'POST',
    body: params,
  });
}

export async function germanPostcodeValidAddressQuery(params: any) {
  return request(makeAssistant.germanPostcodeValidAddressQuery, {
    method: 'POST',
    body: params,
  });
}

export async function frenchPostcodeValidForCityEnquiries(params: any) {
  return request(makeAssistant.frenchPostcodeValidForCityEnquiries, {
    method: 'POST',
    body: params,
  });
}

export async function getKYCDerive(params: any) {
  return request(makeAssistant.getKYCDerive, {
    method: 'POST',
    body: params,
  });
}
