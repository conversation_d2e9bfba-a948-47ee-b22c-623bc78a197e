import request from '@/utils/request';

/*
 *@Description: 问题件
 *@MethodAuthor: dangh
 *@Date: 2023-04-11 16:42:22
 */
const problemPieces = {
  problemPiecesList: '/csc/abnormal/list', // 异常件列表 POST
  problemParcelUploading: '/csc/abnormal/problemParcel/uploading', // 异常件导入POST
  problemPiecesStatisticsnumber: '/csc/abnormal/statisticsNumber', // 异常件头部数量POST
  problemPiecesOrderinformation: '/csc/abnormal/orderInformation', // 异常件修改信息POST
  problemPiecesOldList: '/csc/abnormal/oldList', // 异常历史工单列表POST
  problemPiecesGetHandleMode: '/csc/abnormal/getHandleMode', // 异常方案配置GET
  problemPiecesAllAbnormalCause: '/csc/abnormal/AllAbnormalCause', // 所有异常原因GET
  problemPieceschooseHandleType: '/csc/abnormal/chooseHandleType', // 异常件处理POST
  problemPiecesDownload: '/csc/abnormal/problemParcel/download', // 异常件列表导出POST
  problemPiecesGetByOrderId: '/csc/workOrder/getByOrderId', // 异常工单信息POSt
  getFreezeStatus: '/csc/packet/getFreezeStatus', // 获取商户是否是冻结状态get
  taxationNumber: '/csc/abnormal/edit/taxationNumber', // 修改寄件人税号 post
  computational: '/csc/abnormal/computational', // 异常泡重计算费用
  getAddress: '/csc/abnormal/getAddress', // 获取退件地址
  commercialExpressList: '/csc/commercialExpress/list', // 查询列表
  commercialExpressDetail: '/csc/commercialExpress/detail', // 查询详情
  commercialExpressDispose: '/csc/commercialExpress/dispose', // 处理运单
  orderInformation: '/csc/abnormal/orderInformation', // 异常件修改信息
  getMatchNameList: '/csc/abnormal/getMatchNameList', // 查询品名
  productNameChangeQueryList: '/csc/productNameChange/queryList', // 查询品名工单列表
  productNameChangeBatchHandle: '/csc/productNameChange/batchHandle', // 品名处理接口
  productNameChangeDetail: '/csc/productNameChange/getOperatorLog', // 品名工单详情
  getOperaLog: '/csc/abnormal/getOperaLog', // 查询异常件日志
};

export async function getOperaLog(params) {
  return request(problemPieces.getOperaLog, {
    method: 'POST',
    body: params,
  });
}

export async function productNameChangeDetail(params) {
  return request(problemPieces.productNameChangeDetail, {
    method: 'POST',
    body: params,
  });
}

export async function productNameChangeBatchHandle(params) {
  return request(problemPieces.productNameChangeBatchHandle, {
    method: 'POST',
    body: params,
  });
}

export async function productNameChangeQueryList(params) {
  return request(problemPieces.productNameChangeQueryList, {
    method: 'POST',
    body: params,
  });
}

export async function getMatchNameList(params) {
  return request(problemPieces.getMatchNameList, {
    method: 'POST',
    body: params,
  });
}

export async function orderInformation(params) {
  return request(problemPieces.orderInformation, {
    method: 'POST',
    body: params,
  });
}

export async function commercialExpressDispose(params) {
  return request(problemPieces.commercialExpressDispose, {
    method: 'POST',
    body: params,
  });
}

export async function commercialExpressDetail(params) {
  return request(problemPieces.commercialExpressDetail, {
    method: 'POST',
    body: params,
  });
}

export async function commercialExpressList(params) {
  return request(problemPieces.commercialExpressList, {
    method: 'POST',
    body: params,
  });
}

export async function getAddress(params) {
  return request(problemPieces.getAddress, {
    method: 'POST',
    body: params,
  });
}

export async function computational(params) {
  return request(problemPieces.computational, {
    method: 'POST',
    body: params,
  });
}

export async function taxationNumber(params) {
  return request(problemPieces.taxationNumber, {
    method: 'POST',
    body: params,
    noMessage: true,
  });
}

export async function getFreezeStatus() {
  return request(problemPieces.getFreezeStatus, {
    method: 'GET',
  });
}

export async function problemPiecesList(params: any) {
  return request(problemPieces.problemPiecesList, {
    method: 'POST',
    body: params,
  });
}

export async function problemParcelUploading(params: any) {
  return request(problemPieces.problemParcelUploading, {
    method: 'POST',
    body: params,
  });
}

export async function problemPiecesStatisticsnumber(params: any) {
  return request(problemPieces.problemPiecesStatisticsnumber, {
    method: 'POST',
    body: params,
  });
}

export async function problemPiecesOrderinformation(params: any) {
  return request(problemPieces.problemPiecesOrderinformation, {
    method: 'POST',
    body: params,
  });
}

export async function problemPiecesOldList(params: any) {
  return request(problemPieces.problemPiecesOldList, {
    method: 'POST',
    body: params,
  });
}

export async function problemPiecesGetHandleMode(params: any) {
  return request(problemPieces.problemPiecesGetHandleMode, {
    method: 'GET',
    body: params,
  });
}

export async function problemPiecesAllAbnormalCause(params: any) {
  return request(problemPieces.problemPiecesAllAbnormalCause, {
    method: 'GET',
    body: params,
  });
}

export async function problemPieceschooseHandleType(params: any) {
  return request(problemPieces.problemPieceschooseHandleType, {
    method: 'POST',
    body: params,
    noMessage: true,
  });
}

export async function problemPiecesDownload(params: any) {
  return request(problemPieces.problemPiecesDownload, {
    method: 'POST',
    body: params,
  });
}
export async function problemPiecesGetByOrderId(params: any) {
  return request(problemPieces.problemPiecesGetByOrderId, {
    method: 'POST',
    body: params,
    noMessage: true,
  });
}

/*
 *@Description: 退件签收 returnReceipt
 *@MethodAuthor: dangh
 *@Date: 2023-04-11 16:44:35
 */

const returnReceipt = {
  returnReceiptList: '/csc/abnormalReturn/list', // 退件列表POST
  returnReceiptBatchConfirmation: '/csc/abnormalReturn/batchConfirmation', // 批量确认签收POST
  returnReceiptNumbers: '/csc/abnormalReturn/numbers', // 退件数量POST
  returnReceiptExcelDownload: '/csc/abnormalReturn/excelDownload', // 导出文件POST
};

export async function returnReceiptList(params: any) {
  return request(returnReceipt.returnReceiptList, {
    method: 'POST',
    body: params,
  });
}

export async function returnReceiptBatchConfirmation(params: any) {
  return request(returnReceipt.returnReceiptBatchConfirmation, {
    method: 'POST',
    body: params,
  });
}

export async function returnReceiptNumbers(params: any) {
  return request(returnReceipt.returnReceiptNumbers, {
    method: 'POST',
    body: params,
  });
}

export async function returnReceiptExcelDownload(params: any) {
  return request(returnReceipt.returnReceiptExcelDownload, {
    method: 'POST',
    body: params,
  });
}

/*
 *@Description: 仓外海外 outsideTheWarehouse
 *@MethodAuthor: dangh
 *@Date: 2023-04-11 16:46:42
 */

const outsideTheWarehouse = {
  outsideTheWarehouseSelectPage: '/csc/outsideWarehouse/selectPage', // 仓外/海外列表查询POST
  outsideTheWarehouseGetAbnormalType: '/csc/outsideWarehouse/getAbnormalType', // 所有异常原因GET
  outsideTheWarehouseCustomerHandle: '/csc/outsideWarehouse/customerHandle', // 单个处理仓外POST
  outsideTheWarehouseCustomerBatchHandle: '/csc/outsideWarehouse/customerBatchHandle', // 批量处理POST
  outsideTheWarehouseDownload: '/csc/outsideWarehouse/download', // 导出文件POST
  outsideTheWarehousePortionDownload: '/csc/outsideWarehouse/portionDownload', // 批量导出文件POST
  calculate: '/csc/outsideWarehouse/calculate', // 计算预估重派费POST
  outsideOverseaBatchHandle: '/csc/outsideWarehouse/batchHandleOversea', // 海外派海外重派批量处理
  outsideOverseaExportHandle: '/csc/outsideWarehouse/exportOverseaReSend', // 海外派海外重派导出
  importOverseaBatch: '/csc/outsideWarehouse/importOverseaBatch', // 海外派海外重派导入
};

export async function importOverseaBatch(params: any) {
  return request(outsideTheWarehouse.importOverseaBatch, {
    method: 'POST',
    body: params,
  });
}

export async function outsideOverseaExportHandle(params: any) {
  return request(outsideTheWarehouse.outsideOverseaExportHandle, {
    method: 'POST',
    body: params,
  });
}

export async function outsideOverseaBatchHandle(params: any) {
  return request(outsideTheWarehouse.outsideOverseaBatchHandle, {
    method: 'POST',
    body: params,
  });
}

export async function outsideTheWarehouseSelectPage(params: any) {
  return request(outsideTheWarehouse.outsideTheWarehouseSelectPage, {
    method: 'POST',
    body: params,
  });
}

export async function outsideTheWarehouseGetAbnormalType(params: any) {
  return request(outsideTheWarehouse.outsideTheWarehouseGetAbnormalType, {
    method: 'GET',
    body: params,
  });
}

export async function outsideTheWarehouseCustomerHandle(params: any) {
  return request(outsideTheWarehouse.outsideTheWarehouseCustomerHandle, {
    method: 'POST',
    body: params,
  });
}

export async function outsideTheWarehouseCustomerBatchHandle(params: any) {
  return request(outsideTheWarehouse.outsideTheWarehouseCustomerBatchHandle, {
    method: 'POST',
    body: params,
  });
}

export async function outsideTheWarehouseDownload(params: any) {
  return request(outsideTheWarehouse.outsideTheWarehouseDownload, {
    method: 'POST',
    body: params,
  });
}
export async function outsideTheWarehousePortionDownload(params: any) {
  return request(outsideTheWarehouse.outsideTheWarehousePortionDownload, {
    method: 'POST',
    body: params,
  });
}
export async function calculate(params: any) {
  return request(outsideTheWarehouse.calculate, {
    method: 'POST',
    body: params,
  });
}
