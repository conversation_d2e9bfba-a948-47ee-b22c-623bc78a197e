import request from '@/utils/request';

const user = {
  userInfo: '/csc/api/sys/user/info', // 获取用户信息
  initializePassword: '/csc/user/initializePassword', // 子用户重置密码
  encodeMerchantCode: '/csc/crypto/encodeMerchantCode', // 转码业务账号，提供追踪使用
  getAuthorityStatus: '/csc/index/getAuthorityStatus', // 客户进入页面权限控制
  helpUrl: '/csc/help/url', // 获取帮助中心链接
  getNotification: '/csc/sys/content/getNotification', // 最新通知
  singleSignOn: '/csc/singleSignOn', // 单点登录
  MenuSearch: '/csc/index/MenuSearch', // 菜单搜索栏
  doYouNeedToShow: '/csc/operationGuide/doYouNeedToShow', // 是否需要展示操作指引
  viewTheEnd: '/csc/operationGuide/viewTheEnd', // 展示结束
  checkIfShowBill: '/csc/index/checkIfShowBill', // 是否可以查看账单
  querySafeInfo: '/csc/user/querySafeInfo', // 查询安全登录信息
  enableSafeNotice: '/csc/user/enableSafeNotice', // 启用安全登录信息
  validatePhoneOrEmailExist: '/csc/user/validatePhoneOrEmailExist', //校验手机号是否存在
  submitUpdatePhoneApply: '/csc/user/submitUpdatePhoneApply', // 提交修改手机号申请
  getUpdatePhoneResult: '/csc/user/getUpdatePhoneResult', // 查询修改手机号申请
};

export async function getUpdatePhoneResult() {
  return request(user.getUpdatePhoneResult, {
    method: 'GET',
  });
}

export async function submitUpdatePhoneApply(params) {
  return request(user.submitUpdatePhoneApply, {
    method: 'POST',
    body: params,
  });
}

export async function validatePhoneOrEmailExist(params) {
  return request(user.validatePhoneOrEmailExist, {
    method: 'POST',
    body: params,
    noMessage: true,
  });
}

export async function querySafeInfo() {
  return request(user.querySafeInfo, {
    method: 'GET',
  });
}

export async function enableSafeNotice(params) {
  return request(user.enableSafeNotice, {
    method: 'POST',
    body: params,
  });
}

export async function checkIfShowBill(params) {
  return request(user.checkIfShowBill, {
    method: 'POST',
    body: params,
  });
}

export async function viewTheEnd(params) {
  return request(user.viewTheEnd, {
    method: 'POST',
    body: params,
  });
}

export async function doYouNeedToShow(params) {
  return request(user.doYouNeedToShow, {
    method: 'POST',
    body: params,
  });
}

export async function MenuSearch() {
  return request(user.MenuSearch, {
    method: 'GET',
  });
}

export async function queryCurrent() {
  return request(user.userInfo, {
    method: 'POST',
    noMessage: true,
  });
}

export async function initializePassword(params) {
  return request(user.initializePassword, {
    method: 'POST',
    body: params,
  });
}

export async function encodeMerchantCode() {
  return request(user.encodeMerchantCode, {
    method: 'POST',
  });
}

export async function getAuthorityStatus(params) {
  return request(user.getAuthorityStatus, {
    method: 'POST',
    body: params,
  });
}

export async function getHelpUrl(params) {
  return request(user.helpUrl, {
    method: 'post',
    body: params,
  });
}

export async function getNotification() {
  return request(user.getNotification, {
    method: 'GET',
  });
}

export async function singleSignOn() {
  return request(user.singleSignOn, {
    method: 'GET',
  });
}
