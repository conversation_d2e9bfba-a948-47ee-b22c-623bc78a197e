import request from '@/utils/request';

export const overseas = {
  getForecastTemplate: '/csc/oversea/getForecastTemplate', // 获取预报模板
  getOverseaAccounts: '/csc/oversea/getOverseaAccounts', // 查询海外派制单账号
  getOverseaMerchantInfo: '/csc/oversea/getOverseaMerchantInfo', //查询海外派客户信息
  getOrResetKey: '/csc/oversea/getOrResetKey', //获取或重置密钥 POSTs
  getRefundList: '/csc/oversea/getRefundList', //查询海外派退款工单列表 POST
  createRefundOder: '/csc/oversea/createRefundOder', //创建海外派退款工单 POST
  getOverseaRefundTemplate: '/csc/oversea/getOverseaRefundTemplate', //获取海外派退款工单批量上传模版 GET
  getClaimDamageList: '/csc/oversea/getClaimDamageList', //获取海外派索赔工单列表 POST
  createOverseaClaimDamageOrder: '/csc/oversea/createOverseaClaimDamageOrder', //创建海外派索赔工单 POST
  batchHandleRefund: '/csc/oversea/batchHandleRefund', //批量创建海外派退款工单 POST
  getOversea: '/csc/oversea/getOversea', //查询海外派订单列表 POST
  getOverseaDetail: '/csc/oversea/getOverseaDetail', //查询海外派订单详情 POST
  exportOverseaDetail: '/csc/oversea/exportOverseaDetail', //导出海外派订单详情 POST
  exportRefundData: '/csc/oversea/exportRefundData', //海外派退款工单导出 GET
  updateEncryptInfo: '/csc/oversea/updateEncryptInfo', // 更新海外派业务账号是否加密ioss税号
  getPublicKey: '/csc/oversea/getPublicKey', // 获取秘钥
  addForecast: '/csc/oversea/addForecast', // 预报提交接口
  getForecastList: '/csc/oversea/getForecastList', // 查询列表接口
  getForecastDetail: '/csc/oversea/getForecastDetail', // 查询详情
  getForecastChannelList: '/csc/oversea/getForecastChannelList', // 查询导入预报产品接口
  saveOverseaApply: '/csc/oversea/saveOverseaApply', // 海外派业务提交申请
  getOverseaApplyInfo: '/csc/oversea/getOverseaApplyInfo', // 海外派业务查询业务
  getInjectWarehouseList: '/csc/oversea/getInjectWarehouseList', // 查询海外派入库仓库列表
  addAddress: '/csc/oversea/addAddress', // 海外派多地址新增
  getAddressList: '/csc/oversea/getAddressList', // 海外派多地址查询
  updateAddress: '/csc/oversea/updateAddress', // 海外派多地址更新
  deleteAddress: '/csc/oversea/deleteAddress', // 海外派地址删除
  getYWEBankAccountList: '/csc/settleBankAccount/getYWEBankAccountList', // getYWEBankAccountList
  exportClaimOrder: '/csc/oversea/exportClaimOrder', // 导出索赔工单
  exportClaimTemplate: '/csc/oversea/exportClaimTemplate', // 导出索赔工单
  importClaimData: '/csc/oversea/importClaimData', // 导出索赔工单
  cancelOverseaOrder: '/csc/oversea/cancelOverseaOrder', // ywe业务取消订单
  cancelOverseaOrderWithDebitPage: '/csc/oversea/cancelOverseaOrderWithDebitPage', // ywe业务在预扣款取消订单
};

export async function cancelOverseaOrderWithDebitPage(params) {
  return request(`${overseas.cancelOverseaOrderWithDebitPage}`, {
    method: 'POST',
    body: params,
  });
}

export async function cancelOverseaOrder(params) {
  return request(`${overseas.cancelOverseaOrder}`, {
    method: 'POST',
    body: params,
  });
}

export async function importClaimData(params) {
  return request(`${overseas.importClaimData}`, {
    method: 'POST',
    body: params,
  });
}

export async function exportClaimTemplate(params) {
  return request(`${overseas.exportClaimTemplate}`, {
    method: 'GET',
  });
}

export async function exportClaimOrder(params) {
  return request(`${overseas.exportClaimOrder}`, {
    method: 'POST',
    body: params,
  });
}

export async function getYWEBankAccountList(params) {
  return request(`${overseas.getYWEBankAccountList}`, {
    method: 'POST',
    body: params,
  });
}

export async function addAddress(params) {
  return request(`${overseas.addAddress}`, {
    method: 'POST',
    body: params,
  });
}

export async function getAddressList(params) {
  return request(`${overseas.getAddressList}`, {
    method: 'POST',
    body: params,
  });
}

export async function updateAddress(params) {
  return request(`${overseas.updateAddress}`, {
    method: 'POST',
    body: params,
  });
}

export async function deleteAddress(params) {
  return request(`${overseas.deleteAddress}`, {
    method: 'POST',
    body: params,
  });
}

export async function getInjectWarehouseList(params) {
  return request(`${overseas.getInjectWarehouseList}`, {
    method: 'POST',
    body: params,
  });
}

export async function getOverseaApplyInfo(params) {
  return request(`${overseas.getOverseaApplyInfo}`, {
    method: 'POST',
    body: params,
  });
}

export async function saveOverseaApply(params) {
  return request(`${overseas.saveOverseaApply}`, {
    method: 'POST',
    body: params,
  });
}

export async function getForecastChannelList(params) {
  return request(`${overseas.getForecastChannelList}`, {
    method: 'POST',
    body: params,
  });
}

export async function getForecastDetail(params) {
  return request(`${overseas.getForecastDetail}`, {
    method: 'POST',
    body: params,
  });
}

export async function getForecastList(params) {
  return request(`${overseas.getForecastList}`, {
    method: 'POST',
    body: params,
  });
}

export async function addForecast(params) {
  return request(`${overseas.addForecast}`, {
    method: 'POST',
    body: params,
  });
}

export async function getPublicKey(params) {
  return request(`${overseas.getPublicKey}`, {
    method: 'POST',
    body: params,
  });
}

export async function updateEncryptInfo(params) {
  return request(`${overseas.updateEncryptInfo}`, {
    method: 'POST',
    body: params,
  });
}

export async function exportRefundData(params) {
  return request(`${overseas.exportRefundData}`, {
    method: 'POST',
    body: params,
  });
}

export async function exportOverseaDetail(params) {
  return request(`${overseas.exportOverseaDetail}`, {
    method: 'POST',
    body: params,
  });
}

export async function getOversea(params) {
  return request(`${overseas.getOversea}`, {
    method: 'POST',
    body: params,
  });
}

export async function getOverseaDetail(params) {
  return request(`${overseas.getOverseaDetail}`, {
    method: 'POST',
    body: params,
  });
}

export async function batchHandleRefund(params) {
  return request(`${overseas.batchHandleRefund}`, {
    method: 'POST',
    body: params,
  });
}

export async function createOverseaClaimDamageOrder(params) {
  return request(`${overseas.createOverseaClaimDamageOrder}`, {
    method: 'POST',
    body: params,
  });
}

export async function getClaimDamageList(params) {
  return request(`${overseas.getClaimDamageList}`, {
    method: 'POST',
    body: params,
  });
}

export async function getOverseaRefundTemplate() {
  return request(`${overseas.getOverseaRefundTemplate}`, {
    method: 'GET',
  });
}

export async function createRefundOder(params) {
  return request(`${overseas.createRefundOder}`, {
    method: 'POST',
    body: params,
  });
}

export async function getRefundList(params) {
  return request(`${overseas.getRefundList}`, {
    method: 'POST',
    body: params,
  });
}

export async function getOverseaAccounts(params) {
  return request(`${overseas.getOverseaAccounts}?type=${params}`, {
    method: 'GET',
  });
}

export async function getOverseaMerchantInfo(params) {
  return request(`${overseas.getOverseaMerchantInfo}`, {
    method: 'GET',
  });
}

export async function getOrResetKey(params) {
  return request(`${overseas.getOrResetKey}`, {
    method: 'POST',
    body: params,
  });
}
