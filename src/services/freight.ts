import request from '@/utils/request';

const freight = {
  getWarehouses: '/csc/plm/warehouses', // // ---------- 获取所有出发地---------------
  getCountries: '/csc/plm/countries', // // ---------- 获取所有目的国---------------
  getProducts: '/csc/plm/products', // // ---------- 获取所有产品---------------
  chargePrice: '/csc/web/chargePrice', // // ---------- 列表---------------
  chargePriceOverseas: '/csc/web/chargePriceOverseas', // // ---------- 列表---------------
  quotationInfo: '/csc/quotation/index', // 报价单揽收信息
  quotationDownloadList: '/csc/quotation/listQuotation', // 报价单列表
  downloadCount: '/csc/quotation/downloadCount/increase', // 报价单次数下载增加
  applyQuotation: '/csc/quotation/applyQuotation', // 报价单申请
  manyPieceBatch: '/csc/fba/manyPieceBatch', // FBA运价试算
  busManyPieceBatch: '/csc/business/manyPieces', // 商业快递运价试算
  getCalcConfig: '/csc/fba/getCalcConfig', // FBA运价试算
  manyPiece: '/csc/fba/manyPiece', // FBA运价试算
};

export async function applyQuotation(params) {
  return request(freight.applyQuotation, {
    method: 'POST',
    body: params,
  });
}

export async function getWarehouses(params) {
  return request(freight.getWarehouses, {
    method: 'POST',
    body: params,
  });
}

export async function getCalcConfig(params) {
  return request(freight.getCalcConfig, {
    method: 'POST',
    body: params,
  });
}

export async function getCountries(params) {
  return request(freight.getCountries, {
    method: 'POST',
    body: params,
  });
}

export async function getProducts(params) {
  return request(freight.getProducts, {
    method: 'POST',
    body: params,
  });
}

export async function chargePrice(params) {
  return request(freight.chargePrice, {
    method: 'POST',
    body: params,
  });
}

export async function chargePriceOverseas(params) {
  return request(freight.chargePriceOverseas, {
    method: 'POST',
    body: params,
  });
}

export async function getQuotationInfo() {
  return request(freight.quotationInfo, {
    method: 'GET',
  });
}

export async function quotationDownloadList(params) {
  return request(freight.quotationDownloadList, {
    method: 'POST',
    body: params,
  });
}

export async function manyPieceBatch(params) {
  return request(freight.manyPieceBatch, {
    method: 'POST',
    body: params,
  });
}

export async function busManyPieceBatch(params) {
  return request(freight.busManyPieceBatch, {
    method: 'POST',
    body: params,
  });
}

export async function manyPiece(params) {
  return request(freight.manyPiece, {
    method: 'POST',
    body: params,
  });
}

export async function downloadCount(param) {
  return request(freight.downloadCount + `?quotationId=${param.quotationId}`, {
    method: 'GET',
  });
}
