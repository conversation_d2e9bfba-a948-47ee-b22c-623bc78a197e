import { ProFormInstance } from '@ant-design/pro-components';
import moment from 'moment';

declare global {
  namespace Common {
    interface SearchIProps<T> {
      formRef: React.MutableRefObject<ProFormInstance<T> | undefined>;
      accountData: Array<{ [key: string]: any } & { label: string; value: any }>;
      onSearch: (values: T | undefined) => void;
      buttonLoading: boolean;
      dateTime: Array<moment.Moment>;
      [key: string]: any;
    }
    interface PageProps {
      dispatch: Function;
      location: any;
      [key: string]: unknown;
    }
    interface ResponseData<T> {
      code: string;
      data?: T;
      success: boolean;
      message: string;
      [key: string]: any;
    }
    interface PaginationState {
      current: number;
      pageSize: number;
      total: number;
    }
  }
}

export {};
